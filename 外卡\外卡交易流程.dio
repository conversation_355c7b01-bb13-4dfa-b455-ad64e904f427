<mxfile host="65bd71144e">
    <diagram id="8fWUujHTjNdiXavQaFgq" name="外卡交易业务流">
        <mxGraphModel dx="906" dy="607" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="商户" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="41" y="240" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="宇世&lt;br&gt;（代理商）" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="165" y="240" width="86" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="" style="endArrow=classic;html=1;" parent="1" source="3" target="7" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="350" y="200" as="sourcePoint"/>
                        <mxPoint x="400" y="150" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="8" value="万事达交易" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="4" vertex="1" connectable="0">
                    <mxGeometry x="0.2332" y="-1" relative="1" as="geometry">
                        <mxPoint y="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="5" value="" style="endArrow=classic;html=1;" parent="1" source="2" target="3" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="91" y="270" as="sourcePoint"/>
                        <mxPoint x="141" y="220" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="6" value="外卡交易" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="5" vertex="1" connectable="0">
                    <mxGeometry x="0.2" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="7" value="易票联外卡&lt;br&gt;（斯贝思）" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="362" y="120" width="86" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="会员系统&lt;br&gt;（万开）" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="523" y="120" width="86" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="" style="endArrow=classic;html=1;" parent="1" source="7" target="9" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="480" y="190" as="sourcePoint"/>
                        <mxPoint x="530" y="140" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="12" value="万事达" style="points=[];aspect=fixed;html=1;align=center;shadow=0;dashed=0;image;image=img/lib/allied_telesis/storage/Datacenter_Server_Half_Rack_ToR.svg;" parent="1" vertex="1">
                    <mxGeometry x="682" y="110" width="61.57" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="" style="endArrow=classic;html=1;" parent="1" source="9" target="12" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="650" y="149.5" as="sourcePoint"/>
                        <mxPoint x="714" y="149.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" value="开先&lt;br&gt;（技术服务商）" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="362" y="330" width="86" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="Visa" style="points=[];aspect=fixed;html=1;align=center;shadow=0;dashed=0;image;image=img/lib/allied_telesis/storage/Datacenter_Server_Half_Rack_ToR.svg;" parent="1" vertex="1">
                    <mxGeometry x="682" y="320" width="61.57" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="" style="endArrow=classic;html=1;" parent="1" source="3" target="14" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="130" y="280" as="sourcePoint"/>
                        <mxPoint x="224" y="280" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="17" value="Visa交易" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="16" vertex="1" connectable="0">
                    <mxGeometry x="0.2" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="" style="endArrow=classic;html=1;" parent="1" source="14" target="15" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="470" y="359.5" as="sourcePoint"/>
                        <mxPoint x="554" y="359.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="mVsUpTt6vBJXhSls-s89" name="资金结算流">
        <mxGraphModel dx="906" dy="607" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="N2o2pCTGt00CEG1Pa1Om-1" value="Visa" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="127" y="150" width="90" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="N2o2pCTGt00CEG1Pa1Om-2" value="MasterCard" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="127" y="320" width="90" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="N2o2pCTGt00CEG1Pa1Om-3" value="中银HK&lt;br&gt;（无DDA）" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" vertex="1" parent="1">
                    <mxGeometry x="440" y="320" width="90" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="N2o2pCTGt00CEG1Pa1Om-5" value="CurrencyCloud" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" vertex="1" parent="1">
                    <mxGeometry x="440" y="150" width="90" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="N2o2pCTGt00CEG1Pa1Om-6" value="" style="endArrow=classic;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fillColor=#60a917;strokeColor=#2D7600;strokeWidth=2;" edge="1" parent="1" source="N2o2pCTGt00CEG1Pa1Om-1" target="N2o2pCTGt00CEG1Pa1Om-5">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="300" y="230" as="sourcePoint"/>
                        <mxPoint x="350" y="180" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="N2o2pCTGt00CEG1Pa1Om-7" value="资金结算&lt;br&gt;（Visa只能结算到美国银行户）" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="N2o2pCTGt00CEG1Pa1Om-6">
                    <mxGeometry x="-0.1875" y="-1" relative="1" as="geometry">
                        <mxPoint x="21" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="N2o2pCTGt00CEG1Pa1Om-8" value="代理商" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
                    <mxGeometry x="689" y="230" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="N2o2pCTGt00CEG1Pa1Om-9" value="直客" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
                    <mxGeometry x="689" y="380" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="N2o2pCTGt00CEG1Pa1Om-10" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fillColor=#1ba1e2;strokeColor=#006EAF;strokeWidth=2;" edge="1" parent="1" source="N2o2pCTGt00CEG1Pa1Om-2" target="N2o2pCTGt00CEG1Pa1Om-3">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="300" y="340" as="sourcePoint"/>
                        <mxPoint x="350" y="290" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="N2o2pCTGt00CEG1Pa1Om-11" value="资金结算" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="N2o2pCTGt00CEG1Pa1Om-10">
                    <mxGeometry x="0.3689" y="-3" relative="1" as="geometry">
                        <mxPoint x="-32" y="-3" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="N2o2pCTGt00CEG1Pa1Om-12" value="" style="endArrow=classic;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=0.86;exitDx=0;exitDy=0;exitPerimeter=0;fillColor=#60a917;strokeColor=#2D7600;strokeWidth=2;" edge="1" parent="1" source="N2o2pCTGt00CEG1Pa1Om-5" target="N2o2pCTGt00CEG1Pa1Om-3">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="480" y="190" as="sourcePoint"/>
                        <mxPoint x="620" y="220" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="N2o2pCTGt00CEG1Pa1Om-13" value="付款到中银户" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="N2o2pCTGt00CEG1Pa1Om-12">
                    <mxGeometry x="0.1496" y="2" relative="1" as="geometry">
                        <mxPoint y="-16" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="N2o2pCTGt00CEG1Pa1Om-14" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;fillColor=#a20025;strokeColor=#6F0000;strokeWidth=2;" edge="1" parent="1" source="N2o2pCTGt00CEG1Pa1Om-3" target="N2o2pCTGt00CEG1Pa1Om-8">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="580" y="360" as="sourcePoint"/>
                        <mxPoint x="630" y="310" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="N2o2pCTGt00CEG1Pa1Om-15" value="交易结算&lt;br&gt;（手动）" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="N2o2pCTGt00CEG1Pa1Om-14">
                    <mxGeometry x="0.1249" y="2" relative="1" as="geometry">
                        <mxPoint x="-18" y="6" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="N2o2pCTGt00CEG1Pa1Om-16" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fillColor=#a20025;strokeColor=#6F0000;strokeWidth=2;" edge="1" parent="1" source="N2o2pCTGt00CEG1Pa1Om-3" target="N2o2pCTGt00CEG1Pa1Om-9">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="540" y="342.5" as="sourcePoint"/>
                        <mxPoint x="765" y="270" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="N2o2pCTGt00CEG1Pa1Om-17" value="交易结算&lt;br&gt;（手动）" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="N2o2pCTGt00CEG1Pa1Om-16">
                    <mxGeometry x="0.1249" y="2" relative="1" as="geometry">
                        <mxPoint x="-18" y="6" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="gZQA2rjFBSm4rCRQ9JuH-1" value="①Visa直接结算到美国银行户，因此不能直接结算到中银HK户（MC可以）&lt;br&gt;②CC主要是协助Visa外卡结算户使用，不作汇通收付款业务上游&lt;br&gt;③目前外卡交易结算都是从中银HK账户出&lt;br&gt;④CC的账户主体和中银HK的账户主体不同" style="text;html=1;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;spacingTop=1;spacingBottom=1;" vertex="1" parent="1">
                    <mxGeometry x="140" y="440" width="420" height="70" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>