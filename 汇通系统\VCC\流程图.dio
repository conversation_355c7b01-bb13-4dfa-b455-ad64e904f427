<mxfile host="65bd71144e">
    <diagram id="s2h-ekjKb1Cm74xS_uam" name="充值回款V1">
        <mxGraphModel dx="836" dy="607" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="商户" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="80" y="240" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="汇通系统" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="280" y="360" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="EPSP" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="640" y="360" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="跨境系统" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="280" y="120" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="寻汇" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="640" y="120" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="" style="endArrow=classic;html=1;fillColor=#1ba1e2;strokeColor=#006EAF;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="340" as="sourcePoint"/>
                        <mxPoint x="260" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="8" value="1、申请充值" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="7" vertex="1" connectable="0">
                    <mxGeometry x="0.2958" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="9" value="" style="endArrow=classic;html=1;strokeColor=#006EAF;fillColor=#1ba1e2;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="430" y="410" as="sourcePoint"/>
                        <mxPoint x="610" y="410" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="2、充值下单" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="9" vertex="1" connectable="0">
                    <mxGeometry x="0.2917" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="" style="endArrow=classic;html=1;strokeColor=#006EAF;fillColor=#1ba1e2;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="130" y="280" as="sourcePoint"/>
                        <mxPoint x="620" y="380" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="12" value="3、完成充值" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="11" vertex="1" connectable="0">
                    <mxGeometry x="0.092" relative="1" as="geometry">
                        <mxPoint x="42" y="5" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" value="" style="edgeStyle=none;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#006EAF;fillColor=#1ba1e2;" parent="1" edge="1">
                    <mxGeometry width="100" relative="1" as="geometry">
                        <mxPoint x="620" y="380" as="sourcePoint"/>
                        <mxPoint x="430" y="380" as="targetPoint"/>
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="15" value="4、通知加额" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="14" vertex="1" connectable="0">
                    <mxGeometry x="0.2368" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="" style="edgeStyle=none;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#e3c800;strokeColor=#B09500;" parent="1" edge="1">
                    <mxGeometry width="100" relative="1" as="geometry">
                        <mxPoint x="330" y="340" as="sourcePoint"/>
                        <mxPoint x="330" y="200" as="targetPoint"/>
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="17" value="6、申请资金出境" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="16" vertex="1" connectable="0">
                    <mxGeometry x="0.3238" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="" style="edgeStyle=none;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#B09500;fillColor=#e3c800;" parent="1" edge="1">
                    <mxGeometry width="100" relative="1" as="geometry">
                        <mxPoint x="420" y="140" as="sourcePoint"/>
                        <mxPoint x="620" y="140" as="targetPoint"/>
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="7、跨境资金出境&lt;br&gt;（资金进入多币种钱包）" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="18" vertex="1" connectable="0">
                    <mxGeometry x="-0.0917" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="20" value="" style="edgeStyle=none;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#e51400;strokeColor=#B20000;" parent="1" edge="1">
                    <mxGeometry width="100" relative="1" as="geometry">
                        <mxPoint x="130" y="310" as="sourcePoint"/>
                        <mxPoint x="260" y="370" as="targetPoint"/>
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="8、申请转入卡账户" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="20" vertex="1" connectable="0">
                    <mxGeometry x="0.1242" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="22" value="" style="edgeStyle=none;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#B20000;fillColor=#e51400;" parent="1" edge="1">
                    <mxGeometry width="100" relative="1" as="geometry">
                        <mxPoint x="350" y="350" as="sourcePoint"/>
                        <mxPoint x="630" y="180" as="targetPoint"/>
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="23" value="9、资金转入卡账户指令" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="22" vertex="1" connectable="0">
                    <mxGeometry x="0.1445" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="24" value="运营人员" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="314" y="561" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="" style="edgeStyle=none;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#e3c800;strokeColor=#B09500;" parent="1" edge="1">
                    <mxGeometry width="100" relative="1" as="geometry">
                        <mxPoint x="330" y="550" as="sourcePoint"/>
                        <mxPoint x="330" y="440" as="targetPoint"/>
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="27" value="5、申请回款" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="26" vertex="1" connectable="0">
                    <mxGeometry x="0.3238" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="33" value="充值完成，资金进入ACS" style="shape=note;strokeWidth=2;fontSize=14;size=20;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontColor=#666600;dashed=1;" parent="1" vertex="1">
                    <mxGeometry x="800" y="365" width="130" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="34" value="资金从香港人跨户进入寻汇多币种钱包" style="shape=note;strokeWidth=2;fontSize=14;size=20;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontColor=#666600;dashed=1;" parent="1" vertex="1">
                    <mxGeometry x="800" y="125" width="130" height="50" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="wts-7a1rJ1io_c8A5dt5" name="充值回款V2">
        <mxGraphModel dx="836" dy="607" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="eFhVqCYSKCo3zSC6fi0U-1" value="全球付" style="rounded=0;whiteSpace=wrap;html=1;dashed=1;fillStyle=hatch;fillColor=#647687;fontColor=#ffffff;strokeColor=#314354;" parent="1" vertex="1">
                    <mxGeometry x="6" y="200" width="290" height="270" as="geometry"/>
                </mxCell>
                <mxCell id="eFhVqCYSKCo3zSC6fi0U-2" value="商户&lt;br&gt;VA账户" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="86" y="241" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="eFhVqCYSKCo3zSC6fi0U-3" value="寻汇" style="rounded=0;whiteSpace=wrap;html=1;dashed=1;fillStyle=cross-hatch;fillColor=#76608a;fontColor=#ffffff;strokeColor=#432D57;" parent="1" vertex="1">
                    <mxGeometry x="356" y="200" width="460" height="270" as="geometry"/>
                </mxCell>
                <mxCell id="eFhVqCYSKCo3zSC6fi0U-4" value="易票联国际&lt;br&gt;多币种账户" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="436" y="236" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="ynaLNOXwFo9R_olNYjCR-1" value="商户银行账户" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="86" y="49" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="ynaLNOXwFo9R_olNYjCR-2" value="" style="endArrow=classic;html=1;" parent="1" source="ynaLNOXwFo9R_olNYjCR-1" target="eFhVqCYSKCo3zSC6fi0U-2" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="306" y="640" as="sourcePoint"/>
                        <mxPoint x="356" y="590" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="ynaLNOXwFo9R_olNYjCR-3" value="VA收款" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ynaLNOXwFo9R_olNYjCR-2" vertex="1" connectable="0">
                    <mxGeometry x="-0.087" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="ynaLNOXwFo9R_olNYjCR-4" value="" style="endArrow=classic;html=1;" parent="1" source="SjvFvFQupvelQG_byDsq-1" target="eFhVqCYSKCo3zSC6fi0U-4" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="306" y="251" as="sourcePoint"/>
                        <mxPoint x="356" y="201" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="ynaLNOXwFo9R_olNYjCR-5" value="全球付付款接口&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;同名转账&lt;br&gt;&lt;/font&gt;（资金回款）&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ynaLNOXwFo9R_olNYjCR-4" vertex="1" connectable="0">
                    <mxGeometry x="0.1477" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="ynaLNOXwFo9R_olNYjCR-6" value="易票联国际&lt;br&gt;卡账户" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="441" y="370" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="ynaLNOXwFo9R_olNYjCR-7" value="资金转入" style="curved=1;endArrow=classic;html=1;" parent="1" source="eFhVqCYSKCo3zSC6fi0U-4" target="ynaLNOXwFo9R_olNYjCR-6" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="426" y="290" as="sourcePoint"/>
                        <mxPoint x="450" y="350" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="416" y="320"/>
                            <mxPoint x="396" y="350"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="SjvFvFQupvelQG_byDsq-1" value="易票联国际&lt;br&gt;VA账户" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="86" y="370" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="SjvFvFQupvelQG_byDsq-2" value="资金调拨" style="curved=1;endArrow=classic;html=1;" parent="1" target="SjvFvFQupvelQG_byDsq-1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="85.99555555555543" y="290" as="sourcePoint"/>
                        <mxPoint x="75.44" y="365.42857142857144" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="50.44" y="314"/>
                            <mxPoint x="30.439999999999998" y="344"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="SjvFvFQupvelQG_byDsq-4" value="虚拟卡" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="669" y="370" width="113" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="SjvFvFQupvelQG_byDsq-5" value="开卡/充值" style="endArrow=classic;html=1;" parent="1" source="ynaLNOXwFo9R_olNYjCR-6" target="SjvFvFQupvelQG_byDsq-4" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="586" y="510" as="sourcePoint"/>
                        <mxPoint x="636" y="460" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="SjvFvFQupvelQG_byDsq-6" value="持卡人" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="690" y="481" width="20" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="SjvFvFQupvelQG_byDsq-7" value="商家" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" parent="1" vertex="1">
                    <mxGeometry x="668" y="570" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="SjvFvFQupvelQG_byDsq-8" value="消费" style="endArrow=classic;html=1;" parent="1" source="SjvFvFQupvelQG_byDsq-4" target="SjvFvFQupvelQG_byDsq-7" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="440" y="610" as="sourcePoint"/>
                        <mxPoint x="490" y="560" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>