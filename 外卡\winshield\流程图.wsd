@startuml 流程图

actor 用户 as user
participant 商户 as mer
participant 外卡 as wk
participant Winshield as 3ds
participant 发卡行 as bk

user -> mer: 请求支付
mer -> wk: 发起支付请求
wk -> wk: 下单

==3DS初始化==

wk->3ds: 请求获取Token
note left of 3ds: 接口Auth
3ds-->wk: 返回Token

wk -> 3ds: 是否支持3DS并初始化
note left of 3ds
接口Initiate
参数：token、订单信息、浏览器信息
end note
3ds --> wk: 返回3DS支持信息+银行URL

wk --> mer: 返回3DS支持信息+银行URL
mer --> user: 跳转银行URL
user -> bk: 访问发卡行URL,WEB浏览器指纹识别
bk --> user: 用户表单提交完成
note right of user: 浏览器提交后，发卡行有数据了，表单返回到哪？

==3DS授权==
note right of user: 下面的3DS授权操作，如何开始触发？
wk -> 3ds: 发起3DS授权
note left of 3ds: 接口：Authorize
3ds --> wk: 返回3DS授权结果
note left of 3ds
    transactionStatus: REDIRECT、APPROVED、DECLINED
    eci、cavv、xid
    acsChallengeMandated：是否强制ACS挑战
end note

alt transactionStatus=REDIRECT（3D安全挑战）
    user -> bk: 提交表单，访问ACS URL({threeD.creq, threeD.acsUrl)
    bk --> user: 返回
    note right of user: 浏览器提交后，表单返回到哪？

    wk -> 3ds: 验证3DS的挑战结果
    note left of 3ds: 接口：Challenge
    3ds --> wk: 返回3DS挑战结果
    note left of 3ds
        transStatus: Y（挑战成功）、N（挑战失败）
    end note
    alt transStatus=N（申请身份验证）
        wk -> 3ds: 请求身份认证
        note left of 3ds: 接口：Verify
        3ds --> wk: 返回3DS授权结果
        note left of 3ds
            transactionStatus: REDIRECT？、APPROVED、DECLINED
            eci、cavv、xid
            acsChallengeMandated：是否强制ACS挑战
        end note
    end
end

@enduml