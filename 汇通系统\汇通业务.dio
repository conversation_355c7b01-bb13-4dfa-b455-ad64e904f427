<mxfile host="65bd71144e">
    <diagram id="l1Pjtj0jCS6I0i6PsrCc" name="业务">
        <mxGraphModel dx="804" dy="301" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="14" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="640" y="56" width="120" height="210" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="190" y="100" width="360" height="390" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="商户门户" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="210" y="170" width="80" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="API网关" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="210" y="230" width="80" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="代理商门户" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="210" y="290" width="80" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="VA业务" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="420" y="135" width="90" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="VCC业务" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="420" y="280" width="90" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="Skyee" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;" parent="1" vertex="1">
                    <mxGeometry x="650" y="66" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="全球付" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;" parent="1" vertex="1">
                    <mxGeometry x="650" y="116" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="CC" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;" parent="1" vertex="1">
                    <mxGeometry x="650" y="166" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="HT MSO" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;" parent="1" vertex="1">
                    <mxGeometry x="650" y="216" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="寻汇VCC" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;" parent="1" vertex="1">
                    <mxGeometry x="650" y="285" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="商户" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="50" y="170" width="30" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="" style="endArrow=classic;html=1;" parent="1" source="12" target="2" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="80" y="260" as="sourcePoint"/>
                        <mxPoint x="130" y="210" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="5" target="14" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="580" y="220" as="sourcePoint"/>
                        <mxPoint x="630" y="170" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="17" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="6" target="11" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="590" y="380" as="sourcePoint"/>
                        <mxPoint x="640" y="330" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="代理商" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="50" y="290" width="30" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="" style="endArrow=classic;html=1;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="80" y="309.5" as="sourcePoint"/>
                        <mxPoint x="210" y="309.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="22" value="外卡" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="420" y="390" width="90" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="" style="endArrow=classic;html=1;" parent="1" target="3" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="80" y="190" as="sourcePoint"/>
                        <mxPoint x="220" y="200" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="24" value="易票联外卡" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;" parent="1" vertex="1">
                    <mxGeometry x="650" y="395" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" target="24" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="510" y="414.5" as="sourcePoint"/>
                        <mxPoint x="650" y="414.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="_UhqNxWGEZFXAA83sFdn" name="VA业务资金流">
        <mxGraphModel dx="804" dy="1776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="MfNHS8AXA5xFbeY5hqvu-1" value="境外用户" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="57" y="250" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="MfNHS8AXA5xFbeY5hqvu-2" value="汇通" style="rounded=1;whiteSpace=wrap;html=1;fontSize=20;" parent="1" vertex="1">
                    <mxGeometry x="330" y="120" width="90" height="180" as="geometry"/>
                </mxCell>
                <mxCell id="MfNHS8AXA5xFbeY5hqvu-4" value="" style="endArrow=classic;html=1;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="300" as="sourcePoint"/>
                        <mxPoint x="310" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-6" value="3、&lt;span style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));&quot;&gt;VA收款&lt;/span&gt;&lt;div&gt;&lt;span style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));&quot;&gt;1️⃣服贸收款&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));&quot;&gt;2️⃣香港企业同名充值&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));&quot;&gt;3️⃣电商收款（CC）&lt;/span&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="MfNHS8AXA5xFbeY5hqvu-4" vertex="1" connectable="0">
                    <mxGeometry x="-0.0196" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="MfNHS8AXA5xFbeY5hqvu-5" value="商户账户" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="721" y="149" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="MfNHS8AXA5xFbeY5hqvu-6" value="" style="endArrow=classic;html=1;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="450" y="204" as="sourcePoint"/>
                        <mxPoint x="690" y="190" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="MfNHS8AXA5xFbeY5hqvu-7" value="一般贸易B2B结汇&lt;div&gt;电商B2C结汇&lt;/div&gt;&lt;div&gt;服务贸易结汇&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="MfNHS8AXA5xFbeY5hqvu-6" vertex="1" connectable="0">
                    <mxGeometry x="-0.0334" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-1" value="商户" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="57" y="60" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-2" value="" style="endArrow=classic;startArrow=classic;html=1;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="310" y="140" as="sourcePoint"/>
                        <mxPoint x="110" y="80" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-3" value="1、开通VA业务&lt;div&gt;获取VA信息&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="mwczCmH1MDEkvszJMzDL-2" vertex="1" connectable="0">
                    <mxGeometry x="0.0603" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-4" value="" style="endArrow=classic;html=1;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="77" y="160" as="sourcePoint"/>
                        <mxPoint x="77" y="240" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-5" value="2、提供VA信息" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="mwczCmH1MDEkvszJMzDL-4" vertex="1" connectable="0">
                    <mxGeometry x="-0.35" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-12" value="VCC机构" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" parent="1" vertex="1">
                    <mxGeometry x="686" y="359" width="100" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-13" value="" style="endArrow=classic;html=1;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="450" y="300" as="sourcePoint"/>
                        <mxPoint x="670" y="390" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-14" value="VCC充值（同名）" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="mwczCmH1MDEkvszJMzDL-13" vertex="1" connectable="0">
                    <mxGeometry x="-0.0334" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-15" value="大陆公户" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="721" y="259" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-16" value="" style="endArrow=classic;html=1;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="450" y="259" as="sourcePoint"/>
                        <mxPoint x="700" y="290" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-17" value="Swift汇款" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="mwczCmH1MDEkvszJMzDL-16" vertex="1" connectable="0">
                    <mxGeometry x="-0.0334" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-18" value="境内私户" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="721" y="49" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-19" value="" style="endArrow=classic;html=1;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="450" y="160" as="sourcePoint"/>
                        <mxPoint x="680" y="90" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-20" value="Local付款" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="mwczCmH1MDEkvszJMzDL-19" vertex="1" connectable="0">
                    <mxGeometry x="-0.0334" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-21" value="境外私户" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="721" y="-41" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-22" value="" style="endArrow=classic;html=1;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="450" y="120" as="sourcePoint"/>
                        <mxPoint x="670" y="10" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mwczCmH1MDEkvszJMzDL-23" value="Swift付款" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="mwczCmH1MDEkvszJMzDL-22" vertex="1" connectable="0">
                    <mxGeometry x="-0.0334" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>