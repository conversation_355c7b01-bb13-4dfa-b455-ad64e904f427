<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{ book_name }}</title>
    <script src="{{ url_for('static', filename='javascript/common.js') }}"></script>
    <style>
        .body_style {
            padding: 0 2px; 
            font-size: 20px; 
            line-height: 30px;
            background-color: #a0a0a0;
        }
        h1 {
            font-size: 28px;
            background-color: #a0a0a0;
        }
    </style>
    <script>
        function toggleIframe() {
            var leftChapters = document.getElementById("left_chapters");
            var rightContent = document.getElementById("right_content");

            if (leftChapters.style.display === "none") {
                leftChapters.style.display = "block";
                rightContent.style.width = "81%";
            } else {
                leftChapters.style.display = "none";
                rightContent.style.width = "99%";
            }
        }

        // 页面加载完成后自动调整 iframe 的高度
        window.onload = function() {
            adjustIframeHeight();
            // 检查是否有分屏选项的 cookie
            document.getElementById("split-screen-checkbox").checked = getCookie("options_split_screen") == "yes";
            document.getElementById("scroll-screen-checkbox").checked = getCookie("options_scroll_screen") == "yes";
            document.getElementById("speak-checkbox").checked = getCookie("options_speak") == "yes";

            // 初始化语音合成
            var supportedVoices = getSupportedVoices();
            var voiceName = getCookie("options_speak_voice");
            for(var i = 0; i < supportedVoices.length; i++) {
                var option = document.createElement("option");
                option.value = supportedVoices[i].voice;
                option.text = supportedVoices[i].name;
                if(voiceName == supportedVoices[i].voice) {
                    option.selected = true;
                }
                document.getElementById("voice-select").appendChild(option);
            }

            // 初始化语速
            var speech_rate = getCookie("speech_rate", 1);
            for(var i=0; i<document.getElementById("speech_rate").options.length; i++) {
                if(document.getElementById("speech_rate").options[i].value == speech_rate) {
                    document.getElementById("speech_rate").options[i].selected = true;
                }
            }
        };

        // 窗口大小改变时调整 iframe 的高度
        window.onresize = function() {
            adjustIframeHeight();
        };

        function adjustIframeHeight() {
        };

        // 窗口大小改变时调整 iframe 的高度
        window.onresize = function() {
            adjustIframeHeight();
        };

        function adjustIframeHeight() {
            var rightContent = document.getElementById("right_content");
            rightContent.style.height = (window.innerHeight-80) + "px";
            var leftChapters = document.getElementById("left_chapters");
            leftChapters.style.height = (window.innerHeight-80) + "px";
            // 确保 iframe 的宽度不会超过浏览器窗口的宽度
            if (rightContent.offsetWidth > window.innerWidth) {
                rightContent.style.width = window.innerWidth + "px";
            }
            if (leftChapters.offsetWidth > window.innerWidth) {
                leftChapters.style.width = window.innerWidth + "px";
            }
        }

        function toggleSplitScreen() {
            checked = document.getElementById("split-screen-checkbox").checked;
            setCookie("options_split_screen", checked ? "yes" : "no");
            document.getElementById("right_content").contentWindow.toggleSplitScreen(checked);
        }

        function toggleScrollScreen() {
            checked = document.getElementById("scroll-screen-checkbox").checked;
            setCookie("options_scroll_screen", checked ? "yes" : "no");
            document.getElementById("right_content").contentWindow.toggleScrollScreen(checked);
            if(!checked) {
                document.getElementById("speak-checkbox").checked = false;
                document.getElementById("speak-checkbox").disabled = true;
                setCookie("options_speak", checked ? "yes" : "no");
            } else {
                document.getElementById("speak-checkbox").disabled = false;
            }
        }

        function toggleSpeak() {
            checked = document.getElementById("speak-checkbox").checked;
            setCookie("options_speak", checked ? "yes" : "no");
            //stopSpeak();
        }

        function toggleVoiceChange() {
            var ele = document.getElementById("voice-select");
            setCookie("options_speak_voice", ele.value);
        }

        function toggleSpeechRate() {
            var speech_rate = document.getElementById("speech_rate");
            if (speech_rate.value < 0.1) { 
                speech_rate.value = 0.1; 
            } else if (speech_rate.value > 10) { 
                speech_rate.value = 10; 
            }
            setCookie("speech_rate", speech_rate.value);
        }
    </script>
</head>
<body class="body_style">
    <h1>{{ book_name }}<button onclick="toggleIframe()">隐藏/显示目录</button>
        &nbsp;&nbsp;<input type="checkbox" id="split-screen-checkbox" onclick="toggleSplitScreen()"><span style="font-size: 16px; color: #000000; margin-left: 5px; cursor: pointer;">分屏</span>
        &nbsp;&nbsp;<input type="checkbox" id="scroll-screen-checkbox" onclick="toggleScrollScreen()"><span style="font-size: 16px; color: #000000; margin-left: 5px; cursor: pointer;">自动滚屏</span>
        &nbsp;&nbsp;<input type="checkbox" id="speak-checkbox" onclick="toggleSpeak()"><span style="font-size: 16px; color: #000000; margin-left: 5px; cursor: pointer;">语音阅读</span>
        &nbsp;&nbsp;<select id="voice-select" onchange="toggleVoiceChange()"></select>
        &nbsp;&nbsp;
        <select id="speech_rate" onchange="toggleSpeechRate()">
            <option value="0.5">慢速</option>
            <option value="0.8">较慢</option>
            <option value="1.0">普通</option>
            <option value="1.5">较快</option>
            <option value="2.0">快速</option>
        </select>
    </h1>
    <iframe id="left_chapters" style="float: left; width: 18%; height: 780px;" src="/chapters/{{book_id}}/1/10000"></iframe>
    <iframe id="right_content" style="float: right; width: 81%; height: 780px;" src=""></iframe>
</body>
</html>