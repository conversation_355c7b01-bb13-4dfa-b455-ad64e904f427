@startuml 易灵工第3版：开户、签约、任务

title 易灵工接口流程

actor 委托方 as C
participant 易票联 as Y
actor 任务人员 as R

==开户（线下）==
C -> Y: 提交开户资料
Y -> Y: 进件商户&开通业务
Y -> Y: 开户&绑定受托方ID（资质地ID）
Y --> C: 返回商户号、受托方ID（资质地ID）

==签约==
C -> Y: 签约
Y --> C: 返回受理结果
alt 签约类型=短信签约
    Y -\ R: 发送签约短信
    R -> Y: 提交签约信息
else 签约类型=API签约
    C -> Y: 证件照片上传
    Y --> C: 返回证件照片上传状态
    Y -\ C: 异步通知照片认证结果
end 
Y -\ C: 异步通知签约状态

==任务==
C -> Y: 标签列表查询
Y --> C: 返回标签列表
C -> Y: 创建任务
Y --> C: 返回任务列表
C -> Y: 关联人员
Y --> C: 返回关联人员状态

@enduml

@startuml 易灵工第3版：结算

title 易灵工接口流程

actor 委托方 as C
participant 易票联 as Y
participant 受托方 as S
actor 任务人员 as R

==结算==
C -> Y: 任务人员查询
Y --> C: 返回任务人员信息
C -> C: 制作代付明细表
C -> Y: 上传代付明细
Y -> Y: 筛选任务人员
Y -> Y: 创建代征单
Y -> Y: 筛出超额人员
Y --> C: 返回符合代发人员明细

C -> Y: 申请支付代发款
Y --> C: 返回收银台URL
C --[#red]> C: 完成支付(PC或H5收银台中完成)
alt 支付成功
    Y -> S: D0结算代发款
    S --> Y: 委托代发明细
    Y -> Y: 发起批量代付
    Y -\ R: 资金入账异步通知
    Y -\ C: 代发结果异步通知
end
C -> Y: 代发单查询
Y --> C: 返回代发单详情
@enduml

@startuml 易灵工第3版：发票

title 易灵工接口流程

actor 委托方 as C
participant 易票联 as Y

==发票==
group 开票信息查询
    C -> Y: 查询可开票类目
    Y --> C: 返回可开票类目
    C -> Y: 查询可开票批次
    Y --> C: 返回可开票批次列表
    C -> Y: 查询代付批次订单明细
    Y --> C: 返回代付订单明细列表
end group
group 开票申请
    C -> Y: 发票申请
    Y --> C: 返回发票申请ID
    C -> Y: 查询发票申请记录
    Y --> C: 返回发票申请记录列表
    Y -\ C: 发票审核结果通知（异步）
    Y -\ C: 发票开具通知（异步）
    C -> Y: 发票信息查询
    Y --> C: 返回发票详细信息
end group
Y -> Y: 定期（3个月）生成结算单\n结束任务
@enduml

@startuml 易灵工API技术实现

title 易灵工API技术实现

actor 税筹客户 as C
box EPSP域
    participant API as Y
    participant TAX as T
end box 
participant 阿拉钉 as A

C -> Y: 接口访问，携带接口名称参数
note left of Y
http://domain/api/tax/ylgAPI
HTTP HEAD: x-tax-method
end note
Y -> Y: 签名验签
alt 验签通过
    Y -> T: 报文转发：商户号、接口名称、接口报文
    alt 接口名称 IN 代理接口清单
        T -> T: 包装报文{name=,data=}
        T -> A: 报文转发
        A -> A: 接口处理
        A --> T: 返回处理结果
    else 接口名称 NOT IN 代理接口清单
        T -> T: 业务本地处理
        T -> A: 业务上游处理
        T -> T: 业务本地处理2
        A --> T: 返回处理结果
    end
    T --> Y: 返回处理结果
    Y -> Y: 签名
    Y -> C: 返回处理结果
else 验签失败
    Y -> C: 返回接口结果
end



@enduml