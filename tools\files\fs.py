import glob
from pathlib import Path

def scan_dir(dir):
    for file in glob.glob(dir):
        if Path(file).is_dir():
            scan_dir(file)
        else:
            yield file

def find_files(dir, pattern):
    for file in scan_dir(dir):
        print(file)
        if pattern in file:
            yield file
            
if __name__ == '__main__':
    # print(list(find_files('D:\FCAS\Merchant', 'pom.xml')))
    for x in list(scan_dir('D:\FCAS\Merchant')):
        print(x)