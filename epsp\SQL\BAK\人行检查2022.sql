﻿--1、好多分&有钱收商户时间提前3个月
CREATE TABLE reco_db.cust_customer_20230104 AS 
SELECT * FROM CUST_CUSTOMER cc 
WHERE cc.CUSTOMER_ID in(SELECT customer_id FROM reco_db.cust_customer20220805) OR 
  cc.CUSTOMER_ID in(SELECT customer_id FROM reco_db.cust_customer202208062);

CREATE TABLE reco_db.cust_customer_draft_20230104 AS 
SELECT * FROM CUST_CUSTOMER cc 
WHERE cc.CUSTOMER_ID in(SELECT customer_id FROM reco_db.cust_customer20220805) OR 
  cc.CUSTOMER_ID in(SELECT customer_id FROM reco_db.cust_customer202208062);

UPDATE CUST_CUSTOMER cc 
SET cc.CREATE_TIME =cc.CREATE_TIME - 91, cc.REGISTER_TIME =cc.REGISTER_TIME -91, cc.UPDATE_TIME =cc.UPDATE_TIME  - 91
WHERE cc.CUSTOMER_ID in(SELECT customer_id FROM reco_db.cust_customer20220805) OR 
  cc.CUSTOMER_ID in(SELECT customer_id FROM reco_db.cust_customer202208062);

UPDATE CUST_CUSTOMER_DRAFT cc 
SET cc.CREATE_TIME =cc.CREATE_TIME - 91, cc.REGISTER_TIME =cc.REGISTER_TIME -91, cc.UPDATE_TIME =cc.UPDATE_TIME  - 91
WHERE cc.CUSTOMER_ID in(SELECT customer_id FROM reco_db.cust_customer20220805) OR 
  cc.CUSTOMER_ID in(SELECT customer_id FROM reco_db.cust_customer202208062);
  
--2、恢复好多分提现记录
INSERT INTO TXS_WITHDRAW_TRADE_ORDER SELECT twto.*,'' FROM txs_withdraw_order_20220823 twto;

--3、恢复计费数据记录和对应记账记录
INSERT INTO PAS_ACCT_QUOTA_RECORD SELECT * FROM info.tmp_acc_quota_0811; --18条缴费记录
INSERT INTO ACC_ACCOUNTFLOW SELECT * FROM info.tmp_flow_0811;  --18条缴费记账+1条调账记账

--4、修改商户的注销时间
CREATE TABLE tmp_cust_date_0104(customer_code varchar2(32), dt varchar2(10));
INSERT INTO tmp_cust_date_0104 values('***************','2022/8/10');
INSERT INTO tmp_cust_date_0104 values('***************','2022/11/1');
INSERT INTO tmp_cust_date_0104 values('***************','2022/9/13');
INSERT INTO tmp_cust_date_0104 values('***************','2022/8/17');
INSERT INTO tmp_cust_date_0104 values('***************','2022/8/22');
INSERT INTO tmp_cust_date_0104 values('***************','2022/9/21');

CREATE TABLE reco_db.RC_OPERATE_LOG_20230104 AS 
SELECT * FROM RC_OPERATE_LOG WHERE code IN (SELECT customer_code FROM TMP_CUST_DATE_0104);

MERGE INTO RC_OPERATE_LOG t
USING tmp_cust_date_0104 d ON (t.CODE=d.customer_code)
WHEN MATCHED THEN
  UPDATE SET t.OPERATE_TIME = to_date(d.dt || ' ' || to_char(t.OPERATE_TIME,'hh24:mi:ss'), 'yyyy/mm/dd hh24:mi:ss');
  
--5、构造好多分出金数据
--1）收款记录对应的分账（出金订单）的所属商户改成对应收款商户编号；
--备份数据
CREATE TABLE reco_db.TXS_WITHDRAW_ORDER_20230104 AS
SELECT t.* FROM TXS_WITHDRAW_TRADE_ORDER t INNER JOIN tmp_hdf_out h ON t.TRANSACTION_NO =h.WITHDRAW_TRANSACTION_NO 
WHERE h.CUSTOMER_CODE IN ('***************','***************','***************');
--更新数据
MERGE INTO TXS_WITHDRAW_TRADE_ORDER t
USING (SELECT t1.* FROM tmp_hdf_out t1 
    WHERE t1.CUSTOMER_CODE IN ('***************','***************','***************')) s 
  ON (t.TRANSACTION_NO=s.withdraw_transaction_no)
WHEN MATCHED THEN 
  UPDATE SET t.CUSTOMER_CODE =s.customer_code, t.CUSTOMERNAME =s.customername;

 DROP TABLE TXS_WITHDRAW_ORDER_20230104;
--2）将收款商户分账给平台商的每一笔记录，造出对应的出金记录，出金商户是对应的收款商户编号
CREATE TABLE TXS_WITHDRAW_ORDER_20230104 AS 
select o.ID * -10 AS id, o.OUT_TRADE_NO||'2' AS out_trade_no, o.TRANSACTION_NO||'2' AS TRANSACTION_NO, tsr.AMOUNT AS TOTAL_FEE, o.PAY_CURRENCY, h.CUSTOMER_CODE,
		CHANNEL_TYPE, o.NOTIFY_URL, o.PAY_STATE, o.CREATE_TIME, o.ARRIVAL_TYPE, o.ACTUAL_FEE,
		0 AS PROCEDURE_FEE, o.CARD_NO, o.BEGIN_TIME, o.END_TIME, o.BUSINESS_INST_ID, o.PRODUCE_RATE,
		o.CHANNEL_ORDER, o.CHANNEL_NAME, o.SOURCE_TYPE, o.BANK_ACCOUNT_TYPE, o.CUSTOMERNAME,
		ACCOUNT_TYPE_CODE, o.BUSINESS_CODE, o.TH_STATE, o.BANK_USER_NAME, o.BANK_NAME, o.BANK_NO,
		OPR_OPERATED, o.PAY_METHOD, o.PAY_CHANNEL_ID, o.CARD_NO_CIPHER, o.BANK_USER_NAME_FULL,
		BANK_USER_CERT_FULL, o.CHANNEL_RESP_CODE, o.CHANNEL_RESP_MSG, o.CHANNEL_QUERY_CODE,
		o.CHANNEL_QUERY_MSG, o.BUSINESS_MAN, o.BUSINESS_MAN_ID, o.COMPANY_NAME, o.COMPANY_ID,
		o.TRADE_SOURCE, o.FEE_PER, o.RATE_MODE, o.TRANSACTION_TYPE, o.TERMINAL_NO
from TXS_WITHDRAW_TRADE_ORDER o
     INNER JOIN TMP_HDF_OUT h ON o.TRANSACTION_NO = h.WITHDRAW_TRANSACTION_NO
     INNER JOIN TXS_PAY_TRADE_ORDER tp ON h.TRANSACTION_NO =tp.TRANSACTION_NO 
     INNER JOIN TXS_SPLIT_ORDER tso ON tp.OUT_TRADE_NO =tso.OUT_TRADE_NO  AND tp.CUSTOMER_CODE =tso.CUSTOMER_CODE 
     INNER JOIN TXS_SPLIT_RECORD tsr ON tso.TRANSACTION_NO =tsr.TRANSACTION_NO 
WHERE h.CUSTOMER_CODE IN ('***************','***************','***************') AND 
	tsr.CUSTOMER_CODE ='****************' AND tsr.AMOUNT > 0;

insert into TXS_WITHDRAW_TRADE_ORDER(
		ID, OUT_TRADE_NO, TRANSACTION_NO, TOTAL_FEE, PAY_CURRENCY, CUSTOMER_CODE,
		CHANNEL_TYPE, NOTIFY_URL, PAY_STATE, CREATE_TIME, ARRIVAL_TYPE, ACTUAL_FEE,
		PROCEDURE_FEE, CARD_NO, BEGIN_TIME, END_TIME, BUSINESS_INST_ID, PRODUCE_RATE,
		CHANNEL_ORDER, CHANNEL_NAME, SOURCE_TYPE, BANK_ACCOUNT_TYPE, CUSTOMERNAME,
		ACCOUNT_TYPE_CODE, BUSINESS_CODE, TH_STATE, BANK_USER_NAME, BANK_NAME, BANK_NO,
		OPR_OPERATED, PAY_METHOD, PAY_CHANNEL_ID, CARD_NO_CIPHER, BANK_USER_NAME_FULL,
		BANK_USER_CERT_FULL, CHANNEL_RESP_CODE, CHANNEL_RESP_MSG, CHANNEL_QUERY_CODE,
		CHANNEL_QUERY_MSG, BUSINESS_MAN, BUSINESS_MAN_ID, COMPANY_NAME, COMPANY_ID,
		TRADE_SOURCE, FEE_PER, RATE_MODE, TRANSACTION_TYPE, TERMINAL_NO)
SELECT ID, OUT_TRADE_NO, TRANSACTION_NO, TOTAL_FEE, PAY_CURRENCY, CUSTOMER_CODE,
		CHANNEL_TYPE, NOTIFY_URL, PAY_STATE, CREATE_TIME, ARRIVAL_TYPE, ACTUAL_FEE,
		PROCEDURE_FEE, CARD_NO, BEGIN_TIME, END_TIME, BUSINESS_INST_ID, PRODUCE_RATE,
		CHANNEL_ORDER, CHANNEL_NAME, SOURCE_TYPE, BANK_ACCOUNT_TYPE, CUSTOMERNAME,
		ACCOUNT_TYPE_CODE, BUSINESS_CODE, TH_STATE, BANK_USER_NAME, BANK_NAME, BANK_NO,
		OPR_OPERATED, PAY_METHOD, PAY_CHANNEL_ID, CARD_NO_CIPHER, BANK_USER_NAME_FULL,
		BANK_USER_CERT_FULL, CHANNEL_RESP_CODE, CHANNEL_RESP_MSG, CHANNEL_QUERY_CODE,
		CHANNEL_QUERY_MSG, BUSINESS_MAN, BUSINESS_MAN_ID, COMPANY_NAME, COMPANY_ID,
		TRADE_SOURCE, FEE_PER, RATE_MODE, TRANSACTION_TYPE, TERMINAL_NO
FROM TXS_WITHDRAW_ORDER_20230104;
