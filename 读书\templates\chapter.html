<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{ book_name }} - {{ title }}</title>
    <script src="{{ url_for('static', filename='javascript/common.js') }}"></script>
    <script src="{{ url_for('static', filename='javascript/chapter.js') }}"></script>
    <style>
        .body_style {
            padding: 0 0px; 
            background-color: #a0a0a0;
        }
        .container {
            display: flex;
        }
        .column {
            flex: 0 0 49%;
            padding: 0px;
            font-size: 20px; 
            line-height: 35px;
            margin: 0px 5px;
        }
        .read {
            font-style: italic;
            color: #777;
        }
        .unread {
            font-style: normal;
            color: #333;
        }
        .reading {
            background-color: #ddd;
            color: #000;
        }
    </style>
    <script>
        window.onload = function() {
            splitScreenCookie = getCookie("options_split_screen");
            if (splitScreenCookie == "yes") {
                document.getElementById("content-container").style.display = "flex";
            } else {
                document.getElementById("content-container").style.display = "block";
            }       
    
            setCookie("{{ book_name }}-reading", "{{ chapter_id }}")

            if (window.parent) {
                leftChaptersFrame = window.parent.document.getElementById('left_chapters');
                if(leftChaptersFrame) {
                    leftChaptersFrame.contentWindow.showSelectedChapter({{ chapter_id }});
                }
            }

            var contentColumn1 = document.getElementById("content-column1");
            contentColumn1.addEventListener("click", function(event) {
                if (event.target && event.target.tagName === "P") {
                    toggleReadStatus(event.target);
                }
            });
    
            var contentColumn2 = document.getElementById("content-column2");
            contentColumn2.addEventListener("click", function(event) {
                if (event.target && event.target.tagName === "P") {
                    toggleReadStatus(event.target);
                }
            });
            //
            scrollScreenCookie = getCookie("options_scroll_screen");
            toggleScrollScreen(scrollScreenCookie == "yes");
            //
            var paragraphs = document.querySelectorAll("#content-column1 p, #content-column2 p");
            if(paragraphs.length > 0) {
                paragraphs[0].click();
            }
        }
    </script>
</head>
<body class="body_style">
    <h2>{{ title }}
    </h2>
    <div class="container" id="content-container">
        <div class="column" id="content-column1">
            {{ content1|safe }}
        </div>
        <div class="column" id="content-column2">
            {{ content2|safe }}
        </div>
    </div>
    <a id="chapter_prev" href="/chapter_prev/{{ chapter_id }}">上一章</a>&nbsp;&nbsp;&nbsp;&nbsp;
    <a id="chapter_next" href="/chapter_next/{{ chapter_id }}">下一章</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
    <button id="scroll-to-top" onclick="scrollToTop()">回到顶部</button>

</body>
</html>