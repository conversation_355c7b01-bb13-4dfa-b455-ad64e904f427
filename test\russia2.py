import random
from itertools import combinations
def russian_doll(num):
    if num == 1:
        return [1]
    else:
        result = []
        for i in range(1, len(num)):
            sublist = russian_doll(int(num[i-1]) + int(num[i]))
            result.append([sublist[0]] + list(combinations(sublist[1:], 2)))
        return [result]
def main():
    nums = input("Enter a number between 1 and 9: ")
    russian_doll(nums)
main()
