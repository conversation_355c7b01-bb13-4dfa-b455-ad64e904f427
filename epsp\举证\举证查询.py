###读取指定目录中所有xls和xlsx文件，并读取每一个文件中的内容，输出格式：文件名，第一列，第2列，均要去重
import xlrd
import os
import pandas as pd
import logging
import math
import shutil
import zipfile
import uuid
import re
from datetime import datetime
from docx import Document
from docx.shared import Inches
from docx.oxml.ns import qn
from docx.oxml.ns import nsmap
from docx.oxml import OxmlElement

logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',  # 只包含日志消息本身
    handlers=[
        logging.StreamHandler()  # 控制台处理器
    ]
)
logger = logging.getLogger(__name__)

def read_excel_file(file_path, skiprows=0):
    df = pd.read_excel(file_path, skiprows=skiprows, usecols=range(0, 9))
    df = df.applymap(lambda x: x.strip() if isinstance(x, str) else x)
    return df

def get_all_excel_data(filepath):
    result = {}
    for root, dirs, files in os.walk(filepath):
        for file in files:
            if file.endswith('.xls') or file.endswith('.xlsx'):
                result[file] = read_excel_file(os.path.join(root, file))
    return result

def copy_file_with_new_name(source_file, destination_directory, new_file_name):
    if not os.path.exists(destination_directory):
        os.makedirs(destination_directory)
    destination_file = os.path.join(destination_directory, new_file_name)
    shutil.copy(source_file, destination_file)
    return destination_file

def replace_text_in_docx(file_path, old_text, new_text):
    doc = Document(file_path)
    for paragraph in doc.paragraphs:
        if old_text in paragraph.text:
            for run in paragraph.runs:
                run.text = run.text.replace(old_text, new_text)
    doc.save(file_path)

def insert_file_in_docx(docx_path, attach_file_path, paragraph_index=3, text="附件："):
    """
    将文件作为附件嵌入到Word文档的指定段落

    参数:
        docx_path (str): Word文档路径
        attach_file_path (str): 要插入的附件文件路径
        paragraph_index (int): 要插入附件的段落索引（0表示第一行，默认为3即第4行）
        text (str): 插入附件前的文本（默认为"附件："）
    """
    # 确保附件文件存在
    if not os.path.exists(attach_file_path):
        print(f"错误：附件文件不存在 - {attach_file_path}")
        return False
    
    try:
        # 获取Word文档所在目录
        docx_dir = os.path.dirname(docx_path)
        # 在Word文档目录下创建logs子目录
        logs_dir = os.path.join(docx_dir, 'logs')
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
            
        # 复制附件到logs目录
        file_name = os.path.basename(attach_file_path)
        dest_file = os.path.join(logs_dir, file_name)
        shutil.copy2(attach_file_path, dest_file)

        # 计算相对路径
        rel_path = os.path.join('logs', file_name)
        
        # 打开现有的docx文件
        doc = Document(docx_path)

        # 确定插入位置
        if len(doc.paragraphs) > paragraph_index:
            # 在指定行插入新段落
            paragraph = doc.paragraphs[paragraph_index].insert_paragraph_before(text)
        else:
            # 如果文档行数不足，则在文档末尾添加段落
            paragraph = doc.add_paragraph(text)

        # 创建超链接运行
        run = paragraph.add_run()
        run.add_text(' ')

        # 创建超链接关系
        part = doc.part
        # 使用相对路径创建关系
        r_id = part.relate_to(rel_path, 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink', is_external=True)

        # 创建超链接
        hyperlink = OxmlElement('w:hyperlink')
        hyperlink.set(qn('r:id'), r_id)
        hyperlink.set(qn('w:history'), '1')

        # 创建运行对象
        hyperlink_run = OxmlElement('w:r')

        # 设置运行属性
        rPr = OxmlElement('w:rPr')

        # 添加下划线
        u = OxmlElement('w:u')
        u.set(qn('w:val'), 'single')
        rPr.append(u)

        # 设置颜色为蓝色
        color = OxmlElement('w:color')
        color.set(qn('w:val'), '0000FF')
        rPr.append(color)

        # 将属性添加到运行中
        hyperlink_run.append(rPr)

        # 添加文件名文本
        t = OxmlElement('w:t')
        t.text = file_name
        hyperlink_run.append(t)

        # 将运行添加到超链接
        hyperlink.append(hyperlink_run)

        # 将超链接添加到段落
        paragraph._p.append(hyperlink)

        # 保存修改后的docx文件
        doc.save(docx_path)

        print(f"成功将文件 {file_name} 链接到文档 {docx_path} 的第 {paragraph_index+1} 行")
        return True
    except Exception as e:
        print(f"插入文件时发生错误: {str(e)}")
        return False

def process_data(data_dir, dest_dir, temp_dir = r'D:\WHF\OneDrive\work\其他'):
    # 定义模板文件
    所有 = os.path.join(temp_dir, '所有.docx')
    部分 = os.path.join(temp_dir, '部分.docx')
    无卡 = os.path.join(temp_dir, '无卡.docx')
    # 增加数据SQL输出
    if not os.path.exists(dest_dir):
        os.makedirs(dest_dir)
    log_filename = f'{data_dir}/举证{datetime.now().strftime("%Y%m%d")}.sql'
    if os.path.exists(log_filename):
        os.remove(log_filename)

    formatter = logging.Formatter('%(message)s')
    log_file_handler = logging.FileHandler(log_filename)
    log_file_handler.setFormatter(formatter)
    logger.addHandler(log_file_handler)
    #
    logger.info(f"--开始处理目录：{data_dir}")
    data = get_all_excel_data(data_dir)
    for file, df in data.items():
        transaction_nos = df.iloc[:, 0].tolist()
        logger.info(f"\n--===================={file}====================--")
        transaction_str = ', '.join(map(lambda x: f"'{x}'", transaction_nos))
        cmd = '"/api/txs/protocol/protocolPayPre|/api/txs/protocol/protocolPayConfirm|/api/txs/pay/NativePayment"'
        logger.info("--查询交易日志")
        logger.info(f"SELECT 'grep -E {cmd} epsp.epaylinks.cn.access.log_'||to_char(t.create_time,'yyyy-mm-dd') AS cmd1,'|grep '||t.customer_code AS cmd2,'|grep '||t.out_trade_no AS cmd3 FROM epsp.txs_pay_trade_order t WHERE t.transaction_no IN ({transaction_str});")

        filtered_df = df[df.iloc[:, 5].str.contains('快捷')]
        card_nos = filtered_df.iloc[:, 7].drop_duplicates().tolist()
        dest_file = None
        if len(card_nos) > 0:
            card_str = ', '.join(map(lambda x: f"'{x}'", card_nos))
            logger.info("--查询绑卡日志，WHERE后的卡号要用Swagger加密")
            logger.info(f"SELECT 'grep /api/txs/protocol/bindCard epsp.epaylinks.cn.access.log_'||to_char(t.createtime,'yyyy-mm-dd') AS cmd1,'|grep '||t.customercode as cmd2,'|grep '||to_char(t.createtime,'dd/Mon/yyyy:hh24:mi') AS cmd3 FROM epsp.cum_quickpaycustomerinfo t INNER JOIN epsp.cust_customer c ON t.customercode=c.customer_no WHERE t.bankcardno IN ({card_str});")
            transaction_nos = filtered_df.iloc[:, 0].tolist()
            transaction_str = ', '.join(map(lambda x: f"'{x}'", transaction_nos))
            logger.info("--查询交易信息截屏，确认是否发送短信校验")
            logger.info(f"SELECT t.transaction_no, t.customer_code,t.customername,to_char(t.create_time,'yyyy-mm-dd hh24:mi:ss') AS create_time,t.payer,t.business_code FROM epsp.txs_pay_trade_order t WHERE t.transaction_no IN({transaction_str});")
            if not filtered_df.empty:
                strings_to_match = ['重庆智未渝智能科技有限公司', '太原市小店区玮霖科技有限公司', '成都克莱斯特科技有限公司', 
                                    '福鼎市紫铭科技有限公司', '深圳市叁项易科技有限公司', '泉州市赖式贸易有限公司', 
                                    '厦门鸿衫恒科技有限公司', '郴州顿浩供应链有限公司', '江阴乔冠科技有限公司', '成都基运通博力科技有限公司', 
                                    '广州贝仰贸易有限公司', '广州蓝石头电子商务有限公司', '湖南君谦科技有限公司', '厦门乐胜纳科技有限公司', '广州泰珺电子商务有限公司',
                                    '广州市恩苑科技有限公司', '武汉亦亚贸易有限公司', '上海颜槿冉教育科技有限公司', '武汉花想月科技有限公司', '中莫(武汉)科技有限公司',
                                    '广西蒂玖商务服务有限公司', '湖北奥冲科技有限公司', '南宁儒树商贸有限公司', '广州达辞贸易有限公司', '苏州翰尔诺贸易有限公司',
                                    '陕西瑶梦谈信息科技有限公司', '湖南梁贸科技有限公司', '广州季逃科技有限公司', '天津威尼撕商贸有限公司',
                                    '陕西考普乐科技有限公司']
                pattern = '|'.join([f'.*{s}.*' for s in strings_to_match])
                filtered_df2 = filtered_df[filtered_df.iloc[:, 4].str.contains(pattern, case=False, na=False)]
                if filtered_df2.empty:
                    dest_file = copy_file_with_new_name(所有, dest_dir, file[0:3] + '号举证材料.docx')
                else:
                    company_names = '、'.join(filtered_df2.iloc[:, 4].drop_duplicates().tolist())
                    logger.info(f"--不发送短信校验的商户：{company_names}")
                    dest_file = copy_file_with_new_name(部分, dest_dir, file[0:3] + '号举证材料.docx')
                    replace_text_in_docx(dest_file, '{merchants}', company_names)
            else:
                dest_file = copy_file_with_new_name(所有, dest_dir, file[0:3] + '号举证材料.docx')
        else:
            dest_file = copy_file_with_new_name(无卡, dest_dir, file[0:3] + '号举证材料.docx')

        if dest_file is not None:
            print(f"输出文件：{dest_file}")
            # 将文件插入到文档的第4行
            jy_log = f"{data_dir}/logs/{file[0:3]}_jy.tsv_output.log"
            qy_log = f"{data_dir}/logs/{file[0:3]}_qy.tsv_output.log"
            if len(card_nos) > 0:
                insert_file_in_docx(dest_file, qy_log, paragraph_index=3, text="日志：")
                insert_file_in_docx(dest_file, jy_log, paragraph_index=6, text="日志：")
            else:
                insert_file_in_docx(dest_file, jy_log, paragraph_index=3, text="日志：")

if __name__ == '__main__':
    datestr = datetime.now().strftime("%m%d")
    temp_dir = r'D:\WHF\OneDrive\work\其他'
    data_dir = input(f'请输入数据目录（默认：D:/TMP/举证{datestr}）：')
    dest_dir = input(f'请输入输出目录（默认：D:/WHF/OneDrive/work/其他/举证{datestr}）：')
    if not data_dir:
        data_dir = f'D:/TMP/举证{datestr}'
    if not dest_dir:
        dest_dir = f'D:/WHF/OneDrive/work/其他/举证{datestr}'
    process_data(data_dir, dest_dir, temp_dir)