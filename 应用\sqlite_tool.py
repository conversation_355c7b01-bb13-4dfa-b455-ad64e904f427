import sqlite3
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import re

class SQLiteToolApp:
    def __init__(self, root):
        self.root = root
        self.root.title("SQLite 数据库工具")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        self.connection = None
        self.current_db_path = None
        
        self.setup_ui()
        
    def setup_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部工具栏
        toolbar = ttk.Frame(main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        # 数据库连接区域
        db_frame = ttk.LabelFrame(toolbar, text="数据库连接")
        db_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        self.db_path_var = tk.StringVar()
        ttk.Entry(db_frame, textvariable=self.db_path_var, width=50).pack(side=tk.LEFT, padx=5, pady=5, fill=tk.X, expand=True)
        ttk.Button(db_frame, text="浏览...", command=self.browse_db).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(db_frame, text="断开", command=self.disconnect_db).pack(side=tk.LEFT, padx=5, pady=5)
        
        # 创建分割窗格
        paned_window = ttk.PanedWindow(main_frame, orient=tk.VERTICAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 上半部分：SQL输入和执行
        sql_frame = ttk.LabelFrame(paned_window, text="SQL 查询")
        paned_window.add(sql_frame, weight=1)
        
        # SQL输入区域
        self.sql_text = scrolledtext.ScrolledText(sql_frame, wrap=tk.WORD, height=10)
        self.sql_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 执行按钮
        button_frame = ttk.Frame(sql_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(button_frame, text="执行查询", command=self.execute_query).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="执行更新", command=self.execute_update).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空", command=lambda: self.sql_text.delete(1.0, tk.END)).pack(side=tk.LEFT, padx=5)
        
        # 下半部分：结果显示
        result_frame = ttk.LabelFrame(paned_window, text="查询结果")
        paned_window.add(result_frame, weight=2)
        
        # 创建表格显示区域
        table_frame = ttk.Frame(result_frame)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建滚动条
        x_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL)
        y_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL)
        
        # 创建Treeview用于显示表格数据
        self.result_tree = ttk.Treeview(table_frame, 
                                        yscrollcommand=y_scrollbar.set,
                                        xscrollcommand=x_scrollbar.set)
        
        # 配置滚动条
        x_scrollbar.config(command=self.result_tree.xview)
        y_scrollbar.config(command=self.result_tree.yview)
        
        # 放置组件
        x_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.result_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 底部状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("未连接数据库")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM, pady=(5, 0))
        
        # 表格编辑功能
        self.result_tree.bind("<Double-1>", self.on_item_double_click)
        
        # 设置初始分割位置
        paned_window.sashpos(0, 200)
    
    def browse_db(self):
        """浏览并选择SQLite数据库文件"""
        db_path = filedialog.askopenfilename(
            title="选择SQLite数据库文件",
            filetypes=[("SQLite数据库", "*.db *.sqlite *.sqlite3"), ("所有文件", "*.*")]
        )
        if db_path:
            self.db_path_var.set(db_path)
            self.connect_db()
    
    def connect_db(self):
        """连接到SQLite数据库"""
        db_path = self.db_path_var.get().strip()
        if not db_path:
            messagebox.showerror("错误", "请指定数据库文件路径")
            return
        
        try:
            # 如果已有连接，先关闭
            if self.connection:
                self.connection.close()
            
            # 创建新连接
            self.connection = sqlite3.connect(db_path)
            self.current_db_path = db_path
            self.status_var.set(f"已连接到数据库: {os.path.basename(db_path)}")
            # messagebox.showinfo("成功", f"已成功连接到数据库: {os.path.basename(db_path)}")
            
            # 获取并显示表列表
            self.show_tables()
        except Exception as e:
            messagebox.showerror("连接错误", f"无法连接到数据库: {str(e)}")
            self.status_var.set("连接失败")
    
    def disconnect_db(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            self.current_db_path = None
            self.status_var.set("已断开数据库连接")
            
            # 清空结果区域
            for item in self.result_tree.get_children():
                self.result_tree.delete(item)
            
            # 重置表头
            self.result_tree["columns"] = ()
            self.result_tree["show"] = "tree"
            
            messagebox.showinfo("断开连接", "已断开数据库连接")
        else:
            messagebox.showinfo("提示", "当前没有活动的数据库连接")
    
    def show_tables(self):
        """显示数据库中的表"""
        if not self.connection:
            messagebox.showerror("错误", "未连接到数据库")
            return
        
        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            # 清空现有结果
            for item in self.result_tree.get_children():
                self.result_tree.delete(item)
            
            # 设置表头
            self.result_tree["columns"] = ("table_name",)
            self.result_tree.column("#0", width=0, stretch=tk.NO)
            self.result_tree.column("table_name", anchor=tk.W, width=200)
            
            self.result_tree.heading("#0", text="", anchor=tk.W)
            self.result_tree.heading("table_name", text="表名", anchor=tk.W)
            
            # 添加数据
            for i, (table_name,) in enumerate(tables):
                self.result_tree.insert("", tk.END, text=str(i), values=(table_name,))
            
            self.status_var.set(f"数据库包含 {len(tables)} 个表")
        except Exception as e:
            messagebox.showerror("错误", f"获取表列表失败: {str(e)}")
    
    def execute_query(self):
        """执行SQL查询语句"""
        if not self.connection:
            messagebox.showerror("错误", "未连接到数据库")
            return
        
        sql = self.sql_text.get(1.0, tk.END).strip()
        if not sql:
            messagebox.showerror("错误", "请输入SQL查询语句")
            return
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(sql)
            
            # 获取列名
            column_names = [description[0] for description in cursor.description]
            
            # 获取数据
            rows = cursor.fetchall()
            
            # 显示结果
            self.display_results(column_names, rows)
            
            self.status_var.set(f"查询成功，返回 {len(rows)} 条记录")
        except Exception as e:
            messagebox.showerror("查询错误", f"执行查询失败: {str(e)}")
            self.status_var.set("查询失败")
    
    def execute_update(self):
        """执行SQL更新语句（INSERT, UPDATE, DELETE等）"""
        if not self.connection:
            messagebox.showerror("错误", "未连接到数据库")
            return
        
        sql = self.sql_text.get(1.0, tk.END).strip()
        if not sql:
            messagebox.showerror("错误", "请输入SQL语句")
            return
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(sql)
            self.connection.commit()
            
            affected_rows = cursor.rowcount
            self.status_var.set(f"执行成功，影响了 {affected_rows} 行数据")
            messagebox.showinfo("成功", f"SQL语句执行成功，影响了 {affected_rows} 行数据")
            
            # 如果是SELECT语句，显示结果
            if sql.strip().upper().startswith("SELECT"):
                # 获取列名
                column_names = [description[0] for description in cursor.description]
                
                # 获取数据
                rows = cursor.fetchall()
                
                # 显示结果
                self.display_results(column_names, rows)
        except Exception as e:
            messagebox.showerror("执行错误", f"执行SQL语句失败: {str(e)}")
            self.status_var.set("执行失败")
    
    def display_results(self, column_names, rows):
        """在表格中显示查询结果"""
        # 清空现有结果
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)
        
        # 设置表头
        self.result_tree["columns"] = column_names
        self.result_tree.column("#0", width=0, stretch=tk.NO)
        
        # 配置列
        for col in column_names:
            self.result_tree.column(col, anchor=tk.W, width=100)
            self.result_tree.heading(col, text=col, anchor=tk.W)
        
        # 添加数据
        for i, row in enumerate(rows):
            self.result_tree.insert("", tk.END, text=str(i), values=row)
    
    def on_item_double_click(self, event):
        """处理表格项的双击事件，允许编辑单元格"""
        if not self.connection:
            return
        
        # 获取点击的项和列
        item = self.result_tree.identify_row(event.y)
        column = self.result_tree.identify_column(event.x)
        
        if not item or not column:
            return
        
        # 获取列索引（#1, #2, ...）
        column_index = int(column.replace("#", ""))
        
        # 如果是第一列（#0），不处理
        if column_index == 0:
            return
        
        # 获取列名
        column_name = self.result_tree["columns"][column_index - 1]
        
        # 获取当前值
        current_value = self.result_tree.item(item, "values")[column_index - 1]
        
        # 创建编辑窗口
        self.edit_cell(item, column_index - 1, column_name, current_value)
    
    def edit_cell(self, item, column_index, column_name, current_value):
        """创建单元格编辑对话框"""
        edit_window = tk.Toplevel(self.root)
        edit_window.title(f"编辑 {column_name}")
        edit_window.geometry("300x150")
        edit_window.resizable(False, False)
        edit_window.transient(self.root)
        edit_window.grab_set()
        
        # 居中显示
        edit_window.update_idletasks()
        width = edit_window.winfo_width()
        height = edit_window.winfo_height()
        x = (edit_window.winfo_screenwidth() // 2) - (width // 2)
        y = (edit_window.winfo_screenheight() // 2) - (height // 2)
        edit_window.geometry(f"+{x}+{y}")
        
        # 创建编辑框
        ttk.Label(edit_window, text=f"编辑 {column_name}:").pack(pady=(10, 5))
        
        value_var = tk.StringVar(value=str(current_value) if current_value is not None else "")
        entry = ttk.Entry(edit_window, textvariable=value_var, width=40)
        entry.pack(padx=10, pady=5)
        entry.select_range(0, tk.END)
        entry.focus_set()
        
        # 按钮框架
        button_frame = ttk.Frame(edit_window)
        button_frame.pack(pady=10)
        
        # 保存按钮
        def save_changes():
            new_value = value_var.get()
            
            # 获取所有值
            values = list(self.result_tree.item(item, "values"))
            
            # 更新指定列的值
            values[column_index] = new_value
            
            # 更新表格显示
            self.result_tree.item(item, values=values)
            
            # 尝试更新数据库
            self.update_database(item, column_index, new_value)
            
            edit_window.destroy()
        
        ttk.Button(button_frame, text="保存", command=save_changes).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=edit_window.destroy).pack(side=tk.LEFT, padx=5)
        
        # 绑定回车键
        entry.bind("<Return>", lambda event: save_changes())
    
    def update_database(self, item, column_index, new_value):
        """尝试更新数据库中的值"""
        if not self.connection:
            messagebox.showerror("错误", "未连接到数据库")
            return
        
        # 获取表名和主键信息
        try:
            # 获取当前显示的表名
            # 这里假设最近一次执行的查询是针对单个表的简单查询
            sql = self.sql_text.get(1.0, tk.END).strip()
            
            # 尝试从SQL中提取表名
            match = re.search(r"FROM\s+([^\s,;]+)", sql, re.IGNORECASE)
            if not match:
                messagebox.showinfo("提示", "无法确定要更新的表，请手动编写UPDATE语句")
                return
            
            table_name = match.group(1).strip('`"[]')
            
            # 获取列名
            column_names = self.result_tree["columns"]
            column_name = column_names[column_index]
            
            # 获取所有值（用于构建WHERE子句）
            values = self.result_tree.item(item, "values")
            
            # 构建UPDATE语句
            # 注意：这里使用所有列作为WHERE条件，可能不是最佳方式
            # 理想情况下应该使用主键，但这需要额外的元数据查询
            where_clauses = []
            for i, (col, val) in enumerate(zip(column_names, values)):
                if i != column_index:  # 排除正在更新的列
                    if val is None:
                        where_clauses.append(f"{col} IS NULL")
                    else:
                        where_clauses.append(f"{col} = ?")
            
            where_values = [v for i, v in enumerate(values) if i != column_index and v is not None]
            
            # 构建完整的UPDATE语句
            update_sql = f"UPDATE {table_name} SET {column_name} = ? WHERE {' AND '.join(where_clauses)}"
            
            # 执行更新
            cursor = self.connection.cursor()
            cursor.execute(update_sql, [new_value] + where_values)
            self.connection.commit()
            
            self.status_var.set(f"已更新 {cursor.rowcount} 行数据")
        except Exception as e:
            messagebox.showerror("更新错误", f"更新数据库失败: {str(e)}\n请考虑手动编写UPDATE语句")
            self.status_var.set("更新失败")

if __name__ == "__main__":
    root = tk.Tk()
    app = SQLiteToolApp(root)
    root.mainloop()
