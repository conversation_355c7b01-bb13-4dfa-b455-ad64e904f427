import zipfile
import os

customers = '562866004033498-宁波帛唯贸易有限公司,562031004033104-深圳市益汇百贸易有限公司,562404004043781-杭州明璜网络科技有限公司,562343004020258-上海辑冉科技有限公司,562765004034834-广州冬桦科技有限公司,562139004057025-武汉汇熙科技有限公司,562632004042815-重庆予予堃科技有限公司,562619004043527-贵州荣星优创商贸有限公司,562190004044678-安庆哲磐贸易有限公司,562771004045118-深圳市蔚蓝源网络技术有限公司,562351004047155-广州桂旭科技有限公司,562128004034839-广州乐檬科技有限公司,562912004043638-深圳市古典风格科技有限公司,562125004047280-重庆镒达合创网络信息科技有限公司,562697004123303-吉林悦沣科技有限公司,562242004125590-长春鸿驰科技有限公司,562588003977223-淼承（厦门）科技有限公司,562445004075010-众鑫（深圳）互联科技有限公司,562347004042043-河南邝迈电子科技有限公司,562078004047251-四川曾若巨科技有限公司,562155004044411-北京文朋辉煌科技有限公司,562144004136713-武汉市顺金泰科技有限公司,562926004137804-广州烙炎科技有限公司,562407004048210-甘肃亿思辰商贸有限公司,562225004042433-泉州市杲恩网络科技有限公司,562074004047090-北京君达维科技有限公司,562785004103856-北京维纵启横科技发展有限公司,562468004043809-江西艾菲网络有限公司,562332004113497-环宇智信(北京) 网络科技有限公司,562215004045422-南昌望衍电子商务有限公司,562222004048321-芯科电子（深圳）有限公司,562242004041679-温州敖谷网络科技有限公司,562983004044416-广州顺均信息科技有限公司,562558004048189-长春市燃旺网络科技有限公司,562997004045362-嘉兴毅拓商贸有限公司,562206004045424-赣州尊铭电子商务有限公司,562598004047223-浙江哆慧商贸有限公司,562693004118725-义乌市揭扬商贸有限公司,562415004033786-安庆市畅盈贸易有限公司,562072004042691-厦门千百赞贸易有限公司,562386004043831-湖北水维科技有限公司,562256004026945-福建宝莲灯信息技术有限公司,562708004043142-贵州富衍商贸有限公司,562680004045934-四川华投蓉实业有限公司,562078004070981-深圳市国天盛贸易有限公司,562680004045509-厦门弘驰青云网络科技有限公司,562628004070470-武汉鸿烨供应链有限公司,562596004082889-广州靖新网络科技有限公司,562122004138538-厦门航喜之贸易有限公司,562245004140793-深圳市永盛浩科技有限公司,562536004047009-北京欣玮科技有限公司,562079004048188-厦门人才出众人力资源有限公司,562287004097187-成都直播魂文化传媒有限公司,562589004115509-成都芸洪瑞科技有限公司,562425004140294-圆弧（海南）信息科技有限公司,562871004033928-广州超弘网络科技有限公司,562474004031309-广州健文科技有限公司,562935004018091-贵州旺赫商贸有限公司,562607004019849-广州佳航网络科技有限公司,562622004034240-海南莉敏娜科技有限公司,562827004033010-广州瑞凡网络科技有限公司,562594004017679-广州盛寂网络科技有限公司,562750004033508-广州显维网络科技有限公司,562073004049158-汕头市金平区得意好建材店'
# 指定要压缩的文件夹路径
folder_path = 'D:/usr/local/logs/efps/clr/'

def get_filename(file_char):
    return file_char + '.zip'

def zipf(file_char, filename):
    result = []
    with zipfile.ZipFile(filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # 遍历文件夹下的所有文件
        for root, _, files in os.walk(folder_path):
            for file in files:
                if file_char in file:
                    # 获取文件的完整路径
                    file_path = os.path.join(root, file)
                    # 将文件添加到zip文件中
                    zipf.write(file_path, os.path.relpath(file_path, folder_path))
                    result.append(file)
    return result

if __name__ == "__main__":
    index = 0
    for customer in list(customers.split(',')):
        index += 1
        customer_code = customer.split('-')[0]
        filelist = zipf(customer_code, customer+'.zip')
        print(index, customer+'.zip', filelist)