# 测试环境参数

商户号：2410300100000277
APP ID: 8d43b8188921e0865825c6b9137a39ca
SECRET: 3ee5dc91bfa8b6fc0c9bf9dcd4441659

商户号：****************
APP ID: d04984fc6b35fe0caea08629bb98b715
SECRET: 0712a1e9c4f772877a016a77575f4e09

VA收款模式

# 流程图

## 汇通系统与全球付系统交互流程

```mermaid
sequenceDiagram
    participant 客户
    participant 汇通服务平台
    participant 全球付系统

    客户->>汇通服务平台: 提交开户申请
    汇通服务平台->>全球付系统: 调用开户接口 (/api/openAccount)
    全球付系统-->>汇通服务平台: 返回开户结果 (Success/Failure)
    汇通服务平台-->>客户: 通知开户成功

    客户->>汇通服务平台: 请求开通VA账户
    汇通服务平台->>全球付系统: 调用开通VA接口 (/api/createVA)
    全球付系统-->>汇通服务平台: 返回VA账户信息 (VA Account Details)
    汇通服务平台-->>客户: 通知VA账户开通成功

    客户->>汇通服务平台: 提交资金入账请求
    汇通服务平台->>全球付系统: 调用资金入账接口 (/api/depositFunds)
    全球付系统-->>汇通服务平台: 返回资金入账确认 (Deposit Confirmation)
    汇通服务平台-->>客户: 通知资金入账成功

    客户->>汇通服务平台: 请求换汇
    汇通服务平台->>全球付系统: 调用换汇接口 (/api/exchangeCurrency)
    全球付系统-->>汇通服务平台: 返回换汇结果 (Exchange Result)
    汇通服务平台-->>客户: 通知换汇完成

    客户->>汇通服务平台: 提交付款请求
    汇通服务平台->>全球付系统: 调用付款接口 (/api/processPayment)
    全球付系统-->>汇通服务平台: 返回付款确认 (Payment Confirmation)
    汇通服务平台-->>客户: 通知付款成功
```

## 汇通出金流程

### 流程图

```mermaid
sequenceDiagram
    participant c as 客户
    box 汇通系统
    participant p as 汇通门户
    participant a as 汇通账户
    end
    participant g as 全球付系统

    c->>p: 提交出金申请
    p->>a: 账户余额扣减
    p->>g: 调用付款接口
    note right of g: 接口: 3.4.8 付款申请<br/>待后续异步通知或查询
    g -->> p: 返回付款结果

    rect rgb(20, 120, 120)
        g->>p: 付款结果异步通知
        note right of g: 接口: 3.7.4 付款通知
    end

    rect rgb(20, 120, 120)
        p->>g: 调用查询接口
        note right of g: 接口: 3.4.9 付款订单查询
        g -->> p: 返回付款结果
    end
    alt 成功
        p->>a: 补录手续费
        p->>p: 出金成功
        p-->>c: 通知出金成功
    else 失败
        p->>a: 记账回滚
        p->>p: 出金成功
        p-->>c: 通知出金失败
    end
```

### 流程说明

主要特点，业务才有代理商模式接入全球付，出金过程手续费由上游（全球付）计算，业务只决定客户是否开通付款功能，而非手续费。
