import sqlite3
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, PhotoImage, filedialog
from tkcalendar import DateEntry
from dataclasses import dataclass
import os  # 添加 os 模块
from PIL import Image, ImageTk  # 添加 PIL 模块
import shutil  # 用于文件操作

@dataclass
class Todo:
    id: int
    title: str
    description: str
    status: str
    created_time: str
    planned_time: str
    completed_time: str
    parent_id: int
    tag: str

class TodoDatabase:
    def __init__(self, db_path="todo.db"):
        self.connection = sqlite3.connect(db_path)
        self.create_table()
        self.migrate_database()

    def create_table(self):
        query = """
        CREATE TABLE IF NOT EXISTS todos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            status TEXT CHECK(status IN ('待处理', '已完成')) DEFAULT '待处理',
            created_time TEXT NOT NULL,
            planned_time TEXT,
            completed_time TEXT,
            parent_id INTEGER,
            tag TEXT,
            FOREIGN KEY(parent_id) REFERENCES todos(id)
        )
        """
        self.connection.execute(query)
        self.connection.commit()

    def add_todo(self, title, description="", planned_time=None, parent_id=None, tag=None):
        query = """
        INSERT INTO todos (title, description, created_time, planned_time, parent_id, tag)
        VALUES (?, ?, ?, ?, ?, ?)
        """
        created_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.connection.execute(query, (title, description, created_time, planned_time, parent_id, tag))
        self.connection.commit()

    def get_todos(self, status=None):
        query = "SELECT * FROM todos"
        if status:
            query += " WHERE status = ?"
            return self.connection.execute(query, (status,)).fetchall()
        return self.connection.execute(query).fetchall()

    def migrate_database(self):
        """确保数据库结构是最新的"""
        # 检查tag列是否存在
        cursor = self.connection.cursor()
        cursor.execute("PRAGMA table_info(todos)")
        columns = [column[1] for column in cursor.fetchall()]

        # 如果tag列不存在，添加它
        if 'tag' not in columns:
            cursor.execute("ALTER TABLE todos ADD COLUMN tag TEXT")
            self.connection.commit()
            print("Added tag column to todos table")

        # 创建附件表
        attachment_table_query = """
        CREATE TABLE IF NOT EXISTS attachments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            todo_id INTEGER NOT NULL,
            file_path TEXT,
            file_name TEXT NOT NULL,
            file_content BLOB NOT NULL,
            created_time TEXT NOT NULL,
            FOREIGN KEY(todo_id) REFERENCES todos(id) ON DELETE CASCADE
        )
        """
        cursor.execute(attachment_table_query)
        self.connection.commit()

        # 检查是否需要迁移旧的附件表
        cursor.execute("PRAGMA table_info(attachments)")
        columns = [column[1] for column in cursor.fetchall()]

        # 如果file_content列不存在，添加它
        if 'file_content' not in columns:
            try:
                # 尝试添加新列
                cursor.execute("ALTER TABLE attachments ADD COLUMN file_content BLOB")
                self.connection.commit()
                print("Added file_content column to attachments table")

                # 更新现有记录，读取文件内容并存储
                attachments = cursor.execute("SELECT id, file_path FROM attachments WHERE file_path IS NOT NULL").fetchall()
                for attachment_id, file_path in attachments:
                    if file_path and os.path.exists(file_path):
                        try:
                            with open(file_path, 'rb') as f:
                                file_content = f.read()
                                cursor.execute("UPDATE attachments SET file_content = ? WHERE id = ?",
                                              (file_content, attachment_id))
                        except Exception as e:
                            print(f"Error reading file {file_path}: {e}")
                self.connection.commit()
            except Exception as e:
                print(f"Error migrating attachments table: {e}")

    def add_attachment(self, todo_id, file_path):
        """添加附件到任务，并存储文件内容"""
        # 从文件路径中提取文件名
        file_name = os.path.basename(file_path)
        created_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 读取文件内容
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read()
        except Exception as e:
            print(f"读取文件失败: {e}")
            return None

        query = """
        INSERT INTO attachments (todo_id, file_path, file_name, file_content, created_time)
        VALUES (?, ?, ?, ?, ?)
        """
        self.connection.execute(query, (todo_id, file_path, file_name, file_content, created_time))
        self.connection.commit()
        return self.connection.execute("SELECT last_insert_rowid()").fetchone()[0]

    def get_attachments(self, todo_id):
        """获取任务的所有附件"""
        query = "SELECT * FROM attachments WHERE todo_id = ?"
        return self.connection.execute(query, (todo_id,)).fetchall()

    def delete_attachment(self, attachment_id):
        """删除附件"""
        query = "DELETE FROM attachments WHERE id = ?"
        self.connection.execute(query, (attachment_id,))
        self.connection.commit()

    def close(self):
        self.connection.close()

class TodoApp:
    def __init__(self, root):
        self.db = TodoDatabase()
        self.root = root
        self.root.title("待办事项管理")

        # 初始化上次搜索条件的存储变量
        self.last_search_keyword = ""
        self.last_search_tag = ""
        self.last_search_start_date = None
        self.last_search_end_date = None

        self.apply_dark_theme()  # 应用深色主题
        self.create_menu()  # 创建菜单
        self.create_toolbar()  # 创建工具栏
        self.create_widgets()

    def create_menu(self):
        """创建菜单栏"""
        menu_bar = tk.Menu(self.root, bg="#2e2e2e", fg="white", activebackground="#4e4e4e", activeforeground="white", relief=tk.FLAT)

        # 任务菜单
        task_menu = tk.Menu(menu_bar, tearoff=0, bg="#2e2e2e", fg="white", activebackground="#4e4e4e", activeforeground="white", relief=tk.FLAT)
        task_menu.add_command(label="新增", command=self.add_todo)
        task_menu.add_command(label="删除", command=self.delete_todo)  # 新增删除菜单
        task_menu.add_command(label="刷新", command=self.refresh_todos)
        task_menu.add_command(label="搜索", command=self.toggle_search_frame)  # 新增搜索菜单
        menu_bar.add_cascade(label="任务", menu=task_menu)

        # 帮助菜单
        help_menu = tk.Menu(menu_bar, tearoff=0, bg="#2e2e2e", fg="white", activebackground="#4e4e4e", activeforeground="white", relief=tk.FLAT)
        help_menu.add_command(label="关于", command=self.show_about)
        menu_bar.add_cascade(label="帮助", menu=help_menu)

        self.root.config(menu=menu_bar)

    def create_toolbar(self):
        """创建工具栏"""
        toolbar = tk.Frame(self.root, bg="#2e2e2e", relief=tk.RAISED, bd=1)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        self.toolbar = toolbar  # 保存工具栏引用

        # 获取图片目录路径
        res_dir = os.path.join(os.path.dirname(__file__), "res")

        # 加载并调整图片大小
        image_size = (30, 30)
        add_icon = ImageTk.PhotoImage(Image.open(os.path.join(res_dir, "add.png")).resize(image_size, Image.Resampling.LANCZOS))
        delete_icon = ImageTk.PhotoImage(Image.open(os.path.join(res_dir, "del.png")).resize(image_size, Image.Resampling.LANCZOS))
        refresh_icon = ImageTk.PhotoImage(Image.open(os.path.join(res_dir, "refresh.png")).resize(image_size, Image.Resampling.LANCZOS))
        search_icon = ImageTk.PhotoImage(Image.open(os.path.join(res_dir, "search.png")).resize(image_size, Image.Resampling.LANCZOS))

        # 第一区：新增和刷新按钮
        add_button = tk.Button(toolbar, image=add_icon, command=self.add_todo, bg="#4e4e4e", relief=tk.FLAT)
        add_button.image = add_icon  # 防止图片被垃圾回收
        add_button.pack(side=tk.LEFT, padx=5, pady=5)
        add_button.bind("<Enter>", lambda e: self.show_tip("新增"))
        add_button.bind("<Leave>", lambda e: self.hide_tip())

        del_button = tk.Button(toolbar, image=delete_icon, command=self.delete_todo, bg="#4e4e4e", relief=tk.FLAT)
        del_button.image = delete_icon
        del_button.pack(side=tk.LEFT, padx=5, pady=5)
        del_button.bind("<Enter>", lambda e: self.show_tip("删除"))
        del_button.bind("<Leave>", lambda e: self.hide_tip())

        refresh_button = tk.Button(toolbar, image=refresh_icon, command=self.refresh_todos, bg="#4e4e4e", relief=tk.FLAT)
        refresh_button.image = refresh_icon
        refresh_button.pack(side=tk.LEFT, padx=5, pady=5)
        refresh_button.bind("<Enter>", lambda e: self.show_tip("刷新"))
        refresh_button.bind("<Leave>", lambda e: self.hide_tip())

        search_button = tk.Button(toolbar, image=search_icon, command=self.toggle_search_frame, bg="#4e4e4e", relief=tk.FLAT)
        search_button.image = search_icon
        search_button.pack(side=tk.LEFT, padx=5)
        search_button.bind("<Enter>", lambda e: self.show_tip("搜索"))
        search_button.bind("<Leave>", lambda e: self.hide_tip())

    def toggle_search_frame(self):
        """打开高级搜索对话框"""
        self.open_search_dialog()

    def open_search_dialog(self):
        """打开高级搜索对话框"""
        search_dialog = tk.Toplevel(self.root)
        search_dialog.title("高级搜索")
        search_dialog.configure(bg="#2e2e2e")  # 设置窗口背景为深色

        # 设置窗口在主窗口的中间
        self.root.update_idletasks()
        root_x = self.root.winfo_x()
        root_y = self.root.winfo_y()
        root_width = self.root.winfo_width()
        root_height = self.root.winfo_height()

        search_dialog.update_idletasks()
        width = 400  # 设置固定宽度
        height = 300  # 设置固定高度
        x = root_x + (root_width // 2) - (width // 2)
        y = root_y + (root_height // 2) - (height // 2)
        search_dialog.geometry(f"{width}x{height}+{x}+{y}")

        # 确保窗口在主窗口上面
        search_dialog.transient(self.root)
        search_dialog.grab_set()

        # 关键字搜索
        tk.Label(search_dialog, text="关键字:", bg="#2e2e2e", fg="white").grid(row=0, column=0, padx=10, pady=10, sticky="w")
        keyword_entry = tk.Entry(search_dialog, bg="#3e3e3e", fg="white", width=30)
        keyword_entry.insert(0, self.last_search_keyword)  # 填充上次搜索的关键字
        keyword_entry.grid(row=0, column=1, padx=10, pady=10, sticky="w")

        # 标签搜索
        tk.Label(search_dialog, text="标签:", bg="#2e2e2e", fg="white").grid(row=1, column=0, padx=10, pady=10, sticky="w")
        tag_entry = tk.Entry(search_dialog, bg="#3e3e3e", fg="white", width=30)
        tag_entry.insert(0, self.last_search_tag)  # 填充上次搜索的标签
        tag_entry.grid(row=1, column=1, padx=10, pady=10, sticky="w")

        # 日期范围
        tk.Label(search_dialog, text="开始日期:", bg="#2e2e2e", fg="white").grid(row=2, column=0, padx=10, pady=10, sticky="w")
        start_date_frame = tk.Frame(search_dialog, bg="#2e2e2e")
        start_date_frame.grid(row=2, column=1, padx=10, pady=10, sticky="w")

        # 创建日期选择器，并填充上次搜索的日期
        start_date_entry = DateEntry(start_date_frame, width=12, background="darkblue", foreground="white", borderwidth=2)
        start_date_entry.delete(0, tk.END)  # 先清空默认日期

        # 如果有上次的开始日期，则填充
        if self.last_search_start_date:
            start_date_entry.set_date(self.last_search_start_date)

        start_date_entry.pack(side=tk.LEFT, padx=(0, 5))

        # 添加清除按钮
        tk.Button(start_date_frame, text="清除", command=lambda: start_date_entry.delete(0, tk.END),
                 bg="#4e4e4e", fg="white", width=4).pack(side=tk.LEFT)

        tk.Label(search_dialog, text="结束日期:", bg="#2e2e2e", fg="white").grid(row=3, column=0, padx=10, pady=10, sticky="w")
        end_date_frame = tk.Frame(search_dialog, bg="#2e2e2e")
        end_date_frame.grid(row=3, column=1, padx=10, pady=10, sticky="w")

        # 创建日期选择器，并填充上次搜索的日期
        end_date_entry = DateEntry(end_date_frame, width=12, background="darkblue", foreground="white", borderwidth=2)
        end_date_entry.delete(0, tk.END)  # 先清空默认日期

        # 如果有上次的结束日期，则填充
        if self.last_search_end_date:
            end_date_entry.set_date(self.last_search_end_date)

        end_date_entry.pack(side=tk.LEFT, padx=(0, 5))

        # 添加清除按钮
        tk.Button(end_date_frame, text="清除", command=lambda: end_date_entry.delete(0, tk.END),
                 bg="#4e4e4e", fg="white", width=4).pack(side=tk.LEFT)

        # 搜索按钮
        def do_advanced_search():
            keyword = keyword_entry.get().strip().lower()
            tag = tag_entry.get().strip().lower()

            # 获取日期值，如果日期字段为空，则返回 None
            start_date = None
            end_date = None

            # 检查开始日期是否有值
            if start_date_entry.get().strip():
                try:
                    start_date = start_date_entry.get_date()
                except ValueError:
                    pass

            # 检查结束日期是否有值
            if end_date_entry.get().strip():
                try:
                    end_date = end_date_entry.get_date()
                except ValueError:
                    pass

            # 保存搜索条件以便下次使用
            self.last_search_keyword = keyword
            self.last_search_tag = tag
            self.last_search_start_date = start_date
            self.last_search_end_date = end_date

            self.advanced_search(keyword, tag, start_date, end_date)
            search_dialog.destroy()

        button_frame = tk.Frame(search_dialog, bg="#2e2e2e")
        button_frame.grid(row=4, column=0, columnspan=2, pady=20)

        tk.Button(button_frame, text="搜索", command=do_advanced_search, bg="#4e4e4e", fg="white").pack(side=tk.LEFT, padx=10)
        tk.Button(button_frame, text="取消", command=search_dialog.destroy, bg="#4e4e4e", fg="white").pack(side=tk.LEFT, padx=10)

    def show_tip(self, text):
        """显示提示文本"""
        if not hasattr(self, "tip_label"):
            self.tip_label = tk.Label(self.root, text="", bg="#4e4e4e", fg="white", relief=tk.FLAT)
        self.tip_label.config(text=text)
        self.tip_label.place(x=10, y=self.root.winfo_height() - 30)

    def hide_tip(self):
        """隐藏提示文本"""
        if hasattr(self, "tip_label"):
            self.tip_label.place_forget()

    def show_about(self):
        """显示关于信息"""
        messagebox.showinfo("关于", "待办事项管理\n版本: 1.0\n作者: 示例用户")

    def apply_dark_theme(self):
        """应用深色主题"""
        self.root.configure(bg="#2e2e2e")  # 设置窗口背景颜色
        style = ttk.Style()
        style.theme_use("clam")  # 使用 clam 主题
        style.configure("Treeview",
                        background="#3a3a3a",  # 比原来稍浅的背景色
                        foreground="white",
                        fieldbackground="#3a3a3a",
                        bordercolor="#3a3a3a",  # 设置边框颜色与背景一致以去掉边框
                        rowheight=40)  # 行高增加到40
        style.configure("Treeview.Heading", background="#3a3a3a", foreground="white", rowheight=40)
        style.map("Treeview",
                  background=[("selected", "#5e5e5e")],  # 设置选中行背景色
                  foreground=[("selected", "white")])  # 选中时字体为白色，未选中时由标签控制字体颜色
        style.configure("TButton", background="#4e4e4e", foreground="white", borderwidth=1)
        style.map("TButton", background=[("active", "#5e5e5e")])
        style.configure("TLabel", background="#2e2e2e", foreground="white")
        style.configure("TEntry", fieldbackground="#3e3e3e", foreground="white", bordercolor="#2e2e2e")
        style.configure("TCombobox", fieldbackground="#3e3e3e", foreground="white", bordercolor="#2e2e2e")
        style.map("TCombobox",
                  fieldbackground=[("readonly", "#3e3e3e")],
                  background=[("readonly", "#3e3e3e")],
                  foreground=[("readonly", "white")])
        style.configure("TScrollbar", background="#3e3e3e", troughcolor="#2e2e2e")

    def create_widgets(self):
        # 搜索框架
        self.search_frame = tk.Frame(self.root, bg="#2e2e2e")  # 修改为直接放在 root 上
        self.search_frame.pack(fill=tk.X, padx=5, pady=5)  # 默认显示

        tk.Label(self.search_frame, text="搜索:", bg="#2e2e2e", fg="white").pack(side=tk.LEFT, padx=5)
        self.search_entry = tk.Entry(self.search_frame, bg="#3e3e3e", fg="white")
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.search_entry.bind("<Return>", lambda event: self.search_todos())  # 绑定回车事件

        self.show_pending_var = tk.BooleanVar(value=True)  # 默认选中“不显示历史任务”
        show_pending_checkbox = tk.Checkbutton(
            self.search_frame, text="不显示历史任务", variable=self.show_pending_var,
            bg="#2e2e2e", fg="white", selectcolor="#2e2e2e", activebackground="#2e2e2e", activeforeground="white",
            command=self.refresh_todos
        )
        show_pending_checkbox.pack(side=tk.LEFT, padx=5)

        tk.Button(self.search_frame, text="搜索", command=self.search_todos, bg="#4e4e4e", fg="white").pack(side=tk.LEFT, padx=5)

        # 中间框架
        middle_frame = tk.Frame(self.root, bg="#2e2e2e")
        middle_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧Treeview框架
        self.tree_frame = tk.Frame(middle_frame, bg="#2e2e2e", width=int(self.root.winfo_screenwidth() * 0.7),
                                   highlightthickness=0, bd=0, highlightbackground="#2e2e2e", highlightcolor="#2e2e2e")  # Tree占70%宽度
        self.tree_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Treeview
        self.tree = ttk.Treeview(self.tree_frame, columns=("title", "status", "planned_time", "completed_time"),
                                 show="tree headings")  # 移除 highlightthickness 和 bd
        # 设置表头
        self.tree.heading("#0", text="ID")
        self.tree.heading("title", text="标题")
        self.tree.heading("status", text="状态")
        self.tree.heading("planned_time", text="计划完成")
        self.tree.heading("completed_time", text="实际完成")

        # 设置表头字体大小为16
        style = ttk.Style()
        style.configure("Treeview.Heading", font=("Arial", 12))
        self.tree.column("#0", width=60, stretch=False)
        self.tree.column("title", width=250, stretch=True)
        self.tree.column("status", width=60, stretch=False)
        self.tree.column("planned_time", width=90, stretch=False)
        self.tree.column("completed_time", width=140, stretch=False)

        # 滚动条
        tree_scroll_y = ttk.Scrollbar(self.tree_frame, orient=tk.VERTICAL, command=self.tree.yview, style="Vertical.TScrollbar")
        tree_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.configure(yscrollcommand=tree_scroll_y.set)

        # 配置滚动条样式
        style = ttk.Style()
        style.configure("Vertical.TScrollbar", background="#3e3e3e", troughcolor="#2e2e2e", arrowcolor="white", bordercolor="#2e2e2e")

        self.tree.pack(fill=tk.BOTH, expand=True)

        # 展开选中任务的子任务
        self.tree.bind("<<TreeviewOpen>>", lambda e: self.expand_selected_task())

        # 配置标签样式
        self.tree.tag_configure("alternate", background="#5a5a5a")  # 添加数据行间的淡灰色线条
        self.tree.tag_configure("completed", foreground="white", font=("Arial", 10, "overstrike"))  # 删除线字体
        self.tree.tag_configure("overdue", foreground="yellow")  # 红色文字

        # 右侧详情框架
        self.details_frame = tk.Frame(middle_frame, bg="#2e2e2e", width=300, highlightbackground="white", highlightthickness=1)  # 增加白色边框
        self.details_frame.pack(side=tk.RIGHT, fill=tk.Y)
        self.details_frame.pack_propagate(False)  # 禁止自动调整大小
        self.details_frame.pack_forget()  # 默认隐藏详情框架

        # 在 details_frame 中直接添加内容框架
        self.details_content = tk.Frame(self.details_frame, bg="#2e2e2e")
        self.details_content.pack(fill=tk.BOTH, expand=True)

        # 底部操作按钮框架
        frame = tk.Frame(self.root, bg="#2e2e2e")
        frame.pack(fill=tk.X)

        self.tree.bind("<<TreeviewSelect>>", self.show_details)  # 绑定选中事件
        self.refresh_todos()

    def expand_selected_task(self):
        """仅展开选中任务的子任务"""
        selected_item = self.tree.focus()  # 获取当前选中的任务
        if selected_item:
            self.tree.item(selected_item, open=True)  # 展开选中的任务

    def refresh_todos(self, select_id=None):
        # 保存当前选中和展开状态
        selected_items = self.tree.selection()
        selected_id = selected_items[0] if selected_items else select_id

        # 记录是否是特意选中的任务
        is_specific_selection = select_id is not None

        expanded_items = set()
        for item in self.tree.get_children():
            if self.tree.item(item, "open"):
                expanded_items.add(item)

        # 清空并重新加载数据
        for row in self.tree.get_children():
            self.tree.delete(row)
        todos = self.db.get_todos()
        todos = [Todo(*todo) for todo in todos]  # 将元组转换为 Todo 对象
        current_time = datetime.now()

        def parse_date(date_str):
            """尝试解析多种日期格式"""
            for fmt in ("%Y-%m-%d %H:%M:%S", "%Y/%m/%d", "%Y-%m-%d"):
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            return None  # 无法解析时返回 None

        search_text = self.search_entry.get().strip().lower()
        show_pending_only = self.show_pending_var.get()

        def matches_search(todo):
            """检查任务是否匹配搜索条件"""
            if search_text and search_text not in (todo.title.lower() + (todo.description or "").lower()):
                return False
            if self.show_pending_var.get():
                # 显示未处理任务和当日处理的任务
                planned_time = parse_date(todo.planned_time) if todo.planned_time else None
                completed_time = parse_date(todo.completed_time) if todo.completed_time else None
                if completed_time and completed_time.date() == current_time.date():
                    return True
                if todo.status == "已完成" and (not planned_time or planned_time.date() != current_time.date()):
                    return False
            return True

        # 找到所有匹配的任务及其父级任务
        matched_todos = set()
        todo_dict = {todo.id: todo for todo in todos}  # 构建任务字典，便于查找父级任务

        def add_with_parents(todo):
            """递归添加任务及其父级任务"""
            if todo.id in matched_todos:
                return
            matched_todos.add(todo.id)
            if todo.parent_id and todo.parent_id in todo_dict:
                add_with_parents(todo_dict[todo.parent_id])

        for todo in todos:
            if matches_search(todo):
                add_with_parents(todo)

        # 过滤出匹配的任务
        todos = [todo for todo in todos if todo.id in matched_todos]

        def sort_key(todo):
            status_priority = 0 if todo.status == "待处理" else 1  # 待处理优先
            planned_time = parse_date(todo.planned_time) if todo.planned_time else datetime.min
            planned_time = -planned_time.timestamp() if planned_time else float('inf')  # 倒序排序
            is_root_task = todo.parent_id is None  # 判断是否为第一层任务
            return (status_priority, not is_root_task, planned_time)

        # 按状态排序：未完成的任务在前，已完成的任务在后
        todos.sort(key=sort_key )

        for index, todo in enumerate(todos):
            parent_id = int(todo.parent_id) if todo.parent_id is not None else None
            parent = "" if parent_id is None else parent_id
            row_id = self.tree.insert(parent, tk.END, iid=todo.id, text=todo.id, values=(todo.title, todo.status, todo.planned_time, todo.completed_time, todo.tag))

            # 设置字体样式和背景色
            if todo.status == "已完成":
                self.tree.item(row_id, tags=("completed", "odd" if index % 2 == 0 else "even"))
            elif todo.status == "待处理" and todo.planned_time and parse_date(todo.planned_time) < current_time:
                self.tree.item(row_id, tags=("overdue", "odd" if index % 2 == 0 else "even"))
            else:
                tag = "odd" if index % 2 == 0 else "even"
                self.tree.item(row_id, tags=(tag,))

        # 配置标签样式
        self.tree.tag_configure("completed", font=("Arial", 10, "overstrike"), foreground="white")  # 删除线字体
        self.tree.tag_configure("overdue", foreground="yellow")  # 红色字体
        self.tree.tag_configure("odd", background="#4a4a4a", foreground="white")  # 奇数行颜色
        self.tree.tag_configure("even", background="#3e3e3e", foreground="white")  # 偶数行颜色

        # 修改选中时的样式映射，确保超时任务选中后仍保持红色字体
        style = ttk.Style()
        style.map("Treeview",
                  foreground=[("selected", "white")],  # 默认选中字体为白色
                  background=[("selected", "#5e5e5e")])  # 选中行背景色

        # 保留超时任务黄色字体
        self.tree.tag_configure("overdue:selected", foreground="yellow")

        # 恢复展开状态
        for item in expanded_items:
            if self.tree.exists(item):
                self.tree.item(item, open=True)

        # 恢复选中状态
        if selected_id and self.tree.exists(selected_id):
            self.tree.selection_set(selected_id)
            self.tree.see(selected_id)  # 滚动到选中的任务

            # 如果是新添加或编辑的任务，显示详情窗口
            if is_specific_selection:  # 如果是特意选中的任务
                # 获取任务详情并显示
                query = "SELECT * FROM todos WHERE id = ?"
                todo = self.db.connection.execute(query, (selected_id,)).fetchone()
                if todo:
                    # 显示详情框架
                    self.details_frame.pack(side=tk.RIGHT, fill=tk.Y)
                    self.details_frame.pack_propagate(False)  # 确保宽度固定为300
                    self.details_frame.config(width=350)  # 固定宽度为350

                    # 清空并重新创建详情内容
                    for widget in self.details_content.winfo_children():
                        widget.destroy()

                    # 调用显示详情的方法
                    self.show_details(None)

    def search_todos(self):
        """触发简易搜索并刷新任务列表"""
        self.refresh_todos()

    def advanced_search(self, keyword, tag, start_date, end_date):
        """执行高级搜索

        Args:
            keyword: 关键字，用于搜索标题和内容
            tag: 标签，用于搜索标签字段
            start_date: 开始日期，用于筛选完成日期
            end_date: 结束日期，用于筛选完成日期
        """
        # 保存当前选中和展开状态
        selected_items = self.tree.selection()
        selected_id = selected_items[0] if selected_items else None

        expanded_items = set()
        for item in self.tree.get_children():
            if self.tree.item(item, "open"):
                expanded_items.add(item)

        # 清空并重新加载数据
        for row in self.tree.get_children():
            self.tree.delete(row)
        todos = self.db.get_todos()
        todos = [Todo(*todo) for todo in todos]  # 将元组转换为 Todo 对象
        current_time = datetime.now()

        def parse_date(date_str):
            """尝试解析多种日期格式"""
            if not date_str:
                return None
            for fmt in ("%Y-%m-%d %H:%M:%S", "%Y/%m/%d", "%Y-%m-%d"):
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            return None  # 无法解析时返回 None

        # 转换日期对象为字符串格式，用于比较
        start_date_str = start_date.strftime("%Y-%m-%d") if start_date else None
        end_date_str = end_date.strftime("%Y-%m-%d") if end_date else None

        def matches_advanced_search(todo):
            """检查任务是否匹配高级搜索条件"""
            # 关键字搜索（标题和内容）
            if keyword and keyword not in (todo.title.lower() + (todo.description or "").lower()):
                return False

            # 标签搜索
            if tag and (not todo.tag or tag not in todo.tag.lower()):
                return False

            # 日期范围搜索（完成日期）
            if start_date_str or end_date_str:
                if not todo.completed_time:  # 如果没有完成日期，则不匹配日期范围搜索
                    return False

                completed_date = parse_date(todo.completed_time)
                if not completed_date:  # 如果无法解析完成日期，则不匹配
                    return False

                completed_date_str = completed_date.strftime("%Y-%m-%d")

                if start_date_str and completed_date_str < start_date_str:
                    return False

                if end_date_str and completed_date_str > end_date_str:
                    return False

            return True

        # 找到所有匹配的任务及其父级任务
        matched_todos = set()
        todo_dict = {todo.id: todo for todo in todos}  # 构建任务字典，便于查找父级任务

        def add_with_parents(todo):
            """递归添加任务及其父级任务"""
            if todo.id in matched_todos:
                return
            matched_todos.add(todo.id)
            if todo.parent_id and todo.parent_id in todo_dict:
                add_with_parents(todo_dict[todo.parent_id])

        for todo in todos:
            if matches_advanced_search(todo):
                add_with_parents(todo)

        # 过滤出匹配的任务
        todos = [todo for todo in todos if todo.id in matched_todos]

        def sort_key(todo):
            status_priority = 0 if todo.status == "待处理" else 1  # 待处理优先
            planned_time = parse_date(todo.planned_time) if todo.planned_time else datetime.min
            planned_time = -planned_time.timestamp() if planned_time else float('inf')  # 倒序排序
            is_root_task = todo.parent_id is None  # 判断是否为第一层任务
            return (status_priority, not is_root_task, planned_time)

        # 按状态排序：未完成的任务在前，已完成的任务在后
        todos.sort(key=sort_key)

        for index, todo in enumerate(todos):
            parent_id = int(todo.parent_id) if todo.parent_id is not None else None
            parent = "" if parent_id is None else parent_id
            row_id = self.tree.insert(parent, tk.END, iid=todo.id, text=todo.id, values=(todo.title, todo.status, todo.planned_time, todo.completed_time, todo.tag))

            # 设置字体样式和背景色
            if todo.status == "已完成":
                self.tree.item(row_id, tags=("completed", "odd" if index % 2 == 0 else "even"))
            elif todo.status == "待处理" and todo.planned_time and parse_date(todo.planned_time) < current_time:
                self.tree.item(row_id, tags=("overdue", "odd" if index % 2 == 0 else "even"))
            else:
                tag = "odd" if index % 2 == 0 else "even"
                self.tree.item(row_id, tags=(tag,))

        # 配置标签样式
        self.tree.tag_configure("completed", font=("Arial", 10, "overstrike"), foreground="white")  # 删除线字体
        self.tree.tag_configure("overdue", foreground="yellow")  # 红色字体
        self.tree.tag_configure("odd", background="#4a4a4a", foreground="white")  # 奇数行颜色
        self.tree.tag_configure("even", background="#3e3e3e", foreground="white")  # 偶数行颜色

        # 修改选中时的样式映射，确保超时任务选中后仍保持红色字体
        style = ttk.Style()
        style.map("Treeview",
                  foreground=[("selected", "white")],  # 默认选中字体为白色
                  background=[("selected", "#5e5e5e")])  # 选中行背景色

        # 保留超时任务红色字体
        self.tree.tag_configure("overdue:selected", foreground="yellow")

        # 恢复展开状态
        for item in expanded_items:
            if self.tree.exists(item):
                self.tree.item(item, open=True)

        # 恢复选中状态
        if selected_id and self.tree.exists(selected_id):
            self.tree.selection_set(selected_id)
            self.tree.see(selected_id)  # 滚动到选中的任务

    def add_todo(self):
        def save_todo():
            title = title_entry.get()
            description = description_text.get("1.0", tk.END).strip()
            planned_time = planned_time_entry.get()
            parent_title = parent_id_var.get()
            tag = tag_entry.get().strip()  # 获取标签值
            if not title:
                messagebox.showerror("错误", "标题不能为空")
                return
            # 根据标题获取上级任务的 ID
            parent_id = None if parent_title == "无" else title_to_id.get(parent_title)

            # 添加任务
            self.db.add_todo(title, description, planned_time, parent_id, tag)

            # 获取新添加的任务ID
            query = "SELECT id FROM todos WHERE title = ? ORDER BY id DESC LIMIT 1"
            new_todo_id = self.db.connection.execute(query, (title,)).fetchone()[0]

            # 如果是子级任务，记录父级任务ID，以便展开
            parent_to_expand = parent_id

            # 刷新任务列表，并传递新任务ID以便选中
            self.refresh_todos(new_todo_id)

            # 如果是子级任务，展开父级任务
            if parent_to_expand:
                # 确保父任务展开
                self.tree.item(parent_to_expand, open=True)

            # 再次确保选中新添加的任务
            self.tree.selection_set(new_todo_id)
            self.tree.see(new_todo_id)  # 滚动到新任务的位置

            # 显示任务详情
            self.show_details(None)

            add_window.destroy()

        add_window = tk.Toplevel(self.root)
        add_window.title("添加待办事项")
        add_window.configure(bg="#2e2e2e")  # 设置窗口背景为深色

        # 设置窗口在主窗口的中间
        self.root.update_idletasks()
        root_x = self.root.winfo_x()
        root_y = self.root.winfo_y()
        root_width = self.root.winfo_width()
        root_height = self.root.winfo_height()

        add_window.update_idletasks()
        width = add_window.winfo_width()
        height = add_window.winfo_height()
        x = root_x + (root_width // 2) - (width // 2)
        y = root_y + (root_height // 2) - (height // 2)
        add_window.geometry(f"+{x}+{y}")

        # 确保窗口在主窗口上面
        add_window.transient(self.root)
        add_window.grab_set()

        tk.Label(add_window, text="标题:", bg="#2e2e2e", fg="white").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        title_entry = tk.Entry(add_window, bg="#3e3e3e", fg="white", width=40)  # 调整宽度为40
        title_entry.grid(row=0, column=1, padx=5, pady=5, sticky="w")

        tk.Label(add_window, text="说明:", bg="#2e2e2e", fg="white").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        description_text = scrolledtext.ScrolledText(add_window, width=40, height=8, bg="#3e3e3e", fg="white", insertbackground="white")
        description_text.grid(row=1, column=1, padx=5, pady=5, sticky="w")

        tk.Label(add_window, text="计划日期:", bg="#2e2e2e", fg="white").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        planned_time_entry = DateEntry(add_window, width=12, background="darkblue", foreground="white", borderwidth=2)
        planned_time_entry.grid(row=2, column=1, padx=5, pady=5, sticky="w")

        tk.Label(add_window, text="上级任务:", bg="#2e2e2e", fg="white").grid(row=3, column=0, padx=5, pady=5, sticky="w")
        parent_id_var = tk.StringVar(value="无")
        todos = self.db.get_todos()
        # 过滤掉已完成的任务
        title_to_id = {todo[1]: todo[0] for todo in todos if todo[3] != "已完成"}
        parent_options = ["无"] + list(title_to_id.keys())
        parent_menu = ttk.Combobox(add_window, textvariable=parent_id_var, values=parent_options, state="readonly")
        parent_menu.grid(row=3, column=1, padx=5, pady=5, sticky="w")

        # 添加标签字段
        tk.Label(add_window, text="标签:", bg="#2e2e2e", fg="white").grid(row=4, column=0, padx=5, pady=5, sticky="w")
        tag_entry = tk.Entry(add_window, bg="#3e3e3e", fg="white", width=40)
        tag_entry.grid(row=4, column=1, padx=5, pady=5, sticky="w")

        # 修改下拉列表框为深色主题
        parent_menu.configure(background="#3e3e3e", foreground="white")

        # 设置下拉列表背景为深色
        parent_menu.option_add("*TCombobox*Listbox.background", "#3e3e3e")
        parent_menu.option_add("*TCombobox*Listbox.foreground", "white")
        parent_menu.option_add("*TCombobox*Listbox.selectBackground", "#5e5e5e")

        # 如果有选中的任务，默认设置为父级任务
        selected_item = self.tree.selection()
        if selected_item:
            selected_todo_id = self.tree.item(selected_item[0], "text")
            for title, todo_id in title_to_id.items():
                if todo_id == int(selected_todo_id):
                    parent_id_var.set(title)
                    break

        tk.Button(add_window, text="保存", command=save_todo, bg="#4e4e4e", fg="white").grid(row=5, column=0, columnspan=2, pady=10)

    def edit_todo(self):
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showerror("错误", "请选择一个待办事项")
            return

        todo_id = self.tree.item(selected_item[0], "text")
        query = "SELECT * FROM todos WHERE id = ?"
        todo = self.db.connection.execute(query, (todo_id,)).fetchone()

        if not todo:
            messagebox.showerror("错误", "未找到该待办事项")
            return

        def save_changes():
            title = title_entry.get()
            description = description_text.get("1.0", tk.END).strip()
            planned_time = planned_time_entry.get()
            parent_title = parent_id_var.get()
            tag = tag_entry.get().strip()  # 获取标签值
            if not title:
                messagebox.showerror("错误", "标题不能为空")
                return

            # 获取原来的父任务ID
            old_parent_id = todo[7]

            # 获取新的父任务ID
            new_parent_id = None if parent_title == "无" else title_to_id.get(parent_title)

            update_query = """
            UPDATE todos
            SET title = ?, description = ?, planned_time = ?, parent_id = ?, tag = ?
            WHERE id = ?
            """
            self.db.connection.execute(update_query, (title, description, planned_time, new_parent_id, tag, todo_id))
            self.db.connection.commit()

            # 记录需要展开的父任务ID
            parent_to_expand = None
            if new_parent_id != old_parent_id and new_parent_id is not None:
                parent_to_expand = new_parent_id

            # 刷新任务列表，并传递任务ID以便选中
            self.refresh_todos(todo_id)

            # 如果父任务发生了变化，并且新父任务不为空，展开新父任务
            if parent_to_expand:
                self.tree.item(parent_to_expand, open=True)

            # 再次确保选中编辑后的任务
            self.tree.selection_set(todo_id)
            self.tree.see(todo_id)  # 滚动到任务的位置

            # 显示任务详情
            self.show_details(None)

            edit_window.destroy()

        edit_window = tk.Toplevel(self.root)
        edit_window.title("修改待办事项")
        edit_window.configure(bg="#2e2e2e")  # 设置窗口背景为深色

        # 设置窗口居中
        edit_window.update_idletasks()
        width = edit_window.winfo_width()
        height = edit_window.winfo_height()
        x = (edit_window.winfo_screenwidth() // 2) - (width // 2)
        y = (edit_window.winfo_screenheight() // 2) - (height // 2)
        edit_window.geometry(f"+{x}+{y}")

        # 确保窗口在主窗口上面
        edit_window.transient(self.root)
        edit_window.grab_set()

        tk.Label(edit_window, text="标题:", bg="#2e2e2e", fg="white").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        title_entry = tk.Entry(edit_window, bg="#3e3e3e", fg="white")
        title_entry.insert(0, todo[1])
        title_entry.grid(row=0, column=1, padx=5, pady=5, sticky="w")

        tk.Label(edit_window, text="说明:", bg="#2e2e2e", fg="white").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        description_text = scrolledtext.ScrolledText(edit_window, width=40, height=5, bg="#3e3e3e", fg="white", insertbackground="white")
        description_text.insert("1.0", todo[2])
        description_text.grid(row=1, column=1, padx=5, pady=5, sticky="w")

        tk.Label(edit_window, text="计划日期:", bg="#2e2e2e", fg="white").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        planned_time_entry = DateEntry(edit_window, width=12, background="darkblue", foreground="white", borderwidth=2)
        if todo[5]:
            planned_time_entry.set_date(todo[5])
        planned_time_entry.grid(row=2, column=1, padx=5, pady=5, sticky="w")

        tk.Label(edit_window, text="上级任务:", bg="#2e2e2e", fg="white").grid(row=3, column=0, padx=5, pady=5, sticky="w")
        parent_id_var = tk.StringVar(value="无" if todo[7] is None else "")
        todos = self.db.get_todos()
        title_to_id = {t[1]: t[0] for t in todos if t[3] != "已完成" and t[0] != todo_id}
        parent_options = ["无"] + list(title_to_id.keys())
        parent_menu = ttk.Combobox(edit_window, textvariable=parent_id_var, values=parent_options, state="readonly")
        if todo[7]:
            parent_menu.set(next((k for k, v in title_to_id.items() if v == todo[7]), "无"))
        parent_menu.grid(row=3, column=1, padx=5, pady=5, sticky="w")

        # 设置下拉列表框为深色主题
        parent_menu.configure(background="#3e3e3e", foreground="white")

        # 设置下拉列表背景为深色
        parent_menu.option_add("*TCombobox*Listbox.background", "#3e3e3e")
        parent_menu.option_add("*TCombobox*Listbox.foreground", "white")
        parent_menu.option_add("*TCombobox*Listbox.selectBackground", "#5e5e5e")

        # 添加标签字段
        tk.Label(edit_window, text="标签:", bg="#2e2e2e", fg="white").grid(row=4, column=0, padx=5, pady=5, sticky="w")
        tag_entry = tk.Entry(edit_window, bg="#3e3e3e", fg="white", width=40)
        # 安全地获取tag值，如果存在的话
        try:
            if len(todo) > 8 and todo[8]:
                tag_entry.insert(0, todo[8])
        except (IndexError, TypeError):
            pass
        tag_entry.grid(row=4, column=1, padx=5, pady=5, sticky="w")

        tk.Button(edit_window, text="保存", command=save_changes, bg="#4e4e4e", fg="white").grid(row=5, column=0, columnspan=2, pady=10)

    def mark_completed(self, todo_id=None):
        if not todo_id:
            selected_item = self.tree.selection()
            if not selected_item:
                messagebox.showerror("错误", "请选择一个待办事项")
                return
            todo_id = self.tree.item(selected_item[0], "text")
        completed_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        def mark_subtasks_completed(task_id):
            # 更新当前任务为已完成
            query = "UPDATE todos SET status = '已完成', completed_time = ? WHERE id = ?"
            self.db.connection.execute(query, (completed_time, task_id))
            # 查找子任务
            subtask_query = "SELECT id FROM todos WHERE status='待处理' AND parent_id = ?"
            subtasks = self.db.connection.execute(subtask_query, (task_id,)).fetchall()
            for subtask in subtasks:
                mark_subtasks_completed(subtask[0])  # 递归标记子任务

        # 标记当前任务及其所有子任务
        mark_subtasks_completed(todo_id)
        self.db.connection.commit()

        # 如果是从详情窗口中调用的，更新详情窗口中的按钮状态
        if self.details_frame.winfo_ismapped():
            # 先更新按钮状态
            for widget in self.details_content.winfo_children():
                if isinstance(widget, tk.Frame):
                    for button in widget.winfo_children():
                        if isinstance(button, tk.Button):
                            if button["text"] == "标记完成":
                                button.config(state=tk.DISABLED)
                            elif button["text"] == "标记未完成":
                                button.config(state=tk.NORMAL)

            # 更新状态标签
            for widget in self.details_content.winfo_children():
                if isinstance(widget, tk.Label) and widget["text"].startswith("状态:"):
                    widget.config(text="状态: 已完成")
                elif isinstance(widget, tk.Label) and widget["text"].startswith("完成时间:"):
                    widget.config(text=f"完成时间: {completed_time}")

        # 刷新任务列表，并传递任务ID以便选中
        self.refresh_todos(todo_id)

    def mark_uncompleted(self, todo_id=None):
        if not todo_id:
            selected_item = self.tree.selection()
            if not selected_item:
                messagebox.showerror("错误", "请选择一个待办事项")
                return
            todo_id = self.tree.item(selected_item[0], "text")

        def mark_parents_uncompleted(task_id):
            # 更新当前任务为未完成
            query = "UPDATE todos SET status = '待处理', completed_time = NULL WHERE id = ?"
            self.db.connection.execute(query, (task_id,))
            # 查找父任务
            parent_query = "SELECT parent_id FROM todos WHERE id = ?"
            parent = self.db.connection.execute(parent_query, (task_id,)).fetchone()
            if parent and parent[0]:
                mark_parents_uncompleted(parent[0])  # 递归标记父任务

        # 标记当前任务及其所有父任务
        mark_parents_uncompleted(todo_id)
        self.db.connection.commit()

        # 如果是从详情窗口中调用的，更新详情窗口中的按钮状态
        if self.details_frame.winfo_ismapped():
            # 先更新按钮状态
            for widget in self.details_content.winfo_children():
                if isinstance(widget, tk.Frame):
                    for button in widget.winfo_children():
                        if isinstance(button, tk.Button):
                            if button["text"] == "标记完成":
                                button.config(state=tk.NORMAL)
                            elif button["text"] == "标记未完成":
                                button.config(state=tk.DISABLED)

            # 更新状态标签
            for widget in self.details_content.winfo_children():
                if isinstance(widget, tk.Label) and widget["text"].startswith("状态:"):
                    widget.config(text="状态: 待处理")
                elif isinstance(widget, tk.Label) and widget["text"].startswith("完成时间:"):
                    widget.config(text=f"完成时间: None")

        # 刷新任务列表，并传递任务ID以便选中
        self.refresh_todos(todo_id)

    def delete_todo(self):
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showerror("错误", "请选择一个待办事项")
            return

        todo_id = self.tree.item(selected_item[0], "text")

        # 检查任务状态是否为已完成
        query = "SELECT status FROM todos WHERE id = ?"
        status = self.db.connection.execute(query, (todo_id,)).fetchone()
        if status and status[0] == "已完成":
            messagebox.showerror("错误", "已完成的任务不能删除")
            return

        # 检查是否有子任务
        subtask_query = "SELECT COUNT(*) FROM todos WHERE parent_id = ?"
        subtask_count = self.db.connection.execute(subtask_query, (todo_id,)).fetchone()[0]
        if subtask_count > 0:
            messagebox.showerror("错误", "有子任务的任务不能删除")
            return

        # 确认删除
        if not messagebox.askyesno("确认", "确定要删除该任务吗？"):
            return

        # 隐藏详情窗口
        self.hide_details()

        # 删除任务
        delete_query = "DELETE FROM todos WHERE id = ?"
        self.db.connection.execute(delete_query, (todo_id,))
        self.db.connection.commit()
        self.refresh_todos()

        # 清除选中状态，确保下次点击能正确触发选中事件
        self.tree.selection_remove(self.tree.selection())

    def show_details(self, event=None):
        # event 参数可以为 None，这样可以在代码中直接调用此方法
        selected_item = self.tree.selection()
        if not selected_item:
            return

        todo_id = self.tree.item(selected_item[0], "text")
        query = "SELECT * FROM todos WHERE id = ?"
        todo = self.db.connection.execute(query, (todo_id,)).fetchone()

        if not todo:
            return

        # 显示详情框架
        self.details_frame.pack(side=tk.RIGHT, fill=tk.Y)
        self.details_frame.pack_propagate(False)  # 确保宽度固定为300
        self.details_frame.config(width=350)  # 固定宽度为300
        for widget in self.details_content.winfo_children():
            widget.destroy()

        # 关闭按钮放在右上角
        close_button = tk.Button(self.details_content, text="X", command=self.hide_details, bg="#4e4e4e", fg="white", width=2)
        close_button.pack(anchor="ne", padx=5, pady=5)

        tk.Label(self.details_content, text="任务详情", font=("Arial", 14), bg="#2e2e2e", fg="white").pack(pady=10)

        # 标题编辑
        title_frame = tk.Frame(self.details_content, bg="#2e2e2e")
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        tk.Label(title_frame, text="标题:", bg="#2e2e2e", fg="white").pack(side=tk.LEFT)
        title_entry = tk.Entry(title_frame, bg="#3e3e3e", fg="white")
        title_entry.insert(0, todo[1])
        title_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5,0))

        # 说明编辑
        description_frame = tk.Frame(self.details_content, bg="#2e2e2e")
        description_frame.pack(fill=tk.X, padx=10, pady=5)
        tk.Label(description_frame, text="说明:", bg="#2e2e2e", fg="white").pack(side=tk.LEFT, anchor="n")
        description_text = scrolledtext.ScrolledText(description_frame, wrap=tk.WORD, height=8, bg="#3e3e3e", fg="white")
        description_text.insert("1.0", todo[2])
        description_text.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5,0))

        # 标签编辑
        tag_frame = tk.Frame(self.details_content, bg="#2e2e2e")
        tag_frame.pack(fill=tk.X, padx=10, pady=5)
        tk.Label(tag_frame, text="标签:", bg="#2e2e2e", fg="white").pack(side=tk.LEFT)
        tag_entry = tk.Entry(tag_frame, bg="#3e3e3e", fg="white")
        # 安全地获取tag值，如果存在的话
        try:
            if len(todo) > 8 and todo[8]:
                tag_entry.insert(0, todo[8])
        except (IndexError, TypeError):
            pass
        tag_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5,0))

        # 计划时间编辑
        planned_time_frame = tk.Frame(self.details_content, bg="#2e2e2e")
        planned_time_frame.pack(fill=tk.X, padx=10, pady=5)
        tk.Label(planned_time_frame, text="计划时间:", bg="#2e2e2e", fg="white").pack(side=tk.LEFT)
        planned_time_entry = DateEntry(planned_time_frame, width=12, background="darkblue", foreground="white", borderwidth=2)
        if todo[5]:
            planned_time_entry.set_date(todo[5])
        planned_time_entry.pack(side=tk.LEFT, padx=(5,0))

        # 保存更改的函数
        def save_changes(_=None):
            # 使用下划线参数忽略事件传递的参数
            title = title_entry.get().strip()
            description = description_text.get("1.0", tk.END).strip()
            planned_time = planned_time_entry.get()
            tag = tag_entry.get().strip()  # 获取标签值

            if not title:
                messagebox.showerror("错误", "标题不能为空")
                return
            update_query = """
            UPDATE todos
            SET title = ?, description = ?, planned_time = ?, tag = ?
            WHERE id = ?
            """
            self.db.connection.execute(update_query, (title, description, planned_time, tag, todo_id))
            self.db.connection.commit()
            self.refresh_todos()

        # 绑定保存事件
        title_entry.bind("<FocusOut>", save_changes)
        description_text.bind("<FocusOut>", save_changes)
        planned_time_entry.bind("<<DateEntrySelected>>", save_changes)
        tag_entry.bind("<FocusOut>", save_changes)

        # 状态和时间信息
        tk.Label(self.details_content, text=f"状态: {todo[3]}", bg="#2e2e2e", fg="white").pack(anchor="w", padx=10, pady=5)
        tk.Label(self.details_content, text=f"创建时间: {todo[4]}", bg="#2e2e2e", fg="white").pack(anchor="w", padx=10, pady=5)
        tk.Label(self.details_content, text=f"完成时间: {todo[6]}", bg="#2e2e2e", fg="white").pack(anchor="w", padx=10, pady=5)
        tk.Label(self.details_content, text=f"上级任务ID: {todo[7]}", bg="#2e2e2e", fg="white").pack(anchor="w", padx=10, pady=5)

        # 安全地显示tag值
        tag_value = ''
        try:
            if len(todo) > 8 and todo[8]:
                tag_value = todo[8]
        except (IndexError, TypeError):
            pass

        # 附件部分
        tk.Label(self.details_content, text="附件:", anchor="w", bg="#2e2e2e", fg="white", font=("Arial", 10, "bold")).pack(anchor="w", padx=10, pady=5)

        # 创建附件框架
        attachments_frame = tk.Frame(self.details_content, bg="#2e2e2e")
        attachments_frame.pack(fill=tk.X, padx=10, pady=5)

        # 获取任务的附件
        attachments = self.db.get_attachments(todo_id)

        if attachments:
            # 创建滚动框架
            attachment_canvas = tk.Canvas(attachments_frame, bg="#2e2e2e", highlightthickness=0)
            attachment_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

            attachment_scrollbar = ttk.Scrollbar(attachments_frame, orient=tk.VERTICAL, command=attachment_canvas.yview)
            attachment_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            attachment_canvas.configure(yscrollcommand=attachment_scrollbar.set)

            attachment_inner_frame = tk.Frame(attachment_canvas, bg="#2e2e2e")
            attachment_canvas.create_window((0, 0), window=attachment_inner_frame, anchor="nw")

            # 显示附件列表
            for i, attachment in enumerate(attachments):
                # 适应数据库结构变化，安全地获取字段
                attachment_id = attachment[0]
                todo_id = attachment[1]
                file_path = attachment[2] if len(attachment) > 2 else None
                file_name = attachment[3] if len(attachment) > 3 else "unknown"
                # file_content 字段在索引 4
                created_time = attachment[5] if len(attachment) > 5 else ""

                # 创建附件行框架
                attachment_row = tk.Frame(attachment_inner_frame, bg="#3e3e3e", padx=5, pady=5)
                attachment_row.pack(fill=tk.X, pady=2)

                # 获取文件图标（使用文件路径或文件名）
                file_icon = self.get_file_icon(file_path if file_path else file_name)

                # 显示图标
                icon_label = tk.Label(attachment_row, image=file_icon, bg="#3e3e3e")
                icon_label.image = file_icon  # 防止图片被垃圾回收
                icon_label.pack(side=tk.LEFT, padx=5)

                # 显示文件名（可点击打开）
                file_label = tk.Label(attachment_row, text=file_name, bg="#3e3e3e", fg="#add8e6", cursor="hand2")
                file_label.pack(side=tk.LEFT, fill=tk.X, expand=True, anchor="w")
                # 使用附件ID打开文件
                file_label.bind("<Button-1>", lambda e, aid=attachment_id: self.open_attachment(attachment_id=aid))

                # 删除按钮
                delete_btn = tk.Button(attachment_row, text="×", bg="#4e4e4e", fg="white",
                                      command=lambda aid=attachment_id: self.delete_attachment(aid))
                delete_btn.pack(side=tk.RIGHT)

            # 更新滚动区域
            attachment_inner_frame.update_idletasks()
            attachment_canvas.config(scrollregion=attachment_canvas.bbox("all"))

            # 限制高度
            max_height = min(150, len(attachments) * 35)  # 每个附件行约40像素高
            attachment_canvas.config(height=max_height)
        else:
            # 没有附件时显示提示
            tk.Label(attachments_frame, text="无附件", bg="#2e2e2e", fg="#888888").pack(pady=2)

        # 添加附件按钮
        add_attachment_btn = tk.Button(attachments_frame, text="添加附件", bg="#4e4e4e", fg="white",
                                     command=self.add_attachment_to_task)
        add_attachment_btn.pack(pady=2)

        # 操作按钮
        button_frame = tk.Frame(self.details_content, bg="#2e2e2e")
        button_frame.pack(pady=2, fill=tk.X)

        complete_button = tk.Button(
            button_frame, text="标记完成",
            command=lambda: [self.mark_completed(todo_id)],
            bg="#4e4e4e", fg="white"
        )
        complete_button.pack(side=tk.LEFT, padx=5)

        uncomplete_button = tk.Button(
            button_frame, text="标记未完成",
            command=lambda: [self.mark_uncompleted(todo_id)],
            bg="#4e4e4e", fg="white"
        )
        uncomplete_button.pack(side=tk.LEFT, padx=5)

        # 根据任务状态禁用或启用按钮
        if todo[3] == "已完成":
            complete_button.config(state=tk.DISABLED)
            uncomplete_button.config(state=tk.NORMAL)
        else:
            complete_button.config(state=tk.NORMAL)
            uncomplete_button.config(state=tk.DISABLED)

    def hide_details(self):
        self.details_frame.pack_forget()  # 隐藏详情框架
        self.tree_frame.config(width=self.root.winfo_screenwidth())  # Tree占100%宽度

    def get_file_icon(self, file_path):
        """获取文件类型图标"""
        # 获取文件扩展名
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        # 图标目录
        res_dir = os.path.join(os.path.dirname(__file__), "res")

        # 默认图标
        default_icon_path = os.path.join(res_dir, "file.png")

        # 常见文件类型图标映射
        icon_mapping = {
            ".txt": "text.png",
            ".doc": "word.png",
            ".docx": "word.png",
            ".xls": "excel.png",
            ".xlsx": "excel.png",
            ".ppt": "powerpoint.png",
            ".pptx": "powerpoint.png",
            ".pdf": "pdf.png",
            ".jpg": "image.png",
            ".jpeg": "image.png",
            ".png": "image.png",
            ".gif": "image.png",
            ".mp3": "audio.png",
            ".mp4": "video.png",
            ".zip": "zip.png",
            ".rar": "zip.png",
            ".7z": "zip.png",
            ".exe": "exe.png",
            ".py": "python.png",
            ".html": "html.png",
            ".css": "css.png",
            ".js": "js.png",
        }

        # 获取图标路径
        icon_file = icon_mapping.get(ext, "file.png")
        icon_path = os.path.join(res_dir, icon_file)

        # 如果图标文件不存在，使用默认图标
        if not os.path.exists(icon_path):
            icon_path = default_icon_path

        # 确保默认图标存在
        if not os.path.exists(default_icon_path):
            # 创建 res 目录
            os.makedirs(res_dir, exist_ok=True)
            # 创建一个空白图标作为默认
            img = Image.new('RGBA', (16, 16), (200, 200, 200, 255))
            img.save(default_icon_path)

        try:
            return ImageTk.PhotoImage(Image.open(icon_path).resize((16, 16), Image.Resampling.LANCZOS))
        except Exception as e:
            print(f"Error loading icon: {e}")
            # 创建一个空白图标作为备用
            img = Image.new('RGBA', (16, 16), (200, 200, 200, 255))
            return ImageTk.PhotoImage(img)

    def add_attachment_to_task(self):
        """添加附件到当前选中的任务"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showerror("错误", "请选择一个任务")
            return

        todo_id = self.tree.item(selected_item[0], "text")

        # 打开文件选择对话框
        file_path = filedialog.askopenfilename(
            title="选择附件",
            filetypes=[("All Files", "*.*")]
        )

        if not file_path:
            return  # 用户取消了选择

        # 添加附件到数据库
        self.db.add_attachment(todo_id, file_path)

        # 刷新详情面板显示附件
        self.show_details()

    def delete_attachment(self, attachment_id):
        """删除附件"""
        if messagebox.askyesno("确认", "确定要删除该附件吗？"):
            self.db.delete_attachment(attachment_id)
            # 刷新详情面板
            self.show_details()

    def open_attachment(self, file_path=None, attachment_id=None):
        """打开附件，可以通过文件路径或附件ID打开"""
        # 如果提供了附件ID，优先使用ID
        if attachment_id:
            # 从数据库中获取附件信息
            query = "SELECT file_name, file_content FROM attachments WHERE id = ?"
            attachment = self.db.connection.execute(query, (attachment_id,)).fetchone()

            if not attachment:
                messagebox.showerror("错误", f"附件不存在: ID {attachment_id}")
                return

            file_name, file_content = attachment

            # 创建临时目录
            temp_dir = os.path.join(os.environ.get('TEMP', os.path.expanduser('~')), 'todo_app_temp')
            os.makedirs(temp_dir, exist_ok=True)

            # 创建临时文件
            temp_file_path = os.path.join(temp_dir, file_name)

            try:
                with open(temp_file_path, 'wb') as f:
                    f.write(file_content)

                # 打开文件
                os.startfile(temp_file_path)  # Windows特有方法，使用默认程序打开文件
            except Exception as e:
                messagebox.showerror("错误", f"无法打开文件: {e}")

        # 如果提供了文件路径，尝试直接打开
        elif file_path and os.path.exists(file_path):
            try:
                os.startfile(file_path)  # Windows特有方法，使用默认程序打开文件
            except Exception as e:
                messagebox.showerror("错误", f"无法打开文件: {e}")
        else:
            messagebox.showerror("错误", f"文件不存在或未提供有效的附件信息")

    def on_close(self):
        self.db.close()
        self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = TodoApp(root)
    root.protocol("WM_DELETE_WINDOW", app.on_close)
    root.mainloop()
