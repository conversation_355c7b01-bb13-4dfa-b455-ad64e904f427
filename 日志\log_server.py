from flask import Flask, Response
from log_grep import grep_dir
import os

app = Flask(__name__)

@app.route('/')
def index():
    return '欢迎使用日志查询助手!'

@app.route('/html/<module>/<pattern>/<keywords>')
def grep_html(module, pattern, keywords):
    print(pattern, keywords)
    log_dir = 'D:\TMP\logs'
    logs = grep_dir(log_dir, f"*{pattern}*.log", keywords)
    resp = '<div><pre>'.join(f'{elem.replace("<", "&lt;").replace(">", "&gt;")}</pre></div>' for elem in logs)
    resp.replace(keywords, "<font color='red'>"+keywords+"</font>")
    return Response(resp, content_type='text/html; charset=utf-8')

@app.route('/text/<module>/<pattern>/<keywords>')
def grep_text(module, pattern, keywords):
    print(pattern, keywords)
    log_dir = 'D:\TMP\logs'
    logs = grep_dir(log_dir, f"*{pattern}*.log", keywords)
    resp = '\n'.join(f'{elem}' for elem in logs)
    return Response(resp, content_type='text/plain; charset=utf-8')


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)