<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 聊天</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        #chat-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            border-bottom: 1px solid #ccc;
        }
        #input-container {
            display: flex;
            padding: 10px;
        }
        #input-container input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        #input-container button {
            padding: 10px;
            margin-left: 10px;
            border: none;
            background-color: #007BFF;
            color: white;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div id="chat-container"></div>
    <div id="input-container">
        <input type="text" id="user-input" placeholder="输入你的消息..." onkeydown="if(event.key === 'Enter') sendMessage()">
        <button onclick="sendMessage()">发送</button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
        const apiUrl = 'http://localhost:3001/api/v1/workspace/049da0e1-51e3-46d9-b4ba-ad86c8714b70/thread/479e7ff7-b8e7-49fa-b4aa-820d46ac3c17/chat'; // 替换为你的anythingllm API端点
        const apiKey = 'YV56R7Z-JE74AN5-MFJN5DY-SQ413BY'; // 替换为你的API密钥

        function sendMessage() {
            const userInput = document.getElementById('user-input').value;
            if (!userInput) return;

            appendMessage('用户', userInput);
            document.getElementById('user-input').value = '';

            const xhr = new XMLHttpRequest();
            xhr.open('POST', apiUrl, true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('Authorization', `Bearer ${apiKey}`);
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        typeMessage('AI', response.textResponse);
                    } else {
                        appendMessage('AI', '抱歉，我忙不过来了，稍后再约。');
                    }
                }
            };
            xhr.send(JSON.stringify({ "message": userInput, "mode": "chat" }));
        }

        function appendMessage(sender, message) {
            const chatContainer = document.getElementById('chat-container');
            const messageElement = document.createElement('div');
            messageElement.innerHTML = `<strong>${sender}:</strong> ${marked.parse(message)}`;
            chatContainer.appendChild(messageElement);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function typeMessage(sender, message) {
            const chatContainer = document.getElementById('chat-container');
            const messageElement = document.createElement('div');
            messageElement.innerHTML = `<strong>${sender}:</strong> `;
            chatContainer.appendChild(messageElement);
            chatContainer.scrollTop = chatContainer.scrollHeight;

            let index = 0;
            function type() {
                if (index < message.length) {
                    messageElement.innerHTML += message.charAt(index);
                    index++;
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                    setTimeout(type, 50); // 调整速度
                } else {
                    messageElement.innerHTML = `<strong>${sender}:</strong> ${marked.parse(message)}`;
                }
            }
            type();
        }
    </script>
</body>
</html>
