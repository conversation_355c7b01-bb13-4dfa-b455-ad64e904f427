create or replace and compile java source named geo as
import java.lang.Math;

public class Geo {
   public static double  getDistance(Double latitude1, Double longitude1, Double latitude2, Double longitude2) {
        final double EARTH_RADIUS = 6378137;
        // 纬度
        double lat1 = Math.toRadians(latitude1);
        double lat2 = Math.toRadians(latitude2);
        // 经度
        double lng1 = Math.toRadians(longitude1);
        double lng2 = Math.toRadians(longitude2);
        // 纬度之差
        double a = lat1 - lat2;
        // 经度之差
        double b = lng1 - lng2;
        // 计算两点距离的公式
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(b / 2), 2)));
        // 弧长乘地球半径, 返回单位: 米
        s = s * EARTH_RADIUS;
        return s;
   }
}

--2、create sm3 function
CREATE OR REPLACE FUNCTION geo_distance (latitude1 IN number, longitude1 IN number, 
                                         latitude2 IN number, longitude2 IN number)
   RETURN number
IS
   LANGUAGE JAVA
   NAME 'Geo.getDistance(java.lang.Double,java.lang.Double,java.lang.Double,java.lang.Double) return Double';

   
select geo_distance(23.1, 123.1,23.2,123.2) from dual;