@startuml 换汇
title 寻汇&换汇
actor 商户 as M
box 汇通系统 #LightBlue
    participant 门户服务 as H
    participant 账户服务 as A
end box
participant "Skyee/全球付/CC" as CC #Grey

M -> H: 登录
H --> M: 进入汇通全球
==查询账户余额==
M -> H: 查询账户余额
H -> A: 查询账户余额，CNH、CNY、USD、GBP、CND等币种
A --> H: 账户余额
H --> M: 账户余额
==寻汇==
M -> H: 查询汇率
group 锁汇30秒
    H -> CC: 查询汇率
    note left of CC
        CC，汇率详细，接口：/v2/rates/detailed
        全球付，汇率查询，接口：fxRate/query
        Skyee，创建询价单，接口：ExchangeInquiry
        本地换汇：不到上游，汇率通过本地库查询获取
    end note
    CC --> H: 实时汇率，完成锁汇
end group
H --> M: 客户汇率=实时汇率*(1-0.5%)
note over of H
    固定买入，手续费外币，客户汇率=实时汇率*(1+0.5%)
    固定卖出，手续费RMB，客户汇率=实时汇率*(1-0.5%)，√
end note

==换汇==
M -> H: 申请换汇
H --> H: 创建换汇单，表：VA_Exchange_Order
H -> A: 申请换汇，接口：exchangeApply
A --> H: 返回结果
H -> CC: 发起换汇
note left of CC
    CC，创建换汇单，接口：/v2/conversions/create
    全球付，换汇确认，接口：fxRate/confirm
    Skyee，确认换汇，接口：ExchangeOrder
end note
CC --> H: 换汇结果
H --> H: 更新换汇单状态——处理中
H --> M: 换汇单已受理

CC ->> H: 通知换汇结果
note left of CC
    CC，两状态： ①资金已到位（Funds Arrived Notification），
                ②交易已完成（Trade Settled Notification）
end note
H -> H: 更新换汇单状态

alt 换汇成功
    H -> A: CNH账户加额，接口换汇完成，接口：exchangeConfirm
    A --> H: 返回加额结果
    H -> CC: 转存手续费到主账户
    note left of CC: 转存手续费，接口：Transfer
    CC --> H: 转存手续费成功
    H --> H: 更新换汇单状态——成功
    H ->> M: 异步通知，换汇成功
else 换汇失败
    H -> A: 原币种账户回滚
    A --> H: 返回回滚结果
    H --> H: 更新换汇单状态——失败
    H ->> M: 异步通知，换汇失败
end
@enduml


