# 银行电子账户（慧E通）环境

| 模块                  | 服务&版本                                                  | 说明                         |
| --------------------- | ---------------------------------------------------------- | ---------------------------- |
| bic-admin             | SpringBoot服务<br />tomcat 9.0.63<br />openjdk 11.0.23.0.9 | 运营门户<br />定时任务Worker |
| bic-epay-api          | 同上                                                       |                              |
| bic-monitor-admin.jar | 同上                                                       |                              |
| bic-xxl-job-admin.jar | 同上                                                       |                              |

电子银行账户系统API二级域名：

**生产环境：api.**gdyidui**.cn**

## 测试环境

sanbox-api.**gdyidui**.cn，指向：172.16.6.181:7080 **

![1740550567035](./image/info/1740550567035.png)

### 商户门户

URL：[https://sandbox-merchant.gdyidui.cn](https://sandbox-merchant.gdyidui.cn)

（1）商户账号
mer-100291 / 000000
（2）商户复核员账号
check-100291 / 000000
（3）代理账号
agt-1 / 000000

### 运营门户

URL：https://sandbox-admin.gdyidui.cn
账号：admin
登录密码：admin123
操作密码：000000

### 数据库

url: ***********************************************************************************************************************************************************************************************************************************************************
username: huiet
password: huiet#2024

## 生产环境

![1740551217367](image/info/1740551217367.png)

# 业务功能

## 配置参数

EPSP收款成功后通知分账系统：https://api.gdyidui.cn/callback/efps/payment

## 功能脑图

```mermaid
mindmap
  root((银行电子账户))
    id1{{上游}}
      1.1、新网银行
      1.2、厦门国际
    id2{{2、业务}}
      2.1、商户入驻
        **代理商**
        **商户注册**<br>商户基本信息
        **商户进件**<br>根据渠道要求上送商户信息
        **渠道商户配置**<br>商户进件的结果以及配置
        **商户支付配置**<br>商户交易限额/时段等配置
        **复核人员管理**<br>复核人员手机号码、密码等
        **平台白名单**<br>决定商户资金归集额度
        **商户进件审核**<br>审核进件资料（？？）
      2.2、商户接入
        **商户进件**<br>根据渠道要求上送商户信息
        **渠道商户配置**<br>商户进件的结果以及配置
        **商户支付配置**<br>商户交易限额/时段等配置
  
```

# 代码

## 代码库（git库）

http://172.16.3.10:180/e-bankaccount/epay-epl
http://172.16.3.10:180/e-bankaccount/epay-m
http://172.16.3.10:180/e-bankaccount/epay-ui-epl

## 代码&模块说明

| 模块                              | 说明                                                               |
| --------------------------------- | ------------------------------------------------------------------ |
| 1、bic-admin                      | 1、运营用户管理<br />2、渠道管理<br />3、配置管理<br />4、商户管理 |
| 2、bic-basic                      |                                                                    |
| 2.1、bic-basic.bic-gen            | 代码生成                                                           |
| 2.2、bic-basic.bic-oss            | 对象存储（文件管理）                                               |
| 2.3、bic-basic.bic-pay            | 渠道管理                                                           |
| 2.4、bic-basic.bic-sms            | 4、短信                                                            |
| 2.5、bic-basic.bic-sys            | 5、用户授权                                                        |
| 3、bic-business                   | 业务父模块，实现各种具体服务的逻辑                                 |
| 3.1、bic-business.bic-job         | 定时任务                                                           |
| 3.2、bic-business.bic-service     | 定义了几乎所有的业务对象，主要是strategy延伸的各种策略类           |
| 3.3、bic-business.bic-service-api |                                                                    |
| 3.4、bic-business.bic-service-out |                                                                    |
| 4、bic-common                     | 工具类、枚举/常量定义、                                            |
| 5、bic-extend                     |                                                                    |
| 5.1、bic-extend.bic-epay-api      | 与EPSP交互接口实现                                                 |
| 5.2、bic-extend.bic-epay-demo     |                                                                    |
| 5.3、bic-extend.bic-monitor-admin |                                                                    |
| 5.4、bic-extend.bic-xxl-job-admin | JOB管理                                                            |
| 6、bic-frame                      |                                                                    |

## 模块部署说明

```mermaid
sequenceDiagram
    title 安全交互

    actor mer as 商户
    participant pt as 平台系统
    participant ca as CA

    pt->>pt: 部署安全交互证书
    mer->>pt: 获取平台公钥
    pt-->>mer: 返回平台公钥
    mer->>ca: 申请证书
    ca-->>mer: 返回证书
    mer->>pt: 上传证书公钥
    pt->>pt: 部署安全交互证书
```

# 新网银行对接

## 商户接口清单

### 进件类接口

一、商户进件

1. 商户新增
2. 商户修改
3. 商户签约
4. 商户进件（携带详细信息进件到某渠道）
5. 商户信息查询（商户的审批状态+已经进件的渠道和状态）
6. 商户进件异步通知
7. 查询业务费率

二、商户绑卡（结算卡/代付卡）

1. 新增绑卡
2. 修改绑卡
3. 查询卡片状态

### 交易类接口

一、资金&账户

1. 资金归集
2. 账户余额查询
3. 资金到账通知

二、提现代付

1. 提现
2. 代付
3. 交易查询
4. 异步通知
5. 对账单下载

## 进件

两种方案：

1、系统现在EPSP完成进件，将EPSP商户号作为易票联的银行卡号，分账系统在新网进件后，同时绑定商户，提现时银行卡资金对接，从新网银行账户到易票联银行账户（银行名称为易票联，账户号为商户号）【李皓方案】

2、分账系统在EPSP和新网银行分别进件后，新网银行卡号到EPSP报备，资金从新网银行转账到EPSP时，作为转账的识别

![1726198095438](image/info/1726198095438.png)

## 易分账商户开户状态图（新网银行、EPSP）

```mermaid
---
title: 商户开户状态图
---
stateDiagram-v2
    [*] --> 待提交: 商户准备资料
    待提交 --> 待渠道审核: 系统同时向新网银行和EPSP发起开户请求
    待渠道审核 --> 待联系人验证: 新网银行审核通过
    待联系人验证 --> 待法人验证: 联系人验证通过
    待法人验证 --> 待提交EP: 法人验证通过
    待提交EP --> 待EP审核: 提交EP
    待EP审核 --> 开户成功: EP审核通过
    待EP审核 --> 待提交EP: EP审核驳回
    开户成功 --> [*]: 成功结束

    待渠道审核 --> 开户失败: 新网审核失败
    待联系人验证 --> 开户失败: 联系人验证失败
    待法人验证 --> 开户失败: 法人验证失败
    待EP审核 --> 开户失败: EP审核拒绝

    开户失败 --> [*]: 失败结束
```

## 商户开户流程图

```mermaid
---
title: 商户开户流程图
---
sequenceDiagram
    participant m as 商户
    participant h as 易分账系统
    participant x as 新网银行
    participant e as EPSP

    rect rgb(55, 50, 75)
        m->>h: 商户开户申请
        note right of h: 状态：待渠道审核<br/>3.1.1文件上传<br/>3.1.2企业查询受益人信息<br/>3.1.3商户进件申请<br/>3.1.4商户进件修改
        h->>x: 新网银行开户
        note right of x: 3.6文件视频上传<br/>3.7文件上传<br/>7.4企业最终受益人查询<br/>7.13个体工商户认定<br/>7.14企业开户申请<br/>7.7开户流水查询
        x->>h: 新网银行开户结果
    end
    alt 开户成功
        rect rgb(54, 25, 25)
            note right of h: 状态：待联系人验证
            m->>h: 发送短信验证码（联系人）
            note right of h: 3.1.6发送短信验证码
            h->>x: 发送短信验证码（联系人）
            note right of x: 7.15发送短信验证码
            x-->>h: 处理结果
            h-->>m: 处理结果
        end
        rect rgb(54, 10, 10)
            note right of h: 状态：待法人验证
            m->>h: 发送短信验证码（法人）
            note right of h: 3.1.6发送短信验证码
            h->>x: 发送短信验证码（法人）
            note right of x: 7.15发送短信验证码
            x-->>h: 处理结果
            h-->>m: 处理结果
        end
        rect rgb(65, 17, 54)
            note right of h: 状态：“待提交EP”<br/>非修改模式自动提交<br/>状态：“待EP审核”
            h->>e: EPSP开户
            note right of e:3.1.1商户注册申请<br/>3.1.3商户信息查询<br/>3.1.4商户审核结果通知
            e-->>h: EPSP开户结果
            e->>e: 发送签约短信
        end
        rect rgb(39, 18, 63)
            note right of h: 状态：待签约
            m->>h: 再次发送签约短信
            note right of h: 接口：3.1.8申请签约
            h->>e: 再次发送签约短信
            note right of e:3.1.7发送签约短信
            e->>e: 发送签约短信
            e-->>h: 处理结果
            h-->>m: 处理结果
        end
        rect rgb(18, 44, 38)
            h->>e: 商户状态查询
            h->>h: 状态：开户成功
        end
    else 开户失败
        h->>m: 开户失败
    end
    h-->>m: 异步通知开户结果
```

# 分账系统若干问题汇总

## 1、白名单问题，EPSP和分账系统均找不到这个白名单

【描述】
客户添加白名单，一直审核中状态
【过程】
商户在时间T添加白名单，状态审核中
风控在T+1时间审核不通过
商户在T+2时间报告问题
也不知道审核流程，就是白名单审核是在哪个系统审核，谁负责审核，好不容易找到风控，风控说没有需要审核的，因此问题变成了EPSP和分账系统均找不到这个白名单
家伟在T+3时刻手动同步状态，问题解决
【项目优化】
慧E通开发了定时任务，没有部署，目前已经部署；

## 2、资金归集错误A

【描述】
商户确认新网银行资金足够，但是归集时提示无可以归集的金额
【过程】
上游返回日志确认，商户有资金
定位代码发现：商户可归集金额=新网返回可用余额-未加白金额，减去后，可归集金额=0
没有人知道如何加白名单
家伟咨询技术开发商，添加白名单并刷新交易问题解决

## 3、资金归集错误B

【描述】
上游提示“账户状态异常”，导致归集失败
【过程】
中间很长，不能确定是哪块的账户，最后确认是EPSP这边没做充值卡报备，充值卡报备完成后问题解决

## 4、资金归集成功，账户余额仍为0

【描述】
商户反馈，归集成功，但是账户余额还是0
【过程】
a)归集接口调用成功
b)EP加额成功
c)确认流程，分账系统账户余额来自EP充值异步通知
d)梳理和测试分账系统充值异步通知，并配置到EPSP中
e)手动出发昨日交易异步通知
【项目优化】
EPSP中部署商户资金充值异步通知地址

## 5、商户可以在商户门户上看到其他商户的白名单信息

【描述】
商户反馈可以看到其他商户的白名单数据；
【分析】
确认测试环境不可重现，对比生产环境和测试环境的角色的数据权限，两边不同，生产的数据权限为：全部门，测试环境为：仅本人数据；
【方案】
修改生产环境角色数据权限为：仅本人数据，问题暂时解决；
【遗留】
可能影响商户门户其他菜单的数据查看，待继续验证；

## 6、提现单状态问题

【描述】
提现单，EPSP状态失败，分账系统通过接口查询状态还是处理中
【分析】
1、EP异步通知正常；
2、HUIET确认收到EP异步通知；
3、门户中状态失败（正确），商户接口查询状态为处理中（不正确），一种可能性：错误的订单数据被缓存或者商户查询时间早于异步通知的处理时间；
4、缓存所致，@Cacheable注解
【方案】
注释掉了该方法上的缓存
【遗留】
其他地方使用的缓存可能存在同样的问题；
