-------------------创建EPSP视图--------------------
--创建经营报表视图
create view v_chk_business_jingying_report  as 
select 
      a.ID as id,
      a.TASK_DATE as TASK_DATE  ,
      to_date(a.TASK_DATE_SHOW ,'yyyy-mm-dd') as TASK_DATE_SHOW ,
      a.BUSINESS_TYPE as BUSINESS_TYPE,
      a.CUSTOMER_CODE as CUSTOMER_CODE,
      a.CUSTOMER_NAME as CUSTOMER_NAME,
      a.BUDGET_GROUP_NAME as BUDGET_GROUP_NAME,
      a.AGENT_CUSTOMER_CODE as AGENT_CUSTOMER_CODE,
      a.AGENT_CUSTOMER_NAME as AGENT_CUSTOMER_NAME,
      a.PLATFORM_CUSTOMER_CODE as PLATFORM_CUSTOMER_CODE,
      a.PLATFORM_CUSTOMER_NAME as PLATFORM_CUSTOMER_NAME,
      a.BUSINESS as BUSINESS,
      a.BUSINESS_MAN as BUSINESS_MAN,
      a.COMPANY_NAME as COMPANY_NAME,
      a.PAY_METHOD as PAY_METHOD,
      a.CHANNEL_INST_CODE as CHANNEL_INST_CODE,
      a.CHANNEL_INST_NAME as CHANNEL_INST_NAME,
      a.BANK_NAME as BANK_NAME,
      a.EPL_TERM_NO as EPL_TERM_NO,
      a.ITEM_CODE as ITEM_CODE,
      TO_NUMBER(TRAN_CNT) as TRAN_CNT ,
      TO_NUMBER(TRAN_AMOUNT)/100 as TRAN_AMOUNT ,
      TO_NUMBER(TRAN_MCHT_PROCEDURE_FEE)/100 as TRAN_MCHT_PROCEDURE_FEE ,
      TO_NUMBER(TRAN_MCHT_PROCEDURE_FEE2)/100 as TRAN_MCHT_PROCEDURE_FEE2 ,
      TO_NUMBER(TRAN_LOCAL_PROCEDURE_FEE)/100 as TRAN_LOCAL_PROCEDURE_FEE,
      TO_NUMBER(TRAN_LOCAL_PROCEDURE_FEE2)/100 as TRAN_LOCAL_PROCEDURE_FEE2 ,
      TO_NUMBER(TRAN_CHANNEL_PROCEDURE_FEE)/100 as TRAN_CHANNEL_PROCEDURE_FEE ,
      TO_NUMBER(TRAN_CHANNEL_PROCEDURE_FEE2)/100 as TRAN_CHANNEL_PROCEDURE_FEE2,
      TO_NUMBER(ARRIVE_AMOUNT)/100 as ARRIVE_AMOUNT ,
      a.CREATE_TIME as CREATE_TIME,
      a.STATIC_PERIOD as STATIC_PERIOD,
      TO_NUMBER(FEE)/100 as FEE ,
      TO_NUMBER(PURE_FEE)/100 as PURE_FEE ,
      TO_NUMBER(INCOME)/100 as INCOME  ,
      TO_NUMBER(PURE_INCOME)/100 as PURE_INCOME,
      TO_NUMBER(COST)/100 as COST ,
      TO_NUMBER(PURE_COST)/100 as PURE_COST,
      TO_NUMBER(PROFIT)/100 as PROFIT ,
      TO_NUMBER(PURE_PROFIT)/100 as PURE_PROFIT,
      a.SEQ_NO as SEQ_NO,
      a.REMARK as REMARK,
      a.RECORD_SOURCE as RECORD_SOURCE,
      a.BATCH_NO as BATCH_NO,
      b.PROVINCE_CODE as PROVINCE_CODE,
      b.CITY_CODE as CITY_CODE,
      b.DISTRICT_CODE as DISTRICT_CODE
from chk_business_jingying_report  a
      left join cust_customer b on a.CUSTOMER_CODE=b.CUSTOMER_NO 
where a.task_date>='20240101'
order by a.task_date desc;

--创建商户信息view
create view v_customer_all as 
select c.CLIENT_NO as client_no,ccd.name as name, ccd.CUSTOMER_NO AS customer_no,
      to_date(TO_CHAR(ccd.CREATE_TIME ,'yyyy-mm-dd'),'yyyy-mm-dd') as create_time,
      (CASE ccd.category 
            WHEN 0 THEN '商户'
            WHEN 2 THEN '平台商户'
            WHEN 3 THEN '服务商'
            WHEN 4 THEN '个人'
            WHEN 5 THEN '结算商户' END) as category,
      (CASE ccd."TYPE"
            WHEN 10 THEN '个体工商户'
            WHEN 20 THEN '企业'
            WHEN 30 THEN '境外商户'
            WHEN 50 THEN '小微'
            WHEN 60 THEN '个人客户'
            WHEN 70 THEN '政府/事业单位'
            ELSE '其他组织' end) AS type,
      ccd.plat_customer_no as plat_customer_no,
      (select name from cust_customer c1 where c1.customer_no=ccd.plat_customer_no) as plat_customer_name,
      ccd.service_customer_no as service_customer_no,
      (select name from cust_customer c1 where c1.customer_no=ccd.service_customer_no) AS service_customer_name,
      (SELECT count(1) FROM CUST_BUSINESS cb WHERE cb.customer_id = ccd.customer_id AND state =1 ) as cust_nusiness_count,
      ccd.terminal_code as terminal_code,
      (select pc.name from pas_company pc where  pc.company_id=ccd.company_id) as company_name,
      u.REAL_NAME as real_name,
      (CASE ccd.source_channel
            WHEN '1' THEN 'EPSP平台录入'
            WHEN '2' THEN 'EPSP接口'
            WHEN '3' THEN '云闪付开放平台'
            WHEN '4' THEN '旧系统'
            WHEN '5' THEN 'H5自助平台'
            WHEN '6' THEN 'PC自助平台'
            WHEN '7' THEN '终端自助平台'
            WHEN '8' THEN '代理商门户'
            WHEN '9' THEN '代理商APP'
            WHEN '10' THEN '业务员进件'END ) as source_channel,
      (select name from CUST_DICT_BUSINESS_TYPE bt where bt.code=ccd.inner_business_type) as business_type,
      (CASE WHEN ccd.SIGN_STATUS = 1 THEN '已签约' ELSE '未签约'END)AS SIGN_STATUS,
      (CASE c.STATUS
            WHEN 1 THEN '正常'
            WHEN 2 THEN '冻结'
            WHEN 3 THEN '注销'
            WHEN 4 THEN '止付'
            WHEN 5 THEN '禁止入金' END ) AS STATUS,
      (CASE ccd.AUDIT_STATUS
            WHEN '00' THEN '待初审'
            WHEN '01' THEN '初审未通过'
            WHEN '02' THEN '待复审'
            WHEN '03' THEN '复审未通过'
            WHEN '04' THEN '审核成功'
            WHEN '05' THEN '草稿'
            WHEN '06' THEN '待预审'
            WHEN '07' THEN '预审未通过' END)  as AUDIT_STATUS,
      CASE ra.TEMPORARY_STATUS WHEN '2' THEN '已限制' ELSE '正常' end as TEMPORARY_STATUS,
      CASE c.rc_status WHEN '0' THEN '正常' WHEN '1' THEN '冻结' end as rc_status,
      (SELECT param_value FROM CUST_STATIC_PARAM csp
      WHERE csp.PARAM_TYPE='NEW_BUDGET_GROUP' AND csp.PARAM_NAME=ccd.budget_group) AS budget_group,
      nvl((select (CASE WHEN ei.value='1' then '是' end) as aa from CUST_CUSTOMER_EXTEND_INFO ei WHERE ei.CUSTOMER_ID = ccd.customer_id AND ei.TYPE = 'recyclePaperAgreement'),'否') as recyclePaperAgreement,
      (SELECT param_value FROM CUST_STATIC_PARAM csp WHERE csp.PARAM_TYPE='BUSINESS_ROLE' AND csp.PARAM_NAME=ccd.BUSINESS_ROLE) AS BUSINESS_ROLE,
      CASE sr.STOP_CASH_IN WHEN '1' THEN '已限制' ELSE '正常' end as STOP_CASH_IN,
      to_date(TO_CHAR(c.register_time,'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') as register_time,
      to_date(TO_CHAR(c.update_time ,'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') as update_time,
      to_date(TO_CHAR((CASE WHEN c.rc_status =1 THEN c.frozen_time ELSE NULL END),'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') as frozen_time,
      (CASE WHEN c.rc_status =1 THEN ra.RC_STATUS_REASON ELSE NULL END) as RC_STATUS_REASON,
      to_date(TO_CHAR((CASE WHEN c.STATUS = 2 THEN r.STATUS_TIME ELSE NULL END),'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') as account_frozen_time,
      (CASE WHEN c.STATUS = 2 THEN ra.ACCOUNT_STATUS_REASON ELSE NULL END) as account_frozen_REASON,
      to_date(TO_CHAR((CASE WHEN c.STATUS = 3 THEN r.STATUS_TIME ELSE NULL END),'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') as account_cancel_time,
      (CASE cr.REASON
            WHEN '01' THEN '商户不使用'
            WHEN '02' THEN '商户无交易'
            WHEN '03' THEN '疑似风险商户'
            WHEN '04' THEN '商户资质注销或过期'
            WHEN '05' THEN '自主注销'
            WHEN '99' THEN '其他' END)  as account_cancel_REASON,
      to_date(TO_CHAR((CASE WHEN c.STATUS = 4 THEN r.STATUS_TIME ELSE NULL END),'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') as STOP_PAY_TIME,
      (CASE WHEN c.STATUS = 4 THEN ra.ACCOUNT_STATUS_REASON ELSE NULL END) as STOP_PAY_REASON,
      to_date(TO_CHAR((CASE WHEN c.STATUS = 5 THEN r.STATUS_TIME ELSE NULL END),'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') as STOP_CASH_IN_TIME,
      (CASE WHEN c.STATUS = 5 THEN ra.ACCOUNT_STATUS_REASON ELSE NULL END) as STOP_CASH_IN_REASON,
      (select ei.VALUE from CUST_CUSTOMER_EXTEND_INFO ei where ei.TYPE='remarks' and ei.CUSTOMER_ID=ccd.CUSTOMER_Id) as remarks
from CUST_CUSTOMER_DRAFT ccd
      LEFT JOIN cust_customer c ON c.CUSTOMER_ID =ccd.CUSTOMER_ID
      LEFT JOIN pas_user u ON ccd.BUSINESS_MAN_ID =u.USER_ID
      LEFT JOIN CUST_CUSTOMER_TIME_RECORD r on r.CUSTOMER_ID=ccd.CUSTOMER_ID
      LEFT JOIN CUST_CUSTOMER_SETTING_RECORD sr on sr.CUSTOMER_ID=ccd.CUSTOMER_ID
      LEFT JOIN rc_archive ra ON ccd.customer_no = ra.archive_code and ra.archive_type='005'
      LEFT JOIN CUST_CUSTOMER_CANCEL_RECORD cr on cr.CUSTOMER_NO=ccd.CUSTOMER_NO
order by ccd.CREATE_TIME desc;

--授权给info
grant select on v_chk_business_jingying_report to info;
grant select on v_customer_all to info;

----------------------财务数据表--------------------
--创建财务报表 --
create table cost_statement(
      id number not null primary key,
      department VARCHAR2(50) ,
      produce_date TIMESTAMP,
      course_code VARCHAR2(100) ,
      course_name  VARCHAR2(200),
      cost_type   VARCHAR2(50), 
      amount   NUMBER, 
      create_time  TIMESTAMP(6) default sysdate,          
      batch_no VARCHAR2(100)
);

--备注
COMMENT ON TABLE cost_statement IS '财务报表';
COMMENT ON COLUMN cost_statement.ID IS '主键（自动生成）';
COMMENT ON COLUMN cost_statement.department IS '费用归属部门';
COMMENT ON COLUMN cost_statement.produce_date IS '制单日期';
COMMENT ON COLUMN cost_statement.course_code IS '科目代码';
COMMENT ON COLUMN cost_statement.course_name IS '科目名称';
COMMENT ON COLUMN cost_statement.cost_type IS '分类';
COMMENT ON COLUMN cost_statement.amount IS '金额';
COMMENT ON COLUMN cost_statement.create_time IS '创建时间';
COMMENT ON COLUMN cost_statement.batch_no IS '批次号';

-- 创建序列  Student_stuId_Seq --
create sequence cost_statement_Id_seq
      increment by 1
      start with 1
      minvalue 1
      maxvalue ***********;

---创建一个触发器
create or replace trigger cost_statement_trigger
before insert on cost_statement
for each row
when(new.id is null)
begin
select cost_statement_Id_seq.nextval into :new.id from dual;
end;

--创建部门人数表 --
create table depart_headcount(
      id number not null primary key,
      department VARCHAR2(50),
      count_date TIMESTAMP,
      count_depart number,
      create_time  TIMESTAMP(6) default sysdate,          
      batch_no VARCHAR2(100)
);
select * from depart_headcount;
--备注
COMMENT ON TABLE depart_headcount IS '部门人数表';
COMMENT ON COLUMN depart_headcount.ID IS '主键（自动生成）';
COMMENT ON COLUMN depart_headcount.department IS '归属部门';
COMMENT ON COLUMN depart_headcount.count_date IS '日期';
COMMENT ON COLUMN depart_headcount.count_depart IS '人数';
COMMENT ON COLUMN depart_headcount.create_time IS '创建时间';
COMMENT ON COLUMN depart_headcount.batch_no IS '批次号';

-- 创建序列  Student_stuId_Seq --
create sequence depart_headcount_Id_seq
      increment by 1
      start with 1
      minvalue 1
      maxvalue ***********;

---创建一个触发器
create or replace trigger depart_headcount_trigger
before insert on depart_headcount
for each row
when(new.id is null)
begin
select depart_headcount_Id_seq.nextval into :new.id from dual;
end;

---------------------Metabase数据库操作--------------------
--创建经营表和客户表，并同步所有数据
create table chk_business_jingying_report as select * from epsp.v_chk_business_jingying_report@ycinfo;  --increcement
create table cust_customer as select * from epsp.v_customer_all@ycinfo;  --full flush

--创建利润统计视图
create or replace view v_profit as
select 
      COALESCE(aa.company_name,CCALL.department) as company_name,
      to_date(to_char(COALESCE(aa.stat_date,CCALL.produce_date),'yyyy-mm') ,'yyyy-mm') as stat_date,
      COALESCE(aa.PURE_PROFIT_sum2,0) as PURE_PROFIT_sum, 
      nvl(aa.PURE_PROFIT_sum2,0) - nvl(CCALL.cost_statement_sum,0) as operating_profit,
      nvl(aa.PURE_PROFIT_sum2,0) 
                  - nvl(CCALL.cost_statement_sum,0) 
                  + nvl((select cc.amount from cost_statement cc where aa.company_name=cc.department and to_char(aa.stat_date, 'yyyy-mm')=to_char(CC.produce_date, 'yyyy-mm') and CC.cost_type='营业外收入' and rownum=1),0) 
                  - nvl((select CC.Amount from cost_statement cc where aa.company_name=CC.department and to_char(aa.stat_date, 'yyyy-mm')=to_char(CC.produce_date, 'yyyy-mm') and CC.cost_type='营业外支出' and rownum=1),0) 
            as total_profit,
      nvl(aa.PURE_PROFIT_sum2,0) 
                  - nvl(CCALL.cost_statement_sum,0) 
                  + nvl((select cc.amount from cost_statement cc where aa.company_name=cc.department and to_char(aa.stat_date, 'yyyy-mm')=to_char(CC.produce_date, 'yyyy-mm') and CC.cost_type='营业外收入' and rownum=1),0) 
                  - nvl((select CC.Amount from cost_statement cc where aa.company_name=CC.department and to_char(aa.stat_date, 'yyyy-mm')=to_char(CC.produce_date, 'yyyy-mm') and CC.cost_type='营业外支出' and rownum=1),0) 
                  - nvl((select CC.Amount from cost_statement cc where aa.company_name=CC.department and to_char(aa.stat_date, 'yyyy-mm')=to_char(CC.produce_date, 'yyyy-mm') and CC.cost_type='所得税' and rownum=1),0) 
            as retained_profits
from (
      select t.COMPANY_NAME as company_name,to_date(to_char(t.TASK_DATE, 'yyyy-mm'),'yyyy-mm') as stat_date,
      sum(t.PURE_PROFIT)as PURE_PROFIT_sum2
      from chk_business_jingying_report_view t
      where t.TASK_DATE >= to_date('2020-01', 'yyyy-mm')
      group by to_date(to_char(t.TASK_DATE, 'yyyy-mm'),'yyyy-mm') ,t.COMPANY_NAME
) aa full join (
      select c.department as department, produce_date as produce_date,
            sum(c.amount)as cost_statement_sum
      from cost_statement c
      where
            c.produce_date >= to_date('2020-01', 'yyyy-mm')and c.cost_type not in('营业外收入','营业外支出','所得税')
      group by c.produce_date ,c.department
) CCALL   on aa.company_name=CCALL.department and to_char(aa.stat_date, 'yyyy-mm')=to_char(CCALL.produce_date, 'yyyy-mm')
order by aa.stat_date desc;

--创建利润表并同步所有数据
create table t_profit as select * from v_profit; --full flush

--创建Metabase使用的视图
create view chk_business_jingying_report_view as select * from chk_business_jingying_report;
create view customer_all as select * from cust_customer;
create or replace view profit_view as select * from t_profit;

-------------------------自动增量更新JOB--------------------------------
--同步指定日期的经营数据
create or replace procedure p_sync_business_jingying(v_task_date in varchar2)
as
begin
      --sync business_jingying_report data
      delete from chk_business_jingying_report where task_date=to_date(v_task_date,'yyyyMMdd')+1/2;

      insert into chk_business_jingying_report(
            ID,TASK_DATE,TASK_DATE_SHOW,BUSINESS_TYPE,CUSTOMER_CODE,CUSTOMER_NAME,BUDGET_GROUP_NAME,
            AGENT_CUSTOMER_CODE,AGENT_CUSTOMER_NAME,PLATFORM_CUSTOMER_CODE,PLATFORM_CUSTOMER_NAME,
            BUSINESS,BUSINESS_MAN,COMPANY_NAME,PAY_METHOD,CHANNEL_INST_CODE,CHANNEL_INST_NAME,BANK_NAME,EPL_TERM_NO,ITEM_CODE,
            TRAN_CNT,TRAN_AMOUNT,TRAN_MCHT_PROCEDURE_FEE,TRAN_MCHT_PROCEDURE_FEE2,TRAN_LOCAL_PROCEDURE_FEE,TRAN_LOCAL_PROCEDURE_FEE2,TRAN_CHANNEL_PROCEDURE_FEE,TRAN_CHANNEL_PROCEDURE_FEE2,ARRIVE_AMOUNT,
            CREATE_TIME,STATIC_PERIOD,FEE,PURE_FEE,INCOME,PURE_INCOME,COST,PURE_COST,PROFIT,PURE_PROFIT,SEQ_NO,REMARK,RECORD_SOURCE,BATCH_NO,
            PROVINCE_CODE,CITY_CODE,DISTRICT_CODE)
      select ID,to_date(TASK_DATE,'yyyyMMdd')+1/2 as TASK_DATE,TASK_DATE_SHOW+1/2,BUSINESS_TYPE,CUSTOMER_CODE,CUSTOMER_NAME,BUDGET_GROUP_NAME,
            AGENT_CUSTOMER_CODE,AGENT_CUSTOMER_NAME,PLATFORM_CUSTOMER_CODE,PLATFORM_CUSTOMER_NAME,
            BUSINESS,BUSINESS_MAN,COMPANY_NAME,PAY_METHOD,CHANNEL_INST_CODE,CHANNEL_INST_NAME,BANK_NAME,EPL_TERM_NO,ITEM_CODE,
            TRAN_CNT,TRAN_AMOUNT,TRAN_MCHT_PROCEDURE_FEE,TRAN_MCHT_PROCEDURE_FEE2,TRAN_LOCAL_PROCEDURE_FEE,TRAN_LOCAL_PROCEDURE_FEE2,TRAN_CHANNEL_PROCEDURE_FEE,TRAN_CHANNEL_PROCEDURE_FEE2,ARRIVE_AMOUNT,
            CREATE_TIME,STATIC_PERIOD,FEE,PURE_FEE,INCOME,PURE_INCOME,COST,PURE_COST,PROFIT,PURE_PROFIT,SEQ_NO,REMARK,RECORD_SOURCE,BATCH_NO,
            PROVINCE_CODE,CITY_CODE,DISTRICT_CODE
      from epsp.v_chk_business_jingying_report@ycinfo where task_date=v_task_date;
      --translate: 上海分公司 -> 华东运营中心, 大客户部 -> 总部
      update chk_business_jingying_report set company_name = '华东运营中心' where task_date=to_date(v_task_date,'yyyyMMdd')+1/2 and company_name = '上海分公司';
      update chk_business_jingying_report set company_name = '总部' where task_date=to_date(v_task_date,'yyyyMMdd')+1/2 and company_name = '大客户部';
      commit;
end;
/
--同步当日的经营数据，JOB专用
create or replace procedure p_sync_business_jingying_today
as
begin
      for x in (select task_date,count(0) as cnt from epsp.v_chk_business_jingying_report@ycinfo where create_time > sysdate - 2 group by task_date) loop
            p_sync_business_jingying(x.task_date);
      end loop;
      --p_sync_business_jingying(to_char(sysdate-1,'yyyymmdd'));
      --sync profit data
      execute immediate 'truncate table t_profit';
      insert into t_profit select * from v_profit;
      --delete from t_profit where stat_date>to_date(v_task_date,'yyyyMMdd')-1;
      --insert into t_profit select * from v_profit where stat_date >to_date(v_task_date,'yyyyMMdd')-1;
      commit;
end;
/
--同步当日的CUST数据，JOB专用
create or replace procedure p_sync_customer_today
as
begin
      --sync customer data;
      execute immediate 'truncate table cust_customer ';
      insert into cust_customer select * from epsp.v_customer_all@ycinfo;  --full flush
      commit;
end;
/


--Oracle JOB
begin
      sys.dbms_scheduler.create_job(job_name            => 'p_sync_business_jingying_JOB',
                                    job_type            => 'STORED_PROCEDURE',
                                    job_action          => 'p_sync_business_jingying_today',
                                    start_date          => TRUNC(SYSDATE) + 10/24,
                                    repeat_interval     => 'FREQ=DAILY; BYHOUR=10',
                                    job_class           => 'DEFAULT_JOB_CLASS',
                                    enabled             => true,
                                    auto_drop           => false,
                                    comments            => '');
end;
/

begin
      sys.dbms_scheduler.create_job(job_name            => 'p_sync_customer_JOB',
                                    job_type            => 'STORED_PROCEDURE',
                                    job_action          => 'p_sync_customer_today',
                                    start_date          => TRUNC(SYSDATE) + 2/24,
                                    repeat_interval     => 'FREQ=DAILY; BYHOUR=2',
                                    job_class           => 'DEFAULT_JOB_CLASS',
                                    enabled             => true,
                                    auto_drop           => false,
                                    comments            => '');
end;
/

--监控数据同步
select count(0) from chk_business_jingying_report where task_date=trunc(sysdate-1);

---------------------------成本预测---------------------------
create or replace procedure p_generate_predicate_cost(v_date in date)
as
      i_row_count number;
      s_max_month varchar2(50);
      n_rate number;
begin
      --预测成本
      i_row_count := 0;
      for r in (select t.batch_no,count(0) as c from cost_statement t where to_char(t.produce_date,'yyyymm')=to_char(v_date,'yyyymm') group by t.batch_no) loop
            if lower(r.batch_no) not like '%xls%' then
                  delete cost_statement t where to_char(t.produce_date,'yyyymm')=to_char(v_date,'yyyymm') and t.batch_no = r.batch_no;
                  commit;
            else
                  i_row_count := i_row_count +1;
            end if;
      end loop;
      --Predicate
      if i_row_count = 0 then
            n_rate := 1.0;
            if last_day(v_date) - sysdate>0 then
                  n_rate := EXTRACT(DAY FROM SYSDATE) / EXTRACT(DAY FROM LAST_DAY(SYSDATE));
            end if;
            n_rate := n_rate * 1.1;
            select to_char(max(produce_date),'yyyymm') into s_max_month from cost_statement where lower(batch_no) like '%xls%';
            insert into cost_statement(department,produce_date,course_code,course_name,cost_type,amount,create_time,batch_no)
            select t.department, v_date,t.course_code, t.course_name,t.cost_type, t.amount * n_rate, sysdate,s_max_month
            from cost_statement t where to_char(t.produce_date,'yyyymm')=s_max_month;
            commit;
      end if;
end;
/
--定时任务执行
create or replace procedure p_generate_predicate_cost_today
as
begin
      p_generate_predicate_cost(sysdate);
      p_generate_predicate_cost(add_months(sysdate,-1));
end;
/
--创建JOB
begin
      sys.dbms_scheduler.create_job(job_name            => 'MB_DB.P_PREDICATE_COST_JOB',
                                    job_type            => 'STORED_PROCEDURE',
                                    job_action          => 'p_generate_predicate_cost_today',
                                    start_date          => to_date('03-09-2024 00:00:00', 'dd-mm-yyyy hh24:mi:ss'),
                                    repeat_interval     => 'Freq=Minutely;Interval=10',
                                    end_date            => to_date(null),
                                    job_class           => 'DEFAULT_JOB_CLASS',
                                    enabled             => true,
                                    auto_drop           => false,
                                    comments            => '');
end;
/

--RESYNC data
begin
      p_sync_business_jingying('20250303');
end;
--MON sync data
select * from mb_db.chk_business_jingying_report where task_date = trunc(sysdate - 1) + 1/2;
select * from mb_db.chk_business_jingying_report where task_date = to_date('20250303','yyyymmdd') + 1/2;

--Stat
with vc as(select distinct department as dept, course_code as code, course_name as name from cost_statement) 
select vc.*,
      (select round(nvl(sum(amount),0),0) from cost_statement m where m.department=vc.dept and m.course_code=vc.code and to_char(m.produce_date,'yyyymm')='202401') as "2401",
      (select round(nvl(sum(amount),0),0) from cost_statement m where m.department=vc.dept and m.course_code=vc.code and to_char(m.produce_date,'yyyymm')='202402') as "2402",
      (select round(nvl(sum(amount),0),0) from cost_statement m where m.department=vc.dept and m.course_code=vc.code and to_char(m.produce_date,'yyyymm')='202403') as "2403",
      (select round(nvl(sum(amount),0),0) from cost_statement m where m.department=vc.dept and m.course_code=vc.code and to_char(m.produce_date,'yyyymm')='202404') as "2404",
      (select round(nvl(sum(amount),0),0) from cost_statement m where m.department=vc.dept and m.course_code=vc.code and to_char(m.produce_date,'yyyymm')='202405') as "2405",
      (select round(nvl(sum(amount),0),0) from cost_statement m where m.department=vc.dept and m.course_code=vc.code and to_char(m.produce_date,'yyyymm')='202406') as "2406",
      (select round(nvl(sum(amount),0),0) from cost_statement m where m.department=vc.dept and m.course_code=vc.code and to_char(m.produce_date,'yyyymm')='202407') as "2407",
      (select round(nvl(sum(amount),0),0) from cost_statement m where m.department=vc.dept and m.course_code=vc.code and to_char(m.produce_date,'yyyymm')='202408') as "2408",
      (select round(nvl(sum(amount),0),0) from cost_statement m where m.department=vc.dept and m.course_code=vc.code and to_char(m.produce_date,'yyyymm')='202409') as "2409",
      (select round(nvl(sum(amount),0),0) from cost_statement m where m.department=vc.dept and m.course_code=vc.code and to_char(m.produce_date,'yyyymm')='202410') as "2410",
      (select round(nvl(sum(amount),0),0) from cost_statement m where m.department=vc.dept and m.course_code=vc.code and to_char(m.produce_date,'yyyymm')='202411') as "2411",
      (select round(nvl(sum(amount),0),0) from cost_statement m where m.department=vc.dept and m.course_code=vc.code and to_char(m.produce_date,'yyyymm')='202412') as "2412",
      (select round(nvl(sum(amount),0),0) from cost_statement m where m.department=vc.dept and m.course_code=vc.code and to_char(m.produce_date,'yyyymm')='202501') as "2501",
      (select round(nvl(sum(amount),0),0) from cost_statement m where m.department=vc.dept and m.course_code=vc.code and to_char(m.produce_date,'yyyymm')='202502') as "2502",      
from vc  order by dept desc, code;