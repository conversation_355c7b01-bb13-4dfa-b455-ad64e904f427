import requests
from geopy.distance import geodesic

key1="bf1c0d013a74fee8c6ba882722cbf6a6"  #产品提供KEY
key="d95098beefb9efb66b35d0898822539e"

def geocoder(pos):
    params = {
        "keyWord": pos
    }
    url = "http://api.tianditu.gov.cn/geocoder?ds={}&tk={}".format(params, key)
    r = requests.get(url)
    return r.json().get("location")

def geocode2(lon, lat):
    params = {
        "lon": lon,
        "lat": lat,
        "ver": 1
    }
    url ="http://api.tianditu.gov.cn/geocoder?postStr={}&type=geocode&tk={}".format(params, key)
    r = requests.get(url)
    return r.json().get("result")

def readcsv(filename):
    import csv
    with open("D:/TMP/tianditu2.csv", 'w', newline='', encoding='utf-8') as csvfile:
        csv_writer = csv.writer(csvfile, escapechar='\\')
        with open(filename) as f:
            render = csv.reader(f)
            header = next(render)
            print(header)
            for row in render:
                geo = geocoder(row[0])
                print(row[0], geo)
                if geo:
                    csv_writer.writerow((row[0], geo.get("lat"), geo.get("lon")))
                else:
                    csv_writer.writerow((row[0], "未查询到地址"))
            
if __name__ == "__main__":
    address = ["广东省-江门市-恩平市-广东省-江门市-蓬江区-迎宾西路","广州市海珠区广州大道南368大厦"]
    for a in address:
        geo = geocoder(a)
        print(geo)
        res = geocode2(geo.get("lon"), geo.get("lat"))
        print(res)
        print("------------------------------------------\n")
    # readcsv("D:/TMP/地址.txt")
    res = geocode2(113.12769, 22.65234)
    print(res)
    res = geocode2(113.12761424931, 22.652343674395)
    print(res)
    point1 = (39.9042, 116.4074)  # 北京天安门
    point2 = (39.9082, 116.4107)  # 北京王府井
    distance = geodesic(point1, point2).m
    print("Distance: {:.2f} m".format(distance))