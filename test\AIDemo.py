import torch
from transformers import <PERSON><PERSON>oken<PERSON>, WizardForCaptionWithLMHead

class WizardLLM:
    def __init__(self, model_path):
        self.model = WizardForCaptionWithLMHead(tokenizer=WizardTokenizer)
        self.model.load_state_dict(torch.load("{}/checkpoint".format(model_path)))
    
    def predict(self, input_text):
        inputs = self.model.encode(input_text, add_special_tokens=True)
        
        with torch.no_grad():
            outputs = self.model(inputs)
            
        return outputs[0][0]
    
if __name__ == '__main__':
    model = WizardLLM("D:/GPT4ALLData/wizardlm-13b-v1.1-superhot-8k.ggmlv3.q4_0.bin")
    print(model.predict("你叫什么名字？"))