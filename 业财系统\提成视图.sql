create or REPLACE view  v_commission as 
select  pv.stat_date as 日期, pv.company_name as 分公司, pv.PURE_PROFIT_sum as 月度完成毛利,
(pv.PURE_PROFIT_sum-pv.operating_profit) as 月度总费用，
pv.operating_profit as 月度净利润，
pv.cumulative_profit  as "累计净利润",
(select suggest_total from okr_suggest os where os.department=pv.company_name and to_char(os.suggest_date,'yyyy-mm')=to_char(pv.stat_date,'yyyy-mm')) as 累计净利润目标值,
round(pv.cumulative_profit/(select suggest_total from okr_suggest os where os.department=pv.company_name and to_char(os.suggest_date,'yyyy-mm')=to_char(pv.stat_date,'yyyy-mm')),2) as 累计净利润完成率,
'2250000' as "业绩核定常数（单位：元/年）",
'11000000' as "存量业绩指标（单位：元）",
'9250000' as "增量业绩指标（单位：元）",
'＞15500000' as "超量业绩指标（单位：元）",
'10250000' as "业务提成发放值（单位：元）",
'8%' as "存量提成比例",
round(pv.cumulative_profit*case when (pv.cumulative_profit/11000000)>1 then 1 else (pv.cumulative_profit/11000000) end*0.08,2) as 存量提成,
'12%' as "增量提成比例",
case when pv.cumulative_profit>9250000 then (pv.cumulative_profit-9250000)*0.12 
else 0 end as "增量提成",
'20%' as "超量提成比例",
case when pv.cumulative_profit>15500000 then (pv.cumulative_profit-15500000)*0.2 
else 0 end as "超量提成",
round(pv.cumulative_profit*(pv.cumulative_profit/11000000)*0.08+case when pv.cumulative_profit>11000000 then (pv.cumulative_profit-11000000)*0.12 
else 0 end +case when pv.cumulative_profit>15500000 then (pv.cumulative_profit-15500000)*0.2 
else 0 end,2) as "提成合计"
--'0' as "已发提成",
--'提成合计-已发提成/90%' as "风险准备金余额"
from 
(select pv.stat_date as stat_date, pv.company_name as company_name, pv.PURE_PROFIT_sum as PURE_PROFIT_sum,pv.operating_profit as operating_profit，(select sum(pv_in.operating_profit) from profit_view pv_in  where pv_in.company_name =('业务拓展一部') and to_date(to_char(pv_in.stat_date,'yyyy-mm'),'yyyy-mm') <=to_date(to_char(pv.stat_date,'yyyy-mm'),'yyyy-mm') and pv_in.stat_date>=to_date('202401','yyyy-mm')) as cumulative_profit
from profit_view pv where pv.company_name =('业务拓展一部') and pv.stat_date>=to_date('202406','yyyy-mm')and (pv.PURE_PROFIT_sum-pv.operating_profit)>0)pv
UNION
select  pv.stat_date as 日期, pv.company_name as 分公司, pv.PURE_PROFIT_sum as 月度完成毛利,
(pv.PURE_PROFIT_sum-pv.operating_profit) as 月度总费用，
pv.operating_profit as 月度净利润，
pv.cumulative_profit  as "累计净利润",
(select suggest_total from okr_suggest os where os.department=pv.company_name and to_char(os.suggest_date,'yyyy-mm')=to_char(pv.stat_date,'yyyy-mm')) as 累计净利润目标值,
round(pv.cumulative_profit/(select suggest_total from okr_suggest os where os.department=pv.company_name and to_char(os.suggest_date,'yyyy-mm')=to_char(pv.stat_date,'yyyy-mm')),2) as 累计净利润完成率,
'2250000' as "业绩核定常数（单位：元/年）",
'0' as "存量业绩指标（单位：元）",
'2250000' as "增量业绩指标（单位：元）",
'＞2250000' as "超量业绩指标（单位：元）",
'0' as "业务提成发放值（单位：元）",
'8%' as "存量提成比例",
round(pv.cumulative_profit*0.08*0 ,2) as 存量提成,
'12%' as "增量提成比例",
case when pv.cumulative_profit>0 then (pv.cumulative_profit-0)*0.12 
else 0 end as "增量提成",
'20%' as "超量提成比例",
case when pv.cumulative_profit>2250000 then (pv.cumulative_profit-2250000)*0.2 
else 0 end as "超量提成",
round(pv.cumulative_profit*0.08+case when pv.cumulative_profit>0 then (pv.cumulative_profit-0)*0.12 
else 0 end +case when pv.cumulative_profit>2250000 then (pv.cumulative_profit-2250000)*0.2 
else 0 end ,2)as "提成合计"
--'0' as "已发提成",
--'提成合计-已发提成/90%' as "风险准备金余额"
from 
(select pv.stat_date as stat_date, pv.company_name as company_name, pv.PURE_PROFIT_sum as PURE_PROFIT_sum,pv.operating_profit as operating_profit，(select sum(pv_in.operating_profit) from profit_view pv_in  where pv_in.company_name =('业务拓展三部') and to_date(to_char(pv_in.stat_date,'yyyy-mm'),'yyyy-mm') <=to_date(to_char(pv.stat_date,'yyyy-mm'),'yyyy-mm')and pv_in.stat_date>=to_date('202401','yyyy-mm')) as cumulative_profit
from profit_view pv where pv.company_name =('业务拓展三部') and pv.stat_date>=to_date('202406','yyyy-mm')and (pv.PURE_PROFIT_sum-pv.operating_profit)>0)pv 
UNION
select  pv.stat_date as 日期, pv.company_name as 分公司, pv.PURE_PROFIT_sum as 月度完成毛利,
(pv.PURE_PROFIT_sum-pv.operating_profit) as 月度总费用，
pv.operating_profit as 月度净利润，
pv.cumulative_profit  as "累计净利润",
(select suggest_total from okr_suggest os where os.department=pv.company_name and to_char(os.suggest_date,'yyyy-mm')=to_char(pv.stat_date,'yyyy-mm')) as 累计净利润目标值,
round(pv.cumulative_profit/(select suggest_total from okr_suggest os where os.department=pv.company_name and to_char(os.suggest_date,'yyyy-mm')=to_char(pv.stat_date,'yyyy-mm')),2) as 累计净利润完成率,
'2250000' as "业绩核定常数（单位：元/年）",
'0' as "存量业绩指标（单位：元）",
'1550000' as "增量业绩指标（单位：元）",
'＞2250000' as "超量业绩指标（单位：元）",
'1500000' as "业务提成发放值（单位：元）",
'8%' as "存量提成比例",
round(pv.cumulative_profit*0.08*0 ,2) as 存量提成,
'12%' as "增量提成比例",
case when pv.cumulative_profit>0 then (pv.cumulative_profit-0)*0.12 else 0 end  as "增量提成",
'20%' as "超量提成比例",
case when pv.cumulative_profit>2250000 then (pv.cumulative_profit-2250000)*0.2 
else 0 end as "超量提成",
round(pv.cumulative_profit*0.08*0 +case when pv.cumulative_profit>0 then (pv.cumulative_profit-0)*0.12 else 0 end+case when pv.cumulative_profit>2250000 then (pv.cumulative_profit-2250000)*0.2 
else 0 end ,2)as "提成合计"
--'0' as "已发提成",
--'提成合计-已发提成/90%' as "风险准备金余额"
from 
(select pv.stat_date as stat_date, pv.company_name as company_name, pv.PURE_PROFIT_sum as PURE_PROFIT_sum,pv.operating_profit as operating_profit，(select sum(pv_in.operating_profit) from profit_view pv_in  where pv_in.company_name =('东莞分公司') and to_date(to_char(pv_in.stat_date,'yyyy-mm'),'yyyy-mm') <=to_date(to_char(pv.stat_date,'yyyy-mm'),'yyyy-mm')and pv_in.stat_date>=to_date('202401','yyyy-mm')) as cumulative_profit
from profit_view pv where pv.company_name =('东莞分公司')and pv.stat_date>=to_date('202406','yyyy-mm')and (pv.PURE_PROFIT_sum-pv.operating_profit)>0)pv 

UNION
select  pv.stat_date as 日期, pv.company_name as 分公司, pv.PURE_PROFIT_sum as 月度完成毛利,
(pv.PURE_PROFIT_sum-pv.operating_profit) as 月度总费用，
pv.operating_profit as 月度净利润，
pv.cumulative_profit  as "累计净利润",
(select suggest_total from okr_suggest os where os.department=pv.company_name and to_char(os.suggest_date,'yyyy-mm')=to_char(pv.stat_date,'yyyy-mm')) as 累计净利润目标值,
round(pv.cumulative_profit/(select suggest_total from okr_suggest os where os.department=pv.company_name and to_char(os.suggest_date,'yyyy-mm')=to_char(pv.stat_date,'yyyy-mm')),2) as 累计净利润完成率,
'2250000' as "业绩核定常数（单位：元/年）",
'1200000' as "存量业绩指标（单位：元）",
'1500000' as "增量业绩指标（单位：元）",
'＞2250000' as "超量业绩指标（单位：元）",
'0' as "业务提成发放值（单位：元）",
'8%' as "存量提成比例",
round(pv.cumulative_profit*case when (pv.cumulative_profit/1200000)>1 then 1 else (pv.cumulative_profit/1200000) end*0.08,2) as 存量提成,
'12%' as "增量提成比例",
case when pv.cumulative_profit>1500000 then (pv.cumulative_profit-1500000)*0.12 
else 0 end as "增量提成",
'20%' as "超量提成比例",
case when pv.cumulative_profit>2250000 then (pv.cumulative_profit-2250000)*0.2 
else 0 end as "超量提成",
round(pv.cumulative_profit*(pv.cumulative_profit/1200000)*0.08+case when pv.cumulative_profit>1200000 then (pv.cumulative_profit-1200000)*0.12 
else 0 end +case when pv.cumulative_profit>2250000 then (pv.cumulative_profit-2250000)*0.2 
else 0 end,2) as "提成合计"
--'0' as "已发提成",
--'提成合计-已发提成/90%' as "风险准备金余额"
from 
(select pv.stat_date as stat_date, pv.company_name as company_name, pv.PURE_PROFIT_sum as PURE_PROFIT_sum,pv.operating_profit as operating_profit，(select sum(pv_in.operating_profit) from profit_view pv_in  where pv_in.company_name =('惠州分公司') and to_date(to_char(pv_in.stat_date,'yyyy-mm'),'yyyy-mm') <=to_date(to_char(pv.stat_date,'yyyy-mm'),'yyyy-mm') and pv_in.stat_date>=to_date('202401','yyyy-mm')) as cumulative_profit
from profit_view pv where pv.company_name =('惠州分公司') and pv.stat_date>=to_date('202406','yyyy-mm')and (pv.PURE_PROFIT_sum-pv.operating_profit)>0)pv
UNION
select  pv.stat_date as 日期, pv.company_name as 分公司, pv.PURE_PROFIT_sum as 月度完成毛利,
(pv.PURE_PROFIT_sum-pv.operating_profit) as 月度总费用，
pv.operating_profit as 月度净利润，
pv.cumulative_profit  as "累计净利润",
(select suggest_total from okr_suggest os where os.department=pv.company_name and to_char(os.suggest_date,'yyyy-mm')=to_char(pv.stat_date,'yyyy-mm')) as 累计净利润目标值,
round(pv.cumulative_profit/(select suggest_total from okr_suggest os where os.department=pv.company_name and to_char(os.suggest_date,'yyyy-mm')=to_char(pv.stat_date,'yyyy-mm')),2) as 累计净利润完成率,
'2250000' as "业绩核定常数（单位：元/年）",
'0' as "存量业绩指标（单位：元）",
'1550000' as "增量业绩指标（单位：元）",
'＞2250000' as "超量业绩指标（单位：元）",
'1500000' as "业务提成发放值（单位：元）",
'8%' as "存量提成比例",
round(pv.cumulative_profit*0.08*0,2) as 存量提成,
'12%' as "增量提成比例",
case when pv.cumulative_profit>0 then (pv.cumulative_profit-1550000)*0.12 else 0 end  as "增量提成",
'20%' as "超量提成比例",
case when pv.cumulative_profit>2250000 then (pv.cumulative_profit-2250000)*0.2 
else 0 end as "超量提成",
round(pv.cumulative_profit*0.08*0 +case when pv.cumulative_profit>0 then (pv.cumulative_profit-0)*0.12 else 0 end+case when pv.cumulative_profit>2250000 then (pv.cumulative_profit-2250000)*0.2 
else 0 end,2)  as "提成合计"
--'0' as "已发提成",
--'提成合计-已发提成/90%' as "风险准备金余额"
from 
(select pv.stat_date as stat_date, pv.company_name as company_name, pv.PURE_PROFIT_sum as PURE_PROFIT_sum,pv.operating_profit as operating_profit，(select sum(pv_in.operating_profit) from profit_view pv_in  where pv_in.company_name =('杭州分公司') and to_date(to_char(pv_in.stat_date,'yyyy-mm'),'yyyy-mm') <=to_date(to_char(pv.stat_date,'yyyy-mm'),'yyyy-mm')and pv_in.stat_date>=to_date('202401','yyyy-mm')) as cumulative_profit
from profit_view pv where pv.company_name =('杭州分公司') and pv.stat_date>=to_date('202406','yyyy-mm')and (pv.PURE_PROFIT_sum-pv.operating_profit)>0)pv 
UNION
select  pv.stat_date as 日期, pv.company_name as 分公司, pv.PURE_PROFIT_sum as 月度完成毛利,
(pv.PURE_PROFIT_sum-pv.operating_profit) as 月度总费用，
pv.operating_profit as 月度净利润，
pv.cumulative_profit  as "累计净利润",
(select suggest_total from okr_suggest os where os.department=pv.company_name and to_char(os.suggest_date,'yyyy-mm')=to_char(pv.stat_date,'yyyy-mm')) as 累计净利润目标值,
round(pv.cumulative_profit/(select suggest_total from okr_suggest os where os.department=pv.company_name and to_char(os.suggest_date,'yyyy-mm')=to_char(pv.stat_date,'yyyy-mm')),2) as 累计净利润完成率,
'2250000' as "业绩核定常数（单位：元/年）",
'0' as "存量业绩指标（单位：元）",
'1550000' as "增量业绩指标（单位：元）",
'＞2250000' as "超量业绩指标（单位：元）",
'1500000' as "业务提成发放值（单位：元）",
'8%' as "存量提成比例",
round(pv.cumulative_profit*0.08*0,2)  as 存量提成,
'12%' as "增量提成比例",
case when pv.cumulative_profit>0 then (pv.cumulative_profit-0)*0.12 else 0 end  as "增量提成",
'20%' as "超量提成比例",
case when pv.cumulative_profit>2250000 then (pv.cumulative_profit-2250000)*0.2 
else 0 end as "超量提成",
round(pv.cumulative_profit*0.08*0 +case when pv.cumulative_profit>0 then (pv.cumulative_profit-0)*0.12 else 0 end+case when pv.cumulative_profit>2250000 then (pv.cumulative_profit-2250000)*0.2 
else 0 end,2)  as "提成合计"
--'0' as "已发提成",
--'提成合计-已发提成/90%' as "风险准备金余额"
from 
(select pv.stat_date as stat_date, pv.company_name as company_name, pv.PURE_PROFIT_sum as PURE_PROFIT_sum,pv.operating_profit as operating_profit，(select sum(pv_in.operating_profit) from profit_view pv_in  where pv_in.company_name =('广州分公司') and to_date(to_char(pv_in.stat_date,'yyyy-mm'),'yyyy-mm') <=to_date(to_char(pv.stat_date,'yyyy-mm'),'yyyy-mm')and pv_in.stat_date>=to_date('202401','yyyy-mm')) as cumulative_profit
from profit_view pv where pv.company_name =('广州分公司') and pv.stat_date>=to_date('202406','yyyy-mm')and (pv.PURE_PROFIT_sum-pv.operating_profit)>0)pv 
UNION
select  pv.stat_date as 日期, pv.company_name as 分公司, pv.PURE_PROFIT_sum as 月度完成毛利,
(pv.PURE_PROFIT_sum-pv.operating_profit) as 月度总费用，
pv.operating_profit as 月度净利润，
pv.cumulative_profit  as "累计净利润",
(select suggest_total from okr_suggest os where os.department=pv.company_name and to_char(os.suggest_date,'yyyy-mm')=to_char(pv.stat_date,'yyyy-mm')) as 累计净利润目标值,
round(pv.cumulative_profit/(select suggest_total from okr_suggest os where os.department=pv.company_name and to_char(os.suggest_date,'yyyy-mm')=to_char(pv.stat_date,'yyyy-mm')),2) as 累计净利润完成率,
'2250000' as "业绩核定常数（单位：元/年）",
'0' as "存量业绩指标（单位：元）",
'1550000' as "增量业绩指标（单位：元）",
'＞2250000' as "超量业绩指标（单位：元）",
'0' as "业务提成发放值（单位：元）",
'8%' as "存量提成比例",
round(pv.cumulative_profit*0.08*0,2) as 存量提成,
'12%' as "增量提成比例",
case when pv.cumulative_profit>0 then (pv.cumulative_profit-1550000)*0.12 else 0 end  as "增量提成",
'20%' as "超量提成比例",
case when pv.cumulative_profit>2250000 then (pv.cumulative_profit-2250000)*0.2 
else 0 end as "超量提成",
round(pv.cumulative_profit*0.08*0 +case when pv.cumulative_profit>0 then (pv.cumulative_profit-0)*0.12 else 0 end+case when pv.cumulative_profit>2250000 then (pv.cumulative_profit-2250000)*0.2 
else 0 end,2)  as "提成合计"
--'0' as "已发提成",
--'提成合计-已发提成/90%' as "风险准备金余额"
from 
(select pv.stat_date as stat_date, pv.company_name as company_name, pv.PURE_PROFIT_sum as PURE_PROFIT_sum,pv.operating_profit as operating_profit，(select sum(pv_in.operating_profit) from profit_view pv_in  where pv_in.company_name =('华东运营中心') and to_date(to_char(pv_in.stat_date,'yyyy-mm'),'yyyy-mm') <=to_date(to_char(pv.stat_date,'yyyy-mm'),'yyyy-mm')and pv_in.stat_date>=to_date('202401','yyyy-mm')) as cumulative_profit
from profit_view pv where pv.company_name =('华东运营中心') and pv.stat_date>=to_date('202401','yyyy-mm')and (pv.PURE_PROFIT_sum-pv.operating_profit)>0)pv 
