import os

def delete_files(directory):
    m_files = ["48cdf91805230c85c3aac.jpg",'avatar-default.png','thumbnail.png','js','saved_resource','5b6caa27a4fe50c206729.jpg','475bf75c03a0fe45279d5.jpg','c00da2586027ee048bf47.jpg']
    m_tails = [".css", ".下载",'.js']
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file in m_files or file.endswith(tuple(m_tails)):
                print(os.path.join(root, file))
                os.remove(os.path.join(root, file))

def keep_files(directory):
    m_tails = [".image"]
    for root, dirs, files in os.walk(directory):
        for file in files:
            if not file.endswith(tuple(m_tails)):
                print(os.path.join(root, file))
                # os.remove(os.path.join(root, file))

def move_files(source_dir, target_dir):
    m_tails = [".image"]
    for root, dirs, files in os.walk(source_dir):
        count = 0
        new_file = os.path.basename(root)
        if new_file.endswith("_files"):
            new_file = new_file[:-6]
        if new_file.endswith("-今日头条"):
            new_file = new_file[:-5]
        if new_file.endswith("_微头条"):
            new_file = new_file[:-4]
        for file in files:
            if file.endswith(tuple(m_tails)):
                count += 1
                # print(new_file, count, os.path.join(root, file))
                if not os.path.exists(os.path.join(target_dir, f"{new_file}_{count}.webp")):
                    os.rename(os.path.join(root, file), os.path.join(target_dir, f"{new_file}_{count}.webp"))
                else:
                    print(os.path.join(root, file))
                # print(os.path.join(target_dir, f"{new_file}_{count}.webp"), os.path.join(root, file))
                # os.remove(os.path.join(root, file))

if __name__ == '__main__':
    # 指定要删除文件的目录
    directory = 'C:\\Users\\<USER>\\Downloads\\TMP'

    # 调用函数删除文件
    # delete_files(directory)
    # keep_files(directory)
    move_files(directory, "C:\\TMP\\Photo\\TT")