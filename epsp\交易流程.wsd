@startuml 快捷支付流程

actor "付款人/商户" as M
participant 收单机构 as A
participant 银联 as U
participant 发卡行 as I

group 触发签约验证短信
    M -> A: 填写身份信息
    A -> U: 账户验证请求
    U -> I: 转发账户请求
    I -> I: 身份验证处理
    I -> U: 返回验证结果
    U -> A: 返回验证结果
    A -> M: 返回验证结果
    I -\ M: 下发短信验证码
end 

group 快捷支付签约
    M -> A: 填写短信验证码
    A -> U: 发起签约申请
    U -> I: 转发签约申请
    I -> I: 验证签约短信\n处理签约
    I -> U: 返回签约结果
    U -> A: 返回验签约结果
    A -> M: 返回签约结果
end 

group 快捷支付
    M -> A: 发起交易申请
    note right of M: 扣款（借记）/付款（贷记）
    A -> U: 发起交易申请
    U -> I: 转发扣款/付款申请
    I -> I: 验证签约信息\n处理交易
    I -> U: 返回交易结果
    U -> A: 返回交易结果
    A -> M: 返回交易结果
end

@enduml