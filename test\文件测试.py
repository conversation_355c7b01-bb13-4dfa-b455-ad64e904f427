import cx_Oracle
import pandas as pd
import logging

logging.basicConfig(encoding='utf-8')
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log_console_handler = logging.StreamHandler()  # 创建控制台处理器
log_console_handler.setFormatter(formatter)
logger.addHandler(log_console_handler)

def import_csv_to_oracle(csv_file, table_name, batch_size=1000):
    oracle_conn = cx_Oracle.connect('efps01/efps01@172.16.1.2/testdb')  # 连接测试环境数据库

    # 读取CSV文件，指定chunksize
    reader = pd.read_csv(csv_file, chunksize=batch_size, dtype=str)

    # 遍历数据块并分批插入到数据库
    for chunk in reader:
        # 将DataFrame转换为SQL插入语句
        insert_query = "INSERT INTO {} VALUES ({})".format(table_name, ', '.join([':' + str(i) for i in range(1, len(chunk.columns) + 1)]))
        # 执行SQL插入语句
        cursor = oracle_conn.cursor()
        cursor.executemany(insert_query, chunk.values.tolist())
        oracle_conn.commit()

        # 关闭游标
        cursor.close()

    # 关闭数据库连接
    oracle_conn.close()

# 使用示例
logger.info('开始导入数据')
import_csv_to_oracle('D:/TMP/JR.csv', 'tmp', batch_size=1000)
logger.info('数据导入完成')

