@baseurl=https://devapi.currencycloud.com

## Get token

post {{baseurl}}/v2/authenticate/api HTTP/1.1
Content-Type: application/json

{
  "login_id": "ye<PERSON><PERSON><PERSON>@epaylinks.cn",
  "api_key": "4836ec87027e9fb4c317a865afc5ef60c1ccee039350b7c38578385e25de8adf"
}

{{auth_token}}={{@response.body.auth_token}}

### Create  Account（创建子账户）

@auth_token=06fd4cbed7142a763cdb873d91e7e40c
# @account_id=d70630b6-ac04-47ed-8437-778f0462e78e
@account_id=16d098e0-dee6-4c49-b0f2-9792ead9070d
@customer_code=**********
@customer_code=***************
@contact_id=d9e04cc1-becd-43a7-a255-776eb19d35a0

post {{baseurl}}/v2/accounts/create HTTP/1.1
Content-Type: application/json
X-Auth-Token: {{auth_token}}

{
  "account_name": "Test Company",
  "legal_entity_type": "company",
  "street": "Guangzhou Road",
  "city": "guangzhou",
  "country": "CN",
  "status": "enabled",
  "api_trading": "true",
  "online_trading": "false",
  "phone_trading": "false",
  "identification_type": "incorporation_number",
  "identification_value": "123456789012345678"
}

### Get account
get {{baseurl}}/v2/accounts/{{account_id}} HTTP/1.1
Content-Type: application/json
X-Auth-Token: {{auth_token}}

###Creates  Contact（创建子账户的联系人）
post {{baseurl}}/v2/contacts/create HTTP/1.1
Content-Type: application/json
X-Auth-Token: {{auth_token}}

{
  "account_id": "{{account_id}}",
  "first_name": "John",
  "last_name": "Doe",
  "email_address": "<EMAIL>",
  "phone_number": "***********",
  "login_id": "{{customer_code}}.2",
  "status": "enabled"
}

###三、【申请Sub_account VA】
get {{baseurl}}/v2/funding_accounts/find?currency=USD HTTP/1.1
Content-Type: application/json
X-Auth-Token: {{auth_token}}

###三、【申请Sub_account VA】
get {{baseurl}}/v2/funding_accounts/find?currency=HKD HTTP/1.1
Content-Type: application/json
X-Auth-Token: {{auth_token}}

###三、【申请Sub_account VA】
get {{baseurl}}/v2/funding_accounts/find?currency=EUR HTTP/1.1
Content-Type: application/json
X-Auth-Token: {{auth_token}}

###三、【申请Sub_account VA】
get {{baseurl}}/v2/funding_accounts/find?currency=GBP HTTP/1.1
Content-Type: application/json
X-Auth-Token: {{auth_token}}


###四、【VA收款】
###查询所有收款交易
@transaction_id=0fa45202-3045-4934-bd79-a0fa85c05a78


get {{baseurl}}/v2/transactions/find?per_page=25&page=1 HTTP/1.1
X-Auth-Token: {{auth_token}}

###查询收款状态
get {{baseurl}}/v2/transactions/{{transaction_id}} HTTP/1.1
X-Auth-Token: {{auth_token}}

###查询收款人——新接口
get {{baseurl}}/v2/funding_transactions/{{transaction_id}} HTTP/1.1
X-Auth-Token: {{auth_token}}

###查询收款人——旧接口
get {{baseurl}}/v2/transactions/sender/6a03a48f-31b1-4134-ab0a-c9d10e26ddef HTTP/1.1
X-Auth-Token: {{auth_token}}

###查询子账户余额
GET {{baseurl}}/v2/balances/find?on_behalf_of={{contact_id}} HTTP/1.1
X-Auth-Token: {{auth_token}}

###模拟收款转账
POST {{baseurl}}/v2/demo/funding/create
  ?id=8bd7ba19-eca0-425d-a3f0-968577ba2a81
  &sender_name=Test Sender
  &sender_country=GB
  &sender_reference=sender-Ref
  &sender_routing_code=
  &receiver_account_number=**********
  &receiver_routing_code=
  &amount=150.03
  &currency=USD
  &action=approve HTTP/1.1
X-Auth-Token: {{auth_token}}

###校验收款人合法性
POST {{baseurl}}/v2/beneficiaries/validate
 ?bank_country=CN
  &currency=CNH
  &account_number=6216666666666666666
  &bic_swift=MRMDUS33XXX
  &beneficiary_entity_type=company
  &beneficiary_address=广州大道南100号
  &beneficiary_city=广州
  &beneficiary_country=CN
  &beneficiary_company_name=广州公司
X-Auth-Token: {{auth_token}}

###查询付款的受益人条件
GET {{baseurl}}/v2/reference/beneficiary_required_details
  ?currency=USD
  &bank_account_country=DK
  &beneficiary_country=CN
X-Auth-Token: {{auth_token}}

### 查询付款的付款人条件
GET {{baseurl}}/v2/reference/payer_required_details
  ?currency=USD
  &payer_country=US
  &payer_entity_type=company
  &payment_type=regular
X-Auth-Token: {{auth_token}}

###子账户联系人
POST {{baseurl}}/v2/contacts/find
 ?account_id=178b8745-764b-4a8a-b9ed-01ca3dad6310
X-Auth-Token: {{auth_token}}

###付款目的码
GET {{baseurl}}/v2/reference/payment_purpose_codes
 ?currency=CNY
 &bank_account_country=CN
X-Auth-Token: {{auth_token}}

###付款
POST {{baseurl}}/v2/payments/create
 ?currency=CNY
  &beneficiary_id=dc2fa79a-3fd4-4325-99b6-47cb166fe887
  &amount=100
  &reason=test payment
  &reference=test payment
  &unique_request_id=1234
  &on_behalf_of=88240c18-ed75-40ad-a7d3-39bf18c7abd4
  &purpose_code=GOD
X-Auth-Token: {{auth_token}}