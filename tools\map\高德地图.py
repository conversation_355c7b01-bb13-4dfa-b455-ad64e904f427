import requests

def geocode_address(address, key):
    base_url = "https://restapi.amap.com/v3/geocode/geo"
    params = {
        'address': address,
        'key': key
    }

    response = requests.get(base_url, params=params)
    print(response.text)

    data = response.json()

    if data['status'] == '1' and int(data['count']) > 0:
        location = data['geocodes'][0]['location']
        return location
    else:
        return "未找到该地址的经纬度信息"

# 使用示例
address = "广州市海珠区广州大道南368大厦"
key = "624c93e6a7079abe9ee3f6970e3f18fc"  # 请替换为您自己的API密钥
location = geocode_address(address, key)
print(location)
