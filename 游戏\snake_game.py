import pygame
import random
import sys
import os  # 添加os模块

# 初始化游戏
pygame.init()

# 游戏常量
WIDTH = 1200
HEIGHT = 800
CELL_SIZE = 40
FPS = 10
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)

# 初始化显示窗口
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption('贪吃蛇')
clock = pygame.time.Clock()

def read_res_file(filename):
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        file_path = os.path.join(sys._MEIPASS, "res", filename)
    else:
        # 如果是开发环境
        file_path = os.path.join(os.path.dirname(__file__), "res", filename)
    return file_path

# 加载并拉伸背景图片
background_image = pygame.image.load(read_res_file("bk.jpg"))
background_image = pygame.transform.scale(background_image, (WIDTH, HEIGHT))

# 设置中文字体
pygame.font.init()
font_path = os.path.join(read_res_file("simhei.ttf"))  # 使用相对路径
font74 = pygame.font.Font(font_path, 74)
font36 = pygame.font.Font(font_path, 36)

# 按钮透明度
BUTTON_ALPHA = 128

# 按钮颜色
BUTTON_COLOR = (50, 50, 50, BUTTON_ALPHA)

# 按钮位置和大小
BUTTON_SIZE = 40
BUTTON_MARGIN = 10

# 按钮矩形
button_up = pygame.Rect(WIDTH - BUTTON_SIZE * 2.5 - BUTTON_MARGIN, HEIGHT // 2 - BUTTON_SIZE * 2, BUTTON_SIZE, BUTTON_SIZE)
button_down = pygame.Rect(WIDTH - BUTTON_SIZE * 2.5 - BUTTON_MARGIN, HEIGHT // 2 + BUTTON_SIZE, BUTTON_SIZE, BUTTON_SIZE)
button_left = pygame.Rect(WIDTH - BUTTON_SIZE * 3.5 - BUTTON_MARGIN * 2, HEIGHT // 2 - BUTTON_SIZE // 2, BUTTON_SIZE, BUTTON_SIZE)
button_right = pygame.Rect(WIDTH - BUTTON_SIZE * 1.5 - BUTTON_MARGIN, HEIGHT // 2 - BUTTON_SIZE // 2, BUTTON_SIZE, BUTTON_SIZE)

FOOD_COLOR = (255, 0, 0)
PLAYER1_COLOR = (80, 255, 80)
PLAYER2_COLOR = (0, 250, 255)

PLAYER1_LISTEN_KEYS = [pygame.K_w, pygame.K_s, pygame.K_a, pygame.K_d] # 玩家1监听的按键, 上, 下, 左, 右
PLAYER2_LISTEN_KEYS = [pygame.K_UP, pygame.K_DOWN, pygame.K_LEFT, pygame.K_RIGHT] # 玩家2监听的按键, 上, 下, 左, 右

DIRECTION_UP = 'UP'
DIRECTION_DOWN = 'DOWN'
DIRECTION_LEFT = 'LEFT'
DIRECTION_RIGHT = 'RIGHT'

def draw_rounded_rect(surface, color, rect, corner_radius):
    pygame.draw.rect(surface, color, rect, border_radius=corner_radius)

def random_color():
    return (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))

def read_files_from_subdirectory(subdirectory):
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        directory_path = os.path.join(sys._MEIPASS, "res", subdirectory)
    else:
        # 如果是开发环境
        directory_path = os.path.join(os.path.dirname(__file__), "res", subdirectory)
    if not os.path.exists(directory_path):
        return []
    files = os.listdir(directory_path)
    files.sort()
    return [os.path.join(directory_path, file) for file in files]

class Snake:
    def __init__(self, name, color, image_path, listen_keys=None):
        self.name = name
        self.body = [self.random_start_pos()]
        self.direction = self.random_start_direction()
        self.color = color
        self.new_direction = self.direction  # 添加新方向属性
        self.speed = 1
        self.score = 0
        self.visible = True  # 添加可见性属性
        self.status_failed = False  # 添加失败状态属性
        
        self.listen_key_up = listen_keys[0]
        self.listen_key_down = listen_keys[1]
        self.listen_key_left = listen_keys[2]
        self.listen_key_right = listen_keys[3]
        
        self.images = []
        if image_path:
            files = read_files_from_subdirectory(image_path)
            for file in files:
                image = pygame.image.load(file)
                # image.fill(color, special_flags=pygame.BLEND_MULT)
                image = pygame.transform.scale(image, (CELL_SIZE * 0.8, CELL_SIZE * 0.8))
                self.images.append(image)

    def random_start_pos(self):
        return [random.randrange(1, (WIDTH//CELL_SIZE)-1)*CELL_SIZE,
                random.randrange(1, (HEIGHT//CELL_SIZE)-1)*CELL_SIZE]

    def random_start_direction(self):
        return random.choice([DIRECTION_UP, DIRECTION_DOWN, DIRECTION_LEFT, DIRECTION_RIGHT])

    def listen_key(self, event_key):
        if not self.visible:
            return None
        if event_key == self.listen_key_up:
            return self.adjust(DIRECTION_UP)
        elif event_key == self.listen_key_down:
            return self.adjust(DIRECTION_DOWN)
        elif event_key == self.listen_key_left:
            return self.adjust(DIRECTION_LEFT)
        elif event_key == self.listen_key_right:
            return self.adjust(DIRECTION_RIGHT)

    def adjust(self, new_direction):
        acc_speed = 0
        if new_direction == self.direction:
            acc_speed = 1
        elif new_direction == DIRECTION_RIGHT and self.direction == DIRECTION_LEFT:
            acc_speed = -1
        elif new_direction == DIRECTION_LEFT and self.direction == DIRECTION_RIGHT:
            acc_speed = -1
        elif new_direction == DIRECTION_UP and self.direction == DIRECTION_DOWN:
            acc_speed = -1
        elif new_direction == DIRECTION_DOWN and self.direction == DIRECTION_UP:
            acc_speed = -1
        new_speed = self.speed + acc_speed
        if new_speed < 1:
            new_speed = 1
            if self.direction == DIRECTION_RIGHT:
                new_direction = DIRECTION_LEFT
            elif self.direction == DIRECTION_LEFT:
                new_direction = DIRECTION_RIGHT
            elif self.direction == DIRECTION_UP:
                new_direction = DIRECTION_DOWN
            elif self.direction == DIRECTION_DOWN:
                new_direction = DIRECTION_UP
        elif new_speed > 10:
            new_speed = 10
        self.speed = new_speed
        self.direction = new_direction
        return self.direction

    def move(self):
        # if self.name == '玩家2':
        #     self.speed = 0
        #     self.visible = False
        # self.direction = self.new_direction  # 更新方向
        head = self.body[0].copy()
        if self.direction == DIRECTION_RIGHT:
            head[0] += CELL_SIZE / 10 * self.speed
        elif self.direction == DIRECTION_LEFT:
            head[0] -= CELL_SIZE / 10 * self.speed
        elif self.direction == DIRECTION_UP:
            head[1] -= CELL_SIZE / 10 * self.speed
        elif self.direction == DIRECTION_DOWN:
            head[1] += CELL_SIZE / 10 * self.speed
        self.body.insert(0, head)
    
    def eat_food(self, food):
        head_rect = pygame.Rect(self.body[0][0], self.body[0][1], CELL_SIZE, CELL_SIZE)
        eat_flag = head_rect.colliderect(food.get_rect()) and food.visible and self.visible
        if eat_flag:
            food.visible = False
            food.reset_position()
            self.score += 10
            self.speed += 1
            if self.speed > 10:
                self.speed = 10
        return eat_flag
    
    def eat_snake(self, other_snake):
        for segment in self.body[1:]:
            if other_snake.body[0] == segment:
                other_snake.status_failed = True

    def check_collision(self):
        for i in range(1, len(self.body)):
            if self.body[0] == self.body[i]:
                return True
        return False
    
    def check_boundary(self):
        if (self.body[0][0] < 0 or self.body[0][0] >= WIDTH or
            self.body[0][1] < 0 or self.body[0][1] >= HEIGHT):
            self.status_failed = True

    def draw(self):
        def get_image(index):
            if len(self.images) == 0:
                return None
            if len(self.images) == 1:
                if index == 0:
                    return self.images[0]
                else:
                    return None
            if len(self.images) >= len(self.body):
                return self.images[index]
            else:
                pre_part = len(self.images) // 2
                post_part = len(self.body) - pre_part
                if index <= pre_part:
                    return self.images[index]
                elif index > pre_part and index <= post_part:
                    return self.images[pre_part]
                else:
                    return self.images[index - post_part + pre_part - 1]
        
        def draw_segment(pos, image):
            if image:
                rect = pygame.Rect(pos[0], pos[1], CELL_SIZE, CELL_SIZE)
                screen.blit(image, rect)
            else:
                pygame.draw.rect(screen, self.color, (pos[0], pos[1], CELL_SIZE, CELL_SIZE))
        
        if not self.visible:
            return None
        
        for i, pos in enumerate(self.body):
            image = get_image(i)
            draw_segment(pos, image)

class Food:
    def __init__(self, color):
        self.position = self.randomize_position()
        self.visible = True  # 添加可见性属性
        self.blink_timer = 0  # 添加闪动计时器
        self.color = color
        
        self.images = []
        files = read_files_from_subdirectory("food")
        for file in files:
            image = pygame.image.load(file)
            image = pygame.transform.scale(image, (CELL_SIZE*1.5, CELL_SIZE*1.5))
            self.images.append(image)
        
        self.image = random.choice(self.images)

    def randomize_position(self):
        return [
            random.randrange(1, (WIDTH//CELL_SIZE)-1)*CELL_SIZE,
            random.randrange(1, (HEIGHT//CELL_SIZE)-1)*CELL_SIZE
        ]

    def reset_position(self):
        self.position = self.randomize_position()
        self.visible = True  # 重置可见性
        self.blink_timer = 0  # 重置闪动计时器
        self.image = random.choice(self.images)
        
    def get_rect(self):
        return pygame.Rect(self.position[0], self.position[1], CELL_SIZE, CELL_SIZE)

    def update(self):
        self.blink_timer += 1
        if self.blink_timer >= 30 and self.visible:  # 每40帧切换一次可见性
            self.visible = False
            self.blink_timer = 0
        if self.blink_timer >= 10 and not self.visible:
            self.visible = True
            self.blink_timer = 0
            
    def draw(self):
        self.visible = True
        def draw_segment(pos, image):
            if image:
                rect = pygame.Rect(pos[0], pos[1], CELL_SIZE, CELL_SIZE)
                screen.blit(image, rect)
            else:
                pygame.draw.rect(screen, self.color, (pos[0], pos[1], CELL_SIZE, CELL_SIZE))
                
        if self.visible:
            draw_segment(self.position, self.image)
            # pygame.draw.rect(screen, self.color, (self.position[0], self.position[1], CELL_SIZE, CELL_SIZE))

bubble_images = []
files = read_files_from_subdirectory("bubble")
for file in files:
    image = pygame.image.load(file)
    bubble_images.append(image)
    
class Bubble:
    def __init__(self, color, radius, image, position=None, speed=None):
        self.position = position or [0, random.randint(0, WIDTH)]
        self.color = color
        self.radius = radius
        if speed:
            self.speedX = speed[0]
            self.speedY = speed[1]
        else:
            self.speedX = random.uniform(1.5, 20.0)
            self.speedY = random.uniform(1.5, 20.0)
        self.speedYFinal = random.uniform(1.5, 5.0)
        
        self.image = image
        image_size = CELL_SIZE * random.randint(20, 120) // 200
        self.image = pygame.transform.scale(image, (image_size, image_size))

    def move(self):
        if self.speedX < 0:
            self.position[0] += self.speedX
            self.speedX += 0.8
            if self.speedX > 0:
                self.speedX = 0
        elif self.speedX > 0:
            self.position[0] -= self.speedX
            self.speedX -= 0.8
            if self.speedX < 0:
                self.speedX = 0
        self.position[1] -= self.speedY
        if self.speedY > self.speedYFinal:
            self.speedY -= 0.3

    def draw(self):
        screen.blit(self.image, (int(self.position[0]) - self.radius, int(self.position[1]) - self.radius))

def generate_bubble(position=None, speed=None):
    color = random_color()
    radius = random.randint(5, 15)
    image = random.choice(bubble_images)
    image = pygame.transform.scale(image, (radius * 2, radius * 2))
    bubble = Bubble(color, radius, image, position, speed)
    return bubble

def generate_bubbles(snake, num=None):
    num_bubbles = num or random.randint(1, 3)
    position = snake.body[0]
    speed = [random.uniform(-20.0, 20.0), random.uniform(0.5, 10.0)]
    bubbles = []
    for _ in range(num_bubbles):
        pos = [position[0] + random.randint(-20, 20), position[1] + random.randint(-20, 20)]
        bubbles.append(generate_bubble(pos, speed))
    return bubbles

def init_snakes():
    snake1 = Snake('玩家1', PLAYER1_COLOR, image_path="snake1", listen_keys=PLAYER1_LISTEN_KEYS)
    snake2 = Snake('玩家2', PLAYER2_COLOR, image_path="snake2", listen_keys=PLAYER2_LISTEN_KEYS)
    return snake1, snake2

# 游戏主循环
def main():
    snake1, snake2 = init_snakes()
    food = Food(FOOD_COLOR)
    game_over = False

    # 添加水泡
    bubbles = []

    # 添加倒计时
    countdown = 60  # 倒计时
    start_ticks = pygame.time.get_ticks()  # 获取开始时间

    while True:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYDOWN:
                snake1.listen_key(event.key)
                snake2.listen_key(event.key)

        snake1.move()
        snake2.move()
        food.update()  # 更新食物状态

        # 更新水泡状态
        for bubble in bubbles:
            bubble.move()
            if bubble.position[1] < 0:
                bubbles.remove(bubble)

        # 碰撞检测
        # 双蛇食物检测
        snake1_ate = snake1.eat_food(food)
        snake2_ate = snake2.eat_food(food)

        if snake1_ate or snake2_ate:
            # 吃到食物的蛇需要增长
            if snake1_ate:
                snake2.body.pop()
                bubbles.extend(generate_bubbles(snake1))
            if snake2_ate:
                snake1.body.pop()
                bubbles.extend(generate_bubbles(snake2))
        else:
            # 双方都没吃到食物时都移除尾部
            snake1.body.pop()
            snake2.body.pop()
            if random.randint(0, 30) < 5:
                bubbles.extend(generate_bubbles(random.choice([snake1, snake2]), 1))

        # 双蛇碰撞检测
        snake1.eat_snake(snake2)
        snake2.eat_snake(snake1)
        
        # 边界碰撞检测（两条蛇独立检测）
        snake1.check_boundary()
        snake2.check_boundary()

        # 游戏结束处理
        if snake1.status_failed and snake2.status_failed:
            game_over = True
            winner = "无"
        elif snake1.status_failed:
            game_over = True
            winner = snake2.name
        elif snake2.status_failed:
            game_over = True
            winner = snake1.name

        # 倒计时处理
        seconds = (pygame.time.get_ticks() - start_ticks) / 1000  # 计算已过去的秒数
        if seconds >= countdown:
            game_over = True
            if snake1.score > snake2.score:
                winner = snake1.name
            elif snake2.score > snake1.score:
                winner = snake2.name
            else:
                winner = "无"

        if game_over:
            text = font74.render(f'{winner} 获胜！', True, WHITE)
            screen.blit(text, (WIDTH//2-140, HEIGHT//2-50))
            
            restart_text = font36.render('点击屏幕重新开始', True, WHITE)
            screen.blit(restart_text, (WIDTH//2-120, HEIGHT//2+50))
            
            # 重置时重新初始化两条蛇
            snake1, snake2 = init_snakes()
            
            pygame.display.update()
            
            waiting = True
            while waiting:
                for event in pygame.event.get():
                    if event.type == pygame.QUIT:
                        pygame.quit()
                        sys.exit()
                    if event.type == pygame.MOUSEBUTTONDOWN:
                        game_over = False
                        snake1, snake2 = init_snakes()
                        start_ticks = pygame.time.get_ticks()  # 重置倒计时
                        waiting = False
                    # 传递其他事件到主循环
                    else:
                        pygame.event.post(event)

        screen.fill(BLACK)
        
        screen.blit(background_image, (0, 0))  # 绘制拉伸后的背景图片

        # 绘制蛇和食物
        snake1.draw()
        snake2.draw()
        food.draw()

        # 绘制水泡
        for bubble in bubbles:
            bubble.draw()

        # 标注玩家和对应的蛇颜色
        player1_label = font36.render(f'{snake1.name} 得分:{snake1.score}', True, snake1.color)
        player2_label = font36.render(f'{snake2.name} 得分:{snake2.score}', True, snake2.color)
        screen.blit(player1_label, (WIDTH - 300, 10))
        screen.blit(player2_label, (WIDTH - 300, 50))

        # 绘制半透明圆角按钮
        draw_rounded_rect(screen, BUTTON_COLOR, button_up, 10)
        draw_rounded_rect(screen, BUTTON_COLOR, button_down, 10)
        draw_rounded_rect(screen, BUTTON_COLOR, button_left, 10)
        draw_rounded_rect(screen, BUTTON_COLOR, button_right, 10)

        # 按键动画
        keys = pygame.key.get_pressed()
        if keys[snake1.listen_key_up]:
            draw_rounded_rect(screen, snake1.color, button_up, 10)
        if keys[snake1.listen_key_down]:
            draw_rounded_rect(screen, snake1.color, button_down, 10)
        if keys[snake1.listen_key_left]:
            draw_rounded_rect(screen, snake1.color, button_left, 10)
        if keys[snake1.listen_key_right]:
            draw_rounded_rect(screen, snake1.color, button_right, 10)
            
        if keys[snake2.listen_key_up]:
            draw_rounded_rect(screen, snake2.color, button_up, 10)
        if keys[snake2.listen_key_down]:
            draw_rounded_rect(screen, snake2.color, button_down, 10)
        if keys[snake2.listen_key_left]:
            draw_rounded_rect(screen, snake2.color, button_left, 10)
        if keys[snake2.listen_key_right]:
            draw_rounded_rect(screen, snake2.color, button_right, 10)

        # 绘制倒计时
        countdown_text = font36.render(f'倒计时: {int(countdown - seconds)}', True, WHITE)
        screen.blit(countdown_text, (10, 10))

        pygame.display.update()
        pygame.display.flip()
        clock.tick(FPS)

if __name__ == '__main__':
    main()