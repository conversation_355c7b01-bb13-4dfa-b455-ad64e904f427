@startuml 创建收款人
title 创建收款人

actor 商户 as M
actor 运营 as O
box 汇通系统 #LightBlue
    participant 商户门户 as MP
    participant 运营门户 as OP
    participant 渠道服务 as CH
end box
participant "CurrencyCloud" as CC #Grey

==创建收款人==
autonumber 1.1
M -> MP: 登录
MP --> M: 进入汇通全球
MP -> CH: 查询收款人需求
CH -> CC: 查询收款人需求
note left of CC: 接口：/v2/reference/beneficiary_required_details
CC --> CH: 收款人需求
CH --> MP: 收款人需求
MP --> M: 展现收款人需求

M -> MP: 创建收款人
MP -> CH: 校验收款人
CH -> CC: 校验收款人
note left of CC: 接口：/v2/beneficiaries/validate
CC --> CH: 收款人校验结果
CH --> MP: 收款人校验结果
MP -> MP: 保存收款人
MP --> M: 收款人状态：待审核

==收款人审核==
autonumber 2.1
O -> OP: 收款人KYC审核
alt KYC审核通过
    OP -> CH: 创建收款人
    CH -> CC: 创建收款人
    note left of CC: 接口：/v2/beneficiaries/create
    CC --> CH: 创建结果
    CH -> OP: 创建结果
    OP -> MP: 创建成功
else KYC审核不通过
    OP -> MP: 审核失败
end

@enduml

@startuml 付款流程
title 付款流程

actor 商户 as M
actor 运营 as O
actor 收款人 as R
box 汇通系统 #LightBlue
    participant 商户门户 as MP
    participant 运营门户 as OP
    participant 交易服务 as TX
    participant 账户服务 as AC
    participant 风控服务 as RC
    participant 渠道服务 as CH
end box
participant "CurrencyCloud" as CC #Grey

@enduml

@startuml 付款
title 付款

actor 商户 as M
actor 运营员 as O
box 汇通系统 #LightBlue
    participant 商户门户 as MP
    participant 运营门户 as OP
    participant 交易服务 as TX
    participant 风控服务 as RC
    participant 账户服务 as AC
    participant 渠道服务 as CH
end box
participant "CurrencyCloud" as CC #Grey

==付款申请&审核==
autonumber 1.1
M -> MP: 商户填写付款单
note left of MP
    付款单：
    1. 付款金额
    2. 收款人
    3. KYC信息
end note
MP -> TX: 提交付款单
TX -> RC: 申请风控核验
RC --> TX: 核验结果
alt 通过
    TX -> TX: 创建付款单
    note left of TX: 状态：待审核
    O -> OP: 审核付款单
    alt 通过
        ==付款处理==
        autonumber 2.1
        TX -> AC: 资金扣减
        TX -> TX: 状态：待支付
        TX -> CH: 申请付款
        CH -> CC: 申请付款
        note left of CC: 接口：/v2/payments/create
        CC --> CH: 返回受理结果
        CH --> TX: 返回受理结果
        TX -> TX: 状态：支付中
        alt 受理成功
            CC ->> CH: 通知支付结果
            loop 查询付款单状态
                TX -> CH: 查询付款单状态
                CH -> CC: 查询付款单状态
                note left of CC: 接口：/v2/payments/{paymentId}
                CC --> CH: 返回付款单状态
                CH --> TX: 返回付款单状态
            end
            alt 付款成功
                TX -> TX: 状态：支付成功
                MP ->> M: 支付成功
            else 付款失败
                TX -> AC: 资金退回
                TX -> TX: 状态：支付失败
                MP ->> M: 支付失败
            end
            MP -> MP: 支付成功
        else 受理失败
            TX -> TX: 状态：支付失败
            MP -\ M: 支付失败
        end
    else 审核拒绝
        TX -> TX: 状态：支付失败
        MP -\ M: 支付失败
    end
else 风控失败
    TX -> TX: 状态支付失败
    MP -\ M: 支付失败
end
@enduml