**斯贝思系统**

# 斯贝思环境

已配置UAT域名,[hosts绑定ip- **************]: ga-admin-uat.epaylinks.cn、ga-check-uat.epaylinks.cn、ga-pay-uat.epaylinks.cn

| 模块     | 服务&版本                                        | 说明           |
| -------- | ------------------------------------------------ | -------------- |
| ACI      | tomcat应用<br />tomcat 8.5.82<br />jdk 1.8.0_201 | 接口网关       |
| Agent    | 同上                                             | 代理商门户     |
| CCMM     | 同上                                             | 商户门户       |
| CCPG     | 同上                                             | 接口           |
| CCSM     | 同上                                             | 运营门户       |
| sbs_task | tomcat 9.0.82<br />jdk ********                  | SpringBoot服务 |

## 生产环境

交易接口：https://ga-pay.epaylinks.cn/payment.jsp
交易查询：https://ga-check.epaylinks.cn/order_list.jsp
退款接口：https://ga-check.epaylinks.cn/refund_submit
退款查询：https://ga-check.epaylinks.cn/refund_list
商户门户：https://ga-mer.epaylinks.cn/login.jsp

p_mid：91050
p_account_num：********
key：64t06b280n00vhB
p_transaction_type：SALE
（Mastercard测试）

![*************](image/README/*************.png)

## UAT环境

https://ga-admin-uat.epaylinks.cn/

进行hosts文件绑定
************** ga-admin-uat.epaylinks.cn


### SBS认证数据对象

【涉及数据库对象】

表：MER_MCC、MER_URL、TMP_SQL_TEMPLATE、TMP_ALERT、TMP_ALERT_DETAIL、TMP_CONFIG、TMP_SQL_FILE

视图：V_DS_TRADE_TEST、V_TMP_CONFIG、V_DS_EXPORT（需要修改where条件中处理交易的时间范围）、V_ALERT_ALERT_TRANS_DETAIL

存储过程：P_TMP_ALERT_DETAIL（存储过程）

### SBS认证操作指导

【部署机器】**************、**************

【操作指导】

1、生成基础文件（第一个文件 1、指标结果文件（分指标，共**个））
http://localhost:8080/swagger-ui/index.html#/export-controller/execSqlTemplate
beginDate：2022-01-01
endDate：2023-07-17
templateId：不填
params：不填

2、生成合并文件及根据规则id汇总文件，并将数据插入TMP_ALERT表（第2,3个文件 2、指标结果汇总文件 3、指标结果汇总文件记录数情况）
http://localhost:8080/swagger-ui/index.html#/file-collect-controller/fileCollect
directoryPath：/opt/sbs_test/file2
resultFilePath：/opt/sbs_test/指标结果汇总文件2024-01-12.csv
countFilePath：/opt/sbs_test/指标结果汇总文件记录数情况2024-01-12.csv

3、执行存储过程，无参数直接执行
http://localhost:8080/swagger-ui/index.html#/file-collect-controller/execProcedure

4、生成视图数据文件（第4个文件）
http://localhost:8080/swagger-ui/index.html#/export-controller/execSql
sql：select * from v_alert_trans_detail
filePath：/opt/sbs_test
fileName：文件名称.csv
params：不填

1、指标结果文件（共85个指标目录）
2、指标结果汇总文件(1个文件)
3、指标结果汇总文件记录数情况（1个文件）
4、指标结果汇总对应交易明细（118个文件）

## UAT环境

 URL ： https://ga-admin-uat.epaylinks.cn/
进行hosts文件绑定
************** ga-admin-uat.epaylinks.cn

## 测试环境

测试服务器：
***********
tomcat/tomcat

数据库：
**********
wkproj/wkproj@123

| 项目  | 说明                           | URL                                                                                                                   |
| ----- | ------------------------------ | --------------------------------------------------------------------------------------------------------------------- |
| CCSM  | 后端，运营门户<br />端口：8005 | [http://***********:8005/main.html](http://***********:8005/main.html) <br />账号：liuyq/admin123 ga_admin.epaylinks.cn  |
| CCMM  | 前端，商户门户<br />端口：8003 | [Merchant Management System](http://***********:8003/) <br />90006<br />alibaba/1234%qwerT <br />ga_mer.epaylinks.cn     |
| CCPG  | 交易接口                       | 支付接口<br />[https://ga-pay.epaylinks.cn/payment.jsp](https://ga-pay.epaylinks.cn/payment.jsp)                         |
| ACI   | 自动勾兑                       | 订单查询（或退款）接口<br />[https://ga-check.epaylinks.cn/order_list.jsp](https://ga-check.epaylinks.cn/order_list.jsp) |
| Agent | 代理商门户<br />端口：8002     | 2002ypldl/1234%qwerT                                                                                                  |

交易URL：https://test-wk.epaylinks.cn/pay/payment.jsp
查询URL：https://test-wk.epaylinks.cn/aci/order_list.jsp
退款URL：https://test-wk.epaylinks.cn/aci/refund_submit
退款URL：https://test-wk.epaylinks.cn/aci/refund_list
商户门户：https://test-wk.epaylinks.cn，登录信息：90006  alibaba/N0bF4Nnx
测试商户：90006
账户号：90006001、90006003（3DS）
秘钥：12345678

# 业务

## 风控

【脑图】

```mermaid
mindmap
  root((风控))
    网址黑名单
    来源网址限定
    交易币种限定
    商户IP段限定
    IP城市限定
    黑名单限定
    金额限定
    支付次数限定
    发卡行国家限定
    商户独立黑名单
    ＩＰ所在国家限定
    发卡行限定
    CyberSource3DSCheck
    DM+3DS
```

【说明】

| 风控规则            | 业务规则 | 处理说明 |
| ------------------- | -------- | -------- |
| 网址黑名单          |          |          |
| 来源网址限定        |          |          |
| 交易币种限定        |          |          |
| 商户IP段限定        |          |          |
| IP城市限定          |          |          |
| 黑名单限定          |          |          |
| 金额限定            |          |          |
| 支付次数限定        |          |          |
| 发卡行国家限定      |          |          |
| 商户独立黑名单      |          |          |
| IP所在国家限定      |          |          |
| 发卡行限定          |          |          |
| CyberSource3DSCheck |          |          |
| DM+3DS              |          |          |
|                     |          |          |

## 对接CyberSource

修改CCPG的tomcat启动脚本，java启动参数 JAVA_OPTS 中添加 -Dspring.profiles.active=prod ，如果是UAT环境则为 -Dspring.profiles.active=uat

修改WEB-INF/classes/sysConfig-uat.properties 和 WEB-INF/classes/sysConfig-prod.properties 配置以下参数：
agent.cybersource.signNo=
agent.cybersource.yourCertFile=
agent.cybersource.myPrivateKeyFile=
agent.cybersource.myPrivateKeyPassword=
agent.cybersource.host=

# 定时任务清单

## Spring JOB

| 任务                 | 频率            | 说明                                                                                                                                          |
| -------------------- | --------------- | --------------------------------------------------------------------------------------------------------------------------------------------- |
| autoSendSMS          | 0 30 08 ? * *   | 发送短信，数据表：ccps_sendsms<br />生产没有数据，无效                                                                                        |
| autoGetUnTrade       | 0 30 08 ? * *   | 自动调用异常交易查询接口, 获取异常交易信息, 并更新到系统<br />生产未配置数据，无效                                                            |
| autoRefund           | 0 0/8 * * * ?   | 自动向上游发起退款<br /> 数据来源：ccps_unnormal_process、ccps_creditinfo、ccps_traderecord                                                   |
| autoCheckPaySpc      | 0 0/10 * * * ?  | 自动勾兑<br />数据来源：ccps_traderecord                                                                                                      |
| autoSendEmails       | 0 0/1 * * * ? * | 发送邮件<br />数据来源：                                                                                                                      |
| autoUpdateMERCheckIp | 0 0 10 * * ? *  | 勾兑IP生效，从数据库中加载到内存静态变量<br />数据来源：ccps_mer_checkip                                                                      |
| autoRecoverChannl    | 0 08 01 ? * *   | 切换渠道，①备份渠道扣率；②删除卡种记录；③删除渠道扣率；④删除备份扣率；<br />数据来源：ccps_mer_rate\ccps_mer_channel、ccps_change_cha_mer |

## Oracle JOB

| 任务 | 频率 | 说明 |
| ---- | ---- | ---- |
|      |      |      |
|      |      |      |

# 工具类

SecUtil：数据库密码等加解密工具

# 配置

# 常见问题

### 1、R0000问题分析

【现象】商户交易出错，出错信息：R0000:Failed

【分析】

1）分析日志；

日志位置：/home/<USER>/ccpg/logs/CCPG/

![1741157263591](image/README/1741157263591.png)

设定了交易币种限定，并且分数100，为必过项目

![1741157078568](image/README/1741157078568.png)

订单币种为orderCurrency=CNY

2）查数据库：SELECT * FROM CCPS_CURRENCY_LIMMIT，确认商户做了；

![1741155054955](image/README/1741155054955.png)

设定限制币种无CNY

【结论】限定了交易币种，没有CNY，如上图，只有USD、GBP、EUR
