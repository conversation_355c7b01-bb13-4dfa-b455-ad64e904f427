@startuml 支付令牌状态图
title 支付令牌状态图

state "待激活-9"  as 9
state "已激活-1"  as 1
state "已停用-5"  as 5
state "已冻结-2"  as 2
state "已过期-3"  as 3
state "已锁定-4"  as 4
state "已注销-0"  as 0
state "注销中-99"  as 99

note right of 9
字段：NMD_VCC_CARD.Status
停用：EPAY强制停用，商户不能启用
冻结：商户冻结，商户可以解冻
end note

[*] -> 9: 申请令牌
9 --> 1: 激活
1 --> 99: 注销

1 --> 5: 停用
5 --> 1: 启用
5 --> 99: 注销

1 --> 2: 冻结
2 -> 1: 解冻
2 --> 99: 注销

1 --> 3: 过期
3 --> 99: 注销

1 --> 4: 锁定
4 -> 1: 解锁
4 --> 99: 注销

99 --> 0: 注销完成
0 -> [*]: 结束


@enduml