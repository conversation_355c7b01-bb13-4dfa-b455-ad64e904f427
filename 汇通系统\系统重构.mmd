mindmap
    root((架构重构))
        id0{{环境部署}}
            1、开发测试环境框架搭建
            2、基础数据表导入（参考外卡重构项目）
        id1{{运营门户}}
            1、登录接口重构（PAS登录接口）
                1.1、检查与as接口是否一致，不一致则修改前端
                1.2、密码算法不一样则直接使密码过期
                1.3、修改密码和图形验证码
                1.4、兼容UAA认证和AS认证
            2、系统管理
                2.1、检查接口兼容，不兼容则反馈和优化
                2.2、鉴权数据入UAA_SERVICE
            3、商户管理（鉴权数据）
            4、交易管理（鉴权数据）
            3、风控管理（鉴权数据）
            4、结算管理（鉴权数据）
            5、清理AS代码
                5.1、登录&系统管理代码
                5.2、认证&鉴权代码
        id2{{业务模块}}
            1、新建微服务busyapi
                1.1、业务模块代码迁移到busyapi
                1.2、业务模块数据迁移到UAA_SERVICE
            2、创建客户端访问的SDK
            3、梳理公共模块
                3.1、UM-DAO模块
                3.2、COMMON模块
        id3{{商户门户}}
            1、使用业务模块SDK
        id4{{接口网关}}
            1、在AS中构建，复用代码
            2、复用UAA证书鉴权