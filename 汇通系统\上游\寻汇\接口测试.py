# 汇通系统\VCC\接口测试.py
# import tools.rsa_tool as rsa_tool
# from .tools.rsa_tool import load_public_key_from_pem
# from .tools.rsa_tool import load_private_key_from_pem
# from .tools.rsa_tool import sign_data
# from .tools.rsa_tool import decrypt_data
# from .tools.rsa_tool import encrypt_data

from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
############################################################################
# 加载公钥
def load_public_key_from_pem(pem_file):
    with open(pem_file, 'rb') as f:
        pem_data = f.read()
    public_key = serialization.load_pem_public_key(pem_data)
    return public_key

# 加密数据
def encrypt_data(public_key, data):
    encrypted_data = public_key.encrypt(
        data,
        padding.OAEP(
            mgf=padding.MGF1(algorithm=hashes.SHA256()),
            algorithm=hashes.SHA256(),
            label=None
        )
    )
    return encrypted_data

# 验证签名
def verify_signature(public_key, signature, data):
    try:
        public_key.verify(
            signature,
            data,
            padding.PSS(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        return True
    except:
        return False

# 加载私钥
def load_private_key_from_pem(pem_file):
    with open(pem_file, 'rb') as f:
        pem_data = f.read()
    private_key = serialization.load_pem_private_key(pem_data, password=None)
    return private_key

# 解密数据
def decrypt_data(private_key, encrypted_data):
    decrypted_data = private_key.decrypt(
        encrypted_data,
        padding.OAEP(
            mgf=padding.MGF1(algorithm=hashes.SHA256()),
            algorithm=hashes.SHA256(),
            label=None
        )
    )
    return decrypted_data

# 签名数据
def sign_data(private_key, data):
    signature = private_key.sign(
        data,
        padding.PSS(
            mgf=padding.MGF1(algorithm=hashes.SHA256()),
            salt_length=padding.PSS.MAX_LENGTH
        ),
        hashes.SHA256()
    )
    return signature

def encrypt_aes(plaintext, key, iv):
    # Create a new AES cipher object
    cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
    encryptor = cipher.encryptor()

    # Pad the plaintext to a multiple of the block size
    padder = padding.PKCS7(cipher.algorithm.block_size).padder()
    padded_data = padder.update(plaintext) + padder.finalize()

    # Encrypt the padded plaintext
    ct = encryptor.update(padded_data) + encryptor.finalize()
    return ct
############################################################################

import requests
import datetime
import random
import string

private_key = load_private_key_from_pem('D:/TMP/私钥.pem')
public_key = load_public_key_from_pem('D:/TMP/公钥.pem')



base_url = "https://uatscip-openapi.91xunhui.cn"
content_type = "application/json;charset=UTF-8"

def post2vcc(uri, request_body) :
    ori_key = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(16))
    data = {
        "appId": "AI1814117853882294273",
        "msgId": f'HT{datetime.datetime.now().strftime("%Y%m%d%H%M%S%f")[:-3]}',
        "ip": "127.0.0.1",
        "version": "1.0",
        "timestamp": datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3],
        "key": encrypt_data(public_key, ori_key.encode()).hex(),
        "sign": sign_data(private_key, request_body.encode()).hex(),
        "requestBody": encrypt_aes(request_body, ori_key.encode(), ori_key.encode()).hex()
    }
    print(decrypt_data(private_key, bytes.fromhex(data['key'])).decode())
    url = f'{base_url}{uri}'
    print('接口地址 -->> ', url)
    print("加密KEY -->> ", ori_key)
    print('请求数据 -->> ', data)
    resp = requests.post(url, headers={'Content-Type': content_type}, data=data)
    print('响应数据 -->> ', resp.text)
    return resp.text
    

# post2vcc('/openapi/v1/acctBal/list', '{}')
