#!/bin/bash

ACCESS_LOG="/path/to/nginx/logs/access.log"
AUDIT_LOG="/path/to/nginx/logs/audit.log"

# 使用tail命令实时监控access.log，并处理敏感参数
tail -F "$ACCESS_LOG" | while read -r line; do
    sanitized_line=$(echo "$line" | sed -E 's/p_card_num=[^&]*&?//g' | sed -E 's/p_card_expmonth=[^&]*&?//g' | sed -E 's/p_card_expyear=[^&]*&?//g' | sed -E 's/p_card_csc=[^&]*&?//g')
    echo "$sanitized_line" >> "$AUDIT_LOG"
    # 删除access.log中的这行日志
    sed -i "/$(echo "$line" | sed 's/[\/&]/\\&/g')/d" "$ACCESS_LOG"
done
