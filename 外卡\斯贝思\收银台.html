<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>收银台支付</title>
    <style>
        .form-container {
            display: flex;
            flex-direction: column;
            gap: 10px; /* 控制表单项之间的间距 */
        }

        .form-group {
            display: flex;
            align-items: baseline; /* 使文本和输入框底部对齐 */
        }

        label {
            flex-basis: 100px; /* 设置label的宽度，可根据需要调整 */
            margin-right: 10px; /* 给输入框留出空间 */
            text-align: right;
        }

        input {
            flex-basis: 200px; /* 输入框自适应剩余空间 */
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <script>
        function initOrderNo() {
            document.getElementsByName('p_order_num')[0].value = getCurrentTimeFormatted();
        }

        function getCurrentTimeFormatted() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的，所以加1
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');

            return `${year}${month}${day}${hours}${minutes}${seconds}`;
        }

        function calculateHash() {
            initOrderNo()
            const mid = document.getElementsByName('p_mid')[0].value;
            const account = document.getElementsByName('p_account_num')[0].value;
            const orderNo = document.getElementsByName('p_order_num')[0].value;
            const currency = document.getElementsByName('p_currency')[0].value;
            const amount = document.getElementsByName('p_amount')[0].value;
            const key = document.getElementsByName('p_key')[0].value;
            const content = mid + account + orderNo + currency + amount + key
            console.log(content)
            const hash = CryptoJS.SHA256(content);

            document.getElementsByName('p_signmsg')[0].value = hash.toString(CryptoJS.enc.Hex);
        }
    </script>
</head>
<body>

<h2>收银台支付</h2>
<div class="form-container">
    <!--    <form id="paymentForm" action="http://localhost:8080/CCPG_epl_war_exploded/payment.jsp" method="POST">-->
<!--        <form id="paymentForm" action="https://ga-pay.epaylinks.cn/payment.jsp" method="POST">-->
    <form id="paymentForm" action="http://***********:8004/payment.jsp" method="POST">
        <div class="form-group">
            <label>商户编号:</label>
            <input type="text" name="p_mid" value="90000">
        </div>
        <div class="form-group">
            <label>商户账号:</label>
            <input type="text" name="p_account_num" value="********">
        </div>

        <div class="form-group">
            <label>签名密钥:</label>
            <input type="text" name="p_key" value="********">
        </div>

        <input type="hidden" name="p_transaction_type" value="VIEWSALE">

        <input type="hidden" name="p_order_num">

        <div class="form-group">
            <label>币种:</label>
            <input name="p_currency" value="USD">
        </div>

        <div class="form-group">
            <label>金额:</label>
            <input type="text" name="p_amount" value="0.01">
        </div>

<!--        <div class="form-group">-->
<!--            <label>发行卡:</label>-->
<!--            <input type="text" name="p_card_issuingbank" value="GlobalCash">-->
<!--        </div>-->

        <div class="form-group">
            <label>付款人名:</label>
            <input type="text" name="p_firstname" value="June">
        </div>

        <div class="form-group">
            <label>付款人姓:</label>
            <input type="text" name="p_lastname" value="Liz">
        </div>

        <div class="form-group">
            <label>付款人邮箱:</label>
            <input type="text" name="p_user_email" value="<EMAIL>">
        </div>

        <div class="form-group">
            <label>付款人电话:</label>
            <input type="text" name="p_user_phone" value="***********">
        </div>

<!--        <div class="form-group">-->
<!--            <label>付款人IP:</label>-->
<!--            <input type="text" name="p_user_ipaddress" value="127.0.0.1">-->
<!--        </div>-->

        <div class="form-group">
            <label>交易网站:</label>
            <input type="text" name="p_trans_url" value="gdyidui.cn">
        </div>

        <div class="form-group">
            <label>返回地址:</label>
            <input type="text" name="p_return_url" value="https://www.epaylinks.cn">
        </div>

        <div class="form-group">
            <label>账单国家:</label>
            <input type="text" name="p_bill_country" value="US">
        </div>

        <div class="form-group">
            <label>账单州/省:</label>
            <input type="text" name="p_bill_state" value="MS">
        </div>

        <div class="form-group">
            <label>账单城市:</label>
            <input type="text" name="p_bill_city" value="MS">
        </div>

        <div class="form-group">
            <label>账单地址:</label>
            <input type="text" name="p_bill_address" value="QLD LST 77">
        </div>

        <div class="form-group">
            <label>账单邮编:</label>
            <input type="text" name="p_bill_zip" value="21328">
        </div>

        <div class="form-group">
            <label>收货人名:</label>
            <input type="text" name="p_ship_firstname" value="June">
        </div>

        <div class="form-group">
            <label>收货人姓:</label>
            <input type="text" name="p_ship_lastname" value="Liz">
        </div>

        <div class="form-group">
            <label>收货国家:</label>
            <input type="text" name="p_ship_country" value="US">
        </div>

        <div class="form-group">
            <label>收货州/省:</label>
            <input type="text" name="p_ship_state" value="MS">
        </div>

        <div class="form-group">
            <label>收货城市:</label>
            <input type="text" name="p_ship_city" value="MS">
        </div>

        <div class="form-group">
            <label>收货地址:</label>
            <input type="text" name="p_ship_address" value="QLD LST 77">
        </div>

        <div class="form-group">
            <label>收货邮编:</label>
            <input type="text" name="p_ship_zip" value="21328">
        </div>

        <div class="form-group">
            <label>商品名称:</label>
            <input type="text" name="p_product_name" value="apple">
        </div>

        <div class="form-group">
            <label>商品数量:</label>
            <input type="text" name="p_product_num" value="1">
        </div>

        <div class="form-group">
            <label>商品描述:</label>
            <input type="text" name="p_product_desc" value="apple">
        </div>

        <input type="hidden" name="p_signmsg">
        <input type="hidden" name="p_ext1" value="ext1">
        <input type="hidden" name="p_ext2" value="ext2">
        <input type="hidden" name="p_remark" value="remark">

        <button type="submit" onclick="calculateHash()">立即支付</button>
    </form>
</div>


</body>
</html>