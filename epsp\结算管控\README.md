# 结算管控

包括本次处理的微信小程序结算管控，本质是结算处理需要参考上游的对账单，然后决定易票联对象的清结算；

与现有的不一样，现有的清结算根据的是规则：T+1日两联都会结算给易票联，因此无需参考对账单；管控交易的清结算则依赖对账单（一个重要的原因：账户平台/微信小程序平台可能越过收单机构发起退款）；

## 若干关键日期

1. 交易日期，交易发起的日期
2. 账单日期D0，上游对交易的解冻日期
3. 账单日期D1，易票联获取账单的日期，解冻日期的自然日次日
4. 结算日期T1，上游结算给易票联的日期，解冻日期的工作日次日

## 交易

### 收单交易

### 退款交易

## 商户账单

我们的账单体现的是资金流代表的结算账单，意味着商户的账单和商户的款项（可提现金额或可用余额）一致；

管控账单的几个特点：

1. 冻结交易并不涉及资金流，因此不会体现到上述结算账单中；
2. 冻结交易的退款在机构层面，同样不会涉及资金流（在账户机构侧是有资金流的，因此账户机构会输出退款解冻）

# 附录

## 测试信息

微信小程序资金管控文件

小程序资金管控-间联模式接入微信指引https://doc.weixin.qq.com/doc/w3_AI8ANQaYACswm9yIRZ7RqWQBKZbym?scode=AJEAIQdfAAoCWWXbPtAI8ANQaYACs
![](file:///G:/My/WX/企业微信文件/WXWork/1688851976565884/Cache/Image/2024-11/18b03787-44e0-44b3-b05c-dd92751a8186.jpg)![1731890511328](image/info/1731890511328.png)

测试参数：
从业机构号：1900010281
APPID：wx2421b1c4370ec43b
渠道申请号：409389245
商户号：562492003199244
网联上游号：713181647
sub appid：wxfe00220f14dceafa

交易解冻通知：https://efps.epaylinks.cn/api/txs/miniPromNotify/unfree

小程序被管控通知：https://efps.epaylinks.cn/api/cust/SP/applet/addFreeze
