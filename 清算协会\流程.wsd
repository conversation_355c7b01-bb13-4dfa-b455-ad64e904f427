@startuml 清算协会交易流程

actor 运营人员 as op

participant "EP客户管理" as ep_cm
participant "清协对接（新）" as ca_new
participant "清协对接（旧）" as ca_old
participant 清算协会 as ca

==商户数据上报==
op -> ep_cm: 导出要上报的客户数据
op -> op: 核对客户数据并筛查
op -> ca_new: 上传客户数据
ca_new -> ca_new: 数据入库及校验
ca_new --> op: 返回数据上报结果：处理中
ca_old -> ca_old: 定时任务，扫描待上报数据
ca_old -> ca: 上报数据
ca --> ca_old: 返回数据上报结果

==商户数据查询==
op -> ca_new: 查询上报数据结果
ca_new --> op: 返回查询结果

==删除/关闭/注销上报数据==
op -> op: 选中要删除的商户数据
op -> ca_new: 删除上报数据
ca_new -> ca_old: 删除上报数据
ca_old -> ca: 删除上报数据
ca --> ca_old: 返回删除结果
ca_old --> ca_new: 返回删除结果
ca_new --> op: 返回删除结果


@enduml