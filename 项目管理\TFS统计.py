import requests
import pandas as pd
import logging
from datetime import datetime, timedelta

# 配置日志输出
logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',  # 只包含日志消息本身
    handlers=[
        logging.StreamHandler()  # 控制台处理器
    ]
)
logger = logging.getLogger(__name__)

# Azure DevOps organization and project details
organization = 'epaylinks'
project = 'EPSP'
query_id = 'd96ce50b-20e1-40a2-a823-b4d8a727d409'
personal_access_token = 'z5zey6rj55lm3xd7awcflgavo6treljwns4czk3psnaxmmt5x22a'

PROJECT_NAMES= ['WK', '外卡', 'APS', '聚合', 'EPSP', '诺曼底', '汇通', 'KJ', '跨境', 'HUIET', '易分账', 'E-BankAccount']

PROJECT_ALIAS = {
    'EPSP': 'EPSP',
    "APS": "聚合",
    "聚合": "聚合",
    "WK": "外卡",
    '外卡': "外卡",
    "NMD": "汇通全球",
    "汇通": "汇通全球",
    "诺曼底": "汇通全球",
    "KJ": "跨境",
    "HUIET": "易分账",
    "E-BankAccount": "易分账"
}

def get_work_item_fields(work_item_id):
    url = f'http://***********:8080/tfs/{organization}/{project}/_apis/wit/workitems/{work_item_id}?api-version=6.0'
    response = requests.get(url, auth=('', personal_access_token))
    response.raise_for_status()
    work_item = response.json()
    fields = work_item['fields']
    return fields

def get_req_update_info(work_item_id, assigned_to, default_approved_date=None, changed_field='System.Description'):
    """获取指定人员最后一次修改说明字段的时间"""
    url = f'http://***********:8080/tfs/{organization}/{project}/_apis/wit/workitems/{work_item_id}/updates?api-version=6.0'
    response = requests.get(url, auth=('', personal_access_token))
    response.raise_for_status()
    updates = response.json()['value']
    
    # 获取最后一次批准的日期
    approved_date = default_approved_date
    for update in updates:
        fields = update.get('fields', {})
        state_change = fields.get('System.State', {})
        revised_date = fields.get('System.RevisedDate', {})
        if state_change.get('newValue') == '已批准':
            if revised_date:
                approved_date = revised_date['oldValue']
            else:
                approved_date = update['revisedDate']
            approved_date = approved_date[0:10]
            break
    
    # 获取最后一次修改说明字段的时间
    last_modified_date = None
    for update in updates:
        fields = update.get('fields', {})
        changed_field_value = None
        if changed_field:
            changed_field_value = fields.get(changed_field, {})
        revised_date = update.get('revisedDate', None)
        changed_by = update.get('revisedBy', {}).get('displayName', None)
        changed_date = fields.get('System.ChangedDate', {})
        if changed_date:
            revised_date = changed_date.get('newValue', revised_date)
        if changed_field:
            if changed_field_value and changed_field_value.get('newValue') and revised_date and changed_by == assigned_to:
                last_modified_date = revised_date
        else:
            if revised_date and changed_by == assigned_to:
                last_modified_date = revised_date
    
    return approved_date, last_modified_date[0:10] if last_modified_date else approved_date

def get_work_items_by_query(iteration_path):
    # url = f"http://***********:8080/tfs/{organization}/{project}/_apis/wit/wiql/{query_id}?api-version=6.0&$filter=System.IterationPath eq '{iteration_path}'"
    # response = requests.get(url, auth=('', personal_access_token))
    # response.raise_for_status()
    ###
    url = f"http://***********:8080/tfs/{organization}/{project}/_apis/wit/wiql?api-version=6.0"
    query = {
        "query": f"SELECT [System.Id] FROM workitems WHERE [System.IterationPath] = '{iteration_path}' AND [System.State] <> '已移除' AND [System.WorkItemType] = '任务' AND [System.AssignedTo] NOT IN ('曾远岚', '李皓', '郑燕琴', '叶家伟')"
    }
    response = requests.post(url, json=query, auth=('', personal_access_token))
    response.raise_for_status()
    
    work_items = response.json()['workItems']
    work_item_ids = [item['id'] for item in work_items]
    
    return work_item_ids

def query_work_items(work_item_ids):
    work_item_url = f'http://***********:8080/tfs/{organization}/_apis/wit/workitemsbatch?api-version=6.0'
    payload = {
        "ids": work_item_ids,
        "fields": [
            "System.State", "System.Title", "System.IterationPath", "System.AssignedTo", "Microsoft.VSTS.Scheduling.RemainingWork", "Microsoft.VSTS.Scheduling.Effort", "Microsoft.VSTS.Scheduling.OriginalEstimate",
            "Microsoft.VSTS.Common.Activity", "Microsoft.VSTS.Common.Priority", "System.WorkItemType", "System.CreatedDate", 
            "System.ChangedDate", "System.CreatedBy", "System.ChangedBy", "Microsoft.VSTS.Common.ClosedDate", "System.Parent", "Microsoft.VSTS.Common.BacklogPriority"]
    }
    response = requests.post(work_item_url, json=payload, auth=('', personal_access_token))
    response.raise_for_status()
    work_item_details = response.json()['value']
    result = []
    for item in work_item_details:
        row = {}
        row["工作项ID"] = item['id']
        row["工作项名称"] = item['fields'].get('System.Title', '-')
        row["状态"] = item['fields'].get('System.State', '待处理')
        row["负责人"] = item['fields'].get('System.AssignedTo', None)
        if row["负责人"] is not None:
            row["负责人"] = row["负责人"]["displayName"]
        row["剩余工时"] = item['fields'].get('Microsoft.VSTS.Scheduling.RemainingWork', 0)
        if item['fields'].get('Microsoft.VSTS.Scheduling.Effort', 0) is not None and item['fields'].get('Microsoft.VSTS.Scheduling.Effort', 0) > 0:
            row["计划工时"] = item['fields'].get('Microsoft.VSTS.Scheduling.Effort', 0)
        elif item['fields'].get('Microsoft.VSTS.Common.Activity', 0) is not None:
            row["计划工时"] = item['fields'].get('Microsoft.VSTS.Common.Activity', '0')
        row["优先级"] = item['fields'].get('Microsoft.VSTS.Common.Priority', '2')
        row["创建日期"] = item['fields'].get('System.CreatedDate', '')
        if row["创建日期"] is not None:
            row["创建日期"] = row["创建日期"][0:10]
        row["创建人"] = item['fields'].get('System.CreatedBy', 'Unassigned')["displayName"]
        row["完成日期"] = item['fields'].get('Microsoft.VSTS.Common.ClosedDate', '')
        if row["完成日期"] is not None:
            row["完成日期"] = row["完成日期"][0:10]
        row["修改日期"] = item['fields'].get('System.ChangedDate', '')
        if row["修改日期"] is not None:
            row["修改日期"] = row["修改日期"][0:10]
        row["修改人"] = item['fields'].get('System.ChangedBy', 'Unassigned')["displayName"]
        row["类型"] = item['fields'].get('System.WorkItemType', '任务')
        row["迭代"] = item['fields'].get('System.IterationPath', 'Unassigned')
        row["需求ID"] = item['fields'].get('System.Parent', None)
        row["排序"] = item['fields'].get('Microsoft.VSTS.Common.BacklogPriority', 0)
        result.append(row)
    # 获取需求优先级
    result = sorted(result, key=lambda x: x['排序'])  # 按“排序”字段排序
    return result

def query_tasks(iteration_path):
    work_item_ids = get_work_items_by_query(iteration_path)
    tasks = query_work_items(work_item_ids)
    requirement_ids = list(set([task['需求ID'] for task in tasks if task['需求ID'] is not None]))
    requirements = query_work_items(requirement_ids)
    requirements2 = {requirement['工作项ID']: requirement for requirement in requirements}
    for task in tasks:
        task['优先级'] = requirements2[task['需求ID']]['优先级'] #if task['优先级'] > requirements2[task['需求ID']]['优先级'] else task['优先级']
        task['排序'] = requirements2[task['需求ID']]['排序']
    return tasks, requirements


HOLIDAYS = [
    '2025-04-04', '2025-05-01', '2025-05-02', '2025-05-05', '2025-05-31', '2025-10-01', '2025-10-02', '2025-10-03', 
    '2025-10-06', '2025-10-07', '2025-10-08'
]
WORKDAYS = [
    '2025-04-27', '2025-09-28', '2025-10-11'
]

def is_workday(date):
    """判断是否为工作日"""
    if date.strftime('%Y-%m-%d') in WORKDAYS:  # 调休工作日
        return True
    if date.strftime('%Y-%m-%d') in HOLIDAYS:  # 节假日
        return False
    if date.weekday() >= 5:  # 周六和周日
        return False
    return True

def get_next_workday(start_date):
    """获取下一个工作日，跳过周末和假期"""
    current_date = start_date
    current_date += timedelta(days=1)
    
    # 跳过周末和假期
    while True:
        if is_workday(current_date):
            break
        current_date += timedelta(days=1)
    return current_date

def calculate_end_date_with_remainder(start_date, duration_hours, remaining_hours=8):
    """计算结束日期，跳过周末，并处理剩余小时"""
    current_date = start_date
    if remaining_hours == 8: # 新日期，需要调过周末
        if not is_workday(current_date):
            current_date = get_next_workday(current_date)
    if remaining_hours == 0: # 当日工时用尽，返回下一个工作日
        current_date = get_next_workday(current_date)
    # 计算剩余小时
    hours_remaining = remaining_hours - duration_hours
    if hours_remaining >= 0: # 当日工时足，返回当前工作日
        return current_date, hours_remaining
    else: # 当日工时不足，下一个工作日继续处理剩余小时
        current_date = get_next_workday(current_date)
        return calculate_end_date_with_remainder(current_date, abs(hours_remaining), 8)

def stat_task(iteration_path, output_excel_writer=None, excel_file_path=None, base_dir=None, start_date=None):
    from 项目统计 import BASE_DIR
    from 项目统计 import get_iteration_obj
    iteration_obj = get_iteration_obj(iteration_path)
    iteration_path = iteration_obj['name'] if iteration_obj else iteration_path
    base_dir = BASE_DIR if base_dir is None else base_dir
    
    def set_completion_format(work_items_df, worksheet, excel_columns, workbook):
        completion_col_index = excel_columns.index('完成率') + 1
        completion_col = chr(64 + completion_col_index)  # 将列索引转换为 Excel 列字母
        
        # 清除列中现有的格式
        worksheet.set_column(f'{completion_col}:{completion_col}', None, None)
        
        # 设置为百分比格式
        worksheet.set_column(f'{completion_col}:{completion_col}', None, workbook.add_format({'num_format': '0%'}))
        
        worksheet.conditional_format(
            f'{completion_col}2:{completion_col}{len(work_items_df) + 1}',  # 从第2行到最后一行
            {
                'type': 'data_bar',
                'bar_color': '#63BE7B',  # 绿色
                'data_bar_2010': True,  # 使用 Excel 2010 数据条样式
            }
        )
    
    excel_file_path = iteration_path.replace("\\","-")
    excel_file_path = f'{base_dir}/需求任务({excel_file_path}).xlsx'
    excel_writer = pd.ExcelWriter(excel_file_path, engine='xlsxwriter') if output_excel_writer is None else output_excel_writer
    #
    logger.info(f"\n---------------迭代工作量统计{iteration_path}---------------")
    tasks, requirements = query_tasks(iteration_path) 
    tmp_df = pd.DataFrame(tasks)
    tasks_df = tmp_df[
        (~tmp_df['负责人'].isin(['易礼仁', '张平', '钟俊蛟', '陈誉'])) & (tmp_df['迭代'] == iteration_path)
    ].copy()
    tasks_df['计划工时'] = pd.to_numeric(tasks_df['计划工时'], errors='coerce')
    tasks_df.loc[(tasks_df['状态'] != '完成') & (tasks_df['剩余工时'] == 0), '状态'] = '未评估'
    tasks_df.loc[(tasks_df['状态'] == '未评估'), '计划工时'] = 0
    tasks_df.loc[(tasks_df['状态'] == '未评估') & (tasks_df['剩余工时'] == 0), '剩余工时'] = tasks_df['计划工时']
    #
    logger.info(">>负责人工时统计...")
    grouped_df = tasks_df.groupby(['负责人']).agg({'工作项ID':'count', '计划工时': 'sum', '剩余工时': 'sum'}).reset_index()
    grouped_df['完成工时'] = grouped_df['计划工时'] - grouped_df['剩余工时']
    grouped_df['完成率'] = round(grouped_df['完成工时'] / grouped_df['计划工时'], 2)
    grouped_df.rename(columns={'工作项ID':'任务数'},inplace=True)
    excel_columns = ['负责人', '任务数', '计划工时', '完成工时', '完成率']
    grouped_df[excel_columns].to_excel(excel_writer, sheet_name='负责人工时统计', index=False)
    worksheet = excel_writer.sheets['负责人工时统计']
    # 设置完成率字段的颜色填充
    set_completion_format(grouped_df, worksheet, excel_columns, excel_writer.book)
    #
    logger.info(">>项目工时共计...")
    for requirement in requirements:
        requirement_tasks = tasks_df[tasks_df['需求ID'] == requirement['工作项ID']]
        requirement['计划工时'] = requirement_tasks['计划工时'].sum()
        requirement['剩余工时'] = requirement_tasks['剩余工时'].sum()
        requirement['完成工时'] = requirement_tasks['计划工时'].sum() - requirement_tasks['剩余工时'].sum()
        requirement['项目'] = next((name for name in PROJECT_NAMES if name.lower() in requirement['工作项名称'].lower()), 'EPSP')
        requirement['项目'] = PROJECT_ALIAS.get(requirement['项目'], requirement['项目'])
        requirement['创建日期'], requirement['修改日期'] = get_req_update_info(requirement['工作项ID'], requirement['负责人'], requirement['创建日期'], 'System.Description')
        if requirement['工作项名称'].startswith('【') and '】' in requirement['工作项名称']:
            requirement['工作项名称'] = requirement['工作项名称'].split('】', 1)[1].strip()
    #
    requirements_df = pd.DataFrame(requirements)
    grouped_df = requirements_df.groupby(['优先级', '项目']).agg({'工作项ID':'count', '计划工时': 'sum', '剩余工时': 'sum', '完成工时': 'sum'}).reset_index()
    grouped_df['完成率'] = round(grouped_df['完成工时'] / grouped_df['计划工时'], 2)
    grouped_df.rename(columns={'工作项ID':'需求数'},inplace=True)
    excel_columns = ['优先级', '项目', '需求数', '计划工时', '完成工时', '完成率']
    grouped_df[excel_columns].to_excel(excel_writer, sheet_name='项目工时统计', index=False)
    worksheet = excel_writer.sheets['项目工时统计']
    # 设置完成率字段的颜色填充
    set_completion_format(grouped_df, worksheet, excel_columns, excel_writer.book)
    #
    logger.info(">>需求工时统计...")
    work_items = []
    user_tasks = {}
    for requirement in requirements:
        requirement['类型'] = '需求'
        work_items.append(requirement)
        for task in tasks_df[tasks_df['需求ID'] == requirement['工作项ID']].to_dict(orient='records'):
            task['类型'] = '任务'
            task['项目'] = requirement['项目']
            task['工作项名称'] = '  ' + task['工作项名称']
            work_items.append(task)
            user_tasks.setdefault(task['负责人'], []).append(task)
    
    # 重新计算计划开始和结束日期
        
    start_date = iteration_obj['start_date'] if start_date is None else start_date
    start_date = datetime.strptime(start_date, '%Y-%m-%d')
    
    for user, tasks in user_tasks.items():
        remaining_hours = 8  # 初始化剩余小时
        for i, task in enumerate(tasks):
            if task['状态'] == '未评估':
                continue
            pre_task = None
            for j in range(i - 1, -1, -1):
                if tasks[j]['状态'] != '未评估':
                    pre_task = tasks[j]
                    break
            if pre_task is None:  # 如果没有前置任务，直接从迭代开始日期开始
                task['计划开始日期'] = start_date
                task['计划结束日期'], remaining_hours = calculate_end_date_with_remainder(start_date, task['计划工时'], remaining_hours)
            else:
                task['前置任务ID'] = pre_task['工作项ID']
                if remaining_hours == 0: # 如果剩余小时为0，下一个任务的开始日期为上一个任务的结束日期+1天，并且回避周末
                    task['计划开始日期'] = get_next_workday(pre_task['计划结束日期'])
                    remaining_hours = 8
                else: # 如果剩余小时不为0，下一个任务的开始日期为上一个任务的结束日期
                    task['计划开始日期'] = pre_task['计划结束日期']
                task['计划结束日期'], remaining_hours = calculate_end_date_with_remainder(task['计划开始日期'], task['计划工时'], remaining_hours)
    #
    work_items_df = pd.DataFrame(work_items)
    work_items_df['计划工时'] = pd.to_numeric(work_items_df['计划工时'], errors='coerce')
    work_items_df['完成工时'] = work_items_df['计划工时'] - work_items_df['剩余工时']
    work_items_df['完成率'] = round(work_items_df['完成工时'] / work_items_df['计划工时'], 2)
    work_items_df.loc[(work_items_df['类型'] == '需求'), '计划开始日期'] = work_items_df.loc[
        work_items_df['类型'] == '需求', '工作项ID'
    ].map(lambda req_id: work_items_df.loc[
        (work_items_df['类型'] == '任务') & (work_items_df['需求ID'] == req_id), '计划开始日期'
    ].min())

    work_items_df.loc[(work_items_df['类型'] == '需求'), '计划结束日期'] = work_items_df.loc[
        work_items_df['类型'] == '需求', '工作项ID'
    ].map(lambda req_id: work_items_df.loc[
        (work_items_df['类型'] == '任务') & (work_items_df['需求ID'] == req_id), '计划结束日期'
    ].max())
    work_items_df['计划开始日期'] = work_items_df['计划开始日期'].dt.strftime('%Y-%m-%d')
    work_items_df['计划结束日期'] = work_items_df['计划结束日期'].dt.strftime('%Y-%m-%d')
    work_items_df = work_items_df.sort_values(by=['优先级', '排序'], ascending=[True, True])  # 修改排序逻辑
    work_items_df.loc[work_items_df['类型'] == '任务', '项目'] = None
    # work_items_df[['项目', '类型', '工作项ID', '工作项名称', '优先级', '状态' , '负责人', '计划工时', '剩余工时', '完成工时', '完成率', '创建人', '创建日期', '完成日期']].to_excel(excel_writer, sheet_name='需求工时统计', index=False)
    excel_columns = ['项目', '类型', '工作项名称', '优先级', '状态', '负责人', '计划工时', '完成工时', '完成率', '创建日期', '完成日期', '计划开始日期', '计划结束日期']
    work_items_df[excel_columns].to_excel(
        excel_writer, sheet_name='需求任务详细', index=False
    )

    # 设置需求行的粗体格式
    worksheet = excel_writer.sheets['需求任务详细']
    workbook = excel_writer.book
    # 获取当前系统时间
    current_date = datetime.now().strftime('%Y-%m-%d')
    
    # 设置条件格式：如果状态是“待处理”或“正在进行”，并且计划结束时间小于当前时间，则背景为黄色
    yellow_columns = ['计划开始日期', '计划结束日期']
    for col in yellow_columns:
        col_index = excel_columns.index(col) + 1  # 获取列索引
        col_letter = chr(64 + col_index)  # 将列索引转换为 Excel 列字母
        
        plan_end_col_index = excel_columns.index('计划结束日期') + 1  # 获取计划结束日期列索引
        plan_end_col_letter = chr(64 + plan_end_col_index)  # 将计划结束日期列索引转换为 Excel 列字母
        
        worksheet.conditional_format(
            f'{col_letter}2:{col_letter}{len(work_items_df) + 1}',  # 计划结束日期列范围
            {
                'type': 'formula',
                'criteria': f'=AND(OR(E2="待处理", E2="正在进行"), {plan_end_col_letter}2<"{current_date}")',
                'format': workbook.add_format({'bg_color': '#FFFF00'})  # 黄色背景
            }
        )
    req_format1 = workbook.add_format({'bold': True})
    req_format2 = workbook.add_format({'bold': True, 'bg_color': '#CDDBF7'})
    even_row_format = workbook.add_format({'bg_color': '#CDDBF7'})  # 淡蓝色背景

    for row_num, row_data in enumerate(work_items_df.itertuples(), start=1):  # 从第1行开始
        if row_data.类型 == '需求':
            worksheet.set_row(row_num, None)  # 清除整行格式
            for col_num, col_name in enumerate(excel_columns, start=1):
                if col_name in ['完成率', '计划开始日期', '计划结束日期']:
                    continue
                col_letter = chr(64 + col_num)  # 将列索引转换为 Excel 列字母
                worksheet.write(f'{col_letter}{row_num + 1}', row_data.__getattribute__(col_name), req_format1)

    # 设置完成率字段的颜色填充
    set_completion_format(work_items_df, worksheet, excel_columns, excel_writer.book)

    # 隐藏 B 列
    # worksheet.set_column('B:B', None, None, {'hidden': True})
    # worksheet.set_column('C:C', None, None, {'hidden': True})
    #
    if output_excel_writer is None:
        excel_writer.close()
    logger.info(f"=========================================================")

if __name__ == "__main__":
    iteration_path = input("请输入迭代路径：")
    stat_task(iteration_path)
    # print(get_req_update_info(12539, '李皓', '2025-09-28', 'System.Description'))