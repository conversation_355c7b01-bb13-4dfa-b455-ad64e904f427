from jira import JIRA

jira = JIRA('http://172.20.4.54:18080/', basic_auth=('wanghaifeng', 'kingwang'))

def create_issue():
    issue_dict = {
        'project': {
            'key': 'YBDR'
        },
        'issuetype': {
            'name': '数据提取/处理单'
        },
        'summary': '创建一个测试单',
        # 'customfield_10023': {'value': 'EPSP'},
        'customfield_10208': {'value': 'EPSP系统'},
        # 'customfield_10211': [{'value': '高'}],
        'customfield_10412': {'value': '数据提取'},
        'customfield_10409': '测试数据处理'
    }
    new_issue = jira.create_issue(fields=issue_dict)
    return new_issue.id
# print(jira.projects())

def print_issue(issue_id):
    issue = jira.issue(issue_id)
    issue_fields = issue.fields
    for p in dir(issue_fields):
        print(f"{p}: {getattr(issue_fields, p)}")

def add_attachment(issue_id, attach_file, display_name):
    jira.add_attachment(issue_id, attach_file, display_name)
    
def add_comment(issue_id, comment):
    jira.add_comment(issue_id, comment)
    
def update_issue(issue_id):
    issue = jira.issue(issue_id)
    issue.update(fields={'summary': '使用Rest API创建一个测试单',})

def assign_issue(issue_id, user='wanghaifeng'):
    jira.assign_issue(issue_id, user)
    
def find_transition_id(issue_id):
    issue = jira.issue(issue_id)
    i = 1
    while i < 13000:
        try:
            jira.transition_issue(issue, transition=f'{i}', comment='第二次提交')
            print(i)
            break
        except Exception:
            pass
        i += 1
    return i

if __name__ == '__main__':
    issue_id = create_issue()
    issue = jira.issue(issue_id)
    print(f"issue: {issue}")
    
    add_comment(issue_id, '添加一个评论')
    assign_issue(issue_id, 'wanghaifeng')
    update_issue(issue_id)
    add_attachment(issue_id, 'D:/TMP/1.jpg', '4.jpg')
    
    jira.transition_issue(issue, transition='281', comment='第一次审批')  #第一次提交
    assign_issue(issue_id, 'yiliren')
    jira.transition_issue(issue, transition='421', comment='第二次审批')  #第一次提交
    
    print(jira.transitions(issue_id))
