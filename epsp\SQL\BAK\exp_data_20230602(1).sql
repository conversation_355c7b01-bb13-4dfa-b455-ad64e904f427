﻿insert into tmp_cust_0602(customer_code, name) select c.<PERSON>USTOMER_CODE,c.NAME from epsp.v_cum_customer_info c where c.PLAT_CUSTOMER_CODE='562970003311213';
insert into tmp_cust_0602(customer_code, name) select c.CUSTOMER_CODE,c.NAME from epsp.v_cum_customer_info c where c.CUSTOMER_CODE in('562970003311213','562201003747149');
insert into tmp_cust_0602(customer_code, name) select c.CUSTOMER_CODE,c.NAME from epsp.v_cum_customer_info c where c.CUSTOMER_CODE in('***************','***************');
insert into tmp_cust_0602(customer_code, name) select c.CUSTOMER_CODE,c.NAME from epsp.v_cum_customer_info c where c.CUSTOMER_CODE in(select customer_code from tmp_cust_0608);

create table tmp_cust_0608 as 
select source_customer_code as customer_code,source_customername,sum(amount)/100 as total_amount,count(0) as total_count from tmp_split_record_0608_2 group by source_customer_code,source_customername;

select * from tmp_cust_0602 where state is null;
select distinct t.业务代码 from tmp_pay_0602 t where t.batch_no=609;

select  /*+parallel(10)*/ distinct f.transactiontype from epsp.acc_accountflow f where f.accountcode in(select 'JY-A'||customer_code from tmp_cust_0602);
select  /*+parallel(10)*/* from epsp.acc_accountflow f where f.accountcode in(select 'JY-A'||customer_code from tmp_cust_0602) and f.transactiontype='TZ';

select 
  sum(case when t.业务代码 IN('EnterpriseUnion','AliNative','ACS')  then t.总金额 else 0 end) as INAmount,
  sum(case when t.业务代码 IN('EnterpriseUnion','AliNative','ACS')  then t.总手续费 else 0 end) as INFEE,
  sum(case when t.业务代码 IN ('Withdraw','WithdrawToSettmentDebit') then t.总金额 else 0 end) as OutAmount,
  sum(case when t.业务代码 IN ('Withdraw','WithdrawToSettmentDebit') then t.总手续费 else 0 end) as OutFEE,
  1472870.52 as account_balance
from tmp_pay_0602 t where t.batch_no=611;

select  sum(a1.availablebalance)+sum(a1.floatbalance)+sum(a2.availablebalance/100) as total_balance
from v_cust_stat_0602 v 
  inner join epsp.acc_account a1 on 'JY-A'||v.商户号=a1.code
  left join epsp.acc_account a2 on 'BJ-B'||v.商户号=a2.code;

select * from epsp.pas_acct_quota_record t where t.customer_code in(select customer_code from tmp_cust_0602) ;
select t.change_type,sum(t.amount)/100 from epsp.pas_acct_quota_record t where t.customer_code in(select customer_code from tmp_cust_0602) and t.acct_state='00' group by t.change_type ;

select /*+parallel(10)*/ (t.transactiontype), count(*), sum(t.amount)/100 
from epsp.acc_accountflow t where t.accountcode in (select t1.account_code from tmp_cust_0602 t1)
group by t.transactiontype;

1   66  0
2 TK  5 -8
3 ZHFZ  ********  -**********.52
4 JQ  618 0
5 ZHFZCX  ********  **********.2
6 ZF  10024 **********.39
7 OCR 6476  0
8 TH  117 5432856.71
9 FZJY  16512 **********.26
10  TZ  380 ********.37
11  TX  103226  -**********.17

SELECT  /*+parallel(10)*/
	count(*),
	sum(t.TOTAL_FEE / 100) AS 金额元 , 
	sum(t.PROCEDURE_FEE / 100) AS 手续费元
	FROM epsp.TXS_WITHDRAW_TRADE_ORDER t
WHERE 
	t.CUSTOMER_CODE in (select t1.customer_code from tmp_cust_0602 t1 )
	and t.pay_state='00' 
	and (t.th_state is null or t.th_state = '0');

select min(a.createdatetime),max(a.updatedatetime) from epsp.acc_account a where a.customercode in(select customer_code from tmp_cust_0608);

select t.商户号,sum(t.总笔数) as 总笔数 from tmp_pay_0602 t group by t.商户号 order by sum(t.总笔数) desc;
select t.业务代码,sum(t.总笔数) as 总笔数 from tmp_pay_0602 t group by t.业务代码 order by sum(t.总笔数) desc;
select t.商户号,t.业务代码,sum(t.总笔数) as 总笔数 from tmp_pay_0602 t group by t.商户号,t.业务代码 order by sum(t.总笔数) desc;

select * from tmp_pay_0602 t where t.batch_no is null;
select * from tmp_pay_0602 t where t.batch_no=611;
select ********.15 - ********.26 from dual;
select sum(t.总金额) from tmp_pay_0602 t where t.batch_no=611 and t.业务代码='ACS';
delete from tmp_pay_0602 t where t.batch_no=611 and t.业务代码='ACS';
--update tmp_pay_0602 t set t.batch_no=611 where t.batch_no is null;
---------------OUT------------------------
insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(10)*/t.CUSTOMER_CODE AS 商户编号,t.CUSTOMERNAME AS 商户名称, 
       t.BUSINESS_CODE  AS 业务代码, b.NAME AS 业务名称 ,
       sum(t.TOTAL_FEE) / 100  AS 总金额, sum(t.PROCEDURE_FEE) / 100 AS 总手续费,
       count(t.TRANSACTION_NO) AS 总笔数,to_char(max(t.CREATE_TIME),'yyyy-mm-dd hh24:mi:ss') AS 最后一笔时间
FROM epsp.TXS_WITHDRAW_TRADE_ORDER t 
  INNER JOIN tmp_cust_0602 c ON t.CUSTOMER_CODE =c.CUSTOMER_code and c.state is null
  LEFT JOIN epsp.PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE 
WHERE t.PAY_STATE ='00'
GROUP BY t.CUSTOMER_CODE ,t.CUSTOMERNAME,t.BUSINESS_CODE ,b.NAME ;

insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(10)*/t.CUSTOMER_CODE AS 商户编号,t.CUSTOMERNAME AS 商户名称, 
       t.BUSINESS_CODE||'Z'  AS 业务代码, b.NAME AS 业务名称 ,
       sum(t.TOTAL_FEE) / 100  AS 总金额, sum(t.PROCEDURE_FEE) / 100 AS 总手续费,
       count(t.TRANSACTION_NO) AS 总笔数,to_char(max(t.CREATE_TIME),'yyyy-mm-dd hh24:mi:ss') AS 最后一笔时间
FROM epsp.TXS_WITHDRAW_TRADE_ORDER t 
  INNER JOIN tmp_cust_0602 c ON t.CUSTOMER_CODE =c.CUSTOMER_code and c.state is null
  LEFT JOIN epsp.PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE 
WHERE t.PAY_STATE ='00' and t.id>0
GROUP BY t.CUSTOMER_CODE ,t.CUSTOMERNAME,t.BUSINESS_CODE ,b.NAME ;

insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(10)*/t.CUSTOMER_CODE AS 商户编号,t.CUSTOMERNAME AS 商户名称, 
       t.BUSINESS_CODE||'F'  AS 业务代码, b.NAME AS 业务名称 ,
       sum(t.TOTAL_FEE) / 100  AS 总金额, sum(t.PROCEDURE_FEE) / 100 AS 总手续费,
       count(t.TRANSACTION_NO) AS 总笔数,to_char(max(t.CREATE_TIME),'yyyy-mm-dd hh24:mi:ss') AS 最后一笔时间
FROM epsp.TXS_WITHDRAW_TRADE_ORDER t 
  INNER JOIN tmp_cust_0602 c ON t.CUSTOMER_CODE =c.CUSTOMER_code and c.state is null
  LEFT JOIN epsp.PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE 
WHERE t.PAY_STATE ='00' and t.id<0
GROUP BY t.CUSTOMER_CODE ,t.CUSTOMERNAME,t.BUSINESS_CODE ,b.NAME ;
---------------PAY------------------------
insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(10)*/t.CUSTOMER_CODE AS 商户号,t.CUSTOMERNAME AS 商户名称, 
       t.BUSINESS_CODE  AS 业务代码, b.NAME AS 业务名称 ,
       sum(t.AMOUNT) / 100 AS 总金额, sum(t.PROCEDURE_FEE) / 100 AS 总手续费, count(t.TRANSACTION_NO) AS 总笔数, to_char(max(t.CREATE_TIME),'yyyy-mm-dd hh24:mi:ss') AS 最后一笔时间
FROM epsp.TXS_PAY_TRADE_ORDER t 
  INNER JOIN info.tmp_cust_0602 c ON t.CUSTOMER_CODE =c.CUSTOMER_CODE and c.state is null
  LEFT JOIN epsp.PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE 
WHERE t.STATE ='00' AND t.Transaction_Type<>'ZHFZ'
GROUP BY t.CUSTOMER_CODE ,t.CUSTOMERNAME,t.BUSINESS_CODE ,b.NAME ;
-------------------POS-----------------------
insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(5)*/MERCHANT_NO, a.name,'POS', 'POS', sum(AMOUNT) as total_amount, 0 as fee, count(1) as cnt,null
from epsp.ZHY_POSP_RECORD t 
     inner join  tmp_cust_0602 a on t.merchant_no=a.customer_code  and a.state is null
where CREATION_TIME >= timestamp '2020-05-14 00:00:00'
  and CREATION_TIME < timestamp '2023-05-26 00:00:00'
group by MERCHANT_NO,a.name;
-------------------ACS----------------------
insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(5)*/t.CUSTOMER_CODE AS 商户号,c.name,'ACS','ACS' ,
       sum(t.AMOUNT) / 100 AS 总金额, sum(t.PROCEDURE_FEE) / 100 AS 总手续费,
       count(t.TRANSACTION_NO) AS 总笔数,null
FROM epsp.PAS_ACCT_QUOTA_RECORD t
  INNER JOIN info.tmp_cust_0602 c ON t.CUSTOMER_CODE =c.CUSTOMER_CODE and c.state is null
WHERE t.FUND_TYPE !='7' AND t.ACCT_STATE ='00' and t.change_type='1'
GROUP BY t.CUSTOMER_CODE,c.name;

SELECT sum(t.amount)
FROM epsp.PAS_ACCT_QUOTA_RECORD t,
     tmp_cust_0602 t1
WHERE t.customer_code = t1.customer_code
  and t.ACCT_STATE = '00'
  and t.CHANGE_TYPE = '1'
  and t.fund_type != '7';
-------------------XX----------------------
insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(5)*/t.customer_code,t.customer_name,'XX','XX',sum(t.amt) / 100 as total_amount, sum(t.procedure_fee) as total_fee, count(0), null
from epsp.chk_xx_tran_record t 
     inner join tmp_cust_0602 c on t.customer_code=c.customer_code and  c.state is null
where t.create_time > timestamp '2020-05-14 00:00:00' and t.create_time < timestamp '2023-05-26 00:00:00'
group by t.customer_code,t.customer_name;
-------------------FZ----------------------
insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(10)*/t.source_customer_code,c.name,'FZ-OUT','FZ-分出',sum(t.amount) / 100 as total_amount,sum(t.procedurefee), count(0) as cnt,null
from epsp.txs_split_record t 
     inner join  tmp_cust_0602 c on t.source_customer_code=c.customer_code  and c.state is null
where t.create_time > timestamp '2020-05-14 00:00:00' and t.create_time < timestamp '2023-05-26 00:00:00'  
      and t.state='3' and t.customer_code <> t.source_customer_code
group by t.source_customer_code,c.name;

insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(10)*/t.customer_code,c.name,'FZ-IN','FZ-分入',sum(t.amount) / 100 as total_amount,sum(t.procedurefee), count(0) as cnt,null
from epsp.txs_split_record t 
     inner join  tmp_cust_0602 c on t.customer_code=c.customer_code  and c.state is null
where t.create_time > timestamp '2020-05-14 00:00:00' and t.create_time < timestamp '2023-05-26 00:00:00'  
      and t.state='3' and t.customer_code <> t.source_customer_code
group by t.customer_code,c.name;

-------------------Refund----------------------
insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(5)*/t.customer_code,t.customername,'Refund', 'Refund',sum(t.refund_fee)/100,sum(t.procedure_fee)/100,count(0), null 
from epsp.txs_refund_pre_order t 
     inner join  tmp_cust_0602 c on t.customer_code=c.customer_code and c.state is null
where t.pay_state='00'
group by t.customer_code,t.customername;

SELECT /*+parallel(5)*/t.商户号,t.商户名称,t.业务代码,count(0) from tmp_pay_0602 t group by t.商户号,t.商户名称,t.业务代码 having count(0) > 1 order by t.商户号,t.商户名称,t.业务代码;
SELECT /*+parallel(5)*/* from tmp_pay_0602 t where t.商户号='***************' and t.业务代码='XX';

update tmp_pay_0602 t set t.商户名称=(select c.name from tmp_cust_0602 c where c.customer_code=t.商户号);

select  v.*,a1.availablebalance/100 as 可用余额,a1.floatbalance/100 as 在途余额,a2.availablebalance/100 as 待分账余额
from v_cust_stat_0602 v 
  inner join epsp.acc_account a1 on 'JY-A'||v.商户号=a1.code
  left join epsp.acc_account a2 on 'BJ-B'||v.商户号=a2.code;
  
select * from(
  select 商户号,商户名称,业务名称,sum(总金额NEW) as 总金额NEW,sum(总金额OLD) as 总金额OLD from (
  select t.商户号,t.商户名称,t.业务名称,
    (case when batch_no=611 then t.总金额 else 0 end)/2 as 总金额NEW, 
    (case when batch_no=610 then t.总金额 else 0 end) as 总金额OLD 
  from tmp_pay_0602 t where t.业务代码<>'AccountSplit'
) a 
group by 商户号,商户名称,业务名称) where 总金额NEW<>总金额OLD order by 商户号;

select sum(v.FZ分出),sum(v.FZ分出笔数),sum(v.FZ分入),sum(v.FZ分入笔数) from v_cust_stat_0602 v
union all
select sum(v.FZ分出),sum(v.FZ分出笔数),sum(v.FZ分入),sum(v.FZ分入笔数) from v_cust_stat_0606 v;
--
SELECT /*+parallel(5)*/ t.CUSTOMER_CODE AS 商户编号 ,c.NAME AS 商户名称 ,
  to_char(t.CREATE_TIME,'yyyy-mm') AS 月份,
  sum(t.procedure_fee) / 100 AS 手续费
FROM epsp.TXS_PAY_TRADE_ORDER t
  INNER JOIN info.tmp_cust_0602 c ON t.customer_code =c.customer_code
WHERE t.state='00'
  and t.create_time >=timestamp'2022-06-02 00:00:00'
  and t.create_time < timestamp'2023-05-26 00:00:00'
  and t.TRANSACTION_TYPE != 'ZHFZ'
GROUP BY t.CUSTOMER_CODE,c.NAME,
  to_char(t.CREATE_TIME,'yyyy-mm');
  
-------------分账交易手续费-------------
SELECT /*+parallel(5)*/ t.CUSTOMER_CODE AS 商户编号 ,c.NAME AS 商户名称,
  to_char(t.CREATE_TIME,'yyyy-mm') AS 月份, 
  sum(tsr.procedurefee)/100 as 手续费
FROM epsp.TXS_SPLIT_ORDER t
  INNER JOIN info.tmp_cust_0602 c ON t.CUSTOMER_CODE =c.CUSTOMER_CODE
  INNER JOIN epsp.TXS_SPLIT_RECORD tsr ON t.TRANSACTION_NO = tsr.TRANSACTION_NO
WHERE t.create_time >=timestamp'2023-03-02 00:00:00'
  and t.create_time < timestamp'2023-05-26 00:00:00'
  and t.STATE = '00' AND t.procedure_customer_code=t.customer_code
GROUP BY t.CUSTOMER_CODE,c.NAME,
  to_char(t.CREATE_TIME,'yyyy-mm');


SELECT t.CUSTOMER_CODE as 商户编号, c.NAME as 商户名称, to_char(t.CREATE_TIME,'yyyy-mm') AS 月份, 
  sum(t.procedure_fee)/100 as 手续费
FROM epsp.PAS_ACCT_QUOTA_RECORD t
  INNER JOIN info.tmp_cust_0602 c ON t.CUSTOMER_CODE = c.CUSTOMER_CODE
WHERE t.ACCT_STATE = '00' and t.AUDIT_STATE = '00' and t.CHANGE_TYPE = '1'
GROUP BY t.CUSTOMER_CODE,c.NAME,
  to_char(t.CREATE_TIME,'yyyy-mm');
  
--
create table tmp_split_record_0608_2 as
select  /*+parallel(10)*/ t.id,t.transaction_no,t.source_customer_code,t.source_customername,t.customer_code,t.customername,t.amount
from epsp.txs_split_record t
where t.create_time > timestamp '2020-05-14 00:00:00' and t.create_time < timestamp '2023-05-26 00:00:00' and t.state='3'
  and t.source_customer_code not in(select customer_code from tmp_cust_0602) 
  and t.customer_code in(select customer_code from tmp_cust_0602);
  
select  /*+parallel(10)*/ t.id,t.transaction_no,t.source_customer_code,t.source_customername,t.customer_code,t.customername,t.amount
from epsp.txs_split_record t
where t.create_time > timestamp '2020-05-14 00:00:00' and t.create_time < timestamp '2023-05-26 00:00:00' and t.state='3'
  and t.source_customer_code in(select customer_code from tmp_cust_0602) 
  and t.customer_code not in(select customer_code from tmp_cust_0602);
  
select source_customer_code,source_customername,sum(amount)/100,count(0) from tmp_split_record_0608_2 group by source_customer_code,source_customername;

select sum(t.orgi_total_fee) from epsp.txs_tuihui_record t where t.customercode in(select customer_code from tmp_cust_0602);

select customer_code,customername,sum(amount)/100,count(0) from tmp_split_record_0608_2 group by customer_code,customername;

select min(t.createdatetime),max(t.updatedatetime) from epsp.acc_account t where t.customercode in('***************','***************');
