import http.client, urllib.parse
import json
import cx_Oracle

def get(host, uri, params={}, body=None, headers={}):
    con = http.client.HTTPConnection(host, timeout=10)
    con.request("GET", uri+urllib.parse.urlencode(params), body, headers)
    reader = con.getresponse()
    data = reader.read().decode("utf-8")
    return data

def insert(data):
    print('inserting '+str(data))
    db = cx_Oracle.connect('efps01/efps01@**********/testdb')
    cursor = db.cursor();
    row = cursor.execute('SELECT * FROM MO_CUSTOMER_ACCESS_IPS WHERE SIGNSN=:a AND IP=:b AND URL=:c AND CREATE_TIME > trunc(sysdate)',
                   a=data['signsn'], b =data['ip'], c=data['url'])
    found = False
    for x in row:
        found = True
    if found == False:
        cursor.execute('INSERT INTO MO_CUSTOMER_ACCESS_IPS(SIGNSN, IP, URL, ACCESS_COUNT) VALUES (:a, :b, :c, :d)',
                       a=data['signsn'], b =data['ip'], c=data['url'], d=data['count'])
        db.commit()
    db.close()

def save_ip():
    body = """
    {
        "track_total_hits":true,
        "query":{
            "bool": {
                "must": [
                    {
                        "exists": {
                            "field": "payer_id"
                        }
                    },
                    {
                        "exists": {
                            "field": "remark"
                        }
                    }
                ],
                "must_not": [
                    {
                        "term": {
                            "split_model": {
                                "value": ""
                            }
                        }
                    }
                ]
            }
        },
        "aggs": {
            "result": {
                "terms": {
                    "script": {
                        "inline": "doc['customer_code'].value +','+ doc['business_code'].value +','+ doc['pay_method'].value "
                    },
                    "size":1000000
                }
            }
        }
    }
    """
    content = get('***********:9200', '/txs_pay_trade_order_2201/_doc/_search', body=body, headers={'Content-Type': 'application/json'})
    jo = json.loads(content)
    buckets = jo['aggregations']['result']['buckets']
    for b in buckets:
        s = b['key'].split(',')
        data = {'signsn':s[0], 'ip':s[1], 'url':s[2], 'count':b['doc_count']}
        insert(data)

def send_mail():
    import smtplib
    from email.mime.text import MIMEText
    from email.header import Header

    # 创建 SMTP 对象
    smtp = smtplib.SMTP()
    # 连接（connect）指定服务器
    smtp.connect("smtp.126.com", port=25)
    # 登录，需要：登录邮箱和授权码
    smtp.login(user="<EMAIL>", password="UUSCLRAZVPSYGVNA")

    # 构造MIMEText对象，参数为：正文，MIME的subtype，编码方式
    message = MIMEText('atukoon 邮件发送测试...', 'plain', 'utf-8')
    message['From'] = Header("fairly", 'utf-8')  # 发件人的昵称
    message['To'] = Header("jack", 'utf-8')  # 收件人的昵称
    message['Subject'] = Header('Python SMTP 邮件测试', 'utf-8')  # 定义主题内容
    print(message)

    smtp.sendmail(from_addr="<EMAIL>", to_addrs="<EMAIL>", msg=message.as_string())

if __name__ == '__main__':
    save_ip()