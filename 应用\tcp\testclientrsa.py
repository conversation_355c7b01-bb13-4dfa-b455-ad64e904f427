import socket
import ssl
import sys
from datetime import datetime

def create_ssl_context():
    """创建类似浏览器的SSL上下文"""
    context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
    
    # 放宽TLS版本限制，支持TLS 1.0到1.3
    context.minimum_version = ssl.TLSVersion.TLSv1
    context.maximum_version = ssl.TLSVersion.TLSv1_3
    
    # 使用更宽松的加密套件配置
    context.set_ciphers('DEFAULT')
    
    try:
        # 尝试加载系统CA证书
        context.load_default_certs()
        
        # 加载自定义CA证书
        context.load_verify_locations(
            cafile='C:/haproxy-3.2/certs/rsa.pem',
            capath='C:/haproxy-3.2/certs'
        )
        
    except Exception as e:
        print(f"证书加载错误: {e}")
        # 添加详细的错误信息
        if isinstance(e, ssl.SSLError):
            print(f"SSL错误详情: {str(e)}")
        sys.exit(1)
    
    # 设置验证选项 - 放宽验证要求
    context.verify_mode = ssl.CERT_OPTIONAL
    context.check_hostname = False

    # 添加调试日志
    print("SSL上下文已创建，支持TLS 1.0-1.3，使用默认加密套件")
    
    return context

def main():
    # SERVER_HOST = '************'
    # SERVER_PORT = 9443
    # AD 国密
    SERVER_HOST = '*************'
    SERVER_PORT = 9999
    
    # 创建socket
    client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    client.settimeout(10.0)  # 设置超时
    
    # 创建SSL上下文
    context = create_ssl_context()
    
    # 包装socket
    secure_client = context.wrap_socket(
        client,
        server_hostname=SERVER_HOST,
        do_handshake_on_connect=True
    )
    
    try:
        # 连接服务器
        secure_client.connect((SERVER_HOST, SERVER_PORT))
        
        # 打印连接信息
        cipher = secure_client.cipher()
        cert = secure_client.getpeercert()
        print(f"\n=== 安全连接信息 ===")
        print(f"TLS版本: {secure_client.version()}")
        print(f"加密套件: {cipher[0]}")
        print(f"证书信息: {cert['subject']}")
        print(f"证书有效期: {cert['notBefore']} 到 {cert['notAfter']}")
        print("================\n")
        
        while True:
            message = input("请输入要发送的消息 (输入'quit'退出): ")
            if message.lower() == 'quit':
                break
            
            # 发送数据
            secure_client.send(message.encode())
            
            # 接收响应
            response = secure_client.recv(1024)
            print("\n服务器响应:")
            try:
                print(response.decode('utf-8'))
            except UnicodeDecodeError:
                print("二进制响应:", response.hex())
                
    except ssl.SSLError as e:
        print(f"SSL错误: {e}")
    except socket.timeout:
        print("连接超时")
    except ConnectionRefusedError:
        print("连接被拒绝，请检查服务器状态")
    except Exception as e:
        print(f"错误: {e}")
    finally:
        secure_client.close()
        print("\n连接已关闭")

if __name__ == "__main__":
    main()
