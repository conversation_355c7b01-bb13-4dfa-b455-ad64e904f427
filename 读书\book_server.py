from flask import Flask, Response
import mysql.connector
import os
import re
import urllib.parse

###########################################################

username = 'root'
password = ''
host = 'localhost'
database = 'book2'

# 创建一个MySQL连接池对象，命名为cnx_pool
cnx_pool = mysql.connector.pooling.MySQLConnectionPool(
    # 连接池的名称，可用于标识和管理连接池
    pool_name="mypool",
    # 连接池的大小，即连接池最多可以容纳的连接数量
    pool_size=32,
    # 连接MySQL数据库的用户名
    user=username,
    # 连接MySQL数据库的密码
    password=password,
    # MySQL数据库所在的主机地址
    host=host,
    # 要连接的MySQL数据库的名称
    database=database,
    # 连接超时时间，单位为秒，这里设置为300秒（即5分钟）
    connection_timeout=300 
)

def execute_select_sql(sql, params=None):
    """
    执行SQL查询语句
    :param sql: 要执行的SQL查询语句
    :param params: SQL查询参数，默认为None
    :return: 返回查询结果，类型为列表，每个元素为一行数据
    """
    db = cnx_pool.get_connection()
    cursor = db.cursor()
    try:
        cursor.execute(sql, params)
        result = cursor.fetchall()
        return result
    finally:
        cursor.close()
        db.close()
###########################################################

app = Flask(__name__)

from flask import render_template
from flask import Flask, request

@app.route('/')
def home(page=1, page_size=50):
    return index(page, page_size)

@app.route('/index/<int:page>/<int:page_size>')
def index(page=1, page_size=50):
    page = max(1, page)
    book_name= request.args.get('q')
    if book_name is None:
        book_name = ""
    sql = "SELECT book, book_id, chapter_count FROM bk_book where book like '%{}%' order by read_count desc, chapter_count desc limit {}, {}".format(book_name, (page-1)*page_size, page_size)
    books = execute_select_sql(sql)
    half = int(len(books)/2)
    return render_template('index.html', title='我的书单', books=books[:half],q=book_name, books2=books[half:], page=page, page_size=page_size)

@app.route('/book/<book_id>')
def book(book_id):
    sql = "SELECT book, book_id, chapter_count FROM bk_book where book_id = '{}'".format(book_id)
    books = execute_select_sql(sql)
    if len(books) == 0:
        return '404'
    book_name = books[0][0]
    return render_template('book.html', book_name=book_name, book_id=book_id)

@app.route('/download/<book_id>')
def download(book_id):
    def generator():
        page_size = 1000
        page = 1
        sql = "SELECT title, content FROM bk_chapter where book_id='{}' order by id".format(book_id)
        while True:
            chapters = execute_select_sql(sql + " limit {}, {}".format((page-1)*page_size, page_size))
            if len(chapters) == 0:
                break
            for chapter in chapters:
                yield fix_content(chapter[1], html_style=False, split_show=False)
            page += 1
    sql = "SELECT book, book_id, chapter_count FROM bk_book where book_id = '{}'".format(book_id)
    books = execute_select_sql(sql)
    if len(books) == 0:
        return '404'
    book_name = books[0][0]
    #
    response = Response(generator(), mimetype='text/plain; charset=utf-8')
    file_name = urllib.parse.quote(book_name.encode('utf-8'))
    response.headers.set('Content-Disposition', 'attachment', filename=f'{file_name}.txt')
    return response

@app.route('/chapters/<book_id>/<int:page>/<int:page_size>')
def chapters(book_id,page=1, page_size=30):
    sql = "SELECT book, book_id, chapter_count FROM bk_book where book_id = '{}'".format(book_id)
    books = execute_select_sql(sql)
    if len(books) == 0:
        return '404'
    book_name = books[0][0]
    chapters = execute_select_sql("SELECT id, title,read_count FROM bk_chapter where book_id='{}' limit {}, {}".format(book_id, (page-1)*page_size, page_size))
    return render_template('chapters.html', book_name=book_name, chapters=chapters, page=page, page_size=page_size)

@app.route('/read/<book_id>')
def read(book_id):
    chapters = execute_select_sql("SELECT id FROM bk_chapter where book_id='{}' order by id limit 1".format(book_id))
    if len(chapters) == 0:
        return '404'
    return chapter(chapters[0][0])

@app.route('/chapter/<chapter_id>')
def chapter(chapter_id, content = None):
    chapters = execute_select_sql("SELECT book, title, content, book_id,read_count FROM bk_chapter where id={}".format(chapter_id))
    if len(chapters) == 0:
        return '找不到该章节'
    book_name = chapters[0][0]
    title = chapters[0][1]
    if content is None:
        content = chapters[0][2]
    else:
        content = f"<b>{content}</b><br>\r"+chapters[0][2]
    book_id = chapters[0][3]
    
    cnx = cnx_pool.get_connection()
    try:
        cursor = cnx.cursor()
        cursor.execute("update bk_book set read_time=now(), read_count=read_count+1 where book_id='{}'".format(book_id))
        cursor.execute("update bk_chapter set read_count=read_count+1 where id={}".format(chapter_id))
        cnx.commit()
    finally:
        cursor.close()
        cnx.close()

    content1, content2 = fix_content(content, html_style=True, split_show=True)
    return render_template('chapter.html', book_name=book_name, title=title, content1=content1, content2=content2, chapter_id=chapter_id)

@app.route('/chapter_next/<chapter_id>')
def chapter_next(chapter_id):
    chapters = execute_select_sql("SELECT id FROM bk_chapter where book=(select book from bk_chapter where id={}) and id > {} order by id asc limit 1".format(chapter_id, chapter_id))
    if len(chapters) == 0:
        return chapter(chapter_id, content = '没有下一章了')
    else:
        return chapter(chapters[0][0])

@app.route('/chapter_prev/<chapter_id>')
def chapter_prev(chapter_id):
    chapters = execute_select_sql("SELECT id FROM bk_chapter where book=(select book from bk_chapter where id={}) and id < {} order by id desc limit 1".format(chapter_id, chapter_id))
    if len(chapters) == 0:
        return chapter(chapter_id, content = '没有上一章了')
    else:
        return chapter(chapters[0][0])

keywords = []
def fix_content(content, html_style=True, split_show=False):
    keywords= [r'「请记住邮箱：ltxsba\xa0@\xa0Gmail.com\xa0无法打开网站可发任意内容找回最新地址」',
               r'『地址发布邮箱\xa0ltxsba\xa0@\xa0gmail.com』',
               r'【收藏不迷路!:\xa0以备不时之需】',
               r'【收藏不迷路!:WWW.wwW.\xa0\xa0以备不时之需】',
               r'【收藏不迷路!:wwW.\xa0\xa0以备不时之需】',
               r'【最新\xa0找到回家的路!】',
               r'【最新地址发布页:.COM\xa0收藏不迷路!】',
               r'【回家的路:WwW.\xa0收藏不迷路!】',
               r'『地址发布页邮箱：\xa0ltX sba@ gmail.com\xa0』',
               r'《据说天才只需一秒就能记住,》',
               r'『地址发布邮箱\xa0LtxsbA\xa0@\xa0Gmail.com』',
               r'『地址发布页邮箱：\xa0ltxsba\xa0@\xa0gmail.com\xa0』',
               r'更多小说\xa0LTXSDZ.COM',
               r'01bz.cc',
               r'龙腾小说\xa0ltxsba\xa0@\xa0gmail.com', 
               r'更多小说\xa0LTXSFB.cOm', 
               r'地址发布邮箱\<EMAIL>',
               r'地址发布页\<EMAIL>',
               r'更多小说\xa0ltxsba.xyz',
               r'龙腾小说\<EMAIL>',
               r'请记住邮箱：<EMAIL>\xa0无法打开网站可发任意内容找回最新地址',
               r'地址发布页邮箱：\<EMAIL>', 
               r'地址发布页\xa0ltxsba.info',
               r'发送无法打开地址至LTXsBA\*************获取最新地址男人都懂得！',
               r'发送无法打开地址至LTXsBA\***********获取最新地址男人都懂得！',
               r'发送任意内容到****************获取最新地址《据说天才只需一秒就能记住,》',
               r'发送任意内容到****************获取最新地址',
               r'龙腾小说ltxsba@ gmail.com',
               r'更多小说\xa0ltxsba.top',
               r'更多小说\xa0ltxsba.info']
    # for keyword in keywords:
    #     execute_select_sql("insert into bk_keywords(keyword) values('{}') ".format(keyword))
    if len(keywords) == 0:
        cnx = cnx_pool.get_connection()
        try:
            sql = "select keyword from bk_keywords"
            cursor = cnx.cursor()
            cursor.execute(sql)
            keyword_rows = cursor.fetchall()
            keywords.extend([keyword[0] for keyword in keyword_rows])
        finally:
            cursor.close()
            cnx.close()
    for keyword in keywords:
        content = re.sub(keyword, '', content)
    # print(list(content))
    # print(content)
    content = re.sub(r'\xa0\xa0\xa0\xa0', '\r\xa0\xa0\xa0\xa0', content)
    # content = re.sub(r'</p><p>', '。\r</p><p>', content)
    # 去除多余的换行符
    rows = content.split('\r')
    rows2 = []
    for row in rows:
        parts = row.split('\xa0\xa0\xa0\xa0')
        rows2.extend(parts)
    rows = rows2
    rows2 = []
    
    for row in rows:
        parts = row.split('</p>')
        rows2.extend(parts)
    rows = rows2
    rows2 = []
    
    full_row = ''
    for row in rows:
        row = row.replace('<p>', '')
        if row.startswith(('》', '）', '】','」')) :
            if full_row != '':
                full_row += row
            else:
                last_row = rows2[-1]
                rows2[-1] = last_row + row
        elif row.endswith(('。', '”', '！', '？', '》', '）', '】','」')) :
            full_row += row
            rows2.append(full_row)
            full_row = ''
        else:
            full_row += row
    if full_row!= '':
        rows2.append(full_row)
    # 去除多余的空格
    if split_show:
        line_count = int(len(rows2) / 2)
        content1 = '\r    '.join(rows2[:line_count])
        content2 = '\r    '.join(rows2[line_count:])
        if html_style:
            content1 = '<p>    '+re.sub(r'\r', '\r</p><p>', content1)+'</p>'
            content2 = '<p>    '+re.sub(r'\r', '\r</p><p>', content2)+'</p>'
        return content1, content2
    else:
        content = '\r    '.join(rows2)
        if html_style:
            content = '<p>    '+re.sub(r'\r', '\r</p><p>', content)+'</p>'
        return '    '+content

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=18000, debug=False)
    # fix_content('')