import requests
import logging
import pandas as pd
import json
from datetime import datetime, timedelta

# 配置日志输出
logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',  # 只包含日志消息本身
    handlers=[
        logging.StreamHandler()  # 控制台处理器
    ]
)
logger = logging.getLogger(__name__)

JIRA_URL = "http://172.20.4.54:18080/rest/api/2/"
USERNAME = "wanghaifeng"
PASSWORD = "kingwang"

work_order_authors = ['陈卓林', '李超', '郭镜和', '梁雄福', '张国良', '王文威', '王海峰', '张任全', '刘敏', '陈海阳']

def get_all_project_ids():
    response = requests.get(f"{JIRA_URL}project", auth=(USERNAME, PASSWORD))
    projects = json.loads(response.text)
    return {project['key']: project['id'] for project in projects}

# 获取指定项目的所有定制字段
def get_custom_fields(project_key):
    project_id = get_all_project_ids()[project_key]
    response = requests.get(f"{JIRA_URL}field", auth=(USERNAME, PASSWORD), params={"projectId": project_id})
    fields = json.loads(response.text)
    return [field for field in fields if field['custom']]

def search_issues(project, start_date = None, end_date = None, assignees = None, version = None):
    jql_query = f'project = "{project}"'
    if start_date:
        jql_query += f' AND created >= "{start_date}"'
    if end_date:
        jql_query += f' AND updated <= "{end_date}"'
    if assignees:
        assignee_list = ', '.join([f"'{assignee}'" for assignee in assignees])
        jql_query += f' AND (assignee IN ({assignee_list}) OR reporter IN ({assignee_list}) OR watcher IN ({assignee_list}) OR comment ~ "{assignee_list}" OR worklogAuthor IN ({assignee_list})) '
    if version:
        if isinstance(version, list):
            version_list = ' OR '.join([f'测试版本号 ~ "{v}"' for v in version])
            jql_query += f' AND ({version_list})'
        else:
            jql_query += f' AND 测试版本号 ~ "{version}"'
    #
    cust_fields = get_custom_fields(project)
    logger.info([field['name'] for field in cust_fields])
    logger.info(jql_query)
    response = requests.get(f"{JIRA_URL}search", auth=(USERNAME, PASSWORD), params={'jql': jql_query, 'maxResults': 1000})
    if response.status_code == 200:
        data = response.json()
        #
        issues = data['issues']
        rows = []
        for issue in issues:
            row = {}
            row["id"] = issue['key']
            row["经办人"] = issue['fields']['assignee']['displayName'] if issue['fields']['assignee'] else None
            row["报告人"] = issue['fields']['reporter']['displayName'] if issue['fields']['reporter'] else None
            row["状态"]   = issue['fields']['status']['name'] if issue['fields']['status'] else None
            row["创建日期"] = issue['fields']['created'][0:10] if issue['fields']['created'] else None
            row["解决日期"] = issue['fields']['resolutiondate'][0:10] if issue['fields']['resolutiondate'] else None
            row["描述"]     = issue['fields']['summary']
            row["类型"]     = issue['fields']['issuetype']['name'] if issue['fields']['issuetype'] else None
            #
            for field in cust_fields:
                field_value = issue['fields'].get(field['id'])
                if isinstance(field_value, dict):
                    row[field['name']] = field_value.get('value')
                elif isinstance(field_value, list):
                    continue
                    # row[field['name']] = ', '.join([item.get('value') if isinstance(item, dict) else str(item) for item in field_value if item is not None])
                else:
                    row[field['name']] = str(field_value) if field_value is not None else None
            #
            rows.append(row)
        return rows
    else:
        logger.error(f"Failed to fetch issues: {response.status_code} {response.text}")
        return None

def get_issue_logs(issue_key):
    url = f"{JIRA_URL}issue/{issue_key}"
    params = {'expand': 'changelog'}
    response = requests.get(url, auth=(USERNAME, PASSWORD), params=params)
    if response.status_code == 200:
        issue_data = response.json()
        changelog = issue_data.get('changelog', {}).get('histories', [])
        return changelog
    else:
        logger.error(f"Failed to fetch issue activity logs: {response.status_code} {response.text}")
        return None

def get_issue_authors(issue_key):
    authors = []
    activity_logs = get_issue_logs(issue_key)
    if not activity_logs:
        return authors
    for log in activity_logs:
        for item in log['items']:
            if item['fromString'] == '上线准备就绪' or item['toString'] == '测试人员审核':
                if log['author']['displayName'] not in authors:
                    authors.append(log['author']['displayName'])
    return authors

def stat_bugs(start_date = None, end_date = None, assignees = None, version = None, iteration_path = None, output_excel_writer=None):
    from 项目统计 import get_iteration_obj,BASE_DIR
    iteration_obj = None
    if start_date is None and end_date is None:
        iteration_obj = get_iteration_obj(iteration_path)
        if iteration_obj:
            start_date = iteration_obj['start_date']
            end_date   = iteration_obj['end_date']
        if start_date is None and end_date is None:
            today = datetime.now()
            start_date = today.replace(day=1).strftime('%Y-%m-%d')  # 当前时间的1号
            next_month = today.replace(day=28) + timedelta(days=4)  # 确保跳到下个月
            end_date = next_month.replace(day=1).strftime('%Y-%m-%d')  # 下个月的1号
    #
    issues = search_issues('BUG', start_date, end_date, assignees, version)
    # 统计每个经办人和每个状态的ISSUE数量
    df = pd.DataFrame(issues)
    sum_count = len(issues) if issues else 0
    
    # 打印每个经办人的ISSUE数量
    if df.empty:
        logger.info("无BUG")
        return
    
    logger.info(f"---------------版本：{version} BUG数量统计---------------")
    #
    if iteration_obj:
        excel_file_path = iteration_obj['excel_file_path']
    else:
        excel_file_path = f'{start_date}~{end_date}'
    excel_file_path = f'{BASE_DIR}/BUG统计({excel_file_path}).xlsx'
    excel_writer = pd.ExcelWriter(excel_file_path, engine='xlsxwriter') if output_excel_writer is None else output_excel_writer

    logger.info(">>经办人统计...")
    status_columns = sorted(df['状态'].unique())  # 确保状态列顺序一致
    grouped_df = df.groupby(['经办人', '状态']).agg({'id': 'count'}).unstack(fill_value=0)
    grouped_df.columns = grouped_df.columns.droplevel(0)  # 修复列名层级问题
    grouped_df = grouped_df.reset_index()
    grouped_df = grouped_df.reindex(columns=['经办人'] + status_columns, fill_value=0)  # 确保列名与数据对齐
    grouped_df['合计'] = grouped_df.iloc[:, 1:].sum(axis=1)
    grouped_df.to_excel(excel_writer, sheet_name='BUG经办人统计', index=False)
    
    logger.info(">>严重程度统计...")
    grouped_df = df.groupby(['严重程度']).agg({'id':'count'}).reset_index()
    grouped_df['占比%'] = round(grouped_df['id'] / sum_count * 100)
    grouped_df.rename(columns={'id': 'BUG数'}, inplace=True)
    grouped_df.to_excel(excel_writer, sheet_name='BUG严重程度统计', index=False)
    
    logger.info(">>BUG清单...")
    df.rename(columns={'id': 'BUG ID'}, inplace=True)
    df[['BUG ID','测试版本号','状态','严重程度','经办人','报告人','创建日期','解决日期','描述']].to_excel(excel_writer, sheet_name='BUG详细', index=False)
    if output_excel_writer is None:
        excel_writer.close()
    

def stat_workorder(start_date = None, end_date = None, assignees = None, iteration_path = None, output_excel_writer=None):
    from 项目统计 import get_iteration_obj,BASE_DIR
    iteration_obj = None
    if start_date is None and end_date is None:
        iteration_obj = get_iteration_obj(iteration_path)
        if iteration_obj:
            start_date = iteration_obj['start_date']
            end_date   = iteration_obj['end_date']
        if start_date is None and end_date is None:
            today = datetime.now()
            start_date = today.replace(day=1).strftime('%Y-%m-%d')  # 当前时间的1号
            next_month = today.replace(day=28) + timedelta(days=4)  # 确保跳到下个月
            end_date = next_month.replace(day=1).strftime('%Y-%m-%d')  # 下个月的1号
    #
    issues = search_issues('YBDR', start_date, end_date, assignees)
    for issue in issues:
        issue['处理人'] = get_issue_authors(issue['id'])
    # 统计每个经办人和每个状态的ISSUE数量
    df = pd.DataFrame(issues)
    # df = df[df['类型'] == '数据提取/处理单']
    sum_count = len(issues) if issues else 0
    
    # 打印每个经办人的ISSUE数量
    if df.empty:
        logger.info("无工单")
        return
    
    logger.info(f"---------------版本：{start_date}~{end_date} 工单统计---------------")
    #
    if iteration_obj:
        excel_file_path = iteration_obj['excel_file_path']
    else:
        excel_file_path = f'{start_date}~{end_date}'
    excel_file_path = f'{BASE_DIR}/工单统计({excel_file_path}).xlsx'
    excel_writer = pd.ExcelWriter(excel_file_path, engine='xlsxwriter') if output_excel_writer is None else output_excel_writer

    logger.info(">>处理人...")
    author_stats = []
    for author in work_order_authors:
        author_issues = [issue for issue in issues if author in issue['处理人']]
        author_issues_count = len(author_issues)
        if author_issues_count == 0:
            continue
        author_stat_info = {'处理人': author, '工单数': author_issues_count, '工单ID': ','.join([issue['id'] for issue in author_issues]), 
                            '处理时长（小时）': sum([int(float(issue['实际工时'])) if issue['实际工时'] else 0 for issue in author_issues])}
        author_stats.append(author_stat_info)
    author_stats_df = pd.DataFrame(author_stats, columns=['处理人', '工单数', '工单ID'])
    author_stats_df.to_excel(excel_writer, sheet_name='工单处理人统计', index=False)
    
    logger.info(">>清单...")
    df.sort_values(by='id', inplace=True)
    df['实际工时'] = df.apply(lambda row: int(float(row['实际工时'])) if row['实际工时'] else None, axis=1)
    df.rename(columns={'id': '工单ID', '创建日期': '处理日期', '实际工时': '处理时长（小时）'}, inplace=True)
    df['处理人'] = df.apply(lambda row: ','.join(row['处理人']), axis=1)
    df[['处理日期','工单ID','类型','描述','处理人']].to_excel(excel_writer, sheet_name='工单详细', index=False)
    if output_excel_writer is None:
        excel_writer.close()

if __name__ == "__main__":
    # stat_bugs('2024-12-26', '2025-01-22')
    # gen_all_stat()
    #
    # iteration_path = input("请输入迭代路径：")
    # stat_bugs(iteration_path=iteration_path)
    # stat_workorder(iteration_path=iteration_path)
    stat_workorder(start_date = '2025-06-01')
    stat_bugs(start_date = '2025-06-01')
    # stat_workorder(start_date = '2025-04-01', end_date = '2025-05-01')
    # stat_workorder(start_date = '2025-03-01', end_date = '2025-04-01')
    # stat_workorder(start_date = '2025-02-01', end_date = '2025-03-01')
    # stat_workorder(start_date = '2025-01-01', end_date = '2025-02-01')
    # stat_workorder(start_date = '2025-01-01', end_date = '2025-05-01')
    # stat_bugs(iteration_path='NONE')
    # stat_workorder(iteration_path='NONE')