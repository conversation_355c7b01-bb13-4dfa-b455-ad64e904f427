update tmp_cust_0808 t set t.state='9';
insert into tmp_cust_0808(customer_code,name)
select customer_code,name from epsp.v_cum_customer_info c where c.customer_code='5651300003033343';
select * from tmp_cust_0808 where state is null;
select * from tmp_pay_0808 t where t.batch_no is null;
--update tmp_pay_0808 t set t.batch_no=2 where t.batch_no is null;
---------------OUT------------------------
insert into tmp_pay_0808(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT t.CUSTOMER_CODE AS 商户编号,t.CUSTOMERNAME AS 商户名称, 
       t.BUSINESS_CODE  AS 业务代码, b.NAME AS 业务名称 ,
       sum(t.TOTAL_FEE) / 100  AS 总金额, sum(t.PROCEDURE_FEE) / 100 AS 总手续费,
       count(t.TRANSACTION_NO) AS 总笔数,to_char(max(t.CREATE_TIME),'yyyy-mm-dd hh24:mi:ss') AS 最后一笔时间
FROM epsp.TXS_WITHDRAW_TRADE_ORDER t 
  INNER JOIN tmp_cust_0808 c ON t.CUSTOMER_CODE =c.CUSTOMER_code and c.state is null
  LEFT JOIN epsp.PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE 
WHERE t.PAY_STATE ='00' AND t.CREATE_TIME >= timestamp '2021-01-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-01-01 00:00:00'
GROUP BY t.CUSTOMER_CODE ,t.CUSTOMERNAME,t.BUSINESS_CODE ,b.NAME ;
---------------PAY------------------------
insert into tmp_pay_0808(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT t.CUSTOMER_CODE AS 商户号,t.CUSTOMERNAME AS 商户名称, 
       t.BUSINESS_CODE  AS 业务代码, b.NAME AS 业务名称 ,
       sum(t.AMOUNT) / 100 AS 总金额, sum(t.PROCEDURE_FEE) / 100 AS 总手续费, count(t.TRANSACTION_NO) AS 总笔数, to_char(max(t.CREATE_TIME),'yyyy-mm-dd hh24:mi:ss') AS 最后一笔时间
FROM epsp.TXS_PAY_TRADE_ORDER t 
  INNER JOIN info.tmp_cust_0808 c ON t.CUSTOMER_CODE =c.CUSTOMER_CODE and c.state is null
  LEFT JOIN epsp.PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE 
WHERE t.STATE ='00' AND t.CREATE_TIME >= timestamp '2021-01-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-01-01 00:00:00'
GROUP BY t.CUSTOMER_CODE ,t.CUSTOMERNAME,t.BUSINESS_CODE ,b.NAME ;
-------------------POS-----------------------
insert into tmp_pay_0808(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
select MERCHANT_NO, a.name,'POS', 'POS', sum(AMOUNT) as total_amount, 0 as fee, count(1) as cnt,null
from epsp.ZHY_POSP_RECORD t 
     inner join  tmp_cust_0808 a on t.merchant_no=a.customer_code  and a.state is null
where CREATION_TIME >= timestamp '2021-01-01 00:00:00'
  and CREATION_TIME < timestamp '2022-01-01 00:00:00'
group by MERCHANT_NO,a.name;
-------------------ACS----------------------
insert into tmp_pay_0808(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT t.CUSTOMER_CODE AS 商户号,c.name,'ACS','ACS' ,
       sum(t.AMOUNT) / 100 AS 总金额, sum(t.PROCEDURE_FEE) / 100 AS 总手续费,
       count(t.TRANSACTION_NO) AS 总笔数,null
FROM epsp.PAS_ACCT_QUOTA_RECORD t
  INNER JOIN info.tmp_cust_0808 c ON t.CUSTOMER_CODE =c.CUSTOMER_CODE and c.state is null
WHERE t.FUND_TYPE ='1' AND t.ACCT_STATE ='00' 
      AND t.CREATE_TIME >= timestamp '2021-01-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-01-01 00:00:00'
GROUP BY t.CUSTOMER_CODE,c.name;
-------------------XX----------------------
insert into tmp_pay_0808(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
select t.customer_code,t.customer_name,'XX','XX',sum(t.amt) / 100 as total_amount, sum(t.procedure_fee) as total_fee, count(0), null
from epsp.chk_xx_tran_record t 
     inner join tmp_cust_0808 c on t.customer_code=c.customer_code and  c.state is null
where t.create_time > timestamp '2021-01-01 00:00:00' and t.create_time < timestamp '2022-01-01 00:00:00'
group by t.customer_code,t.customer_name;
-------------------FZ----------------------
insert into tmp_pay_0808(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
select t.source_customer_code,t.source_customername,'FZ','FZ',sum(t.amount) / 100 as total_amount,sum(t.procedurefee), count(0) as cnt,null
from epsp.txs_split_record t 
     inner join  tmp_cust_0808 c on t.source_customer_code=c.customer_code  and c.state is null
where t.create_time > timestamp '2021-01-01 00:00:00' and t.create_time < timestamp '2022-01-01 00:00:00'  
      and t.state='3'  and not exists(select 1 from tmp_cust_0808 a where t.customer_code=a.customer_code)
group by t.source_customer_code,t.source_customername;
-------------------Refund----------------------
insert into tmp_pay_0808(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
select t.customer_code,t.customername,'Refund', 'Refund',sum(t.refund_fee)/100,sum(t.procedure_fee)/100,count(0), null 
from epsp.txs_refund_pre_order t 
     inner join  tmp_cust_0808 c on t.customer_code=c.customer_code and c.state is null
where t.create_time > timestamp '2021-01-01 00:00:00' and t.create_time < timestamp '2022-01-01 00:00:00'  
group by t.customer_code,t.customername;

select t.商户号,t.商户名称,t.业务代码,count(0) from tmp_pay_0808 t group by t.商户号,t.商户名称,t.业务代码 having count(0) > 1 order by t.商户号,t.商户名称,t.业务代码;
select * from tmp_pay_0808 t where t.商户号='562686003758029' and t.业务代码='XX';

update tmp_pay_0808 t set t.商户名称=(select c.name from tmp_cust_0808 c where c.customer_code=t.商户号);
select * from v_cust_stat v where v.商户号='5651300003033343';
