import os
import xml.etree.ElementTree as ET

def parse_pom(pom_path):
    tree = ET.parse(pom_path)
    root = tree.getroot()
    namespace = {'mvn': 'http://maven.apache.org/POM/4.0.0'}

    module = root.find('mvn:artifactId', namespace).text
    parent_module = root.find('mvn:parent/mvn:artifactId', namespace)
    parent_module = parent_module.text if parent_module is not None else None

    modules = [mod.text for mod in root.findall('mvn:modules/mvn:module', namespace)]
    dependencies = [
        dep.find('mvn:artifactId', namespace).text
        for dep in root.findall('mvn:dependencies/mvn:dependency', namespace)
        if dep.find('mvn:groupId', namespace).text == 'com.shanjie'
    ]

    return {
        'module': module,
        'parent_module': parent_module,
        'sub_modules': modules,
        'dependencies': dependencies
    }

def find_pom_files(directory, deep=1):
    if deep >= 3:
        return []
    pom_files = []
    for root, dirs, files in os.walk(directory):
        if 'pom.xml' in files:
            pf = os.path.join(root, 'pom.xml')
            if pf not in pom_files:
                pom_files.append(pf)
        for d in dirs:
            fps = find_pom_files(os.path.join(root, d), deep + 1)
            for fp in fps:
                if fp not in pom_files:
                    pom_files.append(fp)
    return pom_files

def print_modules(directory):
    pom_files = find_pom_files(directory)
    
    pom_modules = {}
    for pom_file in pom_files:
        pm = parse_pom(pom_file)
        if pm['parent_module'] is None or pm['parent_module'] == 'bic-vue-plus':
            pm['full_name'] = pm['module']
        else:
            pm['full_name'] = f"{pm['parent_module']}.{pm['module']}"
        pm['alias'] = "M"+str(len(pom_modules)+1)
        pom_modules[pm['module']] = pm
        
    for pm in pom_modules.values():
        print(f'state "{pm["full_name"]}" as {pm["alias"]}')
    
    relations = []
    for pm in pom_modules.values():
        # for sm in pm['sub_modules']:
        #     r = f"{pm['alias']} --> {pom_modules[sm]['alias']}"
        #     if r not in relations:
        #         relations.append(r)
        for sm in pm['dependencies']:
            r = f"{pm['alias']} --> {pom_modules[sm]['alias']}"
            if r not in relations:
                relations.append(r)
    
    for r in relations:
        print(r)
        
    return pom_modules

if __name__ == '__main__':
    directory = input("请输入目录路径：")
    if not os.path.exists(directory):
        directory = r'D:\E-BankAccount\epay-epl'
    print_modules(directory)
    # print(result)
