@startuml SKYEE流程
title 汇通 @ SKYEE流程

actor 商户 as M
actor 运营 as O
box 
participant 汇通平台 as P
participant 易云帐ACC as A
end box
participant SKYEE as S

==商户申请==
M -> P: 商户申请
note left of P: 接口：vaMerchantApply
P -> P: 写表VA_MCH_INFO、VA_MCH_CHANNEL
P --> M: 返回商户申请结果
P -> S: 报备子客户
S --> P: 子客户（VA_MCH_CHANNEL）状态审核中

S -[#red]\ P: 报备信息审核结果通知\nSUB_REAL_NAME:子客户报备审核
alt 审核通过
    P -> P: 修改状态为审核通过
end

==VA账户申请==
M -> P: VA账户申请，接口：vaAccApply
P -> P: 写表VA_ACC_INFO
P -> S: 申请香港账号
S --> P: 返回受理状态
P -> P: 更新状态审核中
P --> M: 返回VA账户申请结果
P -> S : 查询VA账户信息
S --> P: 返回VA账户信息
alt 审核通过
    P -> P: 修改状态为审核通过，并更新VA账号信息
end

==收款流程==
S -[#red]\ P: 收款入账通知
P -> P: 写表VA_FUNDS_IN
alt 状态：待补充资料
...未知流程，可能线下补充...
else 状态：已退款
P -> P: 更新入账单状态
else 状态：成功
    ...黑名单、风控、名单筛查和入账，参考CC入账流程 ...
end

==换汇流程==
M -> P: 查询汇率，接口：getVaExchangeRate
alt priceId空
    P -> S: 创建询价单
    S --> P: 返回询价单号+汇率+汇率有效时间
else priceId非空
    P -> S: 刷新询价单
    S --> P: 返回新汇率+汇率有效时间
end
P --> M: 返回汇率
M -> P: 确认换汇，接口：vaExchangeOrder
P -> P: 写表VA_EXCHANGE_ORDER
P -> A: 扣减余额，接口：换汇（转账）申请
A --> P: 返回余额扣减
P -> S: 确认换汇
S --> P: 返回换汇结果：单号+状态
P --> M: 返回换汇结果：单号+状态
S -[#red]\ P: 换汇结果通知
alt 状态=FINISH:已结算，换汇成功
    P -> P: 更新换汇单状态
    P -> A: 换汇成功，接口：换汇（转账）确认
else 状态=其他
    P -> P: 更新换汇单状态
    P -> A: 换汇失败，接口：换汇（转账）确认
end
P -[#red]\ M: 换汇结果通知

==付款==
M -> P: 境外收款人申请，接口：abroadBankInfoApply
P -> P: 写表USER_ABROAD_BANK_INFO
P -> S: 收款人报备
S --> P: 返回收款人报备账户ID：receiveBankAcctId
P -> P: 更新收款人报备账户ID
P --> M: 返回收款人报备结果，状态：审核中
S -[#red]\ P: 报备信息审核结果通知\nWITHDRAW:提现账号，AGENT_PAY:供应商代付账号
alt 审核通过
    P -> P: 修改状态为审核通过
else 审核不通过
    ...修改状态，并决定是否调用修改接口...
end
M -> P: 付款申请，接口：vaRemitApply，携收款人ID
P -> P: 写表VA_REMIT_ORDER，状态待审核
P -> M: 返回付款申请结果
O -> P: 付款审核
alt 审核通过
    P -> A: 付款扣款，接口：汇款（消费/提现）
    P -> P: 更新状态为审核通过
else 审核不通过
    P -> P: 更新状态为审核不通过，流程结束
end
alt 提现 EPL固定专户收款人
    P -> S: 提现
else 代付，其他，暂无
    P -> S: 提现，指定收款人
end
P -> P: 更新状态为上游审核中
S -[#red]\ P: 付款通知
note left of S
    COMPLETE_DATA:待补充资料;
    AUDIT_REJECT:审核拒绝;
    WAIT_PAY:等待支付;
    PAY_FAIL:付款失败;
    FINISHED:已完成
end note

alt 状态=FINISHED
    P -> P: 更新状态为已完成
    P -[#red]\ M: 通知付款结果
else 状态=COMPLETE_DATA
    P -> P: 更新状态为待补充资料
else 状态=WAIT_PAY
    P -> P: 更新状态为等待支付
else 状态=AUDIT_REJECT/PAY_FAIL
    P -> A: 付款失败，接口：汇款（消费/提现）退回
    A --> P: 返回退回记账结果
    P -> P: 更新状态为审核拒绝/付款失败
    P -[#red]\ M: 通知付款结果
end

@enduml