﻿--根据上有机构统计
select c.institution_name 上游机构,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='00' then 1 else 0 end) as 成功数量,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='01' then 1 else 0 end) as 失败数量,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='03' then 1 else 0 end) as 处理中数量,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state not in('00','01','03') then 1 else 0 end) as 其他数量,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) as 今日总量,
  sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) as 往日总量,
  case when sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) =0 then null else (round(sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='00' then 1 else 0 end)/sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) * 100,2) )end as "今日成功率%",
  case when sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) =0 then null else (round(sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='00' then 1 else 0 end)/sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) * 100,2) )end as "往日成功率%"
from txs_pay_trade_order t 
  inner join clr_pay_record c on t.transaction_no=c.transaction_no
where t.create_time > to_date(to_char(sysdate-1, 'yyyymmdd'),'yyyymmdd') group by c.institution_name order by c.institution_name;

--根据支付方式统计
select b.pay_method_name as 支付方式,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='00' then 1 else 0 end) as 成功数量,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='01' then 1 else 0 end) as 失败数量,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='03' then 1 else 0 end) as 处理中数量,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state not in('00','01','03') then 1 else 0 end) as 其他数量,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) as 今日总量,
  sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) as 往日总量,
  case when sum(case when t.create_time > sysdate-1/24/6 then 1 else 0 end) =0 then null else (round(sum(case when t.create_time > sysdate-1/24/6 and t.state='00' then 1 else 0 end)/sum(case when t.create_time > sysdate-1/24/6 then 1 else 0 end) * 100,2) )end as "十分钟成功率%",
  case when sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) =0 then null else (round(sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='00' then 1 else 0 end)/sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) * 100,2) )end as "今日成功率%",
  case when sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) =0 then null else (round(sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='00' then 1 else 0 end)/sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) * 100,2) )end as "往日成功率%",
  case when sum(case when t.create_time > sysdate-1/24/6 then 1 else 0 end) =0 then null else (round(sum(case when t.create_time > sysdate-1/24/6 and t.state='00' then seconds_diff(t.create_time, t.end_time) else 0 end)/sum(case when t.create_time > sysdate-1/24/6 then 1 else 0 end),2) )end as "十分钟平均耗时",
  case when sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) =0 then null else (round(sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='00' then seconds_diff(t.create_time, t.end_time) else 0 end)/sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end),2) )end as "今日平均耗时",
  case when sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) =0 then null else (round(sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='00' then seconds_diff(t.create_time, t.end_time) else 0 end)/sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end),2) )end as "往日平均耗时"
from txs_pay_trade_order t 
  inner join cum_pay_method b on t.pay_method=b.pay_method
where t.create_time > to_date(to_char(sysdate-1, 'yyyymmdd'),'yyyymmdd') group by b.pay_method_name order by count(0) desc;

--根据商户统计
select t.customer_code as 商户编号,t.customername as 商户名称,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='00' then 1 else 0 end) as 成功数量,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='01' then 1 else 0 end) as 失败数量,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='03' then 1 else 0 end) as 处理中数量,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state not in('00','01','03') then 1 else 0 end) as 其他数量,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) as 今日总量,
  sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) as 往日总量,
  case when sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) =0 then null else (round(sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='00' then 1 else 0 end)/sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) * 100,2) )end as "今日成功率%",
  case when sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) =0 then null else (round(sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='00' then 1 else 0 end)/sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) * 100,2) )end as "往日成功率%"
from txs_pay_trade_order t
where t.create_time > to_date(to_char(sysdate-1, 'yyyymmdd'),'yyyymmdd') group by t.customer_code,t.customername order by count(0) desc;

--根据商户统计（线下）
select t.customer_code as 商户编号,t.customer_name as 商户名称,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='00' then 1 else 0 end) as 成功数量,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='01' then 1 else 0 end) as 失败数量,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='03' then 1 else 0 end) as 处理中数量,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state not in('00','01','03') then 1 else 0 end) as 其他数量,
  sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) as 今日总量,
  sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) as 往日总量,
  case when sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) =0 then null else (round(sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='00' then 1 else 0 end)/sum(case when t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) * 100,2) )end as "今日成功率%",
  case when sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) =0 then null else (round(sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') and t.state='00' then 1 else 0 end)/sum(case when t.create_time < to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd') then 1 else 0 end) * 100,2) )end as "往日成功率%"
from txs_posp_three t
where t.create_time > to_date(to_char(sysdate-1, 'yyyymmdd'),'yyyymmdd') group by t.customer_code,t.customer_name order by count(0) desc;

--根据错误码和错误信息统计
select t.state 订单状态,t.channel_resp_code 错误码,t.channel_resp_msg 错误信息,count(0) 订单数 
from clr_pay_record t 
where t.state<>'00' and t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd')
group by t.state,t.channel_resp_code,t.channel_resp_msg order by count(0) desc;

--查看订单详细信息（线上收单）
select t.customer_code 商户编号,t.customername 商户名称,t.out_trade_no 商户单号,t.transaction_no 交易单号,t.business_code 业务代码, b.name 业务名称, m.pay_method_name||'-'||m.pay_method 支付方式,t.terminal_no 交易终端,c.terminal_no 清算终端,c.terminal_info 终端信息,
  to_char(t.create_time,'yyyy-mm-dd hh24:mi:ss') as 创建时间,to_char(t.end_time,'hh24:mi:ss') as 支付时间,seconds_diff(t.create_time,t.end_time) as 耗时秒数,t.amount/100 as 订单金额,t.procedure_fee as 手续费,
  decode(t.state,'00','成功','01','失败','03','处理中', '05', '已关闭', '06', '已撤销', t.state) as TXS状态,decode(c.state,'00','成功','01','失败','02','处理中','03','已创建', c.state) as CLR状态,
  decode(p.acc_status,'00','成功','01','失败',p.acc_status) as 记账状态,decode(t.settlement_state,'00','结算成功','01','结算失败','02','待结算',t.settlement_state) as 结算状态,decode(t.settle_type,'1','单笔结算','普通结算') as 结算类型,decode(t.sett_cycle_rule_code,'RealTime','实时结算',t.sett_cycle_rule_code) as 结算周期,
  decode(c.route_type,'0','商户','1','人工','2','智能') as 路由类型,decode(c.idc,'11','北京','21','上海','31','深圳') as 网联IDC,c.channel_trade_no 上游单号,decode(c.result_source,'1','异步通知','2','接口查询','3','人工更新状态','4','同步返回','5','异常,超时','6','数据库无记录,手工补单','7','授权支付订单，不到上游',c.result_source) as 结果来源,
  t.error_code 交易码,t.error_msg 交易消息,c.channel_resp_code 上游码,c.channel_resp_msg 上游消息
  ,t.server_ip as 服务IP,h.channel_mcht_no 上游商户号,h.out_channel_mcht_no 三方商户号,c.pay_channel_id 渠道ID,h.channel_name 渠道名称
from txs_pay_trade_order t 
  left join pay_pay_record p on t.transaction_no=p.transaction_no 
  left join clr_pay_record c on t.transaction_no=c.transaction_no 
  left join cum_channel_mcht_info h on c.channel_mcht_no=h.channel_mcht_no
  inner join pas_pay_method m on t.pay_method=m.pay_method
  inner join pas_business b on t.business_code=b.code
where t.create_time > to_date(to_char(sysdate, 'yyyymmdd'),'yyyymmdd')
  --and t.customer_code in ('562803003101202')
  --and t.pay_method in('84','35','13','7','24','48','49')
  --and c.channel_resp_code not in('')
  --and t.transaction_no in('','')
  and t.state in('01');

--提现查询&统计
--根据错误码统计提现失败订单

select * from txs_withdraw_trade_order

-----
select * from(
  select t.payee_id as 商户,
         t.pay_method as 方式,
         m.institution_id as 机构,
         m.channel_mcht_no as 上游商户号,
         m.out_channel_mcht_no as 三方商户号,
         t.channel_resp_code as error_code,
         sum(case when t.state='00' then 1 else 0 end) as success_count,
         sum(case when t.state='01' then 1 else 0 end) as fail_count,
         sum(case when t.state not in('01','00') then 1 else 0 end) as other_count,
         count(t.transaction_no) as total_count
    from clr_pay_record t, cum_channel_mcht_info m
   where t.channel_mcht_no = m.channel_mcht_no
     and t.create_time > sysdate - 1
   group by t.payee_id, t.pay_method,
         m.institution_id, m.channel_mcht_no, m.out_channel_mcht_no,
         t.channel_resp_code
) t where total_count > 5 and fail_count / total_count > 0.2
order by t.error_code;

--最近错误信息和笔数
--收单
select * from cust_inlet_record t where t.customer_code='562809003709807';
select * from cust_inlet_record t inner join cust_customer c on t.customer_code=c.customer_no where t.channel_resp_msg like '%支付宝%' and c.name like '%中海油%';

select * from clr_withdraw_record t where t.create_time > sysdate -1 order by t.create_time desc;
select t.channel_resp_code,t.channel_resp_msg,count(0) from clr_withdraw_record t where t.create_time > sysdate -1 and t.create_time < sysdate and t.state='01' group by t.channel_resp_code,t.channel_resp_msg order by t.channel_resp_code;
select t.customer_code,t.customer_code,t.transaction_no,to_char(t.create_time,'yyyy-mm-dd hh24:mi:ss') as create_time, t.channel_resp_code,t.channel_resp_msg from clr_withdraw_record t where t.channel_resp_code='0237' and t.create_time > sysdate -1 order by t.create_time desc;
--彩洋代付
select t.customer_code,t.customername,t.transaction_no,t.total_fee/100 as total_fee,t.pay_state,to_char(t.create_time,'yyyy-mm-dd hh24:mi:ss') as create_time from txs_withdraw_trade_order t where t.create_time> sysdate -1 and t.customername like '%彩洋%' order by t.create_time desc;
--海油交易
select t.customer_code,t.customername,t.out_trade_no,t.transaction_no,t.business_code,t.state,to_char(t.create_time,'yyyy-mm-dd hh24:mi:ss') as create_time,t.server_ip,t.error_code,t.error_msg,t.channel_resp_code,t.channel_resp_msg from txs_pay_trade_order t where t.create_time > sysdate -1/24 and t.customername like '%油%' order by t.create_time desc;
---------------交易成功率分布监控----------------
--最近1小时各业务和状态统计信息(业务统计，各主机下的业务统计，各机构下的业务统计)
select t.business_code,t.state, round(avg(seconds_diff(t.create_time,t.end_time)),1) as scnds,count(0) as "COUNT",sum(t.amount) as total_amount from txs_pay_trade_order t where t.create_time > sysdate -1/24 group by t.business_code,t.state order by t.business_code,t.state;


select business_code as "业务代码", business_name as "业务名称",server_ip as "服务器IP", total as "总数",state00 as "成功数",round(state00/total*100,2)||'%' as "成功占比",state03 as "处理中数",round(state03/total*100,2)||'%' as "处理中占比",state01 as "失败数",round(state01/total*100,2)||'%' as "失败占比",other as "其他数",round(other/total*100,2)||'%' as "其他占比" from(select t.server_ip,t.business_code,b.name as business_name,sum(case when t.state='00' then 1 else 0 end) as state00,sum(case when t.state='01' then 1 else 0 end) as state01,sum(case when t.state='03' then 1 else 0 end) as state03,sum(case when t.state not in('00','01','03') then 1 else 0 end) as other,count(0) as TOTAL from txs_pay_trade_order t inner join pas_business b on t.business_code=b.code where t.create_time > sysdate -1/24 group by t.server_ip,t.business_code,b.name order by t.business_code,t.server_ip);
select business_code as "业务代码", business_name as "业务名称",institution_name as "机构名称", total as "总数",state00 as "成功数",round(state00/total*100,2)||'%' as "成功占比",state03 as "处理中数",round(state03/total*100,2)||'%' as "处理中占比",state01 as "失败数",round(state01/total*100,2)||'%' as "失败占比",other as "其他数",round(other/total*100,2)||'%' as "其他占比" from(select r.institution_id,r.institution_name,t.business_code,b.name as business_name,sum(case when t.state='00' then 1 else 0 end) as state00,sum(case when t.state='01' then 1 else 0 end) as state01,sum(case when t.state='03' then 1 else 0 end) as state03,sum(case when t.state not in('00','01','03') then 1 else 0 end) as other,count(0) as TOTAL from txs_pay_trade_order t inner join clr_pay_record r on t.transaction_no=r.transaction_no inner join pas_business b on t.business_code=b.code where t.create_time > sysdate -1/24 group by r.institution_id,r.institution_name,t.business_code,b.name order by t.business_code,r.institution_name);
select business_code as "业务代码", business_name as "业务名称",institution_name as "机构名称",IDC, total as "总数",state00 as "成功数",round(state00/total*100,2)||'%' as "成功占比",state03 as "处理中数",round(state03/total*100,2)||'%' as "处理中占比",state01 as "失败数",round(state01/total*100,2)||'%' as "失败占比",other as "其他数",round(other/total*100,2)||'%' as "其他占比" from(select r.institution_id,r.institution_name,(case when r.institution_name like '%银联%' then '' else r.idc end) as IDC,t.business_code,b.name as business_name,sum(case when t.state='00' then 1 else 0 end) as state00,sum(case when t.state='01' then 1 else 0 end) as state01,sum(case when t.state='03' then 1 else 0 end) as state03,sum(case when t.state not in('00','01','03') then 1 else 0 end) as other,count(0) as TOTAL from txs_pay_trade_order t inner join clr_pay_record r on t.transaction_no=r.transaction_no inner join pas_business b on t.business_code=b.code where t.create_time > sysdate -1/24 group by r.institution_id,r.institution_name,r.idc,t.business_code,b.name order by t.business_code,r.institution_name);

--各上游机构收单数据统计
select institution_name,idc,state,sum(count) as "COUNT" from(select c.institution_name,(case when c.institution_name like '%银联%' then '' else c.idc end) as IDC,t.state,count(0) as "COUNT" from txs_pay_trade_order t inner join clr_pay_record c on t.transaction_no=c.transaction_no where t.create_time > sysdate -1 group by c.institution_name,c.idc,t.state)x group by institution_name,idc,state order by institution_name,idc,state;
select institution_name,idc,state,sum(count) as "COUNT" from(select c.institution_name,(case when c.institution_name like '%银联%' then '' else c.idc end) as IDC,t.pay_state as state,count(0) as "COUNT" from txs_withdraw_trade_order t inner join clr_withdraw_record c on t.transaction_no=c.transaction_no where t.create_time > sysdate -1 group by c.institution_name,c.idc,t.pay_state)x group by institution_name,idc,state order by institution_name,idc,state;
select * from epcc.epcc_payment_order;
--提现(24小时内半小时前的订单统计)
select r.institution_name as "清算机构",(case when t.pay_state = '00' then '成功-00' when t.pay_state='01' then '失败-01' when t.pay_state='03' then '处理中-03' else '其他' end) as "状态" ,count(0) as "数量" from txs_withdraw_trade_order t inner join clr_withdraw_record r on t.transaction_no=r.transaction_no where t.create_time > sysdate -1 and t.create_time < sysdate - 1/48 group by r.institution_name,t.pay_state order by r.institution_name,t.pay_state;
select r.institution_name as "清算机构",r.idc,(case when t.pay_state = '00' then '成功-00' when t.pay_state='01' then '失败-01' when t.pay_state='03' then '处理中-03' else '其他' end) as "状态" ,count(0) as "数量" from txs_withdraw_trade_order t inner join clr_withdraw_record r on t.transaction_no=r.transaction_no where t.create_time > sysdate -1 and r.institution_id=18 group by r.institution_name,r.idc,t.pay_state order by r.institution_name,r.idc,t.pay_state;

--提现(24小时内半小时前的"处理中"订单清单)
select t.customer_code,t.customername,t.out_trade_no,t.transaction_no,t.total_fee,t.create_time, t.pay_state,r.state,r.institution_name,t.error_code,r.channel_resp_code,r.channel_resp_msg from txs_withdraw_trade_order t left join clr_withdraw_record r on t.transaction_no=r.transaction_no where t.create_time > sysdate -1 and t.create_time < sysdate - 1/48 and t.pay_state ='03' order by t.create_time desc;

---------------交易结果获取监控----------------
--最近1小时成功订单结果获取模式(1-异步通知; 2-接口查询; 3-人工更新; 4-同步返回; 5-异常,超时; 6-数据库无记录,手工补单)
select business_code as "业务代码", business_name as "业务名称", total_count as "总数", state1 as "通知数",round(state1/total_count * 100,2)||'%' as "通知占比", state2 as "查询数",round(state2/total_count * 100,2)||'%' as "查询占比", state4 as "同步返回数",round(state4/total_count * 100,2)||'%' as "同步返回占比", other as "其他数",round(other/total_count * 100,2)||'%' as "其他占比" from (select t.business_code,b.name as business_name,count(0) as total_count,sum(case when c.result_source ='1' then 1 else 0 end) as state1,sum(case when c.result_source ='2' then 1 else 0 end) as state2,sum(case when c.result_source ='4' then 1 else 0 end) as state4,sum(case when c.result_source not in('1','2','4') then 1 else 0 end) as other from txs_pay_trade_order t inner join clr_pay_record c on t.transaction_no=c.transaction_no inner join pas_business b on t.business_code=b.code where t.state='00' and t.create_time > sysdate - 1/24 group by t.business_code,b.name order by t.business_code);
select server_ip as "服务器IP",business_code as "业务代码", business_name as "业务名称", total_count as "总数", state1 as "通知数",round(state1/total_count * 100,2)||'%' as "通知占比", state2 as "查询数",round(state2/total_count * 100,2)||'%' as "查询占比", state4 as "同步返回数",round(state4/total_count * 100,2)||'%' as "同步返回占比", other as "其他数",round(other/total_count * 100,2)||'%' as "其他占比" from (select t.server_ip,t.business_code,b.name as business_name,count(0) as total_count,sum(case when c.result_source ='1' then 1 else 0 end) as state1,sum(case when c.result_source ='2' then 1 else 0 end) as state2,sum(case when c.result_source ='4' then 1 else 0 end) as state4,sum(case when c.result_source not in('1','2','4') then 1 else 0 end) as other from txs_pay_trade_order t inner join clr_pay_record c on t.transaction_no=c.transaction_no inner join pas_business b on t.business_code=b.code where t.state='00' and t.create_time > sysdate - 1/24 group by t.server_ip,t.business_code,b.name order by t.server_ip,t.business_code);
--提现结果获取模式
select institution_name, total_count as "总数", state1 as "通知数",round(state1/total_count * 100,2)||'%' as "通知占比", state2 as "查询数",round(state2/total_count * 100,2)||'%' as "查询占比", state4 as "同步返回数",round(state4/total_count * 100,2)||'%' as "同步返回占比", other as "其他数",round(other/total_count * 100,2)||'%' as "其他占比" from (select c.institution_id,c.institution_name,count(0) as total_count,sum(case when c.result_source ='1' then 1 else 0 end) as state1,sum(case when c.result_source ='2' then 1 else 0 end) as state2,sum(case when c.result_source ='4' then 1 else 0 end) as state4,sum(case when c.result_source not in('1','2','4') then 1 else 0 end) as other from txs_withdraw_trade_order t inner join clr_withdraw_record c on t.transaction_no=c.transaction_no where t.pay_state='00' and t.create_time > sysdate - 1/24 group by c.institution_id,c.institution_name order by c.institution_name);

---------------交易终态时长监控----------------
--最近1小时成功业务到达终态时间（秒数）
select business_code,b.name as business_name,count(0) as "COUNT", round(avg(seconds),2) as avg_seconds,max(seconds) as max_seconds from( select t.customer_code,t.customername,t.business_code,t.transaction_no,seconds_diff(t.create_time,t.end_time) as seconds from txs_pay_trade_order t where t.create_time > sysdate -1/24 and t.state='00' ) t inner join pas_business b on t.business_code=b.code group by t.business_code,b.name order by t.business_code;
select * from(select t.customer_code,t.customername,t.business_code,t.transaction_no,seconds_diff(t.create_time,t.end_time) as seconds from txs_pay_trade_order t where t.create_time > sysdate -1 and t.state='00' and t.business_code='UnionSweep')t order by seconds desc;



--查看订单详细信息--代付
select t.customer_code,t.customername,t.out_trade_no,t.transaction_no,c.channel_trade_no,t.card_no,t.bank_account_type,t.bank_user_name_full as card_owner,t.bank_name,t.bank_sub,t.bank_no,c.institution_name,p.idc,
  to_char(t.create_time,'yyyy-mm-dd hh24:mi:ss')as create_time,to_char(t.end_time,'hh24:mi:ss')as end_time,seconds_diff(t.create_time,t.end_time) as seconds,
  t.total_fee,t.actual_fee,t.procedure_fee,
  t.pay_state as t_state,c.state as c_state,c.result_source,c.channel_resp_code,c.channel_resp_msg,t.channel_query_code,t.channel_query_msg
from txs_withdraw_trade_order t 
  left join clr_withdraw_record c on t.transaction_no=c.transaction_no
  left join epcc.epcc_payment_order p on c.channel_trade_no=p.trxid
where t.create_time > sysdate -1 and c.channel_resp_code='0237'
order by t.create_time desc;

select * from epcc.epcc_payment_order;
select * from txs_refund_pre_order t where t.transaction_no='31202204183919433242256';

--最近1小时B区交易明细
select t.customer_code,t.customername,b.name as business_name,t.transaction_no, t.server_ip,t.client_ip,t.state,r.result_source,r.institution_name,to_char(t.create_time,'yyyy-mm-dd hh24:mi:ss') as create_time,seconds_diff(t.create_time,t.end_time) as seconds 
from txs_pay_trade_order t inner join clr_pay_record r on t.transaction_no=r.transaction_no inner join pas_business b on t.business_code=b.code
where t.create_time > sysdate -1/24 and t.server_ip like '10.244%' 
order by t.create_time desc;

select * from cum_institution_channel t order by t.channel_name desc;
select * from txs_pay_trade_order t where t.business_code='EnterpriseUnion' and t.create_time > sysdate - 1 and t.state='00'

--定时任务执行状况
select t.remark,t.cron,t.ip_addres,to_char(t.last_fire_time,'hh24:mi:ss') as fire_time,t.last_execute_record_id as rec_id,to_char(r.create_time,'hh24:mi:ss') as create_time,to_char(r.update_time,'hh24:mi:ss') as update_time,seconds_diff(r.create_time,r.update_time) as seconds,r.ip_address,r.return_code,r.return_message from pas_time_task_record r inner join pas_time_task t on r.id=t.last_execute_record_id where t.job_status='0' order by t.job_name;

--
select * from txs_withdraw_trade_order t where t.create_time > sysdate - 1 and t.pay_state='03'

select t.customer_code,t.customername,t.out_trade_no,t.transaction_no,t.total_fee,t.create_time, t.pay_state,r.state,r.institution_name,t.error_code,r.channel_resp_code,r.channel_resp_msg from txs_withdraw_trade_order t left join clr_withdraw_record r on t.transaction_no=r.transaction_no where t.create_time > sysdate -1 and t.create_time < sysdate - 1/48 and t.pay_state ='03' order by t.create_time desc;
--
select to_char(t.create_time,'yyyy-mm-dd hh24:mi:ss') as time,count(0) as c from txs_pay_trade_order t where t.create_time > sysdate -1 group by to_char(t.create_time,'yyyy-mm-dd hh24:mi:ss') order by count(0) desc;
