@startuml 结汇Skyee版
title 结汇Skyee

actor 商户 as M
actor 运营 as O
box 跨境系统 #LightBlue
participant 汇通门户 as HT
participant 汇通运营  as HTO
participant 跨境系统  as KJ
participant 易云帐VA as VA
end box
participant "Skyee/全球付/CC"  as CC #Grey

==结汇（付款），通过跨境资金进入客户结算账户==
group 结汇单在汇通体系中流转
    M -> HT: 申请付款
    HT -> VA: 查询手续费（率）
    VA --> HT: 返回手续费（率）
    HT --> M: 结汇单状态：待确认
    M -> HT: 提交结汇申报材料
    HT --> M: 结汇单状态：待确认
    M -> HT: 确认结汇，参数：支付密码
    HT --> M: 结汇单状态：待HT审核
    O -> HTO: HT审核结汇单
    HTO --> O: 更新订单状态：结汇中
end group 

loop 结汇单准备进入跨境系统（定时任务），根据上游循环，依次：Skyee、全球付、CC
    HT -> HT: 查询特定上游商户的\n结汇中状态的结汇单
    HT -> HT: 汇总&生成交易清单数据
    note right of HT
        每笔付款单单独生成一份Excel
        参考：交易清单生成规则说明.xlsx
    end note
    HT -> HT: 汇总&生成总交易清单
    HT -> CC: 提现请求
    note left of CC
        1、CC：①每笔调用transfer接口；②汇总后调用payment接口。
        2、全球付：remitOrder/apply接口。
        3、Skyee：AgentPay接口。
    end note
    CC -[#red]\ HT: 提现到账通知（资金到人跨户）
    HT -> HT: 更新该批状态：待付款
    HT -\ KJ: 总交易清单通过FTP传给跨境
end 
group 定时查询付款
    HT -> KJ: 查询平台商账户余额
    KJ --> HT: 返回平台商账户余额
    alt 余额足够，处理代付款批次结汇单
        HT -> KJ: 收单提现，接口：transOutApply，走HTTP协议
        KJ --> HT: 返回结果
    end 
end group 
KJ -[#red]\ HT: 收款提现异步通知
HT -[#red]> HT: 更新结汇单状态：结汇成功
@enduml