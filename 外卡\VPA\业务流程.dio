<mxfile host="65bd71144e">
    <diagram id="y4tyWu-VfpptcUMsJD_X" name="第 1 页">
        <mxGraphModel dx="804" dy="607" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" background="none" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="持卡人" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fontSize=13;" parent="1" vertex="1">
                    <mxGeometry x="50" y="250" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="客户" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fontSize=13;" parent="1" vertex="1">
                    <mxGeometry x="50" y="50" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="商家" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fontSize=13;" parent="1" vertex="1">
                    <mxGeometry x="325" y="292" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="机构&lt;div&gt;(EPAYLINKS)&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=13;" parent="1" vertex="1">
                    <mxGeometry x="280" y="40" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="银行" style="rounded=1;whiteSpace=wrap;html=1;fontSize=13;" parent="1" vertex="1">
                    <mxGeometry x="599" y="250" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="卡组(VISA)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=13;" parent="1" vertex="1">
                    <mxGeometry x="599" y="40" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="" style="endArrow=classic;html=1;fillColor=#d80073;strokeColor=#A50040;fontSize=13;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="110" y="80" as="sourcePoint"/>
                        <mxPoint x="260" y="80" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="9" value="1️⃣申请开卡" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=13;" parent="8" vertex="1" connectable="0">
                    <mxGeometry x="-0.3158" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="" style="endArrow=classic;html=1;fillColor=#d80073;strokeColor=#A50040;fontSize=13;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="420" y="56" as="sourcePoint"/>
                        <mxPoint x="580" y="56" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="2️⃣开卡" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=13;" parent="10" vertex="1" connectable="0">
                    <mxGeometry x="0.2584" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="12" value="" style="endArrow=classic;html=1;fillColor=#d80073;strokeColor=#A50040;fontSize=13;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="689" y="130" as="sourcePoint"/>
                        <mxPoint x="689" y="230" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="2️⃣b开卡同步" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=13;" parent="12" vertex="1" connectable="0">
                    <mxGeometry x="-0.42" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="13" value="" style="endArrow=classic;html=1;dashed=1;fontSize=13;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="64.41" y="151" as="sourcePoint"/>
                        <mxPoint x="64" y="240" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" value="3️⃣虚拟卡分发" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=13;" parent="13" vertex="1" connectable="0">
                    <mxGeometry x="-0.4857" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="15" value="" style="endArrow=classic;html=1;fillColor=#0050ef;strokeColor=#001DBC;fontSize=13;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="110" y="290" as="sourcePoint"/>
                        <mxPoint x="290" y="320" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="4️⃣消费" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=13;" parent="15" vertex="1" connectable="0">
                    <mxGeometry x="0.2643" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="17" value="" style="endArrow=classic;html=1;fillColor=#0050ef;strokeColor=#001DBC;fontSize=13;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="380" y="320" as="sourcePoint"/>
                        <mxPoint x="580" y="300" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="5️⃣授权交易" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=13;" parent="17" vertex="1" connectable="0">
                    <mxGeometry x="-0.1885" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="20" value="" style="endArrow=classic;html=1;fillColor=#0050ef;strokeColor=#001DBC;fontSize=13;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="110" y="260" as="sourcePoint"/>
                        <mxPoint x="580" y="260" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="5️⃣b授权" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=13;" parent="20" vertex="1" connectable="0">
                    <mxGeometry x="-0.1885" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="22" value="" style="endArrow=classic;html=1;fillColor=#6a00ff;strokeColor=#3700CC;fontSize=13;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="629" y="230" as="sourcePoint"/>
                        <mxPoint x="629" y="130" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="23" value="6️⃣交易同步" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=13;" parent="22" vertex="1" connectable="0">
                    <mxGeometry x="-0.1885" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="24" value="" style="endArrow=classic;html=1;fillColor=#6a00ff;strokeColor=#3700CC;fontSize=13;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="580" y="94" as="sourcePoint"/>
                        <mxPoint x="420" y="94" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="25" value="6️⃣b交易同步" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=13;" parent="24" vertex="1" connectable="0">
                    <mxGeometry x="-0.1885" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>