@startuml 管理收款人
title 管理收款人
actor 商户 as M
box 跨境系统 #LightBlue
participant 汇通全球 as MP
participant 风控模块 as RC
end box
participant 易票联EP as EP

M -> MP: 添加收款人
MP -> RC: 黑名单判断
RC --> MP: 返回结果：正常、黑名单
alt 是黑名单
    ...不保存数据，返回提示信息，结束...
else 非黑名单
    MP -> MP: 保存收款人信息，状态：待审核
    MP -> RC: 名单筛查
    RC -[#red]\ MP: 异步通知筛查结果
    alt 正常，筛查结果正常
        MP -> EP: 结算户进件
        EP --> MP: 结算户进件结果
        MP --> M: 添加收款人成功
    else 非正常
        MP --> M: 添加收款人失败，状态禁用
    end
end
@enduml

@startuml 商户注册
title 商户注册
actor 商户 as M
actor 运营 as O
box 跨境系统 #LightBlue
participant 汇通全球 as MP
participant 风控模块 as RC
end box
participant CurrencyCloud as CC #Grey

M -> MP: 客户注册
M -> MP: 客户提交资料
MP -> RC: 黑名单判断
RC --> MP: 返回结果：正常、黑名单
alt 是黑名单
    MP -> MP: 风控不通过，客户注册失败，流程结束
end
MP -> MP: 客户状态：待审核
O -> MP: 客户审核
alt 审核通过
    MP -> MP: 客户状态：审核通过
MP -> CC: 申请开户
note left of CC: Account.Create接口\nyour_reference传入CustomerCode
CC --> MP: account_id和状态
MP -> CC: 创建联系人
note left of CC: Contact.Create接口\nyour_reference传入ContactID
CC --> MP: contact_id和状态
else 审核不通过
    MP -> MP: 审核不通过，客户注册失败
end
O -> MP: 发起名单筛查\n或审核通过后自动审查
MP -> RC: 名单筛查
RC -[#red]\ MP: 异步通知筛查结果
alt 正常，筛查结果正常
    MP -> MP: 更新客户筛查状态：正常
else 非正常
    MP -> MP: 更新客户筛查状态：非正常
end
@enduml


@enduml

@startuml 商户资料修改
title 商户资料修改
actor 商户 as M
actor 运营 as O
box 汇通全球 #LightBlue
participant 商户门户 as MP
participant 运营门户 as OP
end box
participant 渠道 as CC #Grey

==商户注册成功==
autonumber 1.1
OP -> OP: 克隆数据到草稿表

==商户修改资料==
autonumber 2.1
M -> MP: 商户登录
MP --> M: 返回草稿状态资料
M -> MP: 商户查看并提交资料
note left of MP: 无需风控处理
MP -> MP: 修改状态：待审核

==客户修改审核==
autonumber 3.1
O -> OP: 客户更新审核
note left of OP
    客户注册审核有初/复审
    客户可以修改所有资料，等同于重新注册
    客户修改审核仅有1个环节
end note
alt 审核通过
    OP -> CC: 修改开户资料
    note left of CC
    CC接口：POST /v2/accounts/{id}
    全球付接口：/api/merchant/apply
    end note
    loop 待上游审核完成
        CC -\ OP: 通知上游审核结果
        note left of CC
        CC直接返回修改结果
        全球付待异步通知或查询处理
        end note
    end loop
    alt 上游审核通过
        OP -> OP: 客户状态：审核通过
        OP -> OP: 生成版本修改记录
        note left of OP
        保存“更新资料审核”页面内容
        后续追溯每次修改的内容
        end note
        OP -> OP: 克隆数据到正式表
    else 上游审核不通过
        OP -> OP: 客户状态：审核不通过
    end
    MP -\ M: 通知商户修改结果
else 审核不通过
    OP -> OP: 客户状态：审核不通过
    MP -\ M: 通知商户修改结果
end
@enduml