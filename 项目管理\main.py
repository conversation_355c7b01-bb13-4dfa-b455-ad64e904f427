import tkinter as tk
from tkinter import filedialog, messagebox
import sys
import os

# 动态导入TFS统计.py
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from TFS统计 import stat_task

def select_output_path():
    path = filedialog.askdirectory()
    if path:
        output_path_var.set(path)

def on_submit():
    version = version_var.get()
    start_date = start_date_var.get()
    output_path = output_path_var.get()
    if not version or not start_date or not output_path:
        messagebox.showwarning("提示", "请填写所有字段")
        return
    try:
        stat_task(version, start_date=start_date, base_dir=output_path)
        messagebox.showinfo("完成", "任务已完成")
    except Exception as e:
        messagebox.showerror("错误", f"执行失败: {e}")

root = tk.Tk()
root.title("TFS 统计工具")

tk.Label(root, text="版本号:").grid(row=0, column=0, padx=5, pady=5, sticky="e")
version_var = tk.StringVar()
tk.Entry(root, textvariable=version_var, width=30).grid(row=0, column=1, padx=5, pady=5)

tk.Label(root, text="开始日期:").grid(row=1, column=0, padx=5, pady=5, sticky="e")
start_date_var = tk.StringVar()
tk.Entry(root, textvariable=start_date_var, width=30).grid(row=1, column=1, padx=5, pady=5)

tk.Label(root, text="输出路径:").grid(row=2, column=0, padx=5, pady=5, sticky="e")
output_path_var = tk.StringVar()
tk.Entry(root, textvariable=output_path_var, width=30).grid(row=2, column=1, padx=5, pady=5)
tk.Button(root, text="选择...", command=select_output_path).grid(row=2, column=2, padx=5, pady=5)

tk.Button(root, text="开始统计", command=on_submit, width=20).grid(row=3, column=0, columnspan=3, pady=15)

root.mainloop()
