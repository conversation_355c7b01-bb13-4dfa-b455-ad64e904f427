@startuml AE收银台业务交互顺序图

title AE收银台业务交互顺序图

actor 用户 as u
participant 商户 as m
box EPSP
    participant cash
    participant txs
    participant clr
    participant posp
end box
participant AE
note left of posp
POSP等同于AE的前置，属于上游的一部分。
因此clr的transaction_no，
对于POSP而言即是out_trade_no。
清结算时，是不会考虑POSP中的交易数据的。
end note
txs -[#red]> txs: 初始化业务数据\n和卡BIN数据
u -> m: 下单
m -> txs: 调用统一下单接口
txs -> txs: 创建商户单
txs --> m: 收银台URL
m --> u: 收银台URL
u -> cash: 使用浏览器打开收银台URL
cash --> u: 返回商户开通的业务
u -[#red]> u: 展现快捷支付页面\n业务中包含：\n①快捷支付\n②AE快捷支付
u -> txs: 输入并提交快捷支付信息
txs -> txs: 验证快捷支付信息
txs -[#red]> txs: 根据卡号计算实际业务\n并且创建支付单
txs -> clr: 申请上游支付
clr -[#red]> posp: 根据业务类型调用\nPOSP外卡收单接口
posp -[#red]> AE: 发起收单接口调用
AE -> posp: 返回支付结果
posp -> clr: 返回支付结果
clr -> txs: 返回收单结果
txs -> txs: 更新支付单状态
note right of txs
    实时返回支付单状态
    成功或失败
end note
txs -> u: 返回支付结果
txs -\ m: 异步通知支付结果

@enduml