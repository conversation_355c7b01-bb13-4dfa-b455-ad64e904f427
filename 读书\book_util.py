import re
import os

def process_text(input_file_path, output_file_path):
    with open(input_file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    processed_lines = []
    full_line = ""
    for line in lines:
        if line.startswith('###'):
            if full_line != "":
                processed_lines.append(full_line)
                full_line = ""
            processed_lines.append(line.strip())
        else:
            full_line += line.strip()
            if re.search(r'[。”！】》？]$', line):
                # if full_line.count('“') > full_line.count(' ”'):
                #     continue
                processed_lines.append(full_line)
                full_line = ""

    with open(output_file_path, 'w', encoding='utf-8') as file:
        for line in processed_lines:
            if line.startswith('###'):
                file.write('\n\n\n' + line + '\n')
            else:
                line = re.sub(r'(?<!岳)父(?!亲)', '父亲', line)
                line = re.sub(r'岳(?!父|母)', '岳母', line)
                file.write('\n    ' + line)
    # for line in processed_lines:
    #     print(line)
    #     print(list(line))

def split_file(input_file_path, output_file_path):
    with open(input_file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    current_file = None
    current_content = []

    for line in lines:
        if line.startswith('###'):
            if current_file:
                with open(current_file, 'w', encoding='utf-8') as out_file:
                    out_file.writelines(current_content)
                current_content = []
            current_file =  output_file_path + line.strip()[3:] + '.txt'
        else:
            current_content.append(line)

    if current_file:
        with open(current_file, 'w', encoding='utf-8') as out_file:
            out_file.writelines(current_content)

