import requests
from bs4 import BeautifulSoup
import re
import os
import urllib.request

def get_content(url):
    response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36'})
    response.encoding = 'GBK'
    # print(response.text)
    soup = BeautifulSoup(response.text, 'html.parser')
    pb_next_a = soup.find('a', id='pb_next')
    pb_next = pb_next_a.attrs['href']
    div = soup.find('div', id='nr1')
    text = div.get_text()
    print(url, len(text))
    return text, 'http://m.ltxswu.org'+pb_next

def get_book(url, save_file):
    text, url = get_content(url)
    if save_file:
        with open(save_file, 'a', encoding='utf-8') as f:
            f.write(text)
    if url:
        try:
            get_book(url, save_file)
        except Exception as e:
            pass
        
import re

def process_file(file_path):
    replacements = [
        r"地址发布页",
        r"地址发布页邮箱：\s*<EMAIL>",
        r"01bz.cc",
        r"更多小说\s*01bz.cc",
        r"龙腾小说\s*<EMAIL>",
        r"更多小说\s*ltxsba.xyz",
        r"地址发布邮箱\s*<EMAIL>",
        r"更多小说\s*ltxsba.info",
        r"地址发布页\s*ltxsba.info",
        r"更多小说\s*01bz.cc\s*",
        r"更多小说\s*ltxsba.info\s*",
        r"<EMAIL>\s*无法打开网站可发任意内容找回最新地址",
        r"地址发布页\s*01bz.cc请记住邮箱：<EMAIL>"
    ]
    with open(file_path, 'r+', encoding='utf-8') as file:
        lines = file.readlines()
        processed_lines = []
        current_line = ''

        for line in lines:
            line = line.strip()
            if line.endswith('。') or line.endswith('”') or line.endswith('」') or line.endswith('）'): 
                current_line += line
                processed_lines.append(current_line)
                current_line = ''
            else:
                current_line += line

        if current_line:
            for pattern in replacements:
                current_line = re.sub(pattern, "", current_line, flags=re.IGNORECASE)
            lines = current_line.split("   ")
            for line in lines:
                if line:
                    processed_lines.append(line)

        file.seek(0)
        file.write('\n'.join(processed_lines))
        file.truncate()
        
if __name__ == '__main__':
    file_name="C:/TMP/XS/修罗劫.txt"
    datas = [("http://m.ltxswu.org/book/7063/93572.html", "吟乱豪门"),
             ("http://m.ltxswu.org/book/7055/93432.html", "宝莲灯之风流猎艳")
             ]
    for url, file_name in datas:
        file_path = "C:/TMP/XS/"+file_name+".txt"
        # get_book(url, file_path)
        process_file(file_path)