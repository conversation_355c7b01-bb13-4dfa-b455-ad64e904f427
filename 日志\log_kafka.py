from kafka import KafkaConsumer
import threading
import os
import json

log_root = "D:/TMP/logs"

def listen_kafka(topic):
    def output_logs(logs, head):
        if not os.path.exists(f'{log_root}/{topic}'):
            os.makedirs(f'{log_root}/{topic}')
        with open(f'{log_root}/{topic}/{head}.log', 'a', encoding='utf-8') as file:
            for log in logs:
                file.write(log+"\n")
    #---------------------------------
    consumer = KafkaConsumer(
        topic,
        bootstrap_servers=['172.16.2.8:9092'],
        auto_offset_reset='earliest',
        enable_auto_commit=True,
        group_id='py-log-group',
        value_deserializer=lambda x: x.decode('utf-8')
    )
    pre_head = None
    logs = []
    for message in consumer:
        json_message = json.loads(message.value)
        log = json_message['log']
        cur_head = log[0:13]
        cur_head = cur_head.replace('{', '')
        cur_head = cur_head.replace('"', '')
        cur_head = cur_head.replace('\\', '')
        cur_head = cur_head.replace('\'', '')
        if pre_head != cur_head:   # write logs to file
            if pre_head:
                output_logs(logs, pre_head)
                logs.clear()
            pre_head = cur_head
            logs.append(log)
        else:
            if len(logs) >= 100:   # write logs to file
                output_logs(logs, pre_head)
                logs.clear()
            logs.append(log)
        print(f'----->received {topic} message:', message.key, message.value)

def start_kafka_consumer():
    topics = ['txs', 'pay', 'clr', 'acc', 'cum', 'cust']
    threads = []
    for topic in topics:
        t = threading.Thread(target=listen_kafka, args=(topic,))
        t.start()
        threads.append(t)
    for t in threads:
        t.join()

if __name__ == '__main__':
    topics = ['CLR_PayGatewayResult', 'PAY_PayResult']
    threads = []
    for topic in topics:
        t = threading.Thread(target=listen_kafka, args=(topic,))
        t.start()
        threads.append(t)
    for t in threads:
        t.join()