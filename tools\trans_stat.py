import matplotlib.pyplot as plt
import numpy as np


def demo1():
    # 示例数据
    x = [1, 2, 3, 4, 5, 6]
    y = [2, 4, 6, 8, 10, 9]

    # 绘制曲线
    plt.plot(x, y)

    # 设置标题和坐标轴标签
    plt.title("Curve Example")
    plt.xlabel("X-axis")
    plt.ylabel("Y-axis")

    # 显示图形
    plt.show()
    
def demo2():
    # 设置一个数据源
    x_data = np.linspace(0, 10, 100)
    y_data = np.sin(x_data)

    # 初始化图表
    fig, ax = plt.subplots()
    line, = ax.plot(x_data, y_data)

    # 设置刷新间隔
    refresh_interval = 0.1

    # 不断更新数据并重新画图
    for i in range(100):
        y_data = np.sin(x_data + i / 10)
        line.set_ydata(y_data)
        fig.canvas.draw()
        plt.pause(refresh_interval)
    
