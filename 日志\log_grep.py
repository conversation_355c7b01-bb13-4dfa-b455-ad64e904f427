#把EPSP日志格式转换为树状结构
import re
import sys
import glob

log_root = "D:/TMP/logs"

def grep_log_files(log_dir, file_pattern):
    log_files = []
    print(f"grep_log_files: {log_dir} / {file_pattern}")
    files = glob.glob(log_dir + '/'+file_pattern)
    for file in files:
        log_files.append(file)
    return log_files

def grep_file(log_file, keywords):
    logs = []
    thread_logs = {}
    thread_pre_num = {}
    thread_found = {}
    pattern = re.compile(keywords)
    
    with open(log_file, 'r', encoding='utf-8') as f:
        full_line = None
        for line in f.readlines():
            if not full_line:
                full_line = line
            else:
                full_line += "\t"+line
            log = full_line.split("|")
            if len(log) < 16:
                continue
            #开始分析日志
            thread = f"{log[4]}-{log[1]}-{log[2]}"
            if thread not in thread_logs:
                thread_logs[thread] = []
            if thread not in thread_found:
                thread_found[thread] = False
            if thread not in thread_pre_num:
                thread_pre_num[thread] = 0
            
            if "PRE" in log[12]:
                thread_pre_num[thread] += 1
            if not thread_found[thread]:
                if re.search(pattern, full_line):
                    thread_found[thread] = True
            
            if len(thread_logs[thread]) == 0:
                thread_logs[thread].append((f"----- {thread} -----"))
            thread_logs[thread].append((f"{log[0].strip()}\t{log[11].strip()}\t {xjust(log[8].strip())}\t{'--->' * (thread_pre_num[thread]-1)}{log[6].strip().split('.')[-1]}.{log[7].strip()}\t{log[14].strip()}"))
            if "POST" in log[12]:
                if thread_pre_num[thread] == 1:
                    if thread_found[thread]:
                        if len(logs) == 0:
                            logs.append(f"\n ===== Source file: {log_file} ===== \n")
                        logs.extend(thread_logs[thread])
                    thread_logs[thread].clear()
                    thread_found[thread] = False
                thread_pre_num[thread] -= 1
                if thread_pre_num[thread] < 0:
                    thread_pre_num[thread] = 0
            full_line = None
    return logs

def xjust(t):
    return " " * (6 - len(t)) + t + ""

def get_cls_name(x):
    return x.split(".")[-1]

def output_logs(logs, dest_file = None):
    if dest_file is not None:
        with open(dest_file, 'a', encoding='utf-8') as file:
            for log in logs:
                file.write(log+"\n")
    else:
        for log in logs:
            print(log)

def grep_dir(log_dir, file_pattern, keywords):
    log_files = grep_log_files(log_dir, file_pattern=file_pattern)
    print(log_files)
    logs = []
    for log_file in log_files:
        logs.extend(grep_file(log_file, keywords = keywords))
    return logs

def test_log_grep(keywords, dest_file):
    logs = grep_dir(log_root, "*.log", keywords = keywords)
    output_logs(logs, dest_file = dest_file)
    
from flask import Flask, Response
import os

app = Flask(__name__)

@app.route('/')
def index():
    return '欢迎使用日志查询助手!'

@app.route('/html/<module>/<pattern>/<keywords>')
def grep_html(module, pattern, keywords):
    print(pattern, keywords)
    logs = grep_dir(f"{log_root}/{module}", f"*{pattern}*.log", keywords)
    resp = '<div><pre>'.join(f'{elem.replace("<", "&lt;").replace(">", "&gt;")}</pre></div>' for elem in logs)
    resp.replace(keywords, "<font color='red'>"+keywords+"</font>")
    return Response(resp, content_type='text/html; charset=utf-8')

@app.route('/text/<module>/<pattern>/<keywords>')
def grep_text(module, pattern, keywords):
    print(pattern, keywords)
    logs = grep_dir(f"{log_root}/{module}", f"*{pattern}*.log", keywords)
    resp = '\n'.join(f'{elem}' for elem in logs)
    return Response(resp, content_type='text/plain; charset=utf-8')


def start_flask_server():
    app.run(host='0.0.0.0', port=5000, debug=True)
    
##############################################################
if __name__ == '__main__':
    # start_kafka_consumer()
    start_flask_server()