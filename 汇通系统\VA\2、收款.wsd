@startuml 收款单状态图
title 收款单状态图

state "初始化"  as CSH
state "风控处理中" as FKCL{
    state "黑名单校验" as HMD
    state "名单筛查" as MDSC
}
state "风控结果" as FKJG <<choice>>
state "未通过风控检查" as FKSB
state "待提交资料" as DZL
state "待审核" as DSH
state "审核结果" as SHJG <<choice>>
state "处理中" as CLZ
state "处理结果" as CLJG <<choice>>
state "成功" as CG
state "失败" as SB

[*] -> CSH: 上游通知有收款记录
CSH --> FKCL: 上游通知款项到位
FKCL --> FKJG: 风控处理完成
FKJG ---> FKSB: 风控校验失败
FKJG -> DZL: 风控校验通过

DZL --> DSH: 提交资料
DSH --> SHJG: 审核完成
SHJG -> CLZ: 审核通过
SHJG --> DZL: 审核不通过

CLZ --> CLJG: 处理完成
CLJG --> CG: 成功
CLJG --> SB: 失败
CG --> [*]: 成功
SB --> [*]: 失败
FKSB --> [*]: 失败

note right of CSH
    收到收款异步通知（如CC，状态为Pending等）
    或者通过查询接口查询到未处理的收款单
    上游收款单状态为Completed等后进入风控处理
end note
note right of FKCL
    风控处理流程
    等待中
end note
note bottom of CLZ
    一般运营线下处理，向渠道提供款项材料
end note

@enduml

@startuml CC收款流程
title CC收款流程
actor 商户 as M
actor 运营 as O
actor 付款人 as P
box 汇通平台 #LightBlue
    participant 汇通全球 as HT
    participant 易云帐VA as VA
    participant 风控模块 as RC
end box
participant "CurrencyCloud" as CC #Grey

M -> HT: 登录
HT --> M: 进入汇通全球
M -> HT: 查询收款账户
HT -> CC: 查询收款账户
note right of CC: Find Funding Accounts接口\n参数account_id和currency
CC --> HT: 收款账户
HT --> M: 展现收款账户

M ---\ P: 提供收款账户(线下)
P -[#red]\ CC: 完成付款（线下银行转款等）
note right of P: 银行转账等方式，demo环境使用Emulate inbound funds接口模拟

==异步通知处理==
CC ->> HT: 付款异步通知，Pending状态
HT -> HT: 创建收款单，状态：初始化
CC ->> HT: 付款异步通知，Deleted状态
HT -> HT: 更新（创建）收款单，状态：失败
CC ->> HT: 付款异步通知，Completed状态
HT -> HT: 更新（创建）收款单，状态：风控处理中
==获取付款人信息==
HT -> CC: 查询付款人信息（Get Sender Details接口）
CC --> HT: 付款人信息
HT -> HT: 保存付款人信息
==风控处理==
HT -> RC: 请求黑名单（收款人、付款人）判定
RC --> HT: 返回结果
alt 是黑名单
    HT -> HT: 更新收款单，状态：未通过风控检查
    ...手动安排退款...
else 非黑名单
    HT -> RC: 请求名单筛查
    RC -[#red]\ HT: 异步通知筛查结果
    alt 名单筛查结果可疑
        HT -> HT: 更新收款单，状态：未通过风控检查
        ...手动安排退款...
    end
    group 调用风控限额接口
        HT -> RC: 调用交易风控接口（限额、限次）
        RC --> HT: 返回风控结果
        alt 风控未通过，标记风控结果
            HT -> HT: 更新收款单，状态：未通过风控检查
            ...手动安排退款...
        end 
    end group 
    HT -> HT: 更新订单状态：待提交资料
    M -> HT: 提交收款资料
    HT -> HT: 更新订单状态：待审核
    O -> HT: 审核
    alt 审核通过
        HT -> VA: 加额（充值接口）
        VA --> HT: 返回充值结果+手续费
        note right of VA: 手续费=实际收款金额*商户的收款费率\n手续费内扣，通知收款金额=手续费+商户加额
        HT -> HT: 更新收款单，状态：成功，登记手续费
        HT -[#red]> CC: 转存手续费，接口Transfer
        CC --[#red]> HT: 返回转存结果
    else 审核未通过
        HT -> HT: 更新订单状态：待提交资料
        ...继续提交资料...
    end

end
@enduml

@startuml 全球付/SKYEE收款流程
title 全球付/SKYEE收款流程
actor 商户 as M
actor 运营 as O
actor 付款人 as P
box 汇通平台 #LightBlue
    participant 汇通全球 as HT
    participant 易云帐VA as VA
    participant 风控模块 as RC
end box
participant "全球付" as CC #Grey

M -> HT: 登录
HT --> M: 进入汇通全球
M -> HT: 查询收款账户
HT --> M: 展现收款账户

M ---\ P: 提供收款账户(线下)
P -[#red]\ CC: 完成付款（线下银行转款等）
note right of P: 银行转账等方式

==异步通知处理==
CC -[#red]\ HT: 资金入账通知
HT -> CC: 资金入账查询，接口：rechargeOrder/query

alt status=06(资金到账)
    HT -> HT: 创建收款单（如无），状态：初始化
    alt 未做风控处理
        HT -> HT: 更新收款单，状态：风控处理中
        HT -> RC: 请求黑名单（收款人、付款人）判定
        RC --> HT: 返回结果
        alt 是黑名单
            HT -> HT: 更新收款单，状态：未通过风控检查
            ...手动安排退款...
        else 非黑名单
            HT -> RC: 请求名单筛查
            RC -[#red]\ HT: 异步通知筛查结果
            alt 名单筛查结果可疑
                HT -> HT: 更新收款单，状态：未通过风控检查
                ...手动安排退款...
            end
            group 调用风控限额接口
                HT -> RC: 调用交易风控接口（限额、限次）
                RC --> HT: 返回风控结果
                alt 风控未通过，标记风控结果
                    HT -> HT: 更新收款单，状态：未通过风控检查
                    ...手动安排退款...
                end 
            end group 
        end
    end
    HT -> HT: 更新订单状态：待提交资料

    group 提交资料并审核
        M -> HT: 提交收款资料
        HT -> HT: 更新订单状态：待审核
        O -> HT: 审核
        alt 审核通过
            HT -[#red]> CC: 资金入账确认，接口：rechargeOrder/confirm
            HT -[#red]> HT: 更新收款单，状态：处理中
        else 审核未通过
            HT -> HT: 更新订单状态：待提交资料
            ...继续提交资料...
        end
    end group

else status=06(审核驳回)
    HT -> HT: 更新订单状态：待提交资料

    group 提交资料并审核
        M -> HT: 提交收款资料
        HT -> HT: 更新订单状态：待审核
        O -> HT: 审核
        alt 审核通过
            HT -[#red]> CC: 资金入账确认，接口：rechargeOrder/confirm
            HT -[#red]> HT: 更新收款单，状态：处理中
        else 审核未通过
            HT -> HT: 更新订单状态：待提交资料
            ...继续提交资料...
        end
    end group

else status=04(审核拒绝)
    HT -> HT: 更新订单状态：渠道失败
else status=05(提交KYC材料) 或 07(审核中)
    ...Do Nothing...
else status=00(入账成功)
    HT -> VA: 加额（充值接口）
    VA --> HT: 返回充值结果+手续费
    note right of VA: 手续费=实际收款金额*商户的收款费率\n手续费内扣，通知收款金额=手续费+商户加额
    HT -> HT: 更新收款单，状态：成功，登记手续费

end
@enduml