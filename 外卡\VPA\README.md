# 业务介绍

VPA业务

## 开卡

1. 三方合作：商户、机构、银行（发卡）、VISA
2. 商户 --> 机构申请合作
3. 机构 --> 银行申请发卡
4. 银行 --> VISA 完成发卡，虚拟卡作为某信用卡主卡，设定限额，完成发卡
5. 商户分发虚拟卡
6. 在虚拟卡的卡片消费交易

## 交易

同信用卡交易，特定场景

## 充值

对所属主卡偿还欠费

## **管理**

# 遗留问题

机构在这种模式下如何获益

# 证书更新

## 证书更新命令

1.Create a new clientkeystore.jks
keytool -genkeypair -alias client -keyalg RSA -keysize 2048 -keystore clientkeystore.jks -storepass `<password>` -keypass `<password>` -dname "CN=`<common name>`, OU=`<organizational unit>`, O=`<organization name>`, L=<city/locality name>, ST=`<state name>`, C=`<country name>`, UID=`<CSR unique Id>`"

keytool -genkeypair -alias client -keyalg RSA -keysize 2048 -keystore cert_clientkeystore.jks -storepass epaylinks2022 -keypass epaylinks2022 -dname "CN=test-kj.epaylinks.cn, OU=kj, O=epaylinks, L=guangzhou, ST=guangdong, C=cn, UID=7d305045-104f-4178-b2a4-58c467374c4f-CERT"

2.Create a new CSR
keytool -certreq -alias client -keystore clientkeystore.jks -storepass `<password>` -keypass `<password>` -file certreq1.csr

keytool -certreq -alias client -keystore cert_clientkeystore.jks -storepass epaylinks2022 -keypass epaylinks2022 -file certreq1.csr

3.相关证书添加到 JKS 文件中
keytool -import -alias root -keystore cert_clientkeystore.jks -file VDPCA.pem -storepass epaylinks2022
keytool -import -alias interm -keystore cert_clientkeystore.jks -file VDPCAI.pem -storepass epaylinks2022
keytool -import -alias client -keystore cert_clientkeystore.jks -file cert.pem -storepass epaylinks2022
keytool -import -alias digicert -keystore cert_clientkeystore.jks -file DigiCertGlobalRootCA.crt -storepass epaylinks2022

20250305纠正如下：

3.1 keytool -import -alias root -keystore clientkeystore.jks -file ServicesCARoot.pem -storepass `<password>`
3.2 keytool -import -alias interm -keystore clientkeystore.jks -file ServicesCAInter.pem -storepass `<password>`
3.3 keytool -import -alias client -keystore clientkeystore.jks -file cert.pem -storepass `<password>`
