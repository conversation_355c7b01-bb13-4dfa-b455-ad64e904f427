<mxfile host="65bd71144e">
    <diagram id="UMWA_ceHav2_FxVM2kZk" name="第 1 页">
        <mxGraphModel dx="345" dy="563" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="4" value="AD with TLS" style="image;html=1;image=img/lib/clip_art/computers/Server_Tower_128x128.png" parent="1" vertex="1">
                    <mxGeometry x="374" y="210" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="IE(5SV)" style="image;html=1;image=img/lib/clip_art/computers/Server_128x128.png" parent="1" vertex="1">
                    <mxGeometry x="180" y="130" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="IE(5SW)" style="image;html=1;image=img/lib/clip_art/computers/Server_128x128.png" parent="1" vertex="1">
                    <mxGeometry x="180" y="280" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="APP Server 1" style="image;aspect=fixed;perimeter=ellipsePerimeter;html=1;align=center;shadow=0;dashed=0;spacingTop=3;image=img/lib/active_directory/generic_server.svg;" parent="1" vertex="1">
                    <mxGeometry x="564" y="145" width="28.000000000000004" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="APP Server n" style="image;aspect=fixed;perimeter=ellipsePerimeter;html=1;align=center;shadow=0;dashed=0;spacingTop=3;image=img/lib/active_directory/generic_server.svg;" parent="1" vertex="1">
                    <mxGeometry x="564" y="295" width="28.000000000000004" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="" style="endArrow=classic;startArrow=classic;html=1;" parent="1" source="8" target="4" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="320" y="380" as="sourcePoint"/>
                        <mxPoint x="370" y="330" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="12" value="" style="endArrow=classic;startArrow=classic;html=1;" parent="1" source="7" target="4" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="270" y="316" as="sourcePoint"/>
                        <mxPoint x="384" y="274" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="13" value="" style="endArrow=classic;startArrow=classic;html=1;" parent="1" source="4" target="9" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="440" y="242" as="sourcePoint"/>
                        <mxPoint x="560" y="200" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" value="" style="endArrow=classic;startArrow=classic;html=1;" parent="1" source="4" target="10" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="464" y="242" as="sourcePoint"/>
                        <mxPoint x="590" y="186" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="15" value="IE是连接客户端，是连接发起者，因此必须让所有IE连接到所有的AppServer，这样，当某个IE故障时，所有的AppServer不受影响。" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;" parent="1" vertex="1">
                    <mxGeometry x="200" y="440" width="440" height="100" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="Srp5P0Uo8x7OS6UDJMG4" name="第 2 页">
        <mxGraphModel dx="812" dy="563" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="EAD5oMnTOrEEoMUgsBRq-9" value="CFT（万事网联）" style="image;html=1;image=img/lib/clip_art/computers/Server_Tower_128x128.png" parent="1" vertex="1">
                    <mxGeometry x="334" y="170" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="EAD5oMnTOrEEoMUgsBRq-10" value="国内客户" style="image;aspect=fixed;perimeter=ellipsePerimeter;html=1;align=center;shadow=0;dashed=0;spacingTop=3;image=img/lib/active_directory/generic_server.svg;" parent="1" vertex="1">
                    <mxGeometry x="560" y="175" width="39.2" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="EAD5oMnTOrEEoMUgsBRq-12" value="万事达服务" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.server_migration_service;fillColor=#5294CF;gradientColor=none;" parent="1" vertex="1">
                    <mxGeometry x="90" y="163.5" width="76.5" height="93" as="geometry"/>
                </mxCell>
                <mxCell id="EAD5oMnTOrEEoMUgsBRq-13" value="" style="endArrow=classic;startArrow=classic;html=1;" parent="1" source="EAD5oMnTOrEEoMUgsBRq-9" target="EAD5oMnTOrEEoMUgsBRq-10" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="390" y="340" as="sourcePoint"/>
                        <mxPoint x="440" y="290" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="EAD5oMnTOrEEoMUgsBRq-14" value="SFTP协议" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="EAD5oMnTOrEEoMUgsBRq-13" vertex="1" connectable="0">
                    <mxGeometry x="0.2329" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="EAD5oMnTOrEEoMUgsBRq-16" value="" style="endArrow=classic;startArrow=classic;html=1;" parent="1" source="EAD5oMnTOrEEoMUgsBRq-12" target="EAD5oMnTOrEEoMUgsBRq-9" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="230" y="250" as="sourcePoint"/>
                        <mxPoint x="280" y="200" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="1Z7ZWJHLIeCOrNk19fvm" name="第 3 页">
        <mxGraphModel dx="1002" dy="563" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-2" value="万开&amp;nbsp;&lt;span style=&quot;background-color: transparent;&quot;&gt;pay-service1&lt;/span&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(63, 63, 63);&quot;&gt;*************:8003&lt;/span&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="40" y="120" width="123" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-3" value="agent1&lt;div&gt;*************:9017&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="242" y="120" width="168" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-4" value="AD" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="532" y="120" width="80" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-5" value="IE1" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="722" y="120" width="80" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-6" value="IE2" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="722" y="240" width="80" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-7" value="AD" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="532" y="240" width="80" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-9" value="" style="endArrow=classic;html=1;strokeColor=#0000CC;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="712" y="270" as="sourcePoint"/>
                        <mxPoint x="622" y="270" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-10" value="" style="endArrow=classic;html=1;strokeColor=#FF0000;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="712" y="250" as="sourcePoint"/>
                        <mxPoint x="622" y="160" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-11" value="" style="endArrow=classic;html=1;strokeColor=#00FF80;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="712" y="170" as="sourcePoint"/>
                        <mxPoint x="622" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-12" value="" style="endArrow=classic;html=1;strokeColor=#FFB056;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="712" y="149.5" as="sourcePoint"/>
                        <mxPoint x="622" y="149.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-13" value="" style="endArrow=classic;html=1;strokeColor=#FFB056;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="512" y="140" as="sourcePoint"/>
                        <mxPoint x="422" y="140" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-14" value="" style="endArrow=classic;html=1;strokeColor=#FF0000;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="512" y="160" as="sourcePoint"/>
                        <mxPoint x="422" y="160" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-15" value="agent2&lt;div&gt;10.200.152.42:9017&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="242" y="240" width="168" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-16" value="" style="endArrow=classic;html=1;strokeColor=#0000CC;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="514" y="280" as="sourcePoint"/>
                        <mxPoint x="424" y="280" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-17" value="" style="endArrow=classic;html=1;strokeColor=#33FF33;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="514" y="260" as="sourcePoint"/>
                        <mxPoint x="424" y="260" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-18" value="万开&amp;nbsp;&lt;span style=&quot;background-color: transparent;&quot;&gt;pay-service2&lt;/span&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(63, 63, 63);&quot;&gt;*************:8003&lt;/span&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="40" y="240" width="123" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-19" value="" style="endArrow=classic;startArrow=classic;html=1;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="167" y="150" as="sourcePoint"/>
                        <mxPoint x="237" y="150" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-20" value="" style="endArrow=classic;startArrow=classic;html=1;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="167" y="269.5" as="sourcePoint"/>
                        <mxPoint x="237" y="269.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cgLbKw1jWSPjwNCR0k7t-21" value="存在问题：&lt;span style=&quot;background-color: transparent;&quot;&gt;两个agent如果1个故障了，意味着依赖的pay-service1将停&lt;/span&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;用&lt;/span&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;解决方法：pay service与agent之间，增加AD负载均衡，暂时无业务，后续考虑&lt;/span&gt;&lt;/div&gt;" style="shape=note2;boundedLbl=1;whiteSpace=wrap;html=1;size=25;verticalAlign=top;align=left;" parent="1" vertex="1">
                    <mxGeometry x="83" y="400" width="697" height="110" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>