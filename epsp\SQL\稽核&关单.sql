﻿select * from (
  select t.transaction_no, t.transaction_type,t.business_code,t.create_time,t.transaction_end_time as expired_time,
    case when t.state='06' then '01' else t.state end as txs_state, 
    case when p.transaction_no is null then 'NONE' when p.pay_state='06' then '01' else p.pay_state end as pay_state, 
    case when c.transaction_no is null then 'NONE' when c.state='02' then '03' else c.state end as clr_state
  from txs_pay_trade_order t 
    left join pay_pay_record p on t.transaction_no=p.transaction_no
    left join clr_pay_record c on t.transaction_no=c.transaction_no
  where t.create_time > sysdate - 1/6 and t.create_time < sysdate - 1/24/12 and 
    t.transaction_type not in('ZHFZ','SF') and t.pay_method not in('59','60')
) 
where txs_state<> pay_state or txs_state <> clr_state or txs_state='03' and expired_time<sysdate;





select * from meta_tables where table_title like '%分润%';
select * from meta_tables where table_title like '%发票%';
select * from meta_tables where table_title like '%分期%';

select *from CUM_INSTALMENT_PERIOD_DETAIL;

select * from CUM_INSTALMENT_PERIOD_DETAIL;
select *from SETT_SETTMENT_FR_RECORD where create_time > sysdate -2;
select * from SETT_SETTMENT_FR_ACC_FLOW where create_time > sysdate -2;
select * from V_ACC_SUM_FP;
select * from ACC_SUM_FP where customer_code='***************';
select *from acc_account t where t.customercode='***************';
select *from V_ACC_FR_DETAIL where account_time > sysdate -1;
select * from PAS_DKTE_RECORD;

select remark, alias from rc_define where remark like '%同%' order by alias;

select * from txs_sql_template;
select * from chk_channel_record;

select *from acc_accountflow t where t.accountdatetime > sysdate -1 order by accountdatetime desc;

select * from cust_static_param where param_type='MCC_CODE' and param_level=2 and param_value like '%办公%批发商%' order by param_value;

select * from txs_withdraw_trade_order t where t.create_time > sysdate -1 and t.fx_state is not null;
select * from meta_columns where table_name='TXS_WITHDRAW_TRADE_ORDER' order by sort_order;
select * from term_industry_code;
select * from term_industry_manage;

select * from cust_patrol_subscribe

select *from chk_xx_mcht_map;

select * from chk_business_jingying_report t where t.task_date >= '********';

select *from rc_gambling_pyramid_record t where t.create_time > sysdate -1;

