@startuml 换汇
title 换汇
actor 商户 as M
box 汇通系统 #LightBlue
    participant 商户门户 as MP
    participant 交易服务 as TX
    participant 账户服务 as AC
    participant 渠道服务 as CH
end box
participant "CurrencyCloud" as CC #Grey

==查询账户余额==
autonumber 1.1
M -> MP: 登录
MP --> M: 进入汇通全球
M -> MP: 查询账户余额
MP -> AC: 查询多币种账户
AC --> MP: 返回账户余额
MP --> M: 返回账户余额

==寻汇==
autonumber 2.1
M -> MP: 查询汇率
group 锁汇30秒
    MP -> CH: 申请寻汇
    CH -> CC: 查询汇率详情
    note left of CC
        CC，汇率详细
        接口：/v2/rates/detailed
    end note
    CC --> CH: 返回汇率详情
    CH --> MP: 实时汇率，完成锁汇
end group
MP -> MP: 汇率加点计算
note over of MP
    固定买入，手续费外币，客户汇率=实时汇率*(1+0.5%)
    固定卖出，手续费RMB，客户汇率=实时汇率*(1-0.5%)，√
end note
MP --> M: 返回加点后汇率

==换汇申请==
autonumber 3.1
M -> MP: 申请换汇
MP --> TX: 创建换汇单
TX -> AC: 换汇资金冻结
AC --> MP: 返回冻结结果
TX -> CH: 发起换汇
CH -> CC: 发起换汇
note left of CC
    CC，创建换汇单
    接口：/v2/conversions/create
end note
CC --> CH: 换汇结果
CH --> TX: 换汇单状态：处理中
MP --> M: 换汇单已受理

==换汇完成==
autonumber 4.1
CC ->> CH: 通知换汇结果
note left of CC
    CC，两状态： 
    ①资金已到位（Funds Arrived Notification）
    ②交易已完成（Trade Settled Notification）
end note
CH -> TX: 更新换汇单状态

alt 换汇成功
    TX -> AC: 换汇成功记账
    note left of AC
        卖出币种账户资金解冻并减额
        买入币种账户资金加额
    end note
    TX -> CH: 转存手续费到主账户
    CH -> CC: 转存手续费
    note left of CC: 接口：Transfer
    TX --> TX: 换汇单状态：成功
    MP ->> M: 换汇成功通知
else 换汇失败
    TX -> AC: 冻结账户回滚
    TX --> TX: 换汇单状态：失败
    MP ->> M: 换汇失败通知
end
@enduml