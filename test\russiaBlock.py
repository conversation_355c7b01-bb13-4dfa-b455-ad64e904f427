import pygame  
import random  
  
# 初始化Pygame  
pygame.init()  
  
# 定义颜色  
BLACK = (0, 0, 0)  
WHITE = (255, 255, 255)  
RED = (255, 0, 0)  
GREEN = (0, 255, 0)  
BLUE = (0, 0, 255)  
YELLOW = (255, 255, 0)  
  
# 设置屏幕大小  
SCREEN_WIDTH = 600  
SCREEN_HEIGHT = 400  
BLOCK_SIZE = 20  
BOARD_WIDTH = 10  
BOARD_HEIGHT = 6  
  
# 创建方块形状列表  
SHAPES = [  
    [[1, 1, 1, 1]],  # I shape  
    [[1, 1], [1, 1]],  # O shape  
    [[1, 1, 0], [0, 1, 1]],  # Z shape  
    [[0, 1, 1], [1, 1, 0]],  # S shape  
    [[1, 1, 1], [0, 1, 0]],  # T shape  
    [[1, 1, 1], [0, 0, 1]],  # L shape  
    [[1, 1, 1], [1, 0, 0]]  # J shape  
]  
  
# 初始化方块  
def init_block():  
    x = random.randint(0, BOARD_WIDTH - 1)  
    y = random.randint(0, BOARD_HEIGHT - 1)  
    shape = random.randint(0, len(SHAPES) - 1)  
    return x, y, shape  
  
# 检查是否能够放置方块  
def can_place_block(x, y):  
    for i in range(y, y + BLOCK_SIZE):  
        for j in range(x, x + BLOCK_SIZE):  
            if j >= BOARD_WIDTH or i >= BOARD_HEIGHT or board[i][j] != 0:  
                return False  
    return True  
  
# 游戏循环  
running = True  
while running:  
    screen.fill(BLACK)  
    board = [[0 for _ in range(BOARD_WIDTH)] for _ in range(BOARD_HEIGHT)]  
    clock = pygame.time.Clock()  
    current_x = -1  
    current_y = -1  
    current_shape = -1  
    while current_x < BOARD_WIDTH and current_y < BOARD_HEIGHT and current_shape < len(SHAPES):  
        for event in pygame.event.get():  
            if event.type == pygame.QUIT:  
                running = False  
            elif event.type == pygame.KEYDOWN:  
                if event.key == pygame.K_LEFT and current_x > 0:  
                    current_x -= BLOCK_SIZE  
                elif event.key == pygame.K_RIGHT and current_x < BOARD_WIDTH - BLOCK_SIZE:  
                    current_x += BLOCK_SIZE  
                elif event.key == pygame.K_DOWN and current_y < BOARD_HEIGHT - BLOCK_SIZE:  
                    current_y += BLOCK_SIZE  
                elif event.key == pygame.K_SPACE:  
                    if can_place_block(current_x + BLOCK_SIZE // 2, current_y + BLOCK_SIZE // 2):  
                        for i in range(BLOCK_SIZE):  
                            for j in range(BLOCK_SIZE):  
                                board[current_y + i][current_x + j] = SHAPES[current_shape][i // BLOCK_SIZE][j // BLOCK_SIZE]