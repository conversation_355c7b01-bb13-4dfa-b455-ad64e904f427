function getCookie(name, defaultValue) {
    if (defaultValue == null) {
        defaultValue = "";
    }
    var name = name + "=";
    var decodedCookie = decodeURIComponent(document.cookie);
    var ca = decodedCookie.split(';');
    for(var i = 0; i <ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) == ' ') {
            c = c.substring(1);
        }
        if (c.indexOf(name) == 0) {
            return c.substring(name.length, c.length);
        }
    }
    return defaultValue;
}

function setCookie(name, value, days) {
    var expires = "";
    if (!days) {
        days = 10000;
    }
    var date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    expires = "; expires=" + date.toUTCString();
    document.cookie = name + "=" + value + expires + "; path=/";
}

function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

function getParagraphWordCount(paragraph) {
    var text = paragraph.textContent.trim();
    return text.length;
}

function speak(text, callback) {
    var synth = window.speechSynthesis;
    var utterance = new SpeechSynthesisUtterance(text);
    rate = parseFloat(getCookie("speech_rate", 1));     // 语速，范围为 0.1 到 10，默认为 1.5
    pitch = parseFloat(getCookie("speech_pitch", 0));     // 音调，范围为 0 到 2，默认为 1
    volume = parseFloat(getCookie("speech_volume", 1));   // 音量，范围为 0 到 1，默认为 1

    utterance.rate = rate;
    utterance.pitch = pitch;
    utterance.volume = volume;
    utterance.voice = synth.getVoices().filter(function(voice) {
        return voice.name === getCookie("options_speak_voice", "Google 普通话（中国大陆）");
    })[0];
    utterance.onpause = function() {
        if (callback["onpause"]) {
            callback["onpause"]();
        }
    }
    utterance.onresume = function() {
        if (callback["onresume"]) {
            callback["onresume"]();
        }
    }
    utterance.onstart = function() {
        if (callback["onstart"]) {
            callback["onstart"]();
        }
    }
    utterance.onend = function() {
        if (callback["onend"]) {
            callback["onend"]();
        }
    };
    utterance.onboundary = function(event) {
        var word = event.charIndex;
        var text = event.utterance.text;
        var wordText = text.substring(word, word + 1);
        if (callback["onboundary"]) {
            callback["onboundary"](event);
        }
    }
    utterance.onerror = function(event) {
        var error = event.error;
        var message = event.message;
        console.error("Speech synthesis error: " + error + " - " + message);
        if (callback["onend"]) {
            callback["onend"](event);
        }
    };
    synth.speak(utterance);
}

function stopSpeak() {
    var synth = window.speechSynthesis;
    synth.cancel();
}

function pauseSpeak() {
    var synth = window.speechSynthesis;
    synth.pause();
}

function resumeSpeak() {
    var synth = window.speechSynthesis;
    synth.resume();
}
function getSupportedVoices() {
    var voices = window.speechSynthesis.getVoices();
    var supportedVoices = [];
    for (var i = 0; i < voices.length; i++) {
        if(voices[i].name.indexOf("Chinese") >= 0 && 
                (voices[i].name.indexOf("PRC") >= 0 || voices[i].name.indexOf("Mainland") >= 0)) {
            supportedVoices.push({"voice": voices[i].name, "name": getVoiceName(voices[i].name)});
        }
    }
    return supportedVoices;
}

function getVoiceName(voiceName) {
    VoiceNames = {
        "Microsoft Huihui - Chinese (Simplified, PRC)": "慧慧 - 中文",
        "Microsoft Kangkang - Chinese (Simplified, PRC)": "康康 - 中文",
        "Microsoft Yaoyao - Chinese (Simplified, PRC)": "瑶瑶 - 中文",
        "Microsoft Xiaoxiao Online (Natural) - Chinese (Mainland)": "晓晓 - 中文",
        "Microsoft Xiaoyi Online (Natural) - Chinese (Mainland)": "小艺 - 中文",
        "Microsoft Yunjian Online (Natural) - Chinese (Mainland)": "云剑 - 中文",
        "Microsoft Yunxi Online (Natural) - Chinese (Mainland)": "云熙 - 中文",
        "Microsoft Yunxia Online (Natural) - Chinese (Mainland)": "云霞 - 中文",
        "Microsoft Yunyang Online (Natural) - Chinese (Mainland)": "云央 - 中文"
    };
    return VoiceNames[voiceName] || voiceName;
}


function setVoice(voiceName) {
    var voices = window.speechSynthesis.getVoices();
    for (var i = 0; i < voices.length; i++) {
        if (voices[i].name === voiceName) {
            var synth = window.speechSynthesis;
            synth.cancel();
            var utterance = new SpeechSynthesisUtterance();
            utterance.voice = voices[i];
            synth.speak(utterance);
            break;
        }
    }
}
window.speechSynthesis.onvoiceschanged = function() {
    var supportedVoices = getSupportedVoices();
    console.log(supportedVoices);
};