import requests

url = "http://172.16.6.8:8081/card/auth"

def auth():
    data = {
        "primaryAccountNumber": "6799970100000000005",
        "processingCode": "300000",
        "transactionAmount": "1",
        "expirationDate": "2512",
        "additionalData": {
            "cvc2Request": "123123"
        }
    }
    resp = requests.post(url, json=data, headers={"Content-Type": "application/json"})
    print(resp.text)

if __name__ == "__main__":
    auth()

