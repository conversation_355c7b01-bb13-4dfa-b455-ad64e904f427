import cx_Oracle
import pymysql

# Oracle数据库连接信息
oracle_host = '**********'
oracle_port = '1521'
oracle_user = 'efps01'
oracle_password = 'efps01'
oracle_service_name = 'TESTDB'

# MySQL数据库连接信息
mysql_host = 'localhost'
mysql_user = 'root'
mysql_password = ''
mysql_database = 'grafana'

# 连接到Oracle数据库
oracle_dsn = cx_Oracle.makedsn(oracle_host, oracle_port, service_name=oracle_service_name)
oracle_conn = cx_Oracle.connect(oracle_user, oracle_password, dsn=oracle_dsn)
oracle_cursor = oracle_conn.cursor()

# 连接到MySQL数据库
mysql_conn = pymysql.connect(host=mysql_host, user=mysql_user, passwd=mysql_password, db=mysql_database, charset='utf8')
mysql_cursor = mysql_conn.cursor()


def get_oracle_table_structure(table_name):
    # 获取Oracle表结构信息
    sql = 'DESCRIBE ' + table_name
    oracle_cursor.execute(sql)
    columns = [column[0] for column in oracle_cursor.fetchall()]
    return columns


def create_mysql_table(table_name, columns):
    # 在MySQL数据库中创建表
    column_str = ''
    for column in columns:
        column_str += column + ' VARCHAR(255),'
    column_str = column_str[:-1]
    sql = 'CREATE TABLE ' + table_name + ' (' + column_str + ')'
    mysql_cursor.execute(sql)
    mysql_conn.commit()


def migrate_data(table_name, page_num, page_size=10000):
    # 迁移数据
    # columns = get_oracle_table_structure(table_name)
    # create_mysql_table(table_name, columns)
    page_count = page_num * page_size
    sql = 'SELECT * FROM ' + table_name + ' OFFSET ' + str((page_num - 1) * page_size) + ' ROWS FETCH NEXT ' + str(
        page_size) + ' ROWS ONLY'
    oracle_cursor.execute(sql)
    data = oracle_cursor.fetchall()
    for row in data:
        values = [str(value) for value in row]
        values_str = ','.join(values)
        sql = 'INSERT INTO ' + table_name + ' VALUES(' + values_str + ')'
        # mysql_cursor.execute(sql)
        # mysql_conn.commit()


if __name__ == '__main__':
    table_name = 'pas_user'
    page_num = 1
    migrate_data(table_name, page_num)
