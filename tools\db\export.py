import cx_Oracle
import csv
import gzip

# 设置Oracle连接信息
dsn = cx_Oracle.makedsn('*************', '1521',service_name='epaydb')
connection = cx_Oracle.connect(user='epaylinks', password='n3SB527AZxn3', dsn=dsn)
batch_size=10000

def export_table(table_name):
    print(f"Exporting table {table_name}")
    sql_query = f"SELECT * FROM {table_name}"
    # 执行SQL查询并将结果写入CSV文件
    with open(f"F:/SBS/{table_name}.csv", 'w', newline='', encoding='utf-8') as csvfile:
        csv_writer = csv.writer(csvfile, escapechar='\\')
        
        # 获取表的列名
        cursor = connection.cursor()
        cursor.execute(f"SELECT * FROM {table_name}")
        column_names = [desc[0] for desc in cursor.description]
        
        # 将表的列名写入CSV文件
        csv_writer.writerow(column_names)
        
        # 执行SQL查询并将结果写入CSV文件
        cursor.execute(sql_query)
        for i, row in enumerate(cursor):
            if i % batch_size == 0 and i > 0:
                print(f"Processed {i} rows")
            csv_writer.writerow(row)

def export_tables():
    with open("F:/SBS/tables.txt", 'r', encoding='utf-8') as csvfile:
        reader = csv.reader(csvfile)
        for row in reader:
            export_table(row[0])

if __name__ == '__main__':
    export_table('T_CLEAR_INFO')

# 关闭数据库连接
# cursor.close()
# connection.close()