{"root": {"data": {"id": "d68ii28jar40", "created": 1733883775646, "text": "进件材料"}, "children": [{"data": {"id": "d68ij5187so0", "created": 1733883860098, "text": "技术信息"}, "children": [{"data": {"id": "d68ij8gnh340", "created": 1733883867561, "text": "接口版本"}, "children": []}, {"data": {"id": "d68ik971zvk0", "created": 1733883947522, "text": "审核回调URL"}, "children": []}, {"data": {"id": "d68ikk7ibkw0", "created": 1733883971494, "text": "业务通知URL"}, "children": []}]}, {"data": {"id": "d68ijk5laow0", "created": 1733883893014, "text": "接入信息"}, "children": [{"data": {"id": "d68ijueazb40", "created": 1733883915309, "text": "代理商"}, "children": []}, {"data": {"id": "d68ijwwu8f40", "created": 1733883920783, "text": "平台商"}, "children": []}, {"data": {"id": "d68ijz7ry1s0", "created": 1733883925798, "text": "商户侧商户编号"}, "children": []}]}, {"data": {"id": "d68ilebdqxk0", "created": 1733884037032, "text": "基本信息"}, "children": [{"data": {"id": "d68ilwhs1eg0", "created": 1733884076601, "text": "注册名称"}, "children": []}, {"data": {"id": "d68ilz3un000", "created": 1733884082289, "text": "简称"}, "children": []}, {"data": {"id": "d68im8e0ny80", "created": 1733884102495, "text": "开户类型"}, "children": [{"data": {"id": "d68imearzhk0", "created": 1733884115359, "text": "仅开户"}, "children": []}, {"data": {"id": "d68imhahs000", "created": 1733884121872, "text": "仅收单"}, "children": []}, {"data": {"id": "d68imjqukhs0", "created": 1733884127215, "text": "开户&收单"}, "children": []}]}, {"data": {"id": "d68imzza8ao0", "created": 1733884162554, "text": "主体类型"}, "children": [{"data": {"id": "d68inek4n2g0", "created": 1733884194289, "text": "小微"}, "children": []}, {"data": {"id": "d68ingpesrk0", "created": 1733884198962, "text": "个体户"}, "children": []}, {"data": {"id": "d68inin7us80", "created": 1733884203183, "text": "企业"}, "children": []}, {"data": {"id": "d68inyrsag00", "created": 1733884238288, "text": "独资企业"}, "children": []}, {"data": {"id": "d68inolk52g0", "created": 1733884216143, "text": "政府事业单位"}, "children": []}]}]}, {"data": {"id": "d68ioo8x6og0", "created": 1733884293743, "text": "经营信息"}, "children": [{"data": {"id": "d68ir6gy5a80", "created": 1733884490139, "text": "经营地址所在省"}, "children": []}, {"data": {"id": "d68ir6gyet40", "created": 1733884490139, "text": "经营地址所在市"}, "children": []}, {"data": {"id": "d68ir6gybuo0", "created": 1733884490139, "text": "经营地址所在区"}, "children": []}, {"data": {"id": "d68ir6gy25k0", "created": 1733884490139, "text": "经营详细地址"}, "children": []}, {"data": {"id": "d68ir6gxz3c0", "created": 1733884490139, "text": "MCC码"}, "children": []}, {"data": {"id": "d68ir6gy7qg0", "created": 1733884490139, "text": "门店门头照"}, "children": []}, {"data": {"id": "d68ir6gxz1c0", "created": 1733884490139, "text": "门店外景照"}, "children": []}, {"data": {"id": "d68ir6gy6f40", "created": 1733884490139, "text": "门店内景照"}, "children": []}, {"data": {"id": "d68ir6gy7hc0", "created": 1733884490139, "text": "门店收银台照"}, "children": []}, {"data": {"id": "d68ir6gxvhc0", "created": 1733884490139, "text": "其他附件"}, "children": []}, {"data": {"id": "d68ir6gxye80", "created": 1733884490139, "text": "协议书签约状态"}, "children": []}, {"data": {"id": "d68ir6gyqe00", "created": 1733884490140, "text": "协议书盖章页/签名页附件"}, "children": []}, {"data": {"id": "d68ir6gyq5s0", "created": 1733884490140, "text": "备注"}, "children": []}, {"data": {"id": "d68ir6gz0vk0", "created": 1733884490140, "text": "证照类型"}, "children": []}, {"data": {"id": "d68ir6gyh5s0", "created": 1733884490140, "text": "证照编号"}, "children": []}, {"data": {"id": "d68ir6gyvfc0", "created": 1733884490140, "text": "证照商户名称"}, "children": []}, {"data": {"id": "d68ir6gyiq80", "created": 1733884490140, "text": "证照图片"}, "children": []}, {"data": {"id": "d68ir6gyzvs0", "created": 1733884490140, "text": "证照有效期(起始)"}, "children": []}, {"data": {"id": "d68ir6gyj6o0", "created": 1733884490140, "text": "证照有效期(截止)"}, "children": []}, {"data": {"id": "d68ir6gz10g0", "created": 1733884490140, "text": "法人姓名"}, "children": []}, {"data": {"id": "d68ir6gyl5c0", "created": 1733884490140, "text": "经营范围"}, "children": []}, {"data": {"id": "d68ir6gylzk0", "created": 1733884490140, "text": "注册地址"}, "children": []}, {"data": {"id": "d68ir6gzbiw0", "created": 1733884490141, "text": "注册资本"}, "children": []}]}, {"data": {"id": "d68iojuywo00", "created": 1733884284192, "text": "联系人信息"}, "children": [{"data": {"id": "d68ipvisydc0", "created": 1733884387942, "text": "联系人姓名"}, "children": []}, {"data": {"id": "d68ipvisz9c0", "created": 1733884387942, "text": "邮箱地址"}, "children": []}, {"data": {"id": "d68ipvisjdc0", "created": 1733884387942, "text": "联系人手机号码"}, "children": []}, {"data": {"id": "d68ipvispv40", "created": 1733884387942, "text": "联系人身份类型"}, "children": []}, {"data": {"id": "d68ipvisq7s0", "created": 1733884387942, "text": "联系人证件类型"}, "children": []}, {"data": {"id": "d68ipvisxuo0", "created": 1733884387942, "text": "联系人证件号码"}, "children": []}, {"data": {"id": "d68ipvisyx40", "created": 1733884387942, "text": "联系人证件有效期（起始）"}, "children": []}, {"data": {"id": "d68ipvisqao0", "created": 1733884387942, "text": "联系人证件有效期（截止）"}, "children": []}, {"data": {"id": "d68ipvispko0", "created": 1733884387942, "text": "联系人证件照正面"}, "children": []}, {"data": {"id": "d68ipvissls0", "created": 1733884387942, "text": "联系人证件照背面"}, "children": []}, {"data": {"id": "d68ipvitd1s0", "created": 1733884387943, "text": "联系人业务办理授权函"}, "children": []}]}, {"data": {"id": "d68iougjtls0", "created": 1733884307265, "text": "法人信息"}, "children": [{"data": {"id": "d68iq3vlza80", "created": 1733884406131, "text": "证件类型"}, "children": []}, {"data": {"id": "d68iq3vms7k0", "created": 1733884406132, "text": "证件号码"}, "children": []}, {"data": {"id": "d68iq3vmmsg0", "created": 1733884406132, "text": "证件正面照"}, "children": []}, {"data": {"id": "d68iq3vmn3c0", "created": 1733884406132, "text": "证件背面照"}, "children": []}, {"data": {"id": "d68iq3vmrm00", "created": 1733884406132, "text": "证件人姓名"}, "children": []}, {"data": {"id": "d68iq3vmi680", "created": 1733884406132, "text": "证件效期(起始)"}, "children": []}, {"data": {"id": "d68iq3vmc9s0", "created": 1733884406132, "text": "证件效期(截止)"}, "children": []}, {"data": {"id": "d68iq3vmv1k0", "created": 1733884406132, "text": "法人手机号码"}, "children": []}, {"data": {"id": "d68iq3vmnvc0", "created": 1733884406132, "text": "法人证件地址（国籍）"}, "children": []}, {"data": {"id": "d68iq3vmlds0", "created": 1733884406132, "text": "法人证件详细地址"}, "children": []}]}, {"data": {"id": "d68ip3fd2wo0", "created": 1733884326784, "text": "注册账户"}, "children": [{"data": {"id": "d68iqh9u6sg0", "created": 1733884435289, "text": "账户类型"}, "children": []}, {"data": {"id": "d68iqh9tnuw0", "created": 1733884435289, "text": "账户名"}, "children": []}, {"data": {"id": "d68iqh9u4800", "created": 1733884435289, "text": "账号"}, "children": []}, {"data": {"id": "d68iqh9ur080", "created": 1733884435290, "text": "开户银行"}, "children": []}, {"data": {"id": "d68iqh9upxc0", "created": 1733884435290, "text": "开户支行"}, "children": []}, {"data": {"id": "d68iqh9ufmo0", "created": 1733884435290, "text": "证明文件（照片）"}, "children": []}]}, {"data": {"id": "d68ip8w4l200", "created": 1733884338682, "text": "结算信息"}, "children": [{"data": {"id": "d68iqrpp09s0", "created": 1733884458016, "text": "结算方式"}, "children": []}, {"data": {"id": "d68iqrppkbk0", "created": 1733884458017, "text": "结算账户类型"}, "children": []}, {"data": {"id": "d68iqrppjgg0", "created": 1733884458017, "text": "结算账户号"}, "children": []}, {"data": {"id": "d68iqrppn2g0", "created": 1733884458017, "text": "结算账户名"}, "children": []}, {"data": {"id": "d68iqrppfqo0", "created": 1733884458017, "text": "开户银行"}, "children": []}, {"data": {"id": "d68iqrppt600", "created": 1733884458017, "text": "开户支行"}, "children": []}, {"data": {"id": "d68iqrppqgo0", "created": 1733884458017, "text": "开户行联行号"}, "children": []}, {"data": {"id": "d68iqrppjg80", "created": 1733884458017, "text": "结算人身份证正面"}, "children": []}, {"data": {"id": "d68iqrppdj40", "created": 1733884458017, "text": "结算人身份证反面"}, "children": []}, {"data": {"id": "d68iqrpp9c00", "created": 1733884458017, "text": "银行卡正面照"}, "children": []}, {"data": {"id": "d68iqrpqbao0", "created": 1733884458018, "text": "银行卡背面照"}, "children": []}, {"data": {"id": "d68iqrppygo0", "created": 1733884458018, "text": "银行预留手机号"}, "children": []}, {"data": {"id": "d68iqrppyhs0", "created": 1733884458018, "text": "附言"}, "children": []}, {"data": {"id": "d68iqrpqa1k0", "created": 1733884458018, "text": "付款用途和事由"}, "children": []}, {"data": {"id": "d68iqrpqeug0", "created": 1733884458018, "text": "开户意愿书照片"}, "children": []}, {"data": {"id": "d68iqrpqg540", "created": 1733884458018, "text": "代付证明"}, "children": []}, {"data": {"id": "d68iqrpq6400", "created": 1733884458018, "text": "转账功能申请书"}, "children": []}, {"data": {"id": "d68iqrpq1880", "created": 1733884458018, "text": "结算账户附件"}, "children": []}, {"data": {"id": "d68iqrppxjc0", "created": 1733884458018, "text": "收款方营业执照图片"}, "children": []}]}, {"data": {"id": "d68ipaz8yls0", "created": 1733884343224, "text": "受益人信息"}, "children": [{"data": {"id": "d68irjz8xqw0", "created": 1733884519544, "text": "实际控制人（受益人）姓名"}, "children": []}, {"data": {"id": "d68irjz9c0g0", "created": 1733884519544, "text": "实际控制人（受益人）证件类型"}, "children": []}, {"data": {"id": "d68irjz9bc80", "created": 1733884519544, "text": "实际控制人（受益人）证件号码"}, "children": []}, {"data": {"id": "d68irjz9opc0", "created": 1733884519545, "text": "实际控制人（受益人）证件效期（起始）"}, "children": []}, {"data": {"id": "d68irjz9kk00", "created": 1733884519545, "text": "实际控制人（受益人）证件效期（截止）"}, "children": []}, {"data": {"id": "d68irjz9u0g0", "created": 1733884519545, "text": "实际控制人（受益人）地址（国籍）"}, "children": []}, {"data": {"id": "d68irjz9ngo0", "created": 1733884519545, "text": "实际控制人（受益人）详细地址"}, "children": []}, {"data": {"id": "d68irjz9u1c0", "created": 1733884519545, "text": "实际控制人（受益人）证件照正面"}, "children": []}, {"data": {"id": "d68irjz9rog0", "created": 1733884519545, "text": "实际控制人（受益人）证件照背面"}, "children": []}]}, {"data": {"id": "d68ipg6i02w0", "created": 1733884354547, "text": "其他信息"}, "children": [{"data": {"id": "d68irsdilpc0", "created": 1733884537821, "text": "平台商户微信渠道号"}, "children": []}, {"data": {"id": "d68irsdj2ow0", "created": 1733884537821, "text": "微信经营类目ID"}, "children": []}, {"data": {"id": "d68irsdii340", "created": 1733884537821, "text": "平台商户支付宝渠道商PID"}, "children": []}, {"data": {"id": "d68irsdjd200", "created": 1733884537822, "text": "支付宝MCC"}, "children": []}, {"data": {"id": "d68irsdjlt40", "created": 1733884537822, "text": "微信认证商户名"}, "children": []}, {"data": {"id": "d68irsdjfg00", "created": 1733884537822, "text": "支付宝认证商户名"}, "children": []}, {"data": {"id": "d68irsdjnp40", "created": 1733884537822, "text": "机构适配商户名"}, "children": []}, {"data": {"id": "d68irsdj4io0", "created": 1733884537822, "text": "银联快捷简称"}, "children": []}, {"data": {"id": "d68irsdj4lc0", "created": 1733884537822, "text": "客服电话"}, "children": []}, {"data": {"id": "d68irsdjnq80", "created": 1733884537822, "text": "ICP备案号"}, "children": []}, {"data": {"id": "d68irsdjlb40", "created": 1733884537822, "text": "网址"}, "children": []}, {"data": {"id": "d68irsdk88o0", "created": 1733884537823, "text": "网站或APP的名称"}, "children": []}, {"data": {"id": "d68irsdjwwo0", "created": 1733884537823, "text": "小程序名称"}, "children": []}, {"data": {"id": "d68irsdjxaw0", "created": 1733884537823, "text": "公众号名称"}, "children": []}, {"data": {"id": "d68irsdk1sw0", "created": 1733884537823, "text": "小程序截图（认证页）"}, "children": []}, {"data": {"id": "d68irsdjs5k0", "created": 1733884537823, "text": "小程序截图（首页）"}, "children": []}, {"data": {"id": "d68irsdjwm80", "created": 1733884537823, "text": "小程序截图（订单提交页）"}, "children": []}, {"data": {"id": "d68irsdk0io0", "created": 1733884537823, "text": "公众号截图（认证页）"}, "children": []}, {"data": {"id": "d68irsdjrns0", "created": 1733884537823, "text": "公众号截图（首页）"}, "children": []}, {"data": {"id": "d68irsdkcow0", "created": 1733884537824, "text": "公众号截图（订单提交页）"}, "children": []}, {"data": {"id": "d68irsdkhew0", "created": 1733884537824, "text": "ICP备案证明截图"}, "children": []}, {"data": {"id": "d68irsdkbr40", "created": 1733884537824, "text": "网站截图（网址首页）"}, "children": []}, {"data": {"id": "d68irsdkrlk0", "created": 1733884537824, "text": "网站截图（网址售后页）"}, "children": []}, {"data": {"id": "d68irsdkgow0", "created": 1733884537824, "text": "网站截图"}, "children": []}, {"data": {"id": "d68irsdkuxk0", "created": 1733884537824, "text": "场景说明书"}, "children": []}, {"data": {"id": "d68irsdku800", "created": 1733884537824, "text": "支付流程图"}, "children": []}, {"data": {"id": "d68irsdkuko0", "created": 1733884537824, "text": "支付流程图（附件）"}, "children": []}, {"data": {"id": "d68irsdkti80", "created": 1733884537824, "text": "特殊许可证"}, "children": []}, {"data": {"id": "d68irsdl58g0", "created": 1733884537825, "text": "代付客户合作协议"}, "children": []}, {"data": {"id": "d68irsdkwyo0", "created": 1733884537825, "text": "应用市场下载截图"}, "children": []}, {"data": {"id": "d68irsdl4bk0", "created": 1733884537825, "text": "app功能视频"}, "children": []}, {"data": {"id": "d68irsdl9ag0", "created": 1733884537825, "text": "其他表格附件"}, "children": []}, {"data": {"id": "d68irsdkxsw0", "created": 1733884537825, "text": "手持开户意愿书照片"}, "children": []}, {"data": {"id": "d68irsdldrs0", "created": 1733884537825, "text": "手持开户意愿书照片视频 "}, "children": []}]}]}, "template": "right", "theme": "fresh-blue", "version": "1.4.43"}