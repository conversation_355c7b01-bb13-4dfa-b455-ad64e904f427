@baseurl=https://fat-mso.globalcash.cn/
###3.1.1	获取TOKEN
post {{baseurl}}/api/token/get
Content-Type: application/json

{
  "appId": "61ab11e4536cc597b2be360c8274229e",
  "appSecret": "17b6cbfd94dd3001a7815d5177d5e1c5"
}

# merchantNo:2410300100000277
# appId:8d43b8188921e0865825c6b9137a39ca
# secret:3ee5dc91bfa8b6fc0c9bf9dcd4441659
###
@token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcHBJZCI6IjYxYWIxMWU0NTM2Y2M1OTdiMmJlMzYwYzgyNzQyMjllIiwianRpIjoiMDIyOTdkOWI2OTZjNDk5ZWE3OTY1MDBhYzlhNTgzOTUifQ.BciRDgiaCBff8qPQ_f0Q3RKvSHxeSlJB_5W0N-gRB28
@plat_merchant_no=2406070100000171
@file_id=fd031931-5764-49ba-87c2-834a1ebce432
@sub_mch_no=2410240100000269
@va_id=241029000001774006

###文件上传
POST {{baseurl}}/api/file/upload HTTP/1.1
token: {{token}}
nonceStr: 123456789012345678901234
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="merchantNo"
Content-Type: text/plain

{{plat_merchant_no}}
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="fileDesc"
Content-Type: text/plain

这是文件说明
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="D:/TMP/1.jpg"
Content-Type: text/plain

This is an example file.
------WebKitFormBoundary7MA4YWxkTrZu0gW--



###3.2.1	子商户开户申请
post {{baseurl}}/api/merchant/apply
Content-Type: application/json
nonceStr: 123456789012345678901234
token: {{token}}

{
    "client_mch_no": "860000000001",
    "mchName": "测试商户",
    "mchEname": "Test Merchant",
    "countryCode": "CN",
    "province": "440000",
    "city": "440100",
    "area": "440105",
    "address": "广州大道中368号",
    "addressEn": "GuangZhou Street No.368",
    "busiCountryCode": "CN",
    "busiAddress": "广州大道中368号",
    "vocationType": "01",
    "registeredCapital": *********,
    "lastYearIncome": 10000000,
    "licenseNo": "12341234",
    "licenseFrom": "2024-01-01",
    "licenseTo": "2030-01-01",
    "icp": "12341234",
    "websiteUrl": "https://www.epaylinks.cn",
    "websiteName": "XX联官网",
    "tradeDesc": "A",
    "mainBusiness": "支付解决方案",
    "companyModel": "其他",
    "companyModelOther": "B2B支付解决方案、跨境支付解决方案",
    "listed": "N",
    "stateOwnedEnterprised": "N",
    "foreignOwnedEnterprised": "N",
    "highRiskIndustry": "N",
    "sanctionedRegionsTrans": "N",
    "expectTransRegion": "大陆,香港,东南亚",
    "expectTransRegionOther": "无",
    "expectTransCurr": "CNY,HKD,USD,EUR,GBP",
    "expectTransCurrOther": "",
    "expectTransAmt": "500万美元以上",
    "attachments": [
        {"fileType": "license", "fileId":"{{file_id}}"},
        {"fileType": "storeHead", "fileId":"{{file_id}}"},
        {"fileType": "storeShopPhoto", "fileId":"{{file_id}}"},
        {"fileType": "storeHall", "fileId":"{{file_id}}"},
        {"fileType": "shareholdingStructure", "fileId":"{{file_id}}"},
        {"fileType": "handheldPhotoForAccount", "fileId":"{{file_id}}"},
        {"fileType": "licenseAcc", "fileId":"{{file_id}}"},
        {"fileType": "websiteCapture", "fileId":"{{file_id}}"},
        {"fileType": "constitution", "fileId":"{{file_id}}"}],
    "mchRelations": [{
        "type": "1",
        "prop": "0",
        "countryCode": "CN",
        "name": "测试商户",
        "idType": "0",
        "idCardNo": "******************",
        "idFrom": "2024-01-01",
        "idTo": "2030-01-01",
        "attachments":[
        {"fileType": "front", "fileId":"{{file_id}}"},
        {"fileType": "back", "fileId":"{{file_id}}"}]
    },{
        "type": "6",
        "prop": "0",
        "countryCode": "CN",
        "name": "测试商户",
        "idType": "0",
        "idCardNo": "******************",
        "idFrom": "2024-01-01",
        "idTo": "2030-01-01",
        "shareholderRate": "100",
        "attachments":[
        {"fileType": "front", "fileId":"{{file_id}}"},
        {"fileType": "back", "fileId":"{{file_id}}"}]
    },{
        "type": "2",
        "prop": "0",
        "countryCode": "CN",
        "name": "测试商户",
        "idType": "0",
        "idCardNo": "******************",
        "idFrom": "2024-01-01",
        "idTo": "2030-01-01",
        "email": "<EMAIL>",
        "zipCode": "100000",
        "phone": "02012341234",
        "attachments":[
        {"fileType": "front", "fileId":"{{file_id}}"},
        {"fileType": "back", "fileId":"{{file_id}}"}]
    }]
}

###3.2.2	子商户查询
post {{baseurl}}/api/merchant/query
Content-Type: application/json
nonceStr: 123456789012345678901234
token: {{token}}

{
    "subMchNo": "{{sub_mch_no}}"
}

###3.2.3	子商户VA申请
post {{baseurl}}/api/va/apply
Content-Type: application/json
nonceStr: 123456789012345678901234
token: {{token}}

{
    "subMchNo": "{{sub_mch_no}}",
    "type": "B2C",
    "fundsSourceCountry": "US",
    "attachments": [
        {"fileType": "vaFlow", "fileId":"{{file_id}}"},
        {"fileType": "vaSettle", "fileId":"{{file_id}}"}
    ]
}

###3.2.4	子商户VA查询
post {{baseurl}}/api/va/query
Content-Type: application/json
nonceStr: 123456789012345678901234
token: {{token}}

{
    "subMchNo": "{{sub_mch_no}}",
    "vaId": "{{va_id}}"
}