﻿--更新山西银行金融机构代码
update bk_bank set bank_code='C1442914000015', new_bank_code='C1442914000015' where bank_name='山西银行股份有限公司'
--补充晋城银行（待茂平）——已补

--银行对应关系——已确认

--同步银行到cum_bank_info表
insert into cum_bank_info               (id,bank_code,new_bank_code,bank_name,is_area_bank,institution_code,operator_id,flag)
select id,bank_code,new_bank_code,bank_name,is_area_bank,institution_code,0,1
from bk_bank 
where bank_code in('JCBANK','C1090614000026','C1090814000016','C1442914000015');
--修改卡bin
create table txs_card_bin231011 as select * from txs_card_bin where bank_icon in('JCBANK','C1090614000026','C1090814000016','JZBAN<PERSON>','DATONGBANK');
create table bk_card_bin231011 as select * from bk_card_bin where bank_icon in('JCBA<PERSON><PERSON>','C1090614000026','C1090814000016','JZBANK','DATONGBANK');

update txs_card_bin t set t.bank_icon='C1442914000015' where bank_icon='JCBANK';
update txs_card_bin t set t.bank_icon='C1442914000015' where bank_icon='C1090614000026';
update txs_card_bin t set t.bank_icon='C1442914000015' where bank_icon='C1090814000016';
update txs_card_bin t set t.bank_icon='C1442914000015' where bank_icon='JZBANK';
update txs_card_bin t set t.bank_icon='C1442914000015' where bank_icon='DATONGBANK';

update bk_card_bin t set t.bank_icon='C1442914000015' where bank_icon='JCBANK';
update bk_card_bin t set t.bank_icon='C1442914000015' where bank_icon='C1090614000026';
update bk_card_bin t set t.bank_icon='C1442914000015' where bank_icon='C1090814000016';
update bk_card_bin t set t.bank_icon='C1442914000015' where bank_icon='JZBANK';
update bk_card_bin t set t.bank_icon='C1442914000015' where bank_icon='DATONGBANK';

--添加山西银行网联快捷支付渠道（木炯）
