import sys
from cx_Freeze import setup, Executable
import os

# 获取当前脚本所在目录
base_dir = os.path.dirname(os.path.abspath(__file__))

# 定义资源目录
resource_dir = os.path.join(base_dir, 'res')
# 收集资源文件
include_files = []
for root, dirs, files in os.walk(resource_dir):
    for file in files:
        source_path = os.path.join(root, file)
        target_path = os.path.relpath(source_path, base_dir)
        include_files.append((source_path, target_path))

# 针对 Windows 系统设置基础选项
base = None
if sys.platform == "win32":
    base = "Win32GUI"

setup(
    name="Snake Game",
    version="1.0",
    description="Snake Game",
    options={
        "build_exe": {
            "include_files": include_files
        }
    },
    executables=[Executable("snake_game.py", base=base)]
)