{"info": {"_postman_id": "2562a169-4793-4e4b-9e0e-6f2a76319d4a", "name": "厦门国际   线上线下", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "34159133"}, "item": [{"name": "商户管理", "item": [{"name": "商户申请", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "YmYzNTE2NTQtMjU4ZS00Ng==", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "HLB24091801", "type": "text"}, {"key": "entityType", "value": "PF", "type": "text"}, {"key": "merchant.id", "value": "918010536600005", "type": "text"}, {"key": "merchant.name", "value": "TEST ", "type": "text"}, {"key": "merchant.business", "value": "TEST MOBILE", "type": "text"}, {"key": "merchant.website", "value": "https://baidu.com", "type": "text"}, {"key": "merchant.street", "value": "东方路69号", "type": "text"}, {"key": "merchant.postcode", "value": "200000", "type": "text"}, {"key": "merchant.city", "value": "SHANGHAI", "type": "text"}, {"key": "merchant.state", "value": "shanghai", "type": "text"}, {"key": "merchant.country", "value": "CN", "type": "text"}, {"key": "merchant.phone", "value": "***********", "type": "text"}, {"key": "merchant.mmcc", "value": "5280", "type": "text"}, {"key": "merchant.vmcc", "value": "5290", "type": "text"}, {"key": "merchant.corporateStatus", "value": "0", "type": "text"}, {"key": "merchant.pfId", "value": "{{merchant.pfId}}", "type": "text"}]}, "url": {"raw": "{{xib.url}}/merchant/create", "host": ["{{xib.url}}"], "path": ["merchant", "create"]}}, "response": []}, {"name": "商户申请 HLB05920001", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{xib.token.HLB05920001}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "{{xib.entityId}}", "type": "text"}, {"key": "entityType", "value": "PF", "type": "text"}, {"key": "merchant.id", "value": "2000105122000300", "type": "text"}, {"key": "merchant.name", "value": "TEST ", "type": "text"}, {"key": "merchant.business", "value": "TEST MOBILE", "type": "text"}, {"key": "merchant.website", "value": "https://baidu.com", "type": "text"}, {"key": "merchant.street", "value": "东方路69号", "type": "text"}, {"key": "merchant.postcode", "value": "200000", "type": "text"}, {"key": "merchant.city", "value": "SHANGHAI", "type": "text"}, {"key": "merchant.state", "value": "shanghai", "type": "text"}, {"key": "merchant.country", "value": "CN", "type": "text"}, {"key": "merchant.phone", "value": "***********", "type": "text"}, {"key": "merchant.mmcc", "value": "5280", "type": "text"}, {"key": "merchant.vmcc", "value": "5290", "type": "text"}, {"key": "merchant.corporateStatus", "value": "0", "type": "text"}, {"key": "merchant.pfId", "value": "HLB05920001", "type": "text"}]}, "url": {"raw": "{{xib.url}}/merchant/create", "host": ["{{xib.url}}"], "path": ["merchant", "create"]}}, "response": []}, {"name": "商户更新", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{xib.token.TEST05920001}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "{{xib.entityId.TEST05920001}}", "type": "text"}, {"key": "entityType", "value": "PARTICIPATOR", "type": "text"}, {"key": "merchant.id", "value": "200000536600001", "type": "text"}, {"key": "merchant.name", "value": "TEST ", "type": "text"}, {"key": "merchant.business", "value": "TEST MOBILE", "type": "text"}, {"key": "merchant.website", "value": "https://baidu.com", "type": "text"}, {"key": "merchant.street", "value": "东方路69号", "type": "text"}, {"key": "merchant.postcode", "value": "200000", "type": "text"}, {"key": "merchant.city", "value": "SHANGHAI", "type": "text"}, {"key": "merchant.state", "value": "shanghai", "type": "text"}, {"key": "merchant.country", "value": "CN", "type": "text"}, {"key": "merchant.phone", "value": "***********", "type": "text"}, {"key": "merchant.mmcc", "value": "5280", "type": "text"}, {"key": "merchant.vmcc", "value": "5290", "type": "text"}, {"key": "merchant.corporateStatus", "value": "0", "type": "text"}, {"key": "merchant.pfId", "value": "TST05920000", "type": "text"}]}, "url": {"raw": "{{xib.url}}/merchant/update", "host": ["{{xib.url}}"], "path": ["merchant", "update"]}}, "response": []}, {"name": "商户 状态维护", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{xib.token.TEST05920001}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "{{xib.entityId.TEST05920001}}", "type": "text"}, {"key": "entityType", "value": "PARTICIPATOR", "type": "text"}, {"key": "merchant.status", "value": "NORMAL", "type": "text"}, {"key": "merchantTransactionId", "value": "202408230000122", "type": "text"}, {"key": "merchant.id", "value": "200000536600001", "type": "text"}, {"key": "merchant.securityStatus", "value": "SUSPECTED", "type": "text", "disabled": true}]}, "url": {"raw": "{{xib.url}}/merchant/status", "host": ["{{xib.url}}"], "path": ["merchant", "status"]}}, "response": []}, {"name": "商户 查询", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "ZDRlMWE3ZGItNGIyZS00NQ==", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "HF24092401", "type": "text"}, {"key": "entityType", "value": "PF", "type": "text"}, {"key": "merchant.id", "value": "918010565100000", "type": "text"}]}, "url": {"raw": "https://sdpt.xib.com.cn:50443/pay/v3/merchant/inquiry", "protocol": "https", "host": ["sdpt", "xib", "com", "cn"], "port": "50443", "path": ["pay", "v3", "merchant", "inquiry"]}}, "response": []}, {"name": "Refund Pos", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{xib.token.HLB05920001}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "{{xib.entityId.HLB05920001}}", "type": "text"}, {"key": "entityType", "value": "PF", "type": "text"}, {"key": "merchant.id", "value": "200010512200007", "type": "text"}, {"key": "merchantTransactionId", "value": "pos-refund-092300002", "type": "text"}, {"key": "originalTransactionId", "value": "868459", "type": "text"}, {"key": "currency", "value": "CNY", "type": "text"}, {"key": "amount", "value": "1.00", "type": "text"}, {"key": "paymentType", "value": "PR", "type": "text"}]}, "url": {"raw": "{{xib.url}}/payments", "host": ["{{xib.url}}"], "path": ["payments"]}}, "response": []}]}, {"name": "终端管理", "item": [{"name": "终端 申请", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{xib.token.TEST05920001}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "{{xib.entityId.TEST05920001}}", "type": "text"}, {"key": "entityType", "value": "PARTICIPATOR", "type": "text"}, {"key": "merchant.id", "value": "200000536600001", "type": "text"}, {"key": "pos.terminalId", "value": "00000231", "type": "text"}, {"key": "pos.pinEntryCapability", "value": "1", "type": "text"}, {"key": "pos.terminalType", "value": "POS", "type": "text"}, {"key": "pos.postcode", "value": "200000", "type": "text"}]}, "url": {"raw": "{{xib.url}}/terminal/create", "host": ["{{xib.url}}"], "path": ["terminal", "create"]}}, "response": []}, {"name": "终端 更新", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{xib.token.TEST05920001}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "{{xib.entityId.TEST05920001}}", "type": "text"}, {"key": "entityType", "value": "PARTICIPATOR", "type": "text"}, {"key": "merchant.id", "value": "200000536600001", "type": "text"}, {"key": "pos.terminalId", "value": "00000231", "type": "text"}, {"key": "pos.pinEntryCapability", "value": "1", "type": "text"}, {"key": "pos.terminalType", "value": "MPOS", "type": "text"}, {"key": "pos.postcode", "value": "200000", "type": "text"}]}, "url": {"raw": "{{xib.url}}/terminal/update", "host": ["{{xib.url}}"], "path": ["terminal", "update"]}}, "response": []}, {"name": "终端 状态维护", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{xib.token.TEST05920001}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "{{xib.entityId.TEST05920001}}", "type": "text"}, {"key": "entityType", "value": "PARTICIPATOR", "type": "text"}, {"key": "merchant.id", "value": "200000536600001", "type": "text"}, {"key": "pos.terminalId", "value": "00000231", "type": "text"}, {"key": "terminal.status", "value": "BLOCKED", "type": "text"}]}, "url": {"raw": "{{xib.url}}/terminal/status", "host": ["{{xib.url}}"], "path": ["terminal", "status"]}}, "response": []}, {"name": "终端 查询", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{xib.token.TEST05920001}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "{{xib.entityId.TEST05920001}}", "type": "text"}, {"key": "entityType", "value": "PARTICIPATOR", "type": "text"}, {"key": "merchant.id", "value": "200000536600001", "type": "text"}, {"key": "pos.terminalId", "value": "00000231", "type": "text"}, {"key": "pos.pinEntryCapability", "value": "1", "type": "text", "disabled": true}, {"key": "pos.terminalType", "value": "MPOS", "type": "text", "disabled": true}, {"key": "pos.postcode", "value": "200000", "type": "text", "disabled": true}]}, "url": {"raw": "https://sdpt.xib.com.cn:50443/pay/v3/terminal/inquiry", "protocol": "https", "host": ["sdpt", "xib", "com", "cn"], "port": "50443", "path": ["pay", "v3", "terminal", "inquiry"]}}, "response": []}]}, {"name": "web transaction", "item": [{"name": "文件下载 -交易明细", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "NzRhMTM2YzgtMmI0Ni00YQ==", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "HLB05920001", "type": "text"}, {"key": "entityType", "value": "PF", "type": "text"}, {"key": "merchantId", "value": "", "type": "text"}, {"key": "reportType", "value": "transaction", "type": "text"}, {"key": "date", "value": "20240914", "type": "text"}, {"key": "transStatus", "value": "00", "type": "text"}]}, "url": {"raw": "https://gds8443.sandbox.efaka.net/xib-merch-web/download/getReport", "protocol": "https", "host": ["gds8443", "sandbox", "efaka", "net"], "path": ["xib-merch-web", "download", "getReport"]}}, "response": []}, {"name": "文件下载 -结算明细", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "NzRhMTM2YzgtMmI0Ni00YQ==", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "HLB05920001", "type": "text"}, {"key": "entityType", "value": "PF", "type": "text"}, {"key": "merchantId", "value": "", "type": "text"}, {"key": "reportType", "value": "settlement", "type": "text"}, {"key": "date", "value": "20240916", "type": "text"}, {"key": "transStatus", "value": "00", "type": "text"}]}, "url": {"raw": "https://gds8443.sandbox.efaka.net/xib-merch-web/download/getReport", "protocol": "https", "host": ["gds8443", "sandbox", "efaka", "net"], "path": ["xib-merch-web", "download", "getReport"]}}, "response": []}, {"name": "文件下载 -结算汇总", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "NzRhMTM2YzgtMmI0Ni00YQ==", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "HLB05920001", "type": "text"}, {"key": "entityType", "value": "PF", "type": "text"}, {"key": "merchantId", "value": "", "type": "text"}, {"key": "reportType", "value": "settlement_summary", "type": "text"}, {"key": "date", "value": "20240916", "type": "text"}, {"key": "transStatus", "value": "success", "type": "text"}]}, "url": {"raw": "https://test.efaka.net:8443/xib-merch-web/download/getReport", "protocol": "https", "host": ["test", "efaka", "net"], "port": "8443", "path": ["xib-merch-web", "download", "getReport"]}}, "response": []}, {"name": "Refund", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "NjEwYjcyYzgtZmZkMy00Yw==", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "888880000011111", "type": "text"}, {"key": "entityType", "value": "PARTICIPATOR", "type": "text"}, {"key": "merchant.id", "value": "888880000011111", "type": "text"}, {"key": "merchantTransactionId", "value": "zwf-card-refund-000008", "type": "text"}, {"key": "originalTransactionId", "value": "zwf-card-purchase-000010", "type": "text"}, {"key": "currency", "value": "USD", "type": "text"}, {"key": "amount", "value": "1.23", "type": "text"}, {"key": "paymentType", "value": "RF", "type": "text"}]}, "url": {"raw": "{{xib.url}}/payments", "host": ["{{xib.url}}"], "path": ["payments"]}}, "response": []}, {"name": "Transaction Query", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "NjEwYjcyYzgtZmZkMy00Yw==", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "https://sdpt.xib.com.cn/eastpay/v3/trans/zwf-card-purchase-000010?entityId=888880000011111&entityType=PARTICIPATOR&merchant.id=888880000011111", "protocol": "https", "host": ["sdpt", "xib", "com", "cn"], "path": ["eastpay", "v3", "trans", "zwf-card-purchase-000010"], "query": [{"key": "entityId", "value": "888880000011111"}, {"key": "entityType", "value": "PARTICIPATOR"}, {"key": "merchant.id", "value": "888880000011111"}]}}, "response": []}, {"name": "Prepare Checkout", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "NjEwYjcyYzgtZmZkMy00Yw==", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "888880000011111", "type": "text"}, {"key": "entityType", "value": "PARTICIPATOR", "type": "text"}, {"key": "merchant.id", "value": "888880000011111", "type": "text"}, {"key": "checkoutType", "value": "AUTHORIZE", "type": "text"}, {"key": "merchantTransactionId", "value": "275", "type": "text"}, {"key": "currency", "value": "USD", "type": "text"}, {"key": "amount", "value": "76.72", "type": "text"}, {"key": "paymentType", "value": "DB", "type": "text"}, {"key": "customer.surname", "value": "test", "type": "text"}, {"key": "customer.givenName", "value": "test1", "type": "text"}, {"key": "customer.email", "value": "<EMAIL>", "type": "text"}, {"key": "billing.street1", "value": "Test Address 1", "type": "text"}, {"key": "billing.city", "value": "Alberta", "type": "text"}, {"key": "billing.state", "value": "AB", "type": "text"}, {"key": "billing.country", "value": "CA", "type": "text"}, {"key": "billing.postcode", "value": "200120", "type": "text"}, {"key": "shipping.street1", "value": "Test Address 1", "type": "text"}, {"key": "shipping.city", "value": "Alberta", "type": "text"}, {"key": "shipping.state", "value": "AB", "type": "text"}, {"key": "shipping.country", "value": "CA", "type": "text"}, {"key": "shipping.postcode", "value": "200120", "type": "text"}, {"key": "notificationUrl", "value": "https://eshop.sandbox.efaka.net/index.php?route=extension/payment/eastpay/webhook", "type": "text"}, {"key": "shopperResultUrl", "value": "https://eshop.sandbox.efaka.net/index.php?route=extension/payment/eastpay/redirect", "type": "text"}]}, "url": {"raw": "{{xib.url}}/checkouts", "host": ["{{xib.url}}"], "path": ["checkouts"]}}, "response": []}, {"name": "Checkout Transaction Query", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "NjEwYjcyYzgtZmZkMy00Yw==", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "url": {"raw": "{{xib.url}}/payments/275?entityId=888880000011111&entityType=PARTICIPATOR&merchant.id=888880000011111", "host": ["{{xib.url}}"], "path": ["payments", "275"], "query": [{"key": "entityId", "value": "888880000011111"}, {"key": "entityType", "value": "PARTICIPATOR"}, {"key": "merchant.id", "value": "888880000011111"}]}}, "response": []}, {"name": "Card Payment Without 3DS", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "NjEwYjcyYzgtZmZkMy00Yw==", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "888880000011111", "type": "text"}, {"key": "entityType", "value": "PARTICIPATOR", "type": "text"}, {"key": "merchant.id", "value": "888880000011111", "type": "text"}, {"key": "merchantTransactionId", "value": "zwf-card-purchase-100010", "type": "text"}, {"key": "card.number", "value": "****************", "type": "text"}, {"key": "card.holder", "value": "Test test", "type": "text"}, {"key": "card.expiryYear", "value": "2031", "type": "text"}, {"key": "card.expiryMonth", "value": "12", "type": "text"}, {"key": "card.cvv", "value": "123", "type": "text"}, {"key": "currency", "value": "USD", "type": "text"}, {"key": "amount", "value": "11.23", "type": "text"}, {"key": "paymentType", "value": "DB", "type": "text"}, {"key": "paymentBrand", "value": "MASTER", "type": "text"}, {"key": "billing.country", "value": "US", "type": "text"}, {"key": "billing.state", "value": "CA", "type": "text"}, {"key": "billing.street1", "value": "1 Market St", "type": "text"}, {"key": "billing.postcode", "value": "000000", "type": "text"}, {"key": "billing.city", "value": "shanghai", "type": "text"}, {"key": "customer.givenName", "value": "test", "type": "text"}, {"key": "customer.surname", "value": "test", "type": "text"}, {"key": "customer.email", "value": "<EMAIL>", "type": "text"}, {"key": "notificationUrl", "value": "https://baidu.com", "type": "text"}, {"key": "threeDSecure.indicator", "value": "force", "type": "text"}, {"key": "customer.browser.challengeWindow", "value": "5", "type": "text"}, {"key": "customer.browser.timezone", "value": "-480", "type": "text"}, {"key": "customer.browser.javaEnabled", "value": "false", "type": "text"}, {"key": "customer.browser.javascriptEnabled", "value": "true", "type": "text", "disabled": true}, {"key": "customer.browser.screenColorDepth", "value": "24", "type": "text"}, {"key": "customer.browser.userAgent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "type": "text"}, {"key": "customer.browser.language", "value": "zh-CN", "type": "text"}, {"key": "customer.browser.screenHeight", "value": "1080", "type": "text"}, {"key": "customer.browser.acceptHeader", "value": "application/json, text/plain, */*", "type": "text"}, {"key": "customer.browser.screenWidth", "value": "1920", "type": "text"}]}, "url": {"raw": "{{xib.url}}/payments", "host": ["{{xib.url}}"], "path": ["payments"]}}, "response": []}, {"name": "Card Payment", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "NjEwYjcyYzgtZmZkMy00Yw==", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "888880000011111", "type": "text"}, {"key": "entityType", "value": "PARTICIPATOR", "type": "text"}, {"key": "merchant.id", "value": "888880000011111", "type": "text"}, {"key": "merchantTransactionId", "value": "zwf-card-purchase-100010", "type": "text"}, {"key": "card.number", "value": "****************", "type": "text"}, {"key": "card.holder", "value": "Test test", "type": "text"}, {"key": "card.expiryYear", "value": "2031", "type": "text"}, {"key": "card.expiryMonth", "value": "12", "type": "text"}, {"key": "card.cvv", "value": "123", "type": "text"}, {"key": "currency", "value": "USD", "type": "text"}, {"key": "amount", "value": "11.23", "type": "text"}, {"key": "paymentType", "value": "DB", "type": "text"}, {"key": "paymentBrand", "value": "MASTER", "type": "text"}, {"key": "billing.country", "value": "US", "type": "text"}, {"key": "billing.state", "value": "CA", "type": "text"}, {"key": "billing.street1", "value": "1 Market St", "type": "text"}, {"key": "billing.postcode", "value": "000000", "type": "text"}, {"key": "billing.city", "value": "shanghai", "type": "text"}, {"key": "customer.givenName", "value": "test", "type": "text"}, {"key": "customer.surname", "value": "test", "type": "text"}, {"key": "customer.email", "value": "<EMAIL>", "type": "text"}]}, "url": {"raw": "{{xib.url}}/payments", "host": ["{{xib.url}}"], "path": ["payments"]}}, "response": []}]}, {"name": "线下", "item": [{"name": "{{xib.url}}/trans", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "\r", "pm.test(\"Response time is less than 200ms\", function () {\r", "  pm.expect(pm.response.responseTime).to.be.below(200);\r", "});\r", "\r", "\r", "pm.test(\"Response has the required fields - data, requestId, and memberId\", function () {\r", "    const responseData = pm.response.json();\r", "    \r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData).to.have.property('data');\r", "    pm.expect(responseData).to.have.property('requestId');\r", "    pm.expect(responseData).to.have.property('memberId');\r", "});\r", "\r", "\r", "pm.test(\"Data is not empty\", function () {\r", "  const responseData = pm.response.json();\r", "\r", "  pm.expect(responseData.data).to.not.be.empty;\r", "});\r", "\r", "\r", "pm.test(\"RequestId and memberId are in a valid format\", function () {\r", "    const responseData = pm.response.json();\r", "    \r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData.requestId).to.be.a('string').and.to.have.lengthOf.at.least(1, \"RequestId should be a non-empty string\");\r", "    pm.expect(responseData.memberId).to.be.a('string').and.to.have.lengthOf.at.least(1, \"memberId should be a non-empty string\");\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "F08Ar8+OnT6q8PpcHdvn/UycrULXvNyexkRqSjqS+Q+ZwZq7rvX88/dLjDmMw4hSGM3M0fGGcfYGYtjs/uEYWC1ExW/sbSGEzMeSz+6UrEiflokF3mDkhLY8NS9glizJopwHNGHBfgQD7iMYvW1VqqbVbP7hS/Iuzcgyx5WJxzLNvzsuJg6ky5cjbmrmW67gMOy9FSMGlxeJAyrl23O4PXJktkzyU0sPdbtyxE0i6hZGETwf5SD4RqJolbaqf4/Ob3ORwKii7JdMzCbYmfeeaEl8wzHfGhreRdS1pJ+6PSIU6i2ff86sXQekLRjpD8ieIg8YV9Moc9p5vItTrbBZ1g==", "type": "string"}]}, "method": "POST", "header": [{"key": "content-type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\"memberId\":\"TST05920000\",\"requestId\":{{transcationId}},\"data\":\"00012300600030000060310032130102007020068020C082301643083531923091970000000000000001000000390720000100374308353192309197D230620100000005000000313030303030303438303035343030303030303720202036303801069F260800BF07D29547378C9F2701809F100706100A03A000009F370464DF4BFB9F36020293950500000000009A032406079C01009F02060000000001005F2A020156820220009F1A0206088407A00000000310109F03060000000000009F6E04207000009F3303E820C8007830325030303030314139323050726F5F76312E312E33325F617562203138353132313735353320202020202020202020202020202020202020202020202020202020202020202020202020202020001422001291000600\"}"}, "url": {"raw": "{{xib.url}}/tran", "host": ["{{xib.url}}"], "path": ["tran"]}}, "response": []}, {"name": "pos-refund", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token.XM48129005}}", "type": "string"}]}, "method": "POST", "header": [{"key": "content-type", "value": "application/json", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "{{pfId.XM48129005}}", "type": "text"}, {"key": "entityType", "value": "PF", "type": "text"}, {"key": "merchant.id", "value": "{{merchant.290050541100001}}", "type": "text"}, {"key": "merchantTransactionId", "value": "{{transactionId}}", "type": "text"}, {"key": "originalTransactionId", "value": "163013", "type": "text"}, {"key": "amount", "value": "10.00", "type": "text"}, {"key": "currency", "value": "CNY", "type": "text"}, {"key": "paymentType", "value": "PR", "type": "text"}]}, "url": {"raw": "{{xib.url}}/payments", "host": ["{{xib.url}}"], "path": ["payments"]}}, "response": []}, {"name": "pos-refund jcb", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "\r", "pm.test(\"Response time is less than 200ms\", function () {\r", "  pm.expect(pm.response.responseTime).to.be.below(200);\r", "});\r", "\r", "\r", "pm.test(\"Response has the required fields - data, requestId, and memberId\", function () {\r", "    const responseData = pm.response.json();\r", "    \r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData).to.have.property('data');\r", "    pm.expect(responseData).to.have.property('requestId');\r", "    pm.expect(responseData).to.have.property('memberId');\r", "});\r", "\r", "\r", "pm.test(\"Data is not empty\", function () {\r", "  const responseData = pm.response.json();\r", "\r", "  pm.expect(responseData.data).to.not.be.empty;\r", "});\r", "\r", "\r", "pm.test(\"RequestId and memberId are in a valid format\", function () {\r", "    const responseData = pm.response.json();\r", "    \r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData.requestId).to.be.a('string').and.to.have.lengthOf.at.least(1, \"RequestId should be a non-empty string\");\r", "    pm.expect(responseData.memberId).to.be.a('string').and.to.have.lengthOf.at.least(1, \"memberId should be a non-empty string\");\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "ZWMyMjBlYWYtMTVlNC00MA==", "type": "string"}]}, "method": "POST", "header": [{"key": "content-type", "value": "application/json", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "entityId", "value": "KP05920004", "type": "text"}, {"key": "entityType", "value": "PF", "type": "text"}, {"key": "merchant.id", "value": "200040581400001", "type": "text"}, {"key": "merchantTransactionId", "value": "aaaaa-refund-162797", "type": "text"}, {"key": "originalTransactionId", "value": "162797", "type": "text"}, {"key": "amount", "value": "600.00", "type": "text"}, {"key": "currency", "value": "CNY", "type": "text"}, {"key": "paymentType", "value": "PR", "type": "text"}]}, "url": {"raw": "{{xib.url}}/payments", "host": ["{{xib.url}}"], "path": ["payments"]}}, "response": []}, {"name": "{{xib.url}}/trans Copy", "event": [{"listen": "prerequest", "script": {"exec": ["console.log(F2)"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "\r", "pm.test(\"Response time is less than 200ms\", function () {\r", "  pm.expect(pm.response.responseTime).to.be.below(200);\r", "});\r", "\r", "\r", "pm.test(\"Response has the required fields - data, requestId, and memberId\", function () {\r", "    const responseData = pm.response.json();\r", "    \r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData).to.have.property('data');\r", "    pm.expect(responseData).to.have.property('requestId');\r", "    pm.expect(responseData).to.have.property('memberId');\r", "});\r", "\r", "\r", "pm.test(\"Data is not empty\", function () {\r", "  const responseData = pm.response.json();\r", "\r", "  pm.expect(responseData.data).to.not.be.empty;\r", "});\r", "\r", "\r", "pm.test(\"RequestId and memberId are in a valid format\", function () {\r", "    const responseData = pm.response.json();\r", "    \r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData.requestId).to.be.a('string').and.to.have.lengthOf.at.least(1, \"RequestId should be a non-empty string\");\r", "    pm.expect(responseData.memberId).to.be.a('string').and.to.have.lengthOf.at.least(1, \"memberId should be a non-empty string\");\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "F08Ar8+OnT6q8PpcHdvn/UycrULXvNyexkRqSjqS+Q+ZwZq7rvX88/dLjDmMw4hSGM3M0fGGcfYGYtjs/uEYWC1ExW/sbSGEzMeSz+6UrEiflokF3mDkhLY8NS9glizJopwHNGHBfgQD7iMYvW1VqqbVbP7hS/Iuzcgyx5WJxzLNvzsuJg6ky5cjbmrmW67gMOy9FSMGlxeJAyrl23O4PXJktkzyU0sPdbtyxE0i6hZGETwf5SD4RqJolbaqf4/Ob3ORwKii7JdMzCbYmfeeaEl8wzHfGhreRdS1pJ+6PSIU6i2ff86sXQekLRjpD8ieIg8YV9Moc9p5vItTrbBZ1g==", "type": "string"}]}, "method": "POST", "header": [{"key": "content-type", "value": "application/json", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "memberId", "value": "TST05920000", "type": "text"}, {"key": "requestId", "value": "00000000035", "type": "text"}, {"key": "bitmap", "value": "[2,3,4,11,22,23,25,35,41,42,49,55,59,60]", "type": "text"}, {"key": "F2", "value": "4012000000001030", "type": "text"}, {"key": "F3", "value": "000000", "type": "text"}, {"key": "F4", "value": "000000000100", "type": "text"}, {"key": "F11", "value": "000039", "type": "text"}, {"key": "F22", "value": "072", "type": "text"}, {"key": "F23", "value": "0001", "type": "text"}, {"key": "F25", "value": "00", "type": "text"}, {"key": "F41", "value": "10000004", "type": "text"}, {"key": "F42", "value": "800540000007", "type": "text"}, {"key": "F49", "value": "608", "type": "text"}, {"key": "F59", "value": "02P00001A920Pro_v1.1.32_aub 1851217553                                        ", "type": "text"}, {"key": "F60", "value": "22001291000600", "type": "text"}]}, "url": {"raw": "{{xib.url}}/trans", "host": ["{{xib.url}}"], "path": ["trans"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}