@startuml MasterCard 3DS 认证流程
title "MasterCard 3DS 认证流程"
actor 持卡人
participant 商户
box 会员系统 #LightCyan
participant "snc-gate"
participant "snc-pay-service"
end box
participant 外部3DS系统
participant 万事达

持卡人 -> 商户:下单支付
商户 -> "snc-gate": 调用支付接口\n/gateway/api
"snc-gate" -> "snc-pay-service": dubbo调用PaymentFactoryImpl.doRequest\nMasterBankRequest.authorizationRequest
"snc-pay-service" --> "snc-gate": 返回3DS验证的payUrl\nhttps://service.epaylinks.cn\n/snc-gate/gateway/verify/{orderId}
"snc-gate" --> 商户: 返回payUrl
商户 --> 持卡人: 返回payUrl
持卡人 -> "snc-gate": 访问payUrl
"snc-gate" -> "snc-pay-service": dubbo调用com.wr.request.\nPaymentFactoryImpl#verifyMReq
"snc-pay-service" -> 外部3DS系统: 访问https://3dss.efaka.net/api/clientReceiver\n请求参数提供验证后重定向notificationURL
外部3DS系统 --> "snc-pay-service": 返回3DS验证的acsURL
"snc-pay-service" --> "snc-gate": 返回acsURL
"snc-gate" --> 持卡人: 返回acsURL
持卡人 -> 外部3DS系统: 访问acsURL完成认证
外部3DS系统 --> 持卡人: 返回notificationURL
持卡人 -> "snc-gate": 访问notificationURL
"snc-gate" -> "snc-pay-service": dubbo调用com.wr.upper.bank.notify.verify3d.\nVerify3DNotifyServiceImpl#redirect
"snc-pay-service" -> "snc-pay-service": 调用MasterBankRequest#authorizationCompletion
"snc-pay-service" -> 万事达: 调用0100授权\n3DS返回的authenticationValue放到48域43子域
万事达 --> "snc-pay-service": 返回授权结果
"snc-pay-service" --> "snc-gate": 返回重定向参数\n商户调用支付接口时传的returnUrl
"snc-gate" --> 持卡人: 返回returnUrl
持卡人 -> 商户: 访问returnUrl
商户 -> 持卡人: 返回订单支付状态
@enduml


@startuml 外卡接口访问顺序图
title "外卡接口访问顺序图"

actor 商户
participant 服务商
participant 运营
participant 运营门户
participant 支付网关

==商户配置==
运营 -> 运营: 配置服务商信息(线下)
运营 -> 运营门户: 配置商户信息
运营门户 --> 运营: 返回商户号
运营 ->> 服务商: 提供商户&秘钥信息（线下）
服务商 ->> 商户: 提供商户&秘钥信息（线下）

==交易==
商户 -> 服务商: 请求支付
服务商 -> 支付网关: 发起支付
note right of 服务商: 使用商户秘钥签名
支付网关 -> 支付网关: 验签并完成支付
note left of 支付网关: 同现有支付流程
支付网关 --> 服务商: 支付结果
服务商 --> 商户: 返回支付结果

@enduml

@startuml SafeKey交易流程
title "SafeKey交易流程"

actor 商户 as M
participant 运通目录服务  as DS
participant 发卡行访问控制器 as ACS

M -> DS: 提交SafeKey交易
DS -> ACS: 转发SafeKey交易
ACS -> ACS: 确认持卡会员身份
group 会员提供一次性密码（可选）

end group

@enduml