﻿--导出商户最小终审成功的审核记录
--update cust_last_audit_record set flag=null;
create table cust_last_audit_record
(
  customer_id            NUMBER(12) not null,
  min_last_audit_version NUMBER,
  flag                   NUMBER   --商户是否完成处理，1：已处理
);
create unique index uidx_cust_last_audit on cust_last_audit_record(customer_id);

insert into cust_last_audit_record(customer_id,min_last_audit_version)
select a.customer_id,min(a.version) as min_last_audit_version 
from cust_customer_audit_record a 
  inner join cust_customer c on a.customer_id=c.customer_id
where audit_type=2 and audit_result=1 and a.customer_id not in(select customer_id from cust_last_audit_record)
  and c.business_role='100' and c.plat_customer_no not in('5651200003083009','562956003616411','562888003666540')
  and c.create_time >= timestamp'2022-06-01 00:00:00'
group by a.customer_id;

--根据商户逐个商户入库审核信息
--drop table CUST_AUDIT_RECORD;
--truncate table CUST_AUDIT_RECORD;
create table CUST_AUDIT_RECORD
(
  customer_id        NUMBER(12) not null,
  customer_no        VARCHAR2(20) not null,
  name               VARCHAR2(100),
  create_time        TIMESTAMP(6),
  plat_customer      VARCHAR2(121),
  service_customer   VARCHAR2(121),
  business_man       VARCHAR2(50),
  version            NUMBER(12) not null,
  submit_time        TIMESTAMP(6),
  submit_msg         CHAR(1),
  pre_audit_time     TIMESTAMP(6),
  pre_audit_result   VARCHAR2(600),
  pre_audit_msg      VARCHAR2(600),
  first_audit_time   TIMESTAMP(6),
  first_audit_result VARCHAR2(600),
  first_audit_msg    VARCHAR2(600),
  last_audit_time    TIMESTAMP(6),
  last_audit_result  VARCHAR2(600),
  last_audit_msg     VARCHAR2(600)
);


declare
  last_audit_type number;
  last_version number;
  type audit_record is record( 
    submit_time        TIMESTAMP(6),
    submit_msg         CHAR(1),
    pre_audit_time     TIMESTAMP(6),
    pre_audit_result   VARCHAR2(600),
    pre_audit_msg      VARCHAR2(600),
    first_audit_time   TIMESTAMP(6),
    first_audit_result VARCHAR2(600),
    first_audit_msg    VARCHAR2(600),
    last_audit_time    TIMESTAMP(6),
    last_audit_result  VARCHAR2(600),
    last_audit_msg     VARCHAR2(600)
  );
  ar audit_record;
begin
  for x in (select * from cust_last_audit_record t where t.flag !=1 or t.flag is null order by t.customer_id) loop
    last_audit_type :=-1;
    last_version := 1;
    for y in (select * from cust_customer_audit_record r where r.customer_id=x.customer_id and r.version <= x.min_last_audit_version order by r.version, r.create_time) loop
      if last_audit_type > y.audit_type then --保存数据，开始新一轮审核
         insert into CUST_AUDIT_RECORD(customer_id,customer_no,name,create_time,
             plat_customer,service_customer,business_man,version,
             submit_time,submit_msg,
             pre_audit_time,pre_audit_result,pre_audit_msg,
             first_audit_time,first_audit_result,first_audit_msg,
             last_audit_time,last_audit_result,last_audit_msg)
         select
             c.customer_id, c.customer_no,c.name, c.create_time,
             c.plat_customer_no || (case when c.plat_customer_no is not null then '-'||(select name from cust_customer x where x.customer_no=c.plat_customer_no) else '' end) as plat_customer,
             c.service_customer_no || (case when c.service_customer_no is not null then '-'||(select name from cust_customer x where x.customer_no=c.service_customer_no) else '' end) as service_customer,
             b.real_name as business_man, last_version,
             ar.submit_time, ar.submit_msg,
             ar.pre_audit_time,ar.pre_audit_result,ar.pre_audit_msg,
             ar.first_audit_time,ar.first_audit_result,ar.first_audit_msg,
             ar.last_audit_time,ar.last_audit_result,ar.last_audit_msg
         from cust_customer c left join pas_user b on c.business_man_id=b.user_id
         where c.customer_id=x.customer_id;
         --清理数据
         ar.submit_time := null;
         ar.pre_audit_time := null;
         ar.pre_audit_result := null;
         ar.pre_audit_msg := null;
         
         ar.first_audit_time := null;
         ar.first_audit_result := null;
         ar.first_audit_msg := null;
         
         ar.last_audit_time := null;
         ar.last_audit_result := null;
         ar.last_audit_msg := null;
         
         last_version := last_version + 1;
      end if;
      
      last_audit_type := y.audit_type;
      if ar.submit_time is null then
        ar.submit_time := y.create_time;
        ar.submit_msg := null;
      end if;
      if y.audit_type = 0 then 
         ar.pre_audit_time := y.audit_time;
         ar.pre_audit_result := (case y.audit_result when 0 then '待审核' when 1 then '通过' when 2 then '不通过' end);
         ar.pre_audit_msg := y.remarks;
      end if;
      if y.audit_type = 1 then 
         ar.first_audit_time := y.audit_time;
         ar.first_audit_result := (case y.audit_result when 0 then '待审核' when 1 then '通过' when 2 then '不通过' end);
         ar.first_audit_msg := y.remarks;
      end if;
      if y.audit_type = 2 then 
         ar.last_audit_time := y.audit_time;
         ar.last_audit_result := (case y.audit_result when 0 then '待审核' when 1 then '通过' when 2 then '不通过' end);
         ar.last_audit_msg := y.remarks;
      end if;
    end loop;
    --保存数据，开始新一轮审核
    insert into CUST_AUDIT_RECORD(customer_id,customer_no,name,create_time,
       plat_customer,service_customer,business_man,version,
       submit_time,submit_msg,
       pre_audit_time,pre_audit_result,pre_audit_msg,
       first_audit_time,first_audit_result,first_audit_msg,
       last_audit_time,last_audit_result,last_audit_msg)
    select
       c.customer_id, c.customer_no,c.name, c.create_time,
       c.plat_customer_no || (case when c.plat_customer_no is not null then '-'||(select name from cust_customer x where x.customer_no=c.plat_customer_no) else '' end) as plat_customer,
       c.service_customer_no || (case when c.service_customer_no is not null then '-'||(select name from cust_customer x where x.customer_no=c.service_customer_no) else '' end) as service_customer,
       b.real_name as business_man, last_version,
       ar.submit_time, ar.submit_msg,
       ar.pre_audit_time,ar.pre_audit_result,ar.pre_audit_msg,
       ar.first_audit_time,ar.first_audit_result,ar.first_audit_msg,
       ar.last_audit_time,ar.last_audit_result,ar.last_audit_msg
    from cust_customer c left join pas_user b on c.business_man_id=b.user_id
    where c.customer_id=x.customer_id;
    --清理数据
    ar.submit_time := null;
    ar.pre_audit_time := null;
    ar.pre_audit_result := null;
    ar.pre_audit_msg := null;
             
    ar.first_audit_time := null;
    ar.first_audit_result := null;
    ar.first_audit_msg := null;
             
    ar.last_audit_time := null;
    ar.last_audit_result := null;
    ar.last_audit_msg := null;
    
    last_version := 1;
    commit;
  end loop;
end;

--select * from CUST_AUDIT_RECORD t where version > 1 order by t.customer_id, version;

-------------------------------------------------------------------------

select t.customer_no as 商户编号,t.name as 商户名称,
       to_char(t.create_time,'yyyy-mm-dd hh24:mi:ss') as 创建时间,
       t.plat_customer as 平台商,t.service_customer as 服务商,t.business_man as 业务员,t.version as 版本,
       to_char(t.submit_time,'yyyy-mm-dd hh24:mi:ss') as 提交审核时间,t.submit_msg as 提交审核备注,
       to_char(t.pre_audit_time,'yyyy-mm-dd hh24:mi:ss') as 预审时间,t.pre_audit_result as 预审结果,t.pre_audit_msg as 预审备注,
       to_char(t.first_audit_time,'yyyy-mm-dd hh24:mi:ss') as 初审时间,t.first_audit_result as 初审结果,t.first_audit_msg as 初审备注,
       to_char(t.last_audit_time,'yyyy-mm-dd hh24:mi:ss') as 复审时间,t.last_audit_result as 复审结果,t.last_audit_msg as 复审备注
from CUST_AUDIT_RECORD t 
where t.first_audit_msg not like '%自动审核' or t.first_audit_msg is null 
order by customer_id,version;

create table cust_audit_record as
      select c.customer_id,
        c.customer_no as 商户编号,c.name as 商户名称,c.create_time,
        c.plat_customer_no || (case when c.plat_customer_no is not null then '-'||(select name from cust_customer x where x.customer_no=c.plat_customer_no) else '' end) as 所属平台商,
        c.service_customer_no || (case when c.service_customer_no is not null then '-'||(select name from cust_customer x where x.customer_no=c.service_customer_no) else '' end) as 所属服务商,
        b.name as 业务员,a.version+1 as 审核版本,
        to_char(a.submit_time,'yyyy-mm-dd hh24:mi:ss') as 提交审核时间, a.submit_msg as 提交审核备注,
        to_char(a.pre_audit_time,'yyyy-mm-dd hh24:mi:ss') as 预审时间, a.pre_audit_result as 预审结果, a.pre_audit_msg as 预审备注,
        to_char(a.first_audit_time,'yyyy-mm-dd hh24:mi:ss') as 初审时间, a.first_audit_result as 初审结果, a.first_audit_msg as 初审备注,
        to_char(a.last_audit_time,'yyyy-mm-dd hh24:mi:ss') as 复审时间, a.last_audit_result as 复审结果, a.last_audit_msg as 复审备注
        
from 
(
  select customer_id, version,
    (case when max(submit_time1) is not null then max(submit_time1) else max(submit_time2) end) as submit_time,
    '-' as submit_msg,
    max(pre_audit_time) as pre_audit_time,
    max(pre_audit_result) as pre_audit_result,
    max(pre_audit_msg) as pre_audit_msg,
    max(first_audit_time) as first_audit_time,
    max(first_audit_result) as first_audit_result,
    max(first_audit_msg) as first_audit_msg,
    max(last_audit_time) as last_audit_time,
    max(last_audit_result) as last_audit_result,
    max(last_audit_msg) as last_audit_msg
  from(
    select customer_id, version,audit_type,
    (case when audit_type=0 then create_time else null end) as submit_time1,
    (case when audit_type=1 then create_time else null end) as submit_time2,
    (case when audit_type=0 then audit_time else null end) as pre_audit_time,
    (case when audit_type=0 then decode(audit_result,0,'待审核',1,'通过',2,'不通过') else null end) as pre_audit_result,
    (case when audit_type=0 then remarks else null end) as pre_audit_msg,
    (case when audit_type=1 then audit_time else null end) as first_audit_time,
    (case when audit_type=1 then decode(audit_result,0,'待审核',1,'通过',2,'不通过') else null end) as first_audit_result,
    (case when audit_type=1 then remarks else null end) as first_audit_msg,
    (case when audit_type=2 then audit_time else null end) as last_audit_time,
    (case when audit_type=2 then decode(audit_result,0,'待审核',1,'通过',2,'不通过') else null end) as last_audit_result,
    (case when audit_type=2 then remarks else null end) as last_audit_msg
    from cust_customer_audit_record t
    where t.create_time >= timestamp'2022-06-01 00:00:00'
  ) r group by customer_id, version
) a inner join cust_customer c on a.customer_id=c.customer_id
  left join pas_user b on c.business_man_id=b.user_id
where c.plat_customer_no not in('5651200003083009','562956003616411','562888003666540')
  --and c.business_role='100' and (a.first_audit_msg!='自动审核' and a.last_audit_msg!='自动审核' or a.first_audit_msg is null or a.last_audit_msg is null)
  and c.customer_no='562547003183210'
order by c.customer_no,a.version;
