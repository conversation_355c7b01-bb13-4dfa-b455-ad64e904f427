<!DOCTYPE html>
<html>
<head>
  <title>DeepSeek问答</title>
  <meta charset="UTF-8">
  <style>
    body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    .container { display: flex; flex-direction: column; gap: 20px; }
    textarea { width: 100%; height: 120px; padding: 10px; font-size: 16px; }
    button { padding: 10px 20px; font-size: 16px; cursor: pointer; }
    .response { background: #f5f5f5; padding: 20px; border-radius: 4px; }
  </style>
</head>
<body>
  <div class="container">
    <h1>DeepSeek问答系统</h1>
    <textarea id="question" placeholder="请输入您的问题..."></textarea>
    <button onclick="askQuestion()">提交问题</button>
    <div class="response">
      <h3>回答：</h3>
      <p id="answer"></p>
    </div>
  </div>

  <script>
    async function askQuestion() {
      const question = document.getElementById('question').value;
      const answerElement = document.getElementById('answer');
      answerElement.textContent = '正在思考...';
      
      try {
        const response = await fetch('/ask', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ question })
        });
        const data = await response.json();
        answerElement.textContent = data.answer;
      } catch (error) {
        answerElement.textContent = '请求失败，请稍后重试';
      }
    }
  </script>
</body>
</html>
