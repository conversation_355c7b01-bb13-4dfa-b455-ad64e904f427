﻿---其他工具
create or replace procedure p_add_perms2customer_user(customer_codes in varchar2, perm_ids in varchar2) as
begin
  for customer in (select regexp_substr(customer_codes,'[^,]+', 1, level, 'i') as customer_code from dual connect by level <= length(customer_codes)-length(regexp_replace(customer_codes, ',', ''))+1) loop
    for perm in(select regexp_substr(perm_ids,'[^,]+', 1, level, 'i') as perm_id from dual connect by level <= length(perm_ids)-length(regexp_replace(perm_ids, ',', ''))+1) loop
      insert into cust_user_perm
      select u.user_id,perm.perm_id from cust_user u where TYPE = 0 and u.username=customer.customer_code;
    end loop;
  end loop;
  commit;
end;

declare
  customer_codes varchar2(100);
  sql_result varchar2(100);
begin
  customer_codes := '562006003077234';
  p_add_perms2customer_user(customer_codes,'8212,8213,82152,82153,82155');
  select case when count(0)=5 then 'SUCCESS' else 'FAILURE' end into sql_result from cust_user_perm u where u.user_id in(select u.user_id from cust_user u where TYPE = 0 and u.username in (select regexp_substr(customer_codes,'[^,]+', 1, level, 'i') as customer_code from dual connect by level <= length(customer_codes)-length(regexp_replace(customer_codes, ',', ''))+1) );
  dbms_output.put_line(sql_result);
end;
/
---开始
create sequence SEQ_META minvalue 1 maxvalue 9999999999999999999999999999 
       start with 100 increment by 1 ;
--创建表-------------------------------------------
create table META_TABLES
(
  table_name      VARCHAR2(40) not null,
  table_title     VARCHAR2(4000),
  app             CHAR(4),
  module          VARCHAR2(60),
  create_time     DATE,
  updator         CHAR(3),
  update_time     DATE,
  partitioned     VARCHAR2(3),
  partition_field VARCHAR2(4000),
  comments        VARCHAR2(4000),
  remarks         VARCHAR2(4000)
);
-- Add comments to the columns 
comment on column META_TABLES.table_name is '表名';
comment on column META_TABLES.table_title is '标题';
comment on column META_TABLES.app is '所属应用';
comment on column META_TABLES.module is '所属模块';
comment on column META_TABLES.create_time is '创建时间';
comment on column META_TABLES.updator is '修改人';
comment on column META_TABLES.update_time is '修改时间';
comment on column META_TABLES.partitioned is '是否分区';
comment on column META_TABLES.partition_field is '分区字段';
comment on column META_TABLES.comments is '注释';
comment on column META_TABLES.remarks is '使用说明';
-- Create/Recreate indexes 
create unique index IDX_TABLE_NAME on META_TABLES (TABLE_NAME);
--
create table META_COLUMNS
(
  table_name  VARCHAR2(30) not null,
  field_name  VARCHAR2(30) not null,
  field_title VARCHAR2(4000),
  data_type   VARCHAR2(106),
  data_length NUMBER not null,
  data_precision NUMBER,
  nullable    VARCHAR2(1),
  comments    VARCHAR2(4000),
  sort_order  NUMBER,
  create_time DATE,
  updator     CHAR(3),
  update_time DATE,
  remarks     VARCHAR2(4000),
  batch_no    NUMBER
);
-- Add comments to the columns 
comment on column META_COLUMNS.table_name is '表名';
comment on column META_COLUMNS.field_name is '字段名';
comment on column META_COLUMNS.field_title is '字段标题';
comment on column META_COLUMNS.data_type is '数据类型';
comment on column META_COLUMNS.data_length is '长度';
comment on column META_COLUMNS.nullable is '是否允许空';
comment on column META_COLUMNS.comments is '备注';
comment on column META_COLUMNS.sort_order is '排序';
comment on column META_COLUMNS.create_time is '创建时间';
comment on column META_COLUMNS.updator is '修改人';
comment on column META_COLUMNS.update_time is '修改时间';
comment on column META_COLUMNS.remarks is '说明';
comment on column META_COLUMNS.batch_no is '批次';
-- Create/Recreate indexes 
create unique index IDX_TABLE_COL on META_COLUMNS (TABLE_NAME, FIELD_NAME);
--
create table META_INDEXES
(
  index_name  VARCHAR2(30) not null,
  index_type  VARCHAR2(27),
  table_name  VARCHAR2(30) not null,
  uniqueness  VARCHAR2(9),
  column_name VARCHAR2(4000),
  create_time DATE,
  updator     CHAR(3),
  update_time DATE,
  batch_no    NUMBER
);
-- Add comments to the columns 
comment on column META_INDEXES.index_name is '索引名称';
comment on column META_INDEXES.index_type is '索引类型';
comment on column META_INDEXES.table_name is '表名';
comment on column META_INDEXES.uniqueness is '是否唯一';
comment on column META_INDEXES.column_name is '索引字段';
comment on column META_INDEXES.create_time is '创建时间';
comment on column META_INDEXES.updator is '修改人';
comment on column META_INDEXES.update_time is '修改时间';
comment on column META_INDEXES.batch_no is '变更批次';
-- Create/Recreate indexes 
create unique index IDX_INDEX_NAME on META_INDEXES (INDEX_NAME);
create unique index IDX_INDEX_TABLE_FIELDS on META_INDEXES (TABLE_NAME, COLUMN_NAME);
--
create table META_COLUMNS_HIS
(
  table_name  VARCHAR2(30) not null,
  field_name  VARCHAR2(30) not null,
  field_title VARCHAR2(4000),
  remarks     VARCHAR2(4000),
  data_type   VARCHAR2(106),
  data_precision NUMBER,
  data_length NUMBER not null,
  nullable    VARCHAR2(1),
  comments    VARCHAR2(4000),
  sort_order  NUMBER,
  create_time DATE,
  batch_no    NUMBER,
  op          VARCHAR2(30)
);
-- Add comments to the columns 
comment on column META_COLUMNS_HIS.table_name is '表名';
comment on column META_COLUMNS_HIS.field_name is '字段名';
comment on column META_COLUMNS_HIS.field_title is '标题';
comment on column META_COLUMNS_HIS.remarks is '说明';
comment on column META_COLUMNS_HIS.data_type is '类型';
comment on column META_COLUMNS_HIS.data_length is '长度';
comment on column META_COLUMNS_HIS.nullable is '是否允许空';
comment on column META_COLUMNS_HIS.comments is '备注';
comment on column META_COLUMNS_HIS.sort_order is '排序';
comment on column META_COLUMNS_HIS.create_time is '创建时间';
comment on column META_COLUMNS_HIS.batch_no is '批号';
comment on column META_COLUMNS_HIS.op is '操作';
-- Create/Recreate indexes 
create index IDX_META_COLUMNS on META_COLUMNS_HIS (TABLE_NAME, FIELD_NAME);
--
create table META_INDEXES_HIS
(
  index_name  VARCHAR2(30) not null,
  index_type  VARCHAR2(27),
  table_name  VARCHAR2(30) not null,
  uniqueness  VARCHAR2(9),
  column_name VARCHAR2(4000),
  create_time DATE,
  batch_no    NUMBER,
  op          VARCHAR2(30)
);
-- Create/Recreate indexes 
create index IDX_INDEX_HIS_TABLE on META_INDEXES_HIS (TABLE_NAME);
----------------------------------------------------------
CREATE OR REPLACE FUNCTION get_field_title(p_text varchar2) RETURN varchar2 IS
  TYPE t_sen_chars IS ARRAY(10) OF varchar2(10);
  sen_chars t_sen_chars := t_sen_chars('（', '(', '：', ':', '，', ',', ' ', '；', '=','[');
  v_title varchar2(4000);
BEGIN
  FOR i IN 1 .. sen_chars.count LOOP
    IF instr(p_text, sen_chars(i)) > 0 THEN
      v_title := substr(p_text,0, instr(p_text, sen_chars(i)) - 1);
    END IF;
  END LOOP;
  IF p_text != v_title THEN
    v_title := get_field_title(v_title);
  ELSE 
    v_title := p_text;
  END IF;
  IF length(v_title) = 1 THEN 
    v_title := '';
  END IF;
  RETURN v_title;
END;
/
CREATE OR REPLACE FUNCTION get_field_remarks(p_text varchar2) RETURN varchar2 IS
  TYPE t_sen_chars IS ARRAY(6) OF varchar2(10);
  sen_chars t_sen_chars := t_sen_chars(',', '，', '：', ':', ' ', '；');
  v_title varchar2(4000);
  v_remarks varchar2(4000);
BEGIN
  v_title := get_field_title(p_text);
  v_remarks := CASE WHEN v_title = '' THEN p_text ELSE trim(substr(p_text, length(v_title)+1)) END;
  FOR i IN 1 .. sen_chars.count LOOP
    IF substr(v_remarks,1,1) = sen_chars(i) THEN
      v_remarks := substr(v_remarks,2);
    END IF;
  END LOOP;
  IF v_remarks LIKE '(%' AND v_remarks LIKE '%)' THEN
    v_remarks := substr(v_remarks,2,length(v_remarks) - 1);
  END IF;
  IF v_remarks LIKE '（%' AND v_remarks LIKE '%）' THEN
    v_remarks := substr(v_remarks,2,length(v_remarks) - 1);
  END IF;
  IF v_remarks LIKE '[%' AND v_remarks LIKE '%]' THEN
    v_remarks := substr(v_remarks,2,length(v_remarks) - 1);
  END IF;
  RETURN v_remarks;
END;
/
----------------------------------------------------------
CREATE OR REPLACE VIEW v_meta_tables AS 
SELECT a.TABLE_NAME,b.COMMENTS AS table_title, 'APS' AS app, SUBSTR(a.TABLE_NAME, 0, instr(a.TABLE_NAME, '_') - 1) AS module,
d.CREATED AS create_time, 'DEV' AS updator, SYSDATE AS update_time,
(CASE WHEN c.NAME IS NOT NULL THEN 'YES' ELSE 'NO' END) AS partitioned,c.COLUMN_NAME AS partition_field,
b.COMMENTS AS comments
FROM USER_TABLES a 
	LEFT JOIN USER_TAB_COMMENTS b ON a.TABLE_NAME =b.TABLE_NAME
	LEFT JOIN USER_PART_KEY_COLUMNS  c ON a.TABLE_NAME = c.name
  INNER JOIN USER_OBJECTS d ON d.OBJECT_TYPE='TABLE' AND a.TABLE_NAME=d.OBJECT_NAME
WHERE a.TABLE_NAME NOT LIKE '%TMP%' AND a.TABLE_NAME NOT LIKE '%TEMP%'  AND a.TABLE_NAME NOT LIKE '%TEST%' 
  AND a.TABLE_NAME NOT LIKE '%BAK%' AND a.TABLE_NAME NOT LIKE 'QRTZ%'
ORDER BY a.TABLE_NAME ;

CREATE OR REPLACE VIEW v_meta_columns AS 
SELECT a.TABLE_NAME,a.COLUMN_NAME AS field_name, 
  get_field_title(b.COMMENTS) AS field_title,
  get_field_remarks(b.comments) as remarks,
  a.DATA_TYPE,a.DATA_LENGTH,a.DATA_PRECISION,a.NULLABLE,/*a.DATA_DEFAULT AS default_value,*/
  b.COMMENTS AS comments,a.COLUMN_ID AS sort_order,
SYSDATE AS create_time, 'DEV'AS updator,SYSDATE AS update_time
FROM USER_TAB_COLS a INNER JOIN USER_TABLES c ON a.TABLE_NAME =c.TABLE_NAME LEFT JOIN USER_COL_comments b ON a.TABLE_NAME =b.TABLE_NAME AND a.COLUMN_NAME =b.COLUMN_NAME 
WHERE a.TABLE_NAME IN (SELECT TABLE_NAME FROM v_meta_tables)
ORDER BY a.TABLE_NAME, a.COLUMN_ID ;

CREATE OR REPLACE VIEW v_meta_indexes AS 
SELECT a.INDEX_NAME, a.INDEX_TYPE, a.TABLE_NAME, a.UNIQUENESS,
	(SELECT listagg(column_name,',') WITHIN GROUP (ORDER BY column_name) FROM user_ind_columns c WHERE a.INDEX_NAME=c.INDEX_NAME) AS column_name,
SYSDATE AS create_time, 'DEV' AS updator, SYSDATE AS update_time
FROM user_indexes a
WHERE a.TABLE_NAME IN (SELECT TABLE_NAME FROM v_meta_tables)
  AND a.INDEX_NAME NOT LIKE 'SYS_%'
ORDER BY a.TABLE_NAME ;

--
CREATE OR REPLACE PROCEDURE SP_META_REFRESH IS
  v_batch_no number;
BEGIN
  --tables
  MERGE INTO META_TABLES T
  USING v_meta_tables s ON (t.table_name=s.table_name)
  WHEN MATCHED THEN
    UPDATE SET t.table_title=(CASE WHEN t.table_title IS NULL THEN s.table_title ELSE t.table_title END),
        t.partitioned=s.partitioned,
        t.partition_field=s.partition_field,
        t.comments=s.comments,
        t.remarks=s.comments,
        t.update_time=sysdate
      WHERE t.partitioned <> s.partitioned OR
        t.partition_field <> s.partition_field OR
        t.comments <> s.comments
  WHEN NOT MATCHED THEN
    INSERT VALUES(s.table_name, s.table_title, s.app, s.module, s.create_time, s.updator, s.update_time,
      s.partitioned, s.partition_field, s.comments, s.comments);

  DELETE FROM META_TABLES mt WHERE TABLE_NAME NOT in(SELECT TABLE_NAME FROM V_META_TABLES vmt);

  --columns
  SELECT nvl(MAX(BATCH_NO)+1,1) INTO v_batch_no FROM META_COLUMNS;

  --合并字段变更
  MERGE INTO META_COLUMNS t
  USING v_meta_columns s ON (t.table_name=s.table_name AND t.field_name=s.field_name)
  WHEN MATCHED THEN
    UPDATE SET
      t.field_title=(CASE WHEN t.field_title IS NULL THEN s.field_title ELSE t.field_title END),
      t.remarks=(CASE WHEN t.remarks IS NULL THEN s.remarks ELSE t.remarks END),
      t.data_type = s.data_type,
      t.data_length = s.data_length,
      t.data_precision = s.data_precision,
      t.nullable = s.nullable,
      t.comments = s.comments,
      t.batch_no = v_batch_no,
      t.update_time = SYSDATE
    WHERE t.data_type <> s.data_type
      OR t.data_length <> s.data_length
      OR t.data_precision <> s.data_precision
      OR t.nullable <> s.nullable
      OR t.comments <> s.comments OR (t.comments is null and s.COMMENTS is not null)
  WHEN NOT MATCHED THEN
    INSERT (table_name, field_name, field_title, data_type, data_length, data_precision,
      nullable, comments, sort_order, create_time, updator, update_time, remarks, batch_no)
    VALUES(s.table_name, s.field_name, s.field_title, s.data_type, s.data_length, s.data_precision,
      s.nullable, s.comments, s.sort_order, s.create_time, s.updator, s.update_time, s.remarks, v_batch_no);
  
  --增加字段变更历史
  INSERT INTO META_COLUMNS_HIS(TABLE_NAME,FIELD_NAME,FIELD_TITLE,REMARKS,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,NULLABLE,COMMENTS,SORT_ORDER,CREATE_TIME,BATCH_NO,OP)
  SELECT TABLE_NAME,FIELD_NAME,FIELD_TITLE,REMARKS,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,NULLABLE,COMMENTS,SORT_ORDER,sysdate,V_BATCH_NO,
    (CASE WHEN CREATE_TIME = UPDATE_TIME THEN 'ADD' ELSE 'MODIFY' END)
  FROM META_COLUMNS mc 
  WHERE mc.batch_no=v_batch_no;
  
  INSERT INTO META_COLUMNS_HIS(TABLE_NAME,FIELD_NAME,FIELD_TITLE,REMARKS,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,NULLABLE,COMMENTS,SORT_ORDER,CREATE_TIME,BATCH_NO,OP)
  SELECT TABLE_NAME,FIELD_NAME,FIELD_TITLE,REMARKS,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,NULLABLE,COMMENTS,SORT_ORDER,sysdate,V_BATCH_NO,
    'DROP'
  FROM META_COLUMNS mc 
  WHERE NOT EXISTS (SELECT 1 FROM V_META_COLUMNS vmc WHERE mc.TABLE_NAME=vmc.TABLE_NAME AND mc.FIELD_NAME=vmc.FIELD_NAME);
  
  --删除字段
  DELETE FROM META_COLUMNS mc WHERE NOT EXISTS (SELECT 1 FROM V_META_COLUMNS vmc WHERE mc.TABLE_NAME=vmc.TABLE_NAME AND mc.FIELD_NAME=vmc.FIELD_NAME);

  --更新表最后变更时间
  MERGE INTO META_TABLES T
  USING (SELECT TABLE_NAME, MAX(UPDATE_TIME) AS UPDATE_TIME FROM META_COLUMNS GROUP BY TABLE_NAME) S
  ON (T.TABLE_NAME = S.TABLE_NAME)
  WHEN MATCHED THEN
    UPDATE SET T.UPDATE_TIME = S.UPDATE_TIME WHERE T.UPDATE_TIME<S.UPDATE_TIME;

  --indexes
  SELECT nvl(MAX(BATCH_NO)+1,1) INTO v_batch_no FROM META_INDEXES;

  --合并索引信息
  MERGE INTO META_INDEXES t
  USING v_meta_indexes s ON (t.index_name = s.index_name)
  WHEN MATCHED THEN
    UPDATE SET t.index_type = s.index_type,
      t.table_name = s.table_name,
      t.uniqueness = s.uniqueness,
      t.column_name = s.column_name,
      t.batch_no = v_batch_no,
      t.update_time = SYSDATE
    WHERE t.index_type <> s.index_type OR
      t.table_name <> s.table_name OR
      t.uniqueness <> s.uniqueness OR
      t.column_name <> s.column_name
  WHEN NOT MATCHED THEN
    INSERT VALUES(s.index_name, s.index_type, s.table_name, s.uniqueness, s.column_name,
      s.create_time, s.updator, s.update_time, v_batch_no);
  
  --增加索引变更历史
  INSERT INTO META_INDEXES_HIS(index_name,index_type,table_name,uniqueness,column_name,create_time,batch_no, OP)
  SELECT index_name,index_type,table_name,uniqueness,column_name,SYSDATE, v_batch_no, (CASE WHEN CREATE_TIME = UPDATE_TIME THEN 'ADD' ELSE 'MODIFY' END)
  FROM META_INDEXES t 
  WHERE t.batch_no=v_batch_no;
  
  INSERT INTO META_INDEXES_HIS(index_name,index_type,table_name,uniqueness,column_name,create_time,batch_no, OP)
  SELECT index_name,index_type,table_name,uniqueness,column_name,SYSDATE, v_batch_no, 'DROP'
  FROM META_INDEXES t 
  WHERE NOT EXISTS(SELECT INDEX_NAME FROM V_META_INDEXES vmi WHERE vmi.INDEX_NAME = t.INDEX_NAME);
  --删除索引
  DELETE FROM META_INDEXES mi WHERE mi.INDEX_NAME NOT in(SELECT INDEX_NAME FROM V_META_INDEXES vmi);
  
  COMMIT;
END;
--调度任务，每60分钟执行1次
begin
  sys.dbms_scheduler.create_job(job_name            => 'SP_META_REFRESH_JOB',
                                job_type            => 'STORED_PROCEDURE',
                                job_action          => 'SP_META_REFRESH',
                                start_date          => to_date('03-02-2023 00:00:00', 'dd-mm-yyyy hh24:mi:ss'),
                                repeat_interval     => 'Freq=Minutely;Interval=60',
                                end_date            => to_date('03-02-2099 00:00:00', 'dd-mm-yyyy hh24:mi:ss'),
                                job_class           => 'DEFAULT_JOB_CLASS',
                                enabled             => true,
                                auto_drop           => false,
                                comments            => '');
end;
/
--构建Create table语句
CREATE OR REPLACE FUNCTION get_create_table_stmt(p_table_name varchar2/*, p_part_field varchar2, p_part_from varchar2*/) RETURN CLOB IS
  v_stmt CLOB;
  v_tmp varchar2(4000);
BEGIN
  v_stmt := 'CREATE TABLE ' || p_table_name;
  --Columns
  v_stmt := v_stmt || '(';
  FOR f IN (SELECT * FROM meta_columns WHERE table_name=p_table_name ORDER BY sort_order) LOOP
    v_stmt := v_stmt || chr(13) || '  ' || f.field_name || chr(9) || f.data_type 
           || (CASE WHEN f.data_length is not null AND f.data_type NOT IN('DATE','TIMESTAMP(0)','TIMESTAMP(6)','NUMBER') THEN '(' || f.data_length || ')' ELSE '' END)
           || (CASE WHEN f.nullable != 'Y' THEN ' NOT NULL' ELSE '' END)
           || ',';
  END LOOP;
  v_stmt := substr(v_stmt,1,length(v_stmt) - 1) || chr(13) || ')';
  --Partition Info
  /*
  SELECT 'P_' || SEQ_META.Nextval INTO v_tmp FROM dual;
  v_stmt := v_stmt || ' partition by range(' || p_part_field || ') interval (numtodsinterval(1,''day''))';
  v_stmt := v_stmt || chr(13) || ' (partition ' || v_tmp ||' values less than (to_date(''' || p_part_from || ''',''YYYY-MM-DD'')))';
  */
  v_stmt := v_stmt || chr(13) || ';';
  --Table Comment
  SELECT 'COMMENT ON TABLE '||TABLE_NAME||' IS '''||(CASE WHEN COMMENTS IS NOT NULL THEN COMMENTS ELSE TABLE_TITLE END)||''';'
  INTO v_tmp
  FROM META_TABLES WHERE table_name = p_table_name;
  v_stmt := v_stmt || chr(13) || chr(13) || v_tmp;
  --Column Comment
  v_stmt := v_stmt || chr(13) || '--Column comments';
  FOR c IN (SELECT 'COMMENT ON COLUMN '||TABLE_NAME||'.'||FIELD_NAME||' IS '''||FIELD_TITLE||(CASE WHEN REMARKS IS NOT NULL THEN '('||REMARKS||')' ELSE '' END)||''';' AS COMMENT_STATEMENT FROM META_COLUMNS WHERE TABLE_NAME=p_table_name ORDER BY TABLE_NAME, SORT_ORDER) LOOP
    v_stmt := v_stmt || chr(13) || c.comment_statement;
  END LOOP;
  RETURN v_stmt;
END;
/
--
BEGIN 
	SP_META_REFRESH;
END;
--构建Comment语句
select get_create_table_stmt('POSP_CHANNEL_KEY') AS CREATE_SQL FROM DUAL
UNION ALL
select get_create_table_stmt('POSP_CHANNEL_MCHT') AS CREATE_SQL FROM DUAL
UNION ALL
select get_create_table_stmt('POSP_LOCATION') AS CREATE_SQL FROM DUAL
UNION ALL
select get_create_table_stmt('POSP_RSP_CODE_MAP') AS CREATE_SQL FROM DUAL
UNION ALL
select get_create_table_stmt('POSP_STORE_FORWARD') AS CREATE_SQL FROM DUAL
UNION ALL
select get_create_table_stmt('POSP_TERMINAL') AS CREATE_SQL FROM DUAL
UNION ALL
select get_create_table_stmt('POSP_TERM_COMMON_PARAM') AS CREATE_SQL FROM DUAL
UNION ALL
select get_create_table_stmt('POSP_TERM_LOCATION') AS CREATE_SQL FROM DUAL
UNION ALL
select get_create_table_stmt('POSP_TERM_PARAM') AS CREATE_SQL FROM DUAL
UNION ALL
select get_create_table_stmt('POSP_TXN') AS CREATE_SQL FROM DUAL
UNION ALL
select get_create_table_stmt('POSP_TXN_CTRL') AS CREATE_SQL FROM DUAL
UNION ALL
select get_create_table_stmt('POSP_TXN_TYPE_MAP') AS CREATE_SQL FROM DUAL;

select * from meta_tables t where t.table_name like 'POSP%';
select * from POSP_CHANNEL_MCHT;

SELECT 'COMMENT ON TABLE '||TABLE_NAME||' IS '''||(CASE WHEN COMMENTS IS NOT NULL THEN COMMENTS ELSE TABLE_TITLE END)||''';' AS COMMENT_STATEMENT FROM META_TABLES WHERE TABLE_NAME='RC_DROOLS_SCENE_WHEN';
SELECT 'COMMENT ON COLUMN '||TABLE_NAME||'.'||FIELD_NAME||' IS '''||FIELD_TITLE||(CASE WHEN REMARKS IS NOT NULL THEN '('||REMARKS||')' ELSE '' END)||''';' AS COMMENT_STATEMENT FROM META_COLUMNS WHERE TABLE_NAME='RC_DROOLS_SCENE_WHEN' ORDER BY TABLE_NAME, SORT_ORDER;

--Query
SELECT * FROM META_TABLES mt where mt.table_name like '%FILE%' ORDER BY mt.CREATE_TIME DESC;
SELECT mc.table_name,mc.field_name,mc.field_title,mc.remarks,mc.comments,mc.data_type,mc.data_length,mc.data_precision,mc.sort_order FROM META_COLUMNS mc WHERE TABLE_NAME = upper('txs_pay_trade_order') ORDER BY mc.SORT_ORDER;
SELECT * FROM META_COLUMNS_HIS mc WHERE TABLE_NAME NOT LIKE 'META%' AND TABLE_NAME IN (SELECT TABLE_NAME FROM META_TABLES) ORDER BY mc.CREATE_TIME DESC, mc.TABLE_NAME;
SELECT FIELD_NAME,FIELD_TITLE,REMARKS,CREATE_TIME,OP FROM META_COLUMNS_HIS mc WHERE TABLE_NAME = 'ACC_BZJ_RECORD' ORDER BY mc.CREATE_TIME DESC;
SELECT * FROM META_INDEXES mi WHERE TABLE_NAME NOT LIKE 'META%' ORDER BY mi.UPDATE_TIME DESC, mi.TABLE_NAME;

--Export
SELECT mt.* FROM META_TABLES mt ORDER BY mt.MODULE,mt.TABLE_NAME;
SELECT mc.* FROM META_COLUMNS mc INNER JOIN META_TABLES mt ON mc.TABLE_NAME =mt.TABLE_NAME ORDER BY mt.MODULE,mt.TABLE_NAME,mc.SORT_ORDER;
SELECT mi.* FROM META_INDEXES mi INNER JOIN META_TABLES mt ON mi.TABLE_NAME =mt.TABLE_NAME ORDER BY mt.MODULE,mt.TABLE_NAME,mi.INDEX_NAME;
SELECT mc.* FROM META_COLUMNS_HIS mc INNER JOIN META_TABLES mt ON mc.TABLE_NAME =mt.TABLE_NAME ORDER BY mt.MODULE,mt.TABLE_NAME,mc.SORT_ORDER;
SELECT mi.* FROM META_INDEXES_HIS mi INNER JOIN META_TABLES mt ON mi.TABLE_NAME =mt.TABLE_NAME ORDER BY mt.MODULE,mt.TABLE_NAME,mi.INDEX_NAME;
--Update
SELECT * FROM META_TABLES mt WHERE mt.Table_Name = 'RC_OUT_CARD_NO' order by mt.update_time desc for update;
SELECT mc.table_name,mc.field_name,mc.field_title,mc.remarks,mc.comments,mc.data_type,mc.data_length,mc.data_precision,mc.sort_order FROM META_COLUMNS mc WHERE mc.TABLE_NAME = 'ACS_OPERATE_RECORDS' ORDER BY mc.TABLE_NAME, SORT_ORDER for update;
SELECT mc.table_name,mc.field_name,mc.field_title,mc.remarks,mc.comments,mc.data_type,mc.data_length,mc.data_precision,mc.sort_order FROM META_COLUMNS mc WHERE mc.FIELD_NAME = 'APP_SERVER_CERT' ORDER BY mc.TABLE_NAME, SORT_ORDER for update;
UPDATE META_COLUMNS a SET FIELD_TITLE=(SELECT FIELD_TITLE FROM META_COLUMNS b WHERE A.FIELD_NAME=b.FIELD_NAME AND b.TABLE_NAME='WALLET_IDCARD_INFO') WHERE a.TABLE_NAME='WALLET_IDCARD_INFO_HIS' AND a.FIELD_TITLE IS NULL;

