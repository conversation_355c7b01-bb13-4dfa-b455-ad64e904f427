# SM2配置文件
# 用于配置SM2密钥和证书路径

import os

# 服务器配置
SERVER_CONFIG = {
    'host': '************',
    'port': 9445,
    'timeout': 10.0
}

# 证书路径配置
CERT_CONFIG = {
    'sm2_cert_path': 'C:/haproxy-3.2/certs/sm2.pem',
    'cert_dir': 'C:/haproxy-3.2/certs',
    'ca_cert_path': 'C:/haproxy-3.2/certs/ca.pem'
}

# SM2密钥配置
# 注意：在生产环境中，这些密钥应该从安全的密钥管理系统中获取
# 而不是硬编码在配置文件中
SM2_CONFIG = {
    # 示例公钥（实际使用时需要替换为真实的密钥）
    # 注意：这里使用十六进制格式的密钥，适用于gmssl库
    'public_key': '',  # 暂时留空，避免格式错误

    # 示例私钥（实际使用时需要替换为真实的密钥）
    # 注意：这里使用十六进制格式的密钥，适用于gmssl库
    'private_key': '',  # 暂时留空，避免格式错误

    # 是否启用SM2加密（设置为True以启用SM2加密）
    'enable_sm2_encryption': True,

    # 连接选项
    'prefer_pure_sm2': True,  # 是否优先使用纯SM2连接（不使用SSL/TLS）

    # SM2密钥文件路径（如果从文件加载）
    'public_key_file': 'C:/haproxy-3.2/certs/sm2_public.pem',
    'private_key_file': 'C:/haproxy-3.2/certs/sm2_private.pem'
}

def load_sm2_keys_from_file():
    """从文件加载SM2密钥"""
    try:
        public_key = ""
        private_key = ""
        
        # 尝试从文件加载公钥
        if os.path.exists(SM2_CONFIG['public_key_file']):
            with open(SM2_CONFIG['public_key_file'], 'r', encoding='utf-8') as f:
                public_key = f.read().strip()
            print(f"从文件加载SM2公钥: {SM2_CONFIG['public_key_file']}")
        
        # 尝试从文件加载私钥
        if os.path.exists(SM2_CONFIG['private_key_file']):
            with open(SM2_CONFIG['private_key_file'], 'r', encoding='utf-8') as f:
                private_key = f.read().strip()
            print(f"从文件加载SM2私钥: {SM2_CONFIG['private_key_file']}")
        
        return public_key, private_key
    except Exception as e:
        print(f"从文件加载SM2密钥失败: {e}")
        return "", ""

def get_sm2_keys():
    """获取SM2密钥"""
    # 首先尝试从配置中获取
    public_key = SM2_CONFIG.get('public_key', '')
    private_key = SM2_CONFIG.get('private_key', '')
    
    # 如果配置中没有密钥，尝试从文件加载
    if not public_key or not private_key:
        file_public_key, file_private_key = load_sm2_keys_from_file()
        if file_public_key:
            public_key = file_public_key
        if file_private_key:
            private_key = file_private_key
    
    return public_key, private_key

# 调试配置
DEBUG_CONFIG = {
    'verbose': True,
    'log_encryption': True,
    'log_certificates': True,
    'ignore_cert_errors': False,  # 是否忽略证书错误
    'test_mode': True  # 测试模式，使用示例密钥
}
