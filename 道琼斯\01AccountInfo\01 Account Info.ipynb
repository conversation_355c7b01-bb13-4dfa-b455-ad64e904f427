{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "5lTl_dvI8TkR"}, "source": ["# Factiva Account Info API"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "M7dlgedO8TkT"}, "source": ["Your Factiva account defines your access level to content and services, including extraction (Snapshot) and article limits.\n", "\n", "You can check on this -- including your current usage -- by using the **Accounts** API endpoint."]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "GBNfG3wc8TkU"}, "source": ["![Accounts API workflow diagram](accounts.png)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"autoexec": {"startup": false, "wait_interval": 0}}, "colab_type": "code", "id": "NwyZOCoM8TkU"}, "outputs": [], "source": ["## python3-style print statement support for backwards compatibility with python2\n", "from __future__ import print_function\n", "\n", "## Python support for HTTPS requests\n", "import requests\n", "\n", "## Support for fetching API key from environment variable\n", "import os"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"autoexec": {"startup": false, "wait_interval": 0}}, "colab_type": "code", "id": "7f8iN1kw8TkY"}, "outputs": [{"ename": "NameError", "evalue": "name 'os' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-1-613fba1b6fa8>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0;31m##\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[0;31m## OR (preferred) set it ask an environment variable and reference via os.environ\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 7\u001b[0;31m \u001b[0mapi_key\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mos\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0menviron\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"API_KEY\"\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'os' is not defined"]}], "source": ["## API key used in the request URL and header\n", "\n", "## Hardcode your API key\n", "## api_key = \"YOUR API KEY\"\n", "## \n", "## OR (preferred) set it ask an environment variable and reference via os.environ\n", "api_key = os.environ[\"API_KEY\"]"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "ORd-TbE28Tka"}, "source": ["####The Accounts endpoint"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"autoexec": {"startup": false, "wait_interval": 0}}, "colab_type": "code", "id": "1igOIKSm8Tkb"}, "outputs": [], "source": ["account_url = \"https://api.dowjones.com/alpha/accounts/{0}\".format(api_key)\n", "print(account_url)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "KLHHsXuI8Tkd"}, "source": ["#### Making the GET request to the Accounts endpoint\n", "\n", "When making requests to Factiva, you'll need to include your API key in the request header."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"autoexec": {"startup": false, "wait_interval": 0}}, "colab_type": "code", "id": "D46y5xQf8Tke"}, "outputs": [], "source": ["headers = {\"\"\n", "    'user-key': api_key,\n", "    'content-type': \"application/json\",\n", "    'cache-control': \"no-cache\"\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"autoexec": {"startup": false, "wait_interval": 0}}, "colab_type": "code", "id": "FBjRc8BX8Tkg"}, "outputs": [], "source": ["response = requests.request(\"GET\", account_url, headers=headers)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "7cCJbuwf8Tki"}, "source": ["The above GET request to the accounts endpoint will return a JSON response containing your account status, for example:"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "cj2ONvI-8Tkj"}, "source": ["```\n", "{\n", "    \"data\": {\n", "        \"attributes\": {\n", "            \"cnt_curr_ext\": 27,\n", "            \"current_downloaded_amount\": **********,\n", "          \n", "          \"max_allowed_document_extracts\": ************,\n", "            \"max_allowed_extracts\": 1000,\n", "            \"name\": \"<PERSON>\",\n", "            \"products\": [\n", "                {\n", "                    \"data\": {\n", "                        \"attributes\": {\n", "                            \"name\": \"DNA\"\n", "                        },\n", "                        \"id\": \"1\",\n", "                        \"type\": \"products_schema\"\n", "                    }\n", "                }\n", "            ],\n", "            \"tot_document_extracts\": **********,\n", "            \"tot_extracts\": 532,\n", "            \"tot_subscriptions\": 15,\n", "            \"tot_topics\": 27\n", "        },\n", "        \"id\": \"x314xxou812xxx7bacxxxxx8675309x9\",\n", "        \"relationships\": {\n", "            \"extractions\": {\n", "                \"links\": {\n", "                    \"related\": \"/alpha/accounts/extractions\"\n", "                }\n", "            }\n", "        },\n", "        \"type\": \"account_with_contract_limits\"\n", "    }\n", "}\n", "```"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "n93D6pob8Tkj"}, "source": ["#### Parsing the account info JSON response"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"autoexec": {"startup": false, "wait_interval": 0}}, "colab_type": "code", "id": "Nyza3rdj8Tkk"}, "outputs": [], "source": ["print(response.json())\n", "acct_info = response.json()[\"data\"][\"attributes\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"autoexec": {"startup": false, "wait_interval": 0}}, "colab_type": "code", "id": "4bqtPnDbwYto"}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"autoexec": {"startup": false, "wait_interval": 0}}, "colab_type": "code", "id": "gwrB9OmZ8Tkm"}, "outputs": [], "source": ["## Parse out some of the more interesting bits of account info...\n", "acct_name                       = acct_info[\"name\"]\n", "acct_products                   = [product[\"name\"].encode('ascii') for product in acct_info[\"products\"]] \n", "acct_max_snapshots              = int(acct_info[\"max_allowed_extracts\"])\n", "acct_max_concurrent_snapshots   = int(acct_info[\"max_allowed_concurrent_extracts\"])\n", "acct_max_articles               = int(acct_info[\"max_allowed_document_extracts\"])\n", "acct_extracted_articles         = int(acct_info[\"tot_document_extracts\"])\n", "## ... and so on"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"autoexec": {"startup": false, "wait_interval": 0}}, "colab_type": "code", "id": "t22HZJEM8Tkp"}, "outputs": [], "source": ["## Print the account info\n", "print('Name: {0}\\nProducts:{1}\\nMax Snapshots: {2}\\nMax Concurrent Snapshots: {3}\\nMax Articles: {4}\\nExtracted Articles: {5}'.format(acct_name, acct_products, acct_max_snapshots, acct_max_concurrent_snapshots, acct_max_articles, acct_extracted_articles))"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "INbvWcf18Tkt"}, "source": ["### Conclusion\n", "The Accounts API allows you to understand your account access limits and your current usage, which makes it easy to monitor and plan projects requiring Factiva API access."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"autoexec": {"startup": false, "wait_interval": 0}}, "colab_type": "code", "id": "60bJQXwn8Tku"}, "outputs": [], "source": []}], "metadata": {"colab": {"collapsed_sections": [], "default_view": {}, "name": "00 Account Info.ipynb", "private_outputs": true, "provenance": [], "version": "0.3.2", "views": {}}, "kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.16"}}, "nbformat": 4, "nbformat_minor": 2}