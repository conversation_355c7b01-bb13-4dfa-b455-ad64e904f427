<mxfile host="65bd71144e">
    <diagram id="POoE4Qmr6arjtYDclta9" name="第 1 页">
        <mxGraphModel dx="906" dy="607" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="用户" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
                    <mxGeometry x="100" y="250" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="Nginx&lt;br&gt;易票联前置机器" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="300" y="250" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="https://?.epaylinks.cn" style="endArrow=classic;html=1;exitX=0.5;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="1" source="2" target="3">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="470" y="340" as="sourcePoint"/>
                        <mxPoint x="520" y="290" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="5" value="财税Saas mycst服务" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="580" y="250" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="HTTP访问&lt;br&gt;IP鉴权" style="endArrow=classic;startArrow=classic;html=1;" edge="1" parent="1" source="3" target="5">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="470" y="330" as="sourcePoint"/>
                        <mxPoint x="520" y="280" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>