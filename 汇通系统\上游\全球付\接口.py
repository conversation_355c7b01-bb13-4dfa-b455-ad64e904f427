import requests
import uuid
import unicodedata

qqf_url = "https://fat-mso.globalcash.cn/"

def get_token():
    req = {"appId":"61ab11e4536cc597b2be360c8274229e", "appSecret":"17b6cbfd94dd3001a7815d5177d5e1c5"}
    r = requests.post(qqf_url + "/api/token/get", 
                      params=req, 
                      data=req,
                      headers={"Content-Type": "application/json", "charset": "utf-8", "nonceStr": uuid.uuid4().hex})
    print(r.content)
    return r.json()["data"]

def unescape_utf8(s):
    return unicodedata.normalize('NFKC', s.decode('unicode_escape'))


if __name__ == '__main__':
    print(get_token())