###生产环境
post https://epsp-wk.epaylinks.cn/api/agent/cybersource

###测试环境
post https://waika-test.epaylinks.cn/api/agent/cybersource
Content-Type: application/json
x-efps-sign-no: yilr20240826
x-efps-sign: {{sign}}
x-efps-sign-type: RSAwithSHA256
x-efps-timestamp: 20190924160000

{
    "api": "/risk/v1/decisions",
    "seqNo": "20190924160000000000000000000000000000000000000",
    "requestData": {
        "paymentInformation.card.number": "",
        "paymentInformation.card.bin": "",
        "paymentInformation.card.expirationMonth": "",
        "paymentInformation.card.expirationYear": "",
        "paymentInformation.card.type": "",
        "paymentInformation.instrumentIdentifier.id": "",
        "paymentInformation.paymentInstrument.id": "",
        "paymentInformation.customer.customerId": "",
        "orderInformation.amountDetails.totalAmount": "",
        "orderInformation.amountDetails.currency": "",
        "orderInformation.lineItems[].unitPrice": "",
        "orderInformation.lineItems[].quantity": "",
        "orderInformation.lineItems[].productCode": "",
        "orderInformation.lineItems[].productName": "",
        "orderInformation.lineItems[].productSKU": "",
        "orderInformation.billTo.firstName": "",
        "orderInformation.billTo.lastName": "",
        "orderInformation.billTo.address1": "",
        "orderInformation.billTo.address2": "",
        "orderInformation.billTo.locality": "",
        "orderInformation.billTo.administrativeArea": "",
        "orderInformation.billTo.postalCode": "",
        "orderInformation.billTo.country": "",
        "orderInformation.billTo.email": "",
        "orderInformation.billTo.phoneNumber": "",
        "buyerInformation.merchantCustomerId": "",
        "buyerInformation.dateOfBirth": "",
        "orderInformation.shipTo.firstName": "",
        "orderInformation.shipTo.lastName": "",
        "orderInformation.shipTo.address1": "",
        "orderInformation.shipTo.address2": "",
        "orderInformation.shipTo.locality": "",
        "orderInformation.shipTo.administrativeArea": "",
        "orderInformation.shipTo.postalCode": "",
        "orderInformation.shipTo.country": "",
        "orderInformation.shipTo.phoneNumber": "",
        "deviceInformation.ipAddress": "",
        "deviceInformation.fingerprintSessionId": "",
        "merchantDefinedInformation[].key": "",
        "merchantDefinedInformation[].value": ""
    },
    "nonceStr": "12345678901234567890123456789012"
}

### 使用Python脚本生成签名
@python sign_request.py
