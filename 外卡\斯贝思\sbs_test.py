import requests
import hashlib
from datetime import datetime
import random

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36"
}
def pay_prod(mid, account_num):
    url = "https://ga-pay.epaylinks.cn/payment.jsp"
    dt_str = datetime.now().strftime("%Y%m%d%H%M%S")
    data = {
            'p_mid': mid,
            'p_account_num': account_num,
            'p_transaction_type':'SALE',    #直连:SALE，收银台: VIEWSALE
            'p_order_num': f"{dt_str}{random.randint(1000, 9999)}",
            'p_currency':'CNY',
            'p_amount':'1.00',
            'p_card_num':'****************',
            'p_card_expmonth':'12',
            'p_card_expyear':'2024',
            'p_card_csc':'1234',
            'p_card_issuingbank':'招商银行',
            'p_firstname':'张',
            'p_lastname':'三',
            'p_user_email':'<EMAIL>',
            'p_user_phone':'***********',
            'p_user_ipaddress':'************',
            'p_trans_url':'https://www.epaylinks.cn',
            'p_return_url':'https://www.baidu.com',
            'p_bill_country':'中国',
            'p_bill_state':'广东省',
            'p_bill_city':'广州市',
            'p_bill_address':'海珠区',
            'p_bill_zip':'441234',
            'p_ship_firstname':'李',
            'p_ship_lastname':'四',
            'p_ship_country':'美国',
            'p_ship_state':'加利福利亚',
            'p_ship_city':'旧金山',
            'p_ship_address':'硅谷1234号',
            'p_ship_zip':'123456',
            'p_product_name':'电脑',
            'p_product_num':'10吨',
            'p_product_desc':'废弃电脑',
            'p_signmsg':'',
            'p_ext1':'No information',
            'p_ext2':'No information',
            'p_remark':'测试创建订单'
    }
    sha256 = hashlib.sha256()
    p_signmsg = data.get('p_mid') + data.get('p_account_num') + data.get('p_order_num') + data.get('p_currency') + data.get('p_amount')+'v2406dBH280ZJ4n'
    sha256.update(p_signmsg.encode('utf-8'))
    data.update({"p_signmsg":sha256.hexdigest()})
    print('请求URL -->> ', url)
    print('\n请求信息 -->> ', data)
    resp = requests.post(url, headers=headers, data=data)
    print('\n响应信息 -->> ', resp.text)
    

def pay_test(mid, account_num, amount):
    url = "https://test-wk.epaylinks.cn/pay/payment.jsp"
    # url = "http://localhost:81/payment.jsp"
    dt_str = datetime.now().strftime("%Y%m%d%H%M%S")
    data = {
            'p_mid': mid,
            'p_account_num': account_num,
            'p_transaction_type':'SALE',    #直连:SALE，收银台: VIEWSALE
            'p_order_num': f"{dt_str}{random.randint(1000, 9999)}",
            'p_currency':'CNY',
            'p_amount':f"{amount}",
            'p_card_num':'****************',  #****************
            'p_card_expmonth':'12',
            'p_card_expyear':'2024',
            'p_card_csc':'123',
            'p_card_issuingbank':'招商银行',
            'p_firstname':'张',
            'p_lastname':'三',
            'p_user_email':'<EMAIL>',
            'p_user_phone':'***********',
            'p_user_ipaddress':'***********',
            'p_trans_url':'https://epaylinks.cn',
            'p_return_url':'https://www.baidu.com',
            'p_bill_country':'CN',
            'p_bill_state':'广东省',
            'p_bill_city':'广州市',
            'p_bill_address':'海珠区',
            'p_bill_zip':'441234',
            'p_ship_firstname':'李',
            'p_ship_lastname':'四',
            'p_ship_country':'美国',
            'p_ship_state':'加利福利亚',
            'p_ship_city':'旧金山',
            'p_ship_address':'硅谷1234号',
            'p_ship_zip':'123456',
            'p_product_name':'电脑',
            'p_product_num':'10吨',
            'p_product_desc':'废弃电脑',
            'p_signmsg':'',
            'p_ext1':'00',
            'p_ext2':'No information',
            'p_remark':'测试创建订单'
    }
    sha256 = hashlib.sha256()
    p_signmsg = data.get('p_mid') + data.get('p_account_num') + data.get('p_order_num') + data.get('p_currency') + data.get('p_amount')+'********'
    sha256.update(p_signmsg.encode('utf-8'))
    data.update({"p_signmsg":sha256.hexdigest()})
    print('请求URL -->> ', url)
    print('\n请求信息 -->> ', data)
    resp = requests.post(url, headers=headers, data=data)
    print('\n响应信息 -->> ', resp.text)
    
def refund_test(mid, account_num, trans_num, amount):
    # url = "http://localhost:82/refund_submit"
    url = "https://test-wk.epaylinks.cn/aci/refund_submit"
    data = {
            'p_mid': mid,
            'p_account_num': account_num,
            'p_transaction_type':'REFUND',
            "p_refund_type":"1",
            'p_trans_num': trans_num,
            'p_refund_amount':amount,
            "p_refund_reason":"test refund",
            "p_order_currency":"CNY",
            "p_order_amount":amount,
            "p_remark":"test refund"
    }
    sha256 = hashlib.sha256()
    p_signmsg = data.get('p_mid') + data.get('p_account_num') + data.get('p_trans_num') + data.get('p_refund_type')+'********'
    sha256.update(p_signmsg.encode('utf-8'))
    data.update({"p_signmsg":sha256.hexdigest()})
    
    print('\n请求URL -->> ', url)
    print('\n请求信息 -->> ', data)
    resp = requests.post(url, headers=headers, data=data)
    print('\n响应信息 -->> ', resp.text)
    
    
def query_test(mid, account_num, order_num):
    url = "http://localhost:82/order_list.jsp"
    # url= "http://***********:8001/order_list.jsp"
    # url = "https://test-wk.epaylinks.cn/aci/order_list.jsp"
    data = {
            'p_mid': mid,
            'p_account_num': account_num,
            'p_transaction_type':'CHECK',
            'p_order_num': order_num
    }
    sha256 = hashlib.sha256()
    p_signmsg = data.get('p_mid') + data.get('p_account_num') +'********'
    sha256.update(p_signmsg.encode('utf-8'))
    data.update({"p_signmsg":sha256.hexdigest()})
    
    print('\n请求URL -->> ', url)
    print('\n请求信息 -->> ', data)
    resp = requests.post(url, headers=headers, data=data)
    print('\n响应信息 -->> ', resp.text)

if __name__ == '__main__':
    # pay_test('90010', '********'， 1000)
    pay_test('90006', '********', 20000)
    # query_test('90006', '********', '202407081627102826')
    # pay_prod('90158', '********')
    # refund_test('90006', '********', '2024112114193910873476', 20000)