import requests
import datetime
import json
import hashlib

base_url = "http://test.newtk7.kdb-tj.com:8038"
pub_key = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAm9MhzZvvrloxckyGr3to6lh1wNWo3ikpOYfNrWnwp0SGtDmnq+aIdpxJORmsZw72kVx6ObUccTaJUBQtVMSAivxKeoCTzHilx3rWJjickKOQeGRszMtm90fF2cNMXEsUAS+YSviiIOt1zkD8ZKkn0Q7UviJwNCLMwYPuNyZ3r3hiCcw39NV5H1Atb1eKL3Sx682wMxWE+mJwDmTW7hEjQTVuu9Y+aELTOkMIHR70X9yMEwQtpoRwkEYu/qH6L9H5OwW21csuZ8Z6H9CAkhaOSKvDY1prI5erBsvYKxAcrV4Yc+v1lclARrxzqEynIrdB8UvM8xNiw81QKswVASMwZQIDAQAB"
pri_key = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCb0yHNm++uWjFyTIave2jqWHXA1ajeKSk5h82tafCnRIa0Oaer5oh2nEk5GaxnDvaRXHo5tRxxNolQFC1UxICK/Ep6gJPMeKXHetYmOJyQo5B4ZGzMy2b3R8XZw0xcSxQBL5hK+KIg63XOQPxkqSfRDtS+InA0IszBg+43JneveGIJzDf01XkfUC1vV4ovdLHrzbAzFYT6YnAOZNbuESNBNW671j5oQtM6QwgdHvRf3IwTBC2mhHCQRi7+ofov0fk7BbbVyy5nxnof0ICSFo5Iq8NjWmsjl6sGy9grEBytXhhz6/WVyUBGvHOoTKcit0HxS8zzE2LDzVAqzBUBIzBlAgMBAAECggEAcRwtKWbL8K+fvOw3yCG44oMb1sJBNiKCvjQJ5JtGCOUBMsm3IFzTKeYAcOZl8tk1ugZ64nDJGJURz4MV9LY+6KSCG7JBlubsz4Qm9s+ZNpTERd//6hirwUvkHlJZl5UYDFRUI/efGG5SsUkS7K6tLF+rpeixseag4se1n2rY7hE+GNsqXX1W/SRGzLRY1AcRFgcvQaBDuH9NiswIdDFD/geL+HC++EZSeef0e6xPLDBY741ppmOQNB7NcOgTsnhxGLxYFxo6Leh6J8sRL78vSkyv8l1IS6oI+Gz/LIGJf7OKRzPhC9hGVWRwSPyS/8XtN6xpTLdEbl0MQ4Vw/3JLAQKBgQDSsqx+YmO0mue/+9MxvLuJzzXRfdmo38yI9DYGX3PWYkdtXI+/+62zdQuSKx9T5TO29y0rKUEViTXDP2K1U5SgzN+5+xQ17Hh4EwODk6xK8gKgQzgK+mkGvbjyJJ4FqSPIk5AVaFzS4zB+y13mzGCkh+mdK/8/2RaRW4cwVDqx9QKBgQC9VBqFHIfUFpGFBCDY87QHFgbN5HD6FZ5UZXLAoI6pf3Iz6d9pmo20YSz3DkzNGSyVIt2ylq2MG14paGJwIkg30Cqs/6O93hzolYtPNIcRq/cneo4o7AQwxIK5dbS4qiOSGEqccFwp+/lGOIoe6tuSNbxUR0e0azu23R9JLA/OsQKBgGGHU1EtOfnfumajqp7bPfQMdl0CZD5HWeYLJPbOOFCELYvxzJiRZ1YqV7DRRS8QaV/ICqBv+DB/0uMN9CL7DVFhhC4SishrLVhndLjzIk2OuKh2dPqf7v2v1GxBhuQX5vhxVcCTDmSPxWLrwAuVjyaZwwl91Ck507h1JZ65XnIBAoGAIHlRhy1qmtU4JpGbtLqwUVrU1fHt8udMXft8oE+pyKbkhsUBnhJ1ZdAZL1MTl0ZPIFF3p08y+0oib4XJBKTMsE4TU9MRWdrEKJyl5XMEC+LsJKhfw+MfoOHD7l2jHt2H9mIWQhj/pe/jRMkj38O237A52lbTlL2j42ywFhPprjECgYBzzmMFC2gWR6eQbtDqCxkJ4Hr027DNTS2pUMBhWshFWOWzbTjVpfdeyYmYVlPpePx6ZOd264IN2xsFgorHHS1J4BDUy8Q5iiheLBMsYg2oqW/HjyYq3e03s/4AhOaoqV0FFulYLqy4ZH9WoOGtMpph8KyEfSF865s5eKRNTanKGA=="

def send_request(url, biz_type, biz_data):
    def sign(data):
        data.pop('sign')
        body = json.dumps(data["body"]).encode('utf-8')
        body = f'bizType={data["bizType"]}&body={body}&nonceStr={data["nonceStr"]}&orgCode={data["orgCode"]}&timestamp={data["timestamp"]}&version={data["version"]}'
        return hashlib.sha256(body.encode('utf-8')).hexdigest()
    
    headers = {
        'Content-Type': 'application/json'
    }
    data = {
        'bizType': biz_type,
        'body': biz_data,
        'nonceStr': '123456',
        'orgCode': 'ST410102000004',
        'version': '1.0',
        'timestamp': datetime.datetime.now().strftime('%Y%m%d%H%M%S'),
        'sign': '123456'
    }
    data.update({'sign': sign(data)})
    response = requests.post(url, headers=headers, data=data)
    return response.text