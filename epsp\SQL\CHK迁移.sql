﻿--迁移Sequence
select 'CREATE SEQUENCE '|| t.sequence_name ||' MINVALUE '|| t.min_value ||' MAXVALUE '||t.max_value||' START WITH '||(t.last_number * 2)||' INCREMENT BY '||t.increment_by||' CACHE '||t.cache_size||';' as SEQ_SQL from user_sequences t where t.sequence_name like '%CHK%';

--迁移View
CREATE OR REPLACE FUNCTION LONG_TO_CHAR(IN_WHERE      VARCHAR,
                                        IN_TABLE_NAME VARCHAR,
                                        IN_COLUMN     VARCHAR2)
  RETURN VARCHAR2 AS
  V_RET VARCHAR2(32767);
  V_SQL VARCHAR2(2000);
 
BEGIN
  V_SQL := 'select ' || UPPER(IN_COLUMN) || ' from
    ' || UPPER(IN_TABLE_NAME) || ' where ' || IN_WHERE;
  EXECUTE IMMEDIATE V_SQL
    INTO V_RET;
 
  RETURN trim(V_RET);
 
END;
/
select 'CREATE VIEW '||VIEW_NAME || ' AS '|| chr(13) || Long_to_char('view_name='''||view_name||'''', 'user_views','TEXT') || ';' || chr(13) as VIEW_SQL from user_views where view_name like '%CHK%';

select * from user_views where view_name like '%CHK%';

----------------------------------------------------------------------------------------------

CREATE VIEW VCHK_ALIPAY_CHANNEL_RECORD AS 
select id                        ,
  trade_no                ,
  out_trade_no            ,
  trade_type              ,
  transaction_create_time ,
  transaction_finish_time ,
 -- dest_account            ,
  total_amount            ,
  receipt_amount          ,
  ali_hongbao_amount      ,
  point_amount            ,
  ali_disacount_amount    ,
  mer_disacount_amount    ,
  ticket_disacount_amount ,
  ticket_name             ,
  mer_hongbao_amount      ,
  bank_card_amount        ,
  refund_batch_no         ,
  service_fee             ,
  net_actual_income       ,
  mer_no                  ,
  bar_type                ,
  remark                  ,
  create_time             ,
  update_time             ,
  task_date               ,
  task_time               ,
  line_idx                ,
  file_name               ,
  task_id
  from CHK_ALIPAY_CHANNEL_RECORD
;

CREATE VIEW VCHK_GUIJI_TASK AS 
select
 id                    ,
  task_name            ,
  file_type            ,
  dest_table           ,
  task_status          ,
  institution_id       ,
  pay_channels         ,
  reconc_time          ,
  reconc_name_remark   ,
  ftp_url              ,
  inst_name            ,
  local_inst_code      ,
  channel_inst_code    ,
  file_name_pattern    ,
  create_time          ,
  update_time          ,
  remark               ,
 -- ftp_user             ,
 -- ftp_passwd           ,
  java_service         ,
  ftp_url_err          ,
  file_name_pattern_err,
  date_format_type,
  by_day,
  need_chk
from CHK_GUIJI_TASK
;

CREATE VIEW VCHK_UN_NOCARD_CHANNEL_RECORD AS 
select id                   ,
  line_idx             ,
  create_time          ,
  tran_type            ,
  seq_num              ,
  currency             ,
  tran_amount          ,
  tran_amount_num      ,
  business_type        ,
  settle_date          ,
  orig_seq_num         ,
  orig_tran_amount     ,
  orig_tran_amount_num ,
  order_no             ,
  inst_code            ,
 -- payer_acount_inst    ,
 -- payer_account_no     ,
  channel_inst         ,
  sign_no              ,
 -- payee_account_inst   ,
 -- payee_account_type   ,
 -- payee_account_no     ,
  mcht_no              ,
  mcht_cat             ,
  net_service_fee      ,
  net_service_fee_num  ,
  brand_fee            ,
  brand_fee_num        ,
  tran_state           ,
  tran_time            ,
  err_flag             ,
  err_reason           ,
  err_fee              ,
  err_fee_num          ,
  err_or_normal        ,
  task_date            ,
  task_time            ,
  file_name            ,
  task_id
  from CHK_UN_NOCARD_CHANNEL_RECORD
;

CREATE VIEW VCHK_WECHAT_CHANNEL_RECORD AS 
select id                     ,
  transaction_time      ,
 -- appid                 ,
  mcht_no               ,
  special_mcht_no       ,
  device_info           ,
  transaction_id        ,
  out_trade_no          ,
  open_id               ,
  trade_type            ,
  trade_state           ,
  bank_type             ,
  fee_type              ,
  settlement_total_fee  ,
  coupon_fee            ,
  refund_id             ,
  out_refund_no         ,
  settlemeht_refund_fee ,
  coupon_refund_fee     ,
  settlement_refund_type,
  refund_status         ,
  procedure_fee         ,
  procedure_fee_rate    ,
  total_fee             ,
  refund_fee            ,
  fee_rate_remark       ,
  create_time           ,
  update_time           ,
  task_date             ,
  task_time             ,
  line_idx              ,
  file_name             ,
  task_id               ,
  insert_time           ,
  goods_name            ,
  mcht_data             ,
  union_add_data        ,
  channel
  from CHK_WECHAT_CHANNEL_RECORD
;

CREATE VIEW VCHK_WITHDRAWL_CHANNEL_RECORD AS 
SELECT  id             ,
  line_idx      ,
  create_time   ,
  acct_no       ,
  acct_name     ,
  cret_debt_flag,
  currency_code ,
  txn_amt       ,
  txn_amt_num   ,
  acct_bal      ,
  acct_bal_num  ,
  acct_date     ,
  txn_type      ,
  txn_no        ,
  txn_date      ,
  --oth_acct_no   ,
  --oth_acct_name ,
 -- oth_bank_no   ,
  ins_seq       ,
  mer_id        ,
  mer_name      ,
  remark        ,
  rtn_reason    ,
  req_reserved  ,
  reserved      ,
  rot_type      ,
  tp_date       ,
  vef_date      ,
  swt_key       ,
  task_date     ,
  task_time     ,
  file_name
  FROM CHK_WITHDRAWL_CHANNEL_RECORD
;

CREATE VIEW V_CHK_PINGAN_RECORD AS 
select id,
       tran_time,
       mcht_name,
       mcht_no,
       term_no,
       batch_no,
       batch_time,
       auth_code,
       tran_amt,
       amt,
       tran_type,
       clear_type,
       t_date,
       t_time,
       is_dcc,
       tran_procedure,
       procedure,
       tran_pure_amount,
       pure_amount,
       task_date,
       task_time,
       line_idx,
       file_name,
       task_id,
       insert_time
  from CHK_PINGAN_RECORD;

CREATE VIEW V_CHK_UNIONPAY_CHANNEL_RECORD AS 
select id,
       line_idx,
       create_time,
       tran_date,
       inst_code,
       fwd_inst_code,
       seq_num,
       tran_time,
       tran_amt,
       amt,
       ref_no,
       pan,
       ori_seq_num,
       pos_entry_mode,
       fee_to_get,
       fee_get,
       fee_to_pay,
       fee_pay,
       fee_switch,
       fee_switch_num,
       reversal_flag,
       cancel_flag,
       settle_date,
       currency,
       inst_procedure_fee,
       inst_procedure_fee_num,
       mcht_procedure_fee,
       mcht_procedure_fee_num,
       order_no,
       pay_method,
       err_tran_flag,
       err_reason,
       file_mark,
       err_or_normal,
       task_date,
       task_time,
       file_name,
       task_id,
       insert_time,
     CARD_TYPE,
     MSG_TYPE,
     PROCESSING_CODE,
     MCHT_NO,
     TERM_NO,
     BRAND_FEE,
     BRAND_FEE_NUM,
     TOTAL_PROCEDURE_FEE,
     task_inst_code,
     TYPE_FLAG,
     CHANNEL_TRANSACTION_NO,
     FEE_TO_GET_ERROR,
     FEE_GET_ERROR,
     FEE_TO_PAY_ERROR,
     FEE_PAY_ERROR
from CHK_UNIONPAY_CHANNEL_RECORD;

CREATE VIEW V_CHK_UN_NOCARD_CHANNEL_RECORD AS 
select id,
       line_idx,
       create_time,
       tran_type,
       seq_num,
       currency,
       tran_amount,
       tran_amount_num,
       business_type,
       settle_date,
       orig_seq_num,
       orig_tran_amount,
       orig_tran_amount_num,
       order_no,
       inst_code,
       payer_acount_inst,
       channel_inst,
       payee_account_inst,
       payee_account_type,
       payee_account_no,
       mcht_no,
       mcht_cat,
       net_service_fee,
       net_service_fee_num,
       brand_fee,
       brand_fee_num,
       tran_state,
       tran_time,
       err_flag,
       err_reason,
       err_fee,
       err_fee_num,
       err_or_normal,
       task_date,
       task_time,
       file_name,
       task_id,
       insert_time
  from CHK_UN_NOCARD_CHANNEL_RECORD;

CREATE VIEW V_CHK_WECHAT_CHANNEL_RECORD AS 
select id,
       transaction_time,
       appid,
       mcht_no,
       special_mcht_no,
       device_info,
       transaction_id,
       out_trade_no,
       trade_type,
       trade_state,
       bank_type,
       fee_type,
       settlement_total_fee,
       coupon_fee,
       refund_id,
       out_refund_no,
       settlemeht_refund_fee,
       coupon_refund_fee,
       settlement_refund_type,
       refund_status,
       procedure_fee,
       procedure_fee_rate,
       total_fee,
       refund_fee,
       fee_rate_remark,
       create_time,
       update_time,
       task_date,
       task_time,
       line_idx,
       file_name,
       task_id,
       insert_time
  from CHK_WECHAT_CHANNEL_RECORD;

CREATE VIEW V_CHK_WITHDRAWL_CHANNEL_RECORD AS 
select id,
       line_idx,
       create_time,
       acct_no,
       acct_name,
       cret_debt_flag,
       currency_code,
       txn_amt,
       txn_amt_num,
       acct_bal,
       acct_bal_num,
       acct_date,
       txn_type,
       txn_no,
       txn_date,
       oth_bank_no,
       ins_seq,
       mer_id,
       mer_name,
       remark,
       rtn_reason,
       req_reserved,
       reserved,
       rot_type,
       tp_date,
       vef_date,
       swt_key,
       task_date,
       task_time,
       file_name,
       task_id,
       insert_time,
       card_no_enc,
       card_no_hash
  from CHK_WITHDRAWL_CHANNEL_RECORD;

CREATE VIEW V_CHK_XX_TRAN_ERROR_RECORD AS 
select id,
       inst_code,
       fwd_inst_code,
       seq_num,
       tran_time,
       tran_amt,
       msg_type,
       process_code,
       term_no,
       mcht_no,
       ref_no,
       authid_resp,
       receive_inst_code,
       ori_seq_num,
       resp_code,
       entry_mode,
       accp_procedure_fee,
       fwd_settle_fee,
       ori_tran_time,
       issue_inst,
       customer_code,
       customer_name,
       business_inst_id,
       business_code,
       amt,
       procedure_fee,
       card_type,
       fee_rate,
       fee_by_rate,
       fee_max,
       transaction_no,
       tran_type,
       ready_for_income,
       create_time,
       update_time,
       chk_task_date,
       chk_flag,
       line_idx,
       end_time,
       card_no_enc,
       sett_amt,
       institution_name,
       channel_category,
       card_area,
       price_mode,
       fee_per,
       fee_min,
       rate_mode,
       low_procedure,
       business_man,
       business_man_id,
       company_name,
       company_id,
       d1_holiday_charge_rate,
       d1_holiday_charge_per,
       d1_hoiday_charged,
       error_code,
       error_desc,
       EPSP_ORDER_NO,
       PROCESSED,
       REDO_TASK_DATE
  from CHK_XX_TRAN_ERROR_RECORD;

CREATE VIEW V_CHK_XX_TRAN_RECORD AS 
select
 id                ,
  inst_code          ,
  fwd_inst_code     ,
  seq_num            ,
  tran_time          ,
  pan,
  tran_amt         ,
  msg_type          ,
  process_code      ,
  term_no            ,
  mcht_no            ,
  ref_no             ,
  authid_resp       ,
  receive_inst_code  ,
  ori_seq_num        ,
  resp_code         ,
  entry_mode         ,
  accp_procedure_fee ,
  fwd_settle_fee    ,
  ori_tran_time     ,
  issue_inst        ,
  customer_code     ,
  customer_name     ,
  business_inst_id   ,
  business_code      ,
  amt                ,
  procedure_fee      ,
  card_type         ,
  fee_rate           ,
  fee_by_rate        ,
  fee_max            ,
  transaction_no     ,
  tran_type          ,
  ready_for_income   ,
  create_time        ,
  update_time       ,
  chk_task_date      ,
  chk_flag          ,
  line_idx           ,
  end_time          ,
  card_no_enc        ,
  sett_amt           ,
  institution_name   ,
  channel_category   ,
  card_area,
  t.low_procedure,
  t.business_man,
  t.business_man_id,
  t.company_name,
  t.company_id,
  t.d1_holiday_charge_rate,
  t.d1_holiday_charge_per,
  t.d1_hoiday_charged,
  t.epsp_order_no
  from CHK_XX_TRAN_RECORD t;
