﻿--==========元数据==========
select * from meta_tables t where t.table_name like '%MCC%' order by t.create_time desc;
select * from meta_tables t where t.table_title like '%白%';
select * from meta_columns c where c.field_title is null order by c.create_time desc;
select * from meta_columns t where t.field_title like '%行业类别%';
select * from meta_columns t where t.field_name like '%KJ_CHANNEL%';
SELECT mc.table_name,mc.field_name,mc.field_title,mc.remarks,mc.comments,mc.data_type,mc.data_length,mc.data_precision,mc.sort_order FROM META_COLUMNS mc WHERE mc.TABLE_NAME = 'RISK_CONTROL_RESULT' ORDER BY mc.TABLE_NAME, SORT_ORDER for update;

--cash
select * from CASH_CASHIER_CHECK_DOMAIN;
--cust--
select * from CUST_CONTACT_DRAFT;
select * from cust_customer c where customer_no='***************';
select * from cust_bank_card;
select * from CUST_BUSINESS_TYPE_SETTING;

select t.inner_business_type,t.* from CUST_CUSTOMER_DRAFT t;
select * from cust_dict_business_type
select * from CUST_CHANGE_REMIND;

select * from cust_user where customer_id=3059532;
update cust_user u set u.password_expired_time=sysdate + 10000 where u.customer_id=3059532;

select * from cust_static_param t where t.param_name='PWD_CHANGE_DATE';

select * from meta_columns t where t.field_title like '%签约%';

select * from pas_time_task
--==========CUST==========
-- 259新银联商户上游取号表
select count(0) from cust_union_inlet_record t where t.create_time > sysdate - 1/24 * 5;
-- 微信、支付宝上游取号表
select count(0) from cust_inlet_record t where t.create_time > sysdate - 1/24 * 5;
-- 网联取号表
select *  from CUST_NU_REPORT;
-- 网联取号- 网联取号详情表
select *  from CUST_NU_REPORT_DETAIL;

select c.customer_no,c.name,
  decode(c.STATUS, 1, '正常', 2, '冻结', 3, '注销', 4, '止付', 5, '禁止入金') AS 账户状态,decode(c.rc_status, '0','正常','1','冻结') as 风控状态,
  case s.stop_cash_in when '1' then '已限制' else '未限制' end as 商管限制入金状态,
  t.id as 进件ID, t.channel_inst_code as 渠道代码, t.channel_id as 渠道ID,t.channel_mcht_no as 渠道商户号,t.inlet_type as 取号类型,
  decode(t.inlet_style,'0','手动输入','1','接口获取') as 取号方式,decode(t.inlet_state,'00','成功','01','失败','02','处理中','03','初始化') as 取号状态
from cust_inlet_record t 
  inner join cust_customer c on t.customer_code=c.customer_no 
  left join cust_customer_setting_record s on c.customer_no=s.customer_no
where t.inlet_type='wechat' and t.inlet_style='0';

select * from CUST_AUTH_CHECK order by check_time desc;

--商户微信配置
select a.customer_code,a.channel_inst_code,a.channel_mcht_no,b.sub_appid,b.sub_channel_merchant_no,b.create_time 
from cust_inlet_record a inner join cust_wechat_config_appid b on a.customer_code=b.acq_mer_id 
where b.sub_appid='wx4a0c7a018cc1072a' and a.channel_mcht_no='*********';

--商户可用余额和业务员
SELECT c.customer_no as 商户号, c.name as 商户名称, a.availablebalance/ 100 as 可用余额, u.real_name as 业务员
FROM ACC_ACCOUNT a INNER JOIN CUST_CUSTOMER c ON a.CUSTOMERCODE =c.CUSTOMER_NO
INNER JOIN PAS_USER U ON C.BUSINESS_MAN_ID=U.USER_ID
WHERE c.status=1 AND c.NAME NOT LIKE '%测试%' AND CODE IN (SELECT 'JY-A'||CUSTOMER_CODE FROM CUST_AUTH_INFO cai WHERE AUTH_TYPE in('TFP','WFP','ACCFZ','ACSFEE')) AND AVAILABLEBALANCE < 1000*100;

select t.customer_code,t.customername,t.business_man,t.transaction_no,r.institution_name,t.error_code,r.channel_resp_code,r.channel_resp_msg 
from txs_withdraw_trade_order t 
  left join clr_withdraw_record r on t.transaction_no=r.transaction_no 
where t.create_time > sysdate -1 and t.create_time < sysdate - 1/48 and t.pay_state ='03' order by t.create_time desc;

select msg_info,t.statue from sms_send_record t where time > sysdate -1 and mobile_no like '138%2277';

--客户授权--
select t.customer_code,t.auth_customer_code,t.auth_type,t.status,to_char(t.create_time,'yyyy-mm-dd hh24:mi:ss') as create_time, t.remarks from cust_auth_info t where t.auth_type='BFZ' order by t.create_time desc;
select * from cust_auth_white_list t where t.user_id <> 0 order by t.audit_time desc;
--========== CUM ==========
select * from cum_pay_method order by to_number(pay_method);
select * from pas_pay_method where pay_method_name like '%银联二维码%' order by pay_method_name;
select * from cum_pos_tran_type_name;

--========== TXS ==========
--交易位置
select t.customer_code,c.terminal_no,'+'||n.longitude||'/+'||n.latitude as term_location, 
  parsejson(c.terminal_info,'location') as trans_location,
  round(geo_distance(
    nvl(to_number(substr(parsejson(c.terminal_info,'location'),1,instr(parsejson(c.terminal_info,'location'),'/')-1)),0),
    nvl(to_number(substr(parsejson(c.terminal_info,'location'),instr(parsejson(c.terminal_info,'location'),'/')+1)),0),
    nvl(n.latitude,0), nvl(n.longitude,0)
  )) as distance
from txs_pay_trade_order t 
  inner join clr_pay_record c on t.transaction_no=c.transaction_no 
  left join cum_terminal_info n on t.terminal_no=n.terminal_code 
where t.create_time > sysdate - 10 and c.terminal_info like '%"location"%';

select * from TXS_PRE_ORDER_SUM;
select t.card_no_sm3,t.card_no_cipher,t.card_no,t.create_time from txs_withdraw_trade_order t where t.card_no_sm3 is not null order by t.create_time;
select t.terminal_code,t.terminal_name,t.status, t.longitude,t.latitude from cum_terminal_info t where t.terminal_code='70036666';

select * from clr_tx_term_info t where t.create_time > sysdate - 1;

select * from txs_withdraw_trade_order t where t.create_time > sysdate - 10;

select * from cum_pay_method where pay_method_name like '%快捷%';
select t.error_code,t.error_msg,t.customer_code,t.transaction_no,t.out_trade_no from txs_pay_trade_order t where t.error_code like '09106%' and t.create_time > sysdate-2;
select t.error_code,t.customer_code,t.transaction_no,t.out_trade_no from txs_withdraw_trade_order t where t.error_code like '09106%' and t.create_time > sysdate-2;

-- 鉴权订单
select * from txs_consistcheck_trade t where t.create_time > sysdate - 30;
-- ========== SETT ==========
select * from sett_settment_fr_record t where t.create_time > sysdate - 10;
select * from sett_settment_fr_voucher t order by t.create_time desc;
select * from sett_settment_fr_acc_flow t order by t.create_time desc;
select * from sett_fr_record;
select * from sett_fr_voucher;

select *
from sett_settment_fr_record r 
  inner join sett_settment_fr_acc_flow f on f.settment_fr_record_id=r.id
  inner join acc_accountflow a on f.transaction_no=a.transactionno ;


--where a.accountdatetime > sysdate - 100
order by a.accountdatetime desc;

--快捷支付，一个人在多个商户累计支付超过5000元
with b as(
select t.cert_no_hash, count(distinct t.customer_code) as c, sum(t.amount) as total_amount from txs_pay_trade_order t 
where t.pay_method in('8','26','29','28','36','37') and t.state='00' and t.create_time>= trunc(sysdate -1) and t.create_time <trunc(sysdate)
group by t.cert_no_hash having count(distinct t.customer_code) > 1 and sum(t.amount)>500000)
select a.cert_no_hash,a.* from txs_pay_trade_order a inner join b on a.cert_no_hash=b.cert_no_hash;

--厦分交易
select t.* from txs_pay_trade_order t where t.route_type='A';

--支付服务费
select * from txs_pay_service_order;
select b.transaction_no,a.service_fee,b.pay_service_fee,b.pay_service_charged from txs_pay_service_order a inner join txs_pay_trade_order b on a.orig_transaction_no=b.transaction_no where b.state='00' and a.service_fee<>b.pay_service_charged;
select b.transaction_no,a.service_fee,b.pay_service_fee,b.pay_service_charged from txs_pay_service_order a inner join txs_withdraw_trade_order b on a.orig_transaction_no=b.transaction_no where b.pay_state='00' and a.service_fee<>b.pay_service_charged;

--==========acc==========
select t.code,t.availablebalance,t.floatbalance,t.frozenbalance,to_char(t.createdatetime,'yyyy-mm-dd hh24:mi:ss') as createtime,to_char(t.updatedatetime,'yyyy-mm-dd hh24:mi:ss') as updatetime from acc_account t where t.customercode='***************';
select * from acc_accountflow t where t.createdatetime > sysdate -1;

select * from acc_account a where exists (select 1 from        
       ( 
select CUSTOMERCODE, ACCOUNTTYPEID, count(*) cnt from acc_account 
        group by         CUSTOMERCODE, ACCOUNTTYPEID having count(*) >1)g   where a.CUSTOMERCODE = g.CUSTOMERCODE and a.ACCOUNTTYPEID = g.ACCOUNTTYPEID
        );

--==========posp==========
select * from posp_location;
select * from posp_term_location;
select * from posp_txn t ;

select distinct t.pay_method from chk_business_jingying_report t where t.business='线下交易' order by t.pay_method;
--==========acc==========
select * from acc_account a where a.code like 'BJ-B%' and a.availablebalance > 0;
select * from acc_accountflow f where f.accountcode like 'JY-A%' and f.balancetype=3 and f.createdatetime > sysdate - 500;
select * from ACC_BZJ_BALANCE;
select * from ACC_FR_SUM_MON_UPD;
--==========terminal==========
select * from cust_terminal;
select terminal_code,count(0) from CUST_TERMINAL_REPORT t group by terminal_code having count(0) > 1;
select * from cust_terminal_report t where t.terminal_type in('11','21'); --记录数据与实际上报不一致，如：部分终端类型终端序列号并不需要报备，但是序列号在数据库中写入了；

select * from report_sett_record t where t.clear_date > sysdate - 2;

select * from SETT_CLEAR_SETTLEMENT_DETAIL ;

select * from chk_company_name_map;
select * from POSP_TERM_LOCATION;
--==========RC==========
select to_char(t.trigger_time,'yyyy-mm-dd') as day,TRIGGER_ACTION,count(0) from RC_RISK_EVENT_RECORD t where t.trigger_time > sysdate - 30 group by to_char(t.trigger_time,'yyyy-mm-dd'),TRIGGER_ACTION order by to_char(t.trigger_time,'yyyy-mm-dd') desc,TRIGGER_ACTION ;
select distinct TRIGGER_ACTION from rc_risk_event_record t;
select * from rc_archive;
select * from rc_limit t where t.business_tager_id='MIDDLE_LEVEL';--风控指标定义
select g.alias,g.remark,t.code,t.remark,t.*,g.* from rc_define t inner join rc_define_group g on t.group_id=g.group_id order by g.name,t.code;
select * from rc_limit_log l; --风控拦截记录
select * from rc_aud_limit; --变更待审
select * from rc_audit_record; --审批记录日志
--==========config==========
select * from common_sys_param;
select * from pas_holiday;
select * from cust_static_param;
select * from cust_mcc_business_type;
select * from pas_business where name like '银联二维码主扫%';
--report
select * from REPORT_COST_BILL;
--==========pas：权限==========

select u.mobile,u.en_mobile,u.user_type from pas_user u where u.real_name='骆芷莹';

--用户、部门、角色
select u.name as 登录名,u.real_name as 实名,u.remark as 说明, decode(u.user_type, 0,'集团用户',1,'分公司管理员',2,'业务员') as 用户类型 ,c.name as "部门/分公司",
  r.name as 角色名, decode(r.data_auth,'0','全量','1','自定义','2','本部门','3','本人') as 数据权限
from pas_user u 
  left join pas_company c on u.company_id=c.company_id
  inner join pas_role_user ru on u.user_id=ru.user_id
  inner join pas_role r on ru.role_id=r.role_id
where u.status='Y' and u.name like 'luo%'
order by u.real_name, r.name;

--角色变更日志
select t.user_name,t.real_name,t.op_time,t.op_content from OPERATION_LOG t 
where t.op_module = '系统管理-角色管理' and t.op_method = '修改角色' and t.op_content like '修改角色：营销岗%' order by t.create_time desc;

select * from uaa_service t where t.uri='/cust/attachment/download';
select distinct r.* ,p.name,p.alias
from pas_role r 
  inner join pas_role_perm rp on r.role_id=rp.role_id 
  inner join pas_perm p on rp.perm_id=p.perm_id
where p.name in('sysParam');
--权限对应的角色
select p.*,sys_connect_by_path(p.alias,'\') as path,(select listagg(name,',') within group (order by name) from pas_role r inner join pas_role_perm rp on r.role_id=rp.role_id where rp.perm_id=p.perm_id) as "ROLES" from pas_perm p start with p.parent_id=0 connect by prior p.perm_id=p.parent_id;
select p.*,sys_connect_by_path(p.alias,'\') as path from pas_perm p start with p.parent_id=0 connect by prior p.perm_id=p.parent_id;

--查询角色及对应的所有权限数据
select r.name,p.perm_id,p.path,p.alias
from pas_role r inner join pas_role_perm rp on r.role_id=rp.role_id
  inner join (select p.*,sys_connect_by_path(p.alias,'\') as path from pas_perm p start with p.parent_id=0 connect by prior p.perm_id=p.parent_id) p on rp.perm_id=p.perm_id
order by r.name;


--运营用户权限查询
select u.name as user_name,u.real_name as real_name,r.name as role_name,p.name as perm_name,p.perm_path,p.url 
from pas_user u 
  inner join pas_role_user ru on u.user_id=ru.user_id
  inner join pas_role r on ru.role_id=r.role_id
  inner join pas_role_perm rp on r.role_id=rp.role_id
  inner join (select p.*,sys_connect_by_path(p.alias,'\') as perm_path from pas_perm p start with p.parent_id=0 connect by prior p.perm_id=p.parent_id) p on rp.perm_id=p.perm_id
  left join uaa_service s on p.url=s.uri
where s.uri='/cust/attachment/download' and u.real_name='元帅';
--==========cas==========
select * from cas_channel order by cid;
select * from cas_channel_name;
select * from cas_apiname;

select * from pas_company

select c.cid as api_id, c.apiname as api, a.remarks as api_name, c.channelno as channel_no, n.name as channel_name,c.costs, c.success_days, c.fail_days
from cas_channel c 
  inner join cas_channel_name n on c.channelno=n.channel_no
  inner join cas_apiname a on c.apiname=a.apiname
where c.state='1'
order by c.cid;
--==========chk==========
select t.business_code,t.card_area,t.card_type,t.card_no_enc,t.error_code,t.error_desc,t.* from chk_xx_tran_error_record t where t.processed<>'1' order by t.create_time desc;

select * from pas_time_task t where t.job_status='0' and t.remark like '%网联%';
select t.job_name,t.status,t.return_code,t.return_message,t.caller_ip_address,t.partition from pas_time_task_record t where t.job_name='epccGetRecFilesNew' and t.create_time > sysdate - 5 order by t.create_time desc;
--==========txs-query==========
select u.name as 用户,u.real_name as 用户实名,u.remark as 用户说明,
  f.file_source as 导出菜单,f.file_name 导出文件名,to_char(f.create_time,'yyyy-mm-dd hh24:mi:ss') as 导出时间,
  round(r.file_size*10.1/1024) as "文件大小KB",round((r.file_size*11-500)/400) as "文件行数",f.remark as 文件说明 
from EXPORT_FILE_MANAGE f 
  inner join pas_user u on f.operator_id=u.user_id 
  inner join fs_upload_record r on f.file_path=r.unique_id 
where f.create_time > timestamp '2023-09-01 00:00:00' and f.file_source like '统一进件 - %' 
order by r.file_size desc;

select * from v_chk_buz_jingying_report;
--==========JOB==========
select t.job_name, t.remark, r.caller_ip_address as caller_ip, r.ip_address as worker_ip,r.partition,
to_char(r.create_time,'yyyy-mm-dd hh24:mi:ss') as start_time, to_char(r.update_time,'yyyy-mm-dd hh24:mi:ss') as end_time,
r.return_code, r.return_message
from pas_time_task t 
    inner join pas_time_task_record r on t.last_execute_record_id=r.id
where t.job_status='0' order by r.create_time desc;

--==========AF risk_control==========
select tx_code, count(0) from V_RISK_CONTROL_MESSAGE group by tx_code order by tx_code;
select * from risk_control_message;
select * from risk_control_result;
select * from risk_control_attachment;
select * from risk_a00102;

select rcm."ID",rcm."TX_CODE",rcm."MESSAGE_FROM",rcm."TRANS_SERIAL_NUMBER",rcm."FILE_PATH",rcm."APPLICATION_ID",rcm."CASE_NUMBER",
  rcm."CASE_TYPE",rcm."TRANSFER_OUT_BANK_ID",rcm."TRANSFER_OUT_BANK_NAME",rcm."TRANSFER_OUT_ACCOUNT_NAME",rcm."TRANSFER_OUT_ACCOUNT_NUMBER",
  rcm."CURRENCY",rcm."TRANSFER_AMOUNT",rcm."TRANSFERTIME",rcm."ONLINE_PAY_COMPANY_ID",rcm."ONLINE_PAY_COMPANY_NAME",rcm."SUBJECT_TYPE",
  rcm."DATA_TYPE",rcm."DATA",rcm."REASON",rcm."REMARK",rcm."START_TIME",rcm."EXPIRE_TIME",rcm."APPLICATION_TIME",rcm."APPLICATION_ORG_ID",
  rcm."APPLICATION_ORG_NAME",rcm."OPERATOR_ID_TYPE",rcm."OPERATOR_ID_NUMBER",rcm."OPERATOR_NAME",rcm."OPERATOR_PHONE_NUMBER",rcm."INVESTIGATOR_ID_TYPE",
  rcm."INVESTIGATOR_ID_NUMBER",rcm."INVESTIGATOR_NAME",rcm."APPLICATION_TYPE",rcm."ORIGINAL_APPLICATION_ID",rcm."TRANSACTION_DATE",rcm."BANK_ID",
  rcm."CASEDOCUMENT_ID",rcm."ID_TYPE",rcm."ID_NUMBER",rcm."FREEZE_TYPE",rcm."BALANCE",rcm."INQUIRY_MODE",rcm."ACCOUNT_NUMBER",rcm."ACCOUNT_OWNER_NAME",
  rcm."ACCOUNT_OWNER_ID_TYPE",rcm."INSERT_TIME",rcm."CUSTOMER_CODE",rcm."CUSTOMER_NAME",rcm."TRANSACTION_NO",rcm."AMOUNT",rcm."TX_CODE_N",rcm."DATA_TYPE_N",
  rcm."MSG_ID",rcm."IMPORT_ID",rm.trx_id,rcr.state,rcr.result_state,rcr.id result_id,rcr.auto_state,rcr.auto_tag
from risk_control_message rcm
  inner join risk_control_result rcr on rcm.tx_code=rcr.tx_code and rcm.message_from=rcr.message_from and rcm.trans_serial_number=rcr.trans_serial_number
  left join risk_message rm on rm.id=rcm.msg_id;

--========== SETT ==========
select * from SETT_BILL_DETAIL order by create_time desc;
select * from txs_pay_service_order;

--工具
select geo_distance(39.9042, 116.4074,   39.9082,116.4107) from dual; --两点地理距离，单位：米，参数：纬度1、经度1、纬度2、经度2

select t.* ,t.rowid   from    sms_send_record  t     order by t.id desc
select * from cust_discern_manage;
select * from cust_discern_record;
select * from cust_discount_full_decrement;

select t.customercode from acc_account t where t.updatedatetime > sysdate - 30;
------------------------------------------------------------------
select * from TFS_10267_AREA_INFO;
select * from TFS_10267_CHANNEL_NAME1;
select customer_code,card_no_sm3,business_code,count(0) from RC_OUT_CARD_NO group by customer_code,business_code,card_no_sm3 having count(0) > 1;
select * from pas_perm where parent_id=0 order by perm_sort;

select * from operation_log t where t.op_content like '%62%';
select * from rc_archive;

select * from cum_institution_cert;
select * from cum_public_certs;
POSP_TXN_TO_THREE
select * from rc_limit_log;
select * from rc_txs_order;
select * from operation_log;
select * from pas_perm;
select * from pas_login_log where username like '56%';

select * from meta_tables where table_title like '%审核%' and table_name like 'CUST%';
select customer_code,count(0) from CUST_ESIGN_RECORD where create_time > timestamp '2023-12-01 00:00:00' group by customer_code having count(0) > 1;
select * from cust_esign_record t where t.customer_code='562194003190090';

select * from txs_pre_order;

select t.client_ip,t.transaction_no from txs_pay_trade_order t where t.create_time > sysdate - 1;
select t.longitude,t.latitude,t.* from posp_txn t where t.create_time > sysdate - 30 and txn_type='CONSUME';
select * from cust_contact c where c.customer_id in(3171515) ;
select * from cust_customer c where c.create_time > sysdate -10;

select  /* +INDEX_COMBINE(t IDX_TXS_PRE_ORDER_1,IDX_TXS_PRE_CODE_OUT)*/ * 
FROM TXS_PRE_ORDER t
  INNER JOIN CUST_TERMINAL ct ON ct.TERMINAL_CODE = t.TERMINAL_NO
where t.customer_code='562566004498147' and t.create_time >= timestamp '2025-03-30 00:00:00' and t.create_time <= timestamp '2025-04-29 23:59:59';

select   * 
FROM TXS_PRE_ORDER t
  INNER JOIN CUST_TERMINAL ct ON ct.TERMINAL_CODE = t.TERMINAL_NO
where t.customer_code='***************' and t.create_time >= timestamp '2025-03-30 00:00:00' and t.create_time <= timestamp '2025-04-29 23:59:59';

select  /* +INDEX(t IDX_TXS_PRE_ORDER_1) INDEX(t IDX_TXS_PRE_CODE_OUT)*/ * 
FROM TXS_PRE_ORDER t
  --INNER JOIN CUST_TERMINAL ct ON ct.TERMINAL_CODE = t.TERMINAL_NO
where t.create_time >= timestamp '2025-03-30 00:00:00' and t.create_time <= timestamp '2025-04-29 23:59:59' and t.customer_code='***************';

--近30日海油POS交易统计
select decode(FROM_TABLE_NAME,'posp_txn','新posp','chk_xx_tran_record','线下直连','posp_order','旧posp') as 类型, 
  to_char(t.create_time,'yyyy-mm-dd') as 日期, count(*) 笔数, sum(t.AMOUNT)/10 金额
from TXS_POSP_THREE t 
where t.create_time > trunc(sysdate - 30) 
  and EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=t.CUSTOMER_CODE and instr(T1.customer_path, '****************') >0)
group by t.FROM_TABLE_NAME,to_char(t.create_time,'yyyy-mm-dd') order by to_char(t.create_time,'yyyy-mm-dd'),t.from_table_name;

select distinct t.customer_code,t.customername from txs_pay_trade_order t where t.create_time > sysdate - 1/24 and t.customername like '%加油%';
select * from rc_risk_event_rule where rule_code='R004';

select * from pas_time_task t where t.ip_addres='rc';
select *from cust_bank_card where create_time > sysdate - 10;

select * from REPORT_COST_BILL where create_time > sysdate -1;
select * from meta_tables where table_name like 'YYZ_VA%';

select p.transaction_no,p.state,p.* from txs_split_record t inner join txs_split_order o on t.transaction_no=o.transaction_no inner join txs_pay_trade_order p on o.customer_code=p.customer_code and o.out_trade_no=p.out_trade_no where t.create_time > sysdate -5 and t.paytransaction_no is null;

select * from txs_split_record t where t.create_time > timestamp '2024-03-01 00:00:00' and t.state='3' and t.paytransaction_no is null;

select * from uaa_certificate t where t.signsn='5651200003067506001' for update;

select * from txs_pay_trade_order t where t.customer_code='***************' and t.out_trade_no='***************';
select * from txs_split_order t where t.transaction_no='66202403071026173399271';
select * from acc_accountflow t where t.transactionno='66202403071026173399271';

select count(0) from rc_define;

select * from rc_calculate_log t where t.business_target_name like '%距离%';
select * from meta_tables where table_name like 'RC%';

select * from uaa_ip_tables;
select * from acc_account;
select * from pas_department for update;

insert into uaa_ip_tables(id, ips, customer_code,create_date, update_date)
values(100,'*************,**************,*************','***************',sysdate, sysdate);

select customer_code,count(0) from cum_customer_settle_info group by customer_code having count(0) > 1;
select * from cum_customer_settle_info where customer_code='***************';
select * from chk_business_jingying_report;
select * from cust_business t where t.remark is not null;

select * from acs_hvps_txs_record;
select t.certificate_to,t.* from cust_customer_enterprise t where t.certificate_to is not null order by t.certificate_to desc;
select * from NMD_MERCHANT@CROSSBORDER order by create_time desc;

select * from fs_upload_record t where t.create_time > sysdate - 10  order by t.file_size desc;

select * from acs_customer_account t order by t.d_create_time desc;

select * from uaa_service where uri like '%fx%';
select * from tax_out_trade_no;
select * from tax_batch order by create_time desc;
select * from tax_detail order by create_time desc;
select * from bk_card_bin t where t.issue_bank_name like '%邮储%';


select * from txs_sql_template;
select * from meta_tables where table_name like 'UAA%';
select * from TXS_DEVOPS_TASK t where t.status in('2','3') and t.receiver_name like '%liulifang%' or task_id=3005833;


select * from FIN_ACC_DATA;
select * from V_FIN_ACC_MAPPING;
select * from chk_business_jingying_report t where t.task_date >= '********';

select t.customer_code,t.out_trade_no,t.transaction_no,t.amount,t.procedure_fee from txs_pay_trade_order t where t.create_time > sysdate - 10 and t.transaction_type='FZ' and t.state='00';

select * from pas_time_task t where t.ip_addres='fs';--DiskFileTransferTaskJob
select *from fs_upload_record t ;

select * from CUST_SUBMIT_BUSINESS;
select * from cust_business_price;

select * from uaa_serviceroleauth;
select * from uaa_certificate;

select * from meta_tables t where t.table_title like '%联系人%';
select * from meta_columns t where t.field_title like '%卡%' and table_name not like 'CARD%' and field_title not like '%片%' and lower(field_title) not like '%bin%' and lower(field_title) not like '%md%' and field_title not like '%类%' and field_title not like '%名%' and field_title not like '%支持%';
select * from CUST_ESIGN_RECORD;
select * from chk_business_jingying_report;
select *from cust_user where create_time > sysdate - 100;

select * from CUST_RECOMMEND_MAN;
select * from T_BUSINESS_JINGYING;
drop table CHK_LOCAL_RECORD0527LWQ;

select * from FIN_ACC_STATISTICS;
select * from FIN_ACC_DATA;
select *from V_FIN_ACC;

select t.business_target_id,t.business_target_name, u.real_name as business_man,
  d.remark||(case when upper(t.rc_limit_define_code) like '%AMOUNT' then '(元)'  else '' end) as rc_limit_define_code, 
  (case when upper(t.rc_limit_define_code) like '%AMOUNT' then to_char(to_number(t.rc_limit_value)/100,'999,999,999.99')  else t.rc_limit_value end) as rc_limit_value,
  t.rc_message, count(t.transaction_no) as COUNT 
from rc_calculate_log t 
  left join cust_customer c on t.business_target_id=c.customer_no and t.business_target_type='005'
  left join pas_user u on c.business_man_id=u.user_id
  left join rc_define d on t.rc_limit_define_code=d.code
where t.create_time > sysdate - 1/24/12 
 and t.rc_limit_define_code in ('Day-Max-OUT-Amount', 'Day-Max-IN-Amount') 
group by t.business_target_id, t.business_target_name, u.real_name,  t.rc_limit_define_code, d.remark, t.rc_limit_value, t.rc_message;


-- =====快捷支付投诉查询=====
-- 签约查询
select t.customercode,t.bankcardno as enc_cardno,t.phonenum as enc_phone,t.protocol,t.institution_id,to_char(t.createtime,'yyyy-mm-dd hh24:mi:ss') as createtime from cum_quickpaycustomerinfo t 
where t.state='Valid' and t.bankcardno='6230583000009858244' and t.customercode in('***************,***************') 
  or t.bankcardno='6222083602016288059' and t.customercode in('***************','***************','***************','***************','***************','***************','***************')
order by t.bankcardno,t.customercode;
-- 交易短信查询
select t.mobile_no,t.encrypt_mobile,t.msg_info,t.err_msg as msg,to_char(t.time,'yyyy-mm-dd hh24:mi:ss') as time from sms_send_record t where t.encrypt_mobile='MTM3MTA3Mjg4ODM=' and t.time > timestamp '2024-07-29 00:00:00' and t.time < timestamp'2024-11-01 00:00:00' order by time;

--

select * from rc_calculate_log;
select * from rc_define;
select *from meta_tables where table_name like '%ACTIVI%';
select * from CUST_WECHAT_ACTIVITY;


select * from txs_split_order;
select * from txs_split_record;

select * from chk_guiji_task

select * from pas_time_task where remark like '%关单%';

select *from pas_company;

select c.signsn,c.customercode,c.mem,to_char(c.notafter,'yyyy-mm-dd') as expire_date,decode(c.type,0,'国际公钥', 1,'私钥', 3,'自制国密公钥', 5,'天威国密公钥', 6,'天威证书raw签名、签名机验签', 7,'天威证书attach签名，签名机验签') as 证书类型 from uaa_certificate c where c.customercode='***************';
select * from meta_columns where field_title like '%意愿%';


select * from meta_tables t where t.table_title like '%大类%';
select * from CUST_AUTH_CHECK;


select * from cum_pay_method order by id;
select t.customer_code,t.business_code,t.amount,t.transaction_no,to_char(t.create_time,'yyyy-mm-dd') as create_date,t.state from txs_pay_trade_order t where t.pay_method='89';
select * from acc_accountflow t where t.transactionno='32240220231102083542892100000161';

select * from sett_bill_xx_detail;
select * from uaa_certificate;

select *from rc_risk_event_rule;

select *from pas_business where name like '%银行卡%' or name like '%订单转账%' order by type;


select * from cust_customer_enterprise e inner join cust_customer c on c.customer_id=e.customer_id where c.category='0' order by customer_id desc;

select* from meta_columns c where c.field_title like '%省%' and table_name like 'CUST%';

select *from cust_history c where c.info like '%签约%' and c.info not like '%未签约%' order by create_time desc;

select * from txs_pay_trade_order t where t.create_time > sysdate - 100 and t.payer like '%bank_code%'


select * from meta_tables where create_time > timestamp'2024-08-15 00:00:00'

select * from CUST_AUTH_CHECK where check_time > sysdate - 20;

select * from acc_account;

select * from  fs_business_type;

select * from pas_business b where b.name like '%聚分期%';
select * from pas_business_group where code=1000022;

select * from txs_split_record

delete from uaa_service where service_id in(11870, 11871, 11872, 11873);
select * from uaa_service order by service_id desc;


insert into uaa_service (SERVICE_ID, AUTHTYPE, URI, METHOD, MODULE, CODE, NAME, SERVICE, REMARK, NEEDAUTH, SUPPORTAUTHTYPE, URI_TYPE, NEEDAUTHORIZE, IP_ADDRESS, ENABLE_IP_LIMIT, EXCEPTION_LEVEL, TOKEN_DELAY)
values ((select max(service_id)+1 from uaa_service), 1, '/pps/ocx/securityPlug', 'GET', 'pps', 'securityPlug', '登录控件OCX请求', 'pps', '登录控件OCX请求', 0, '_', 0, 0, null, null, '1', 1);

insert into uaa_service (SERVICE_ID, AUTHTYPE, URI, METHOD, MODULE, CODE, NAME, SERVICE, REMARK, NEEDAUTH, SUPPORTAUTHTYPE, URI_TYPE, NEEDAUTHORIZE, IP_ADDRESS, ENABLE_IP_LIMIT, EXCEPTION_LEVEL, TOKEN_DELAY)
values ((select max(service_id)+1 from uaa_service), 1, '/pps/ocx/srandNum', 'GET', 'pps', 'srandNum', '登录控件OCX请求', 'pps', '登录控件OCX请求', 0, '_', 0, 0, null, null, '1', 1);

select t.out_trade_no,t.* from posp_txn t where t.out_trade_no is not null;
select * from txs_pre_order t where t.out_trade_no='32202410110610843739078';
select * from txs_pay_trade_order t where t.transaction_no='32202410110610843739078';

select * from meta_columns t where t.table_name='POSP_TXN' and t.field_name='OUT_TRADE_NO';


select * from SETT_BILL_DETAIL;
select * from report_sett_record where report_date > sysdate -2;

create table hanzi_pinyin (pinyin varchar2(50), hanzi varchar2(10));
create index idx_hanzi on hanzi_pinyin(hanzi);

select hanzi,count(pinyin) from hanzi_pinyin group by hanzi order by count(pinyin) desc;

select * from hanzi_pinyin where hanzi='䶮';
alter table hanzi_pinyin add sort_order number(4) default 0;


insert into uaa_service (SERVICE_ID, AUTHTYPE, URI, METHOD, MODULE, CODE, NAME, SERVICE, REMARK, NEEDAUTH, SUPPORTAUTHTYPE, URI_TYPE, NEEDAUTHORIZE, IP_ADDRESS, ENABLE_IP_LIMIT, EXCEPTION_LEVEL, TOKEN_DELAY)
values ((select max(service_id)+1 from uaa_service), 1, '/pps/ocx/securityPlug', 'GET', 'pps', 'securityPlug', '登录控件OCX请求', 'pps', '登录控件OCX请求', 0, '_', 0, 0, null, null, '1', 1);

insert into uaa_service (SERVICE_ID, AUTHTYPE, URI, METHOD, MODULE, CODE, NAME, SERVICE, REMARK, NEEDAUTH, SUPPORTAUTHTYPE, URI_TYPE, NEEDAUTHORIZE, IP_ADDRESS, ENABLE_IP_LIMIT, EXCEPTION_LEVEL, TOKEN_DELAY)
values ((select max(service_id)+1 from uaa_service), 1, '/pps/ocx/srandNum', 'GET', 'pps', 'srandNum', '登录控件OCX请求', 'pps', '登录控件OCX请求', 0, '_', 0, 0, null, null, '1', 1);

select * from uaa_service where uri like '%ocx%';
delete from uaa_service where uri like '%ocx%';

select * from txs_consistcheck_trade t where t.transaction_no='30202307041290547911715';
select * from cas_company_account order by create_time desc;
select * from cas_channel;

select * from acc_account a where a.frozenbalance > 0;

select * from txs_withdraw_trade_order t where t.create_time > sysdate - 1 order by t.create_time desc for update;
SELECT * FROM PAS_TIME_TASK t where t.JOB_NAME = 'matterMiddleStatisticsJob';
select * from pas_time_task_record t where t.job_name='matterMiddleStatisticsJob' order by t.create_time desc;
select * from pas_business where name like '%费%';


select channelno,count(0) from cas_channel where state='1' group by channelno;
select * from meta_columns t where t.field_name like '%ICP%';
select * from meta_tables t where t.table_title like '%发票%';

alter table RC_SPECIAL_MERCHANT_INFO modify ACC_ICP_NO varchar2(32); 

select * from chk_guiji_task  t where FILE_TYPE = 'WECHAT';

select * from txs_pay_trade_order t where t.transaction_no='33202411122511267347211';
select * from pas_business where code like '%Mini%';
select * from sett_settment_voucher;

SELECT * FROM PAS_TIME_TASK t where t.JOB_NAME = 'FrJob_New';

select * from TXS_POSP_THREE t;

select * from pas_time_task t where t.remark like '%主扫%';

select * from REPORT_COST_BILL; --费用账单表
select * from REPORT_INVOICE_RECORD; --发票申请记录表
select * from REPORT_INVOICE_AUDIT_RECORD; --发票审核记录表
select * from REPORT_INVOICE_RED_INFO; --发票红冲信息表

select * from cust_terminal;

select * from cum_terminal_info;

select sum(t.trans_count) from TXS_BUSINESS_STATISTICS t where t.cycle_start_time between timestamp '2023-01-01 00:00:00' and timestamp '2024-01-01 00:00:00';


select c.apiname as API,a.remarks as "API NAME",n.name as CHANNEL,c.channelno from cas_channel c inner join cas_apiname a on c.apiname=a.apiname inner join cas_channel_name n on c.channelno=n.channel_no where state='1' order by a.remarks;

select * from cas_channel_name for update;
select * from cas_apiname order by apiname for update;

select t.create_time , t.USE_STATE, t.IS_SM, t.* from cum_institution_cert t where t.INSTITUTION_CODE = '********';

select * from cum_public_certs;

--分离存储提现卡ID和卡标识
create or replace view cus_bank_card_token as 
select card_id,sm3_encrypt(card_id || customer_no) as card_token from cust_bank_card
union all
select t.settle_id, sm3_encrypt(t.settle_id || t.customer_code) from cum_customer_settle_info t;

--分离存储交易卡ID和卡标识
create or replace view tm_withdraw_card_token as 
select t.transaction_no,sm3_encrypt(t.card_id || t.customer_code) as card_token from txs_withdraw_trade_order t 
where exists(select 1 from cum_customer_settle_info s where t.card_id=s.settle_id);

--创建提现单视图
create or replace view tm_withdraw_trade_order as
select * from txs_withdraw_trade_order;

select geo_distance(39.9042, 116.4074,   39.9082,116.4107) from dual; --单位：米，参数：纬度1、经度1、纬度2、经度2

select * from cum_business where code = 'UnionSweep';
select * from cust_privacy_agreement t where t.customer_no='***************' for update;
delete from cust_privacy_agreement t where t.customer_no='***************';

select * from cust_user where username='zs';
select * from cust_customer where customer_id=3041566;

select title as 任务, t.execution_time 执行计划,to_char(t.create_time,'yyyy-mm-dd') as 创建日期,to_char(t.update_time,'yyyy-mm-dd hh24:mi:ss') as 最近执行时间,t.receiver_name as 接收人,t.remark as 说明 from txs_devops_task t where t.long_term_task='1' and status='3';

select to_char(t.login_time,'yyyy-mm-dd') as "日期", sum(case when EXTRACT(HOUR FROM t.login_time)<= 12 then 1 else 0 end) as "上午登录人数", sum(case when EXTRACT(HOUR FROM t.login_time)> 12 then 1 else 0 end) as "下午登录人数" from pas_login_log t where t.state='1' and t.login_time > sysdate - 30 and t.usertype='2' group by to_char(t.login_time,'yyyy-mm-dd') order by to_char(t.login_time,'yyyy-mm-dd');

select * from meta_columns where field_name like '%CLEAR_ID';

select t.client_ip,t.src_channel_type,t.order_type,t.* from TXS_PAY_TRADE_ORDER t where t.create_time > sysdate - 100 order by t.create_time desc;

select * from SETT_SETTMENT_VOUCHER t where t.create_time > sysdate - 30 and t.clear_id=-3;

select t.customercode,t.signsequence,t.protocol,t.pay_channel_code,to_char(t.createtime,'yyyy-mm-dd') as createtim from cum_quickpaycustomerinfo t where t.bankcardno='' order by t.createtime desc;
select t.bankcardno,count(0) from cum_quickpaycustomerinfo t where t.pay_channel_code<>'union_allchannel' and t.createtime > sysdate - 7 group by t.bankcardno having count(0) > 1 order by count(0) desc;
select t.bankcardno,t.signsequence,count(distinct signsequence) from cum_quickpaycustomerinfo t where t.pay_channel_code<>'union_allchannel' and t.createtime > sysdate - 7 group by t.bankcardno,t.signsequence having count(0) > 1 order by count(0) desc;


select * from pas_time_task t where t.job_name='ReportSett_New';
select * from pas_time_task_record t where t.job_name='ReportSett_New' and create_time > sysdate - 2 order by create_time desc ;


select t.code, count(*)  from acc_account t group by t.code having count(*) >1;

select t.id,t.code,t.createdatetime,t.updatedatetime from acc_account t where t.code in(select t.code  from acc_account t group by t.code having count(*) >1) order by t.code;


select * from bk_bank_branch t where t.lbnk_no='************';
select * from cum_bank_info;
select * from pas_bkcd;
select * from chk_card_hash;

select * from meta_columns t where t.field_name='MER_ID' and t.table_name like '%TERMINAL%';

select mer_id,t.* from CHK_WITHDRAWL_CHANNEL_RECORD t where t.create_time > sysdate - 10;
select t.customername from cum_quickpaycustomerinfo t where t.createtime > sysdate - 50;

select CUSTOMER_CODE,   
        sum(LAST_DAY_BALANCE) LAST_DAY_BALANCE,
        sum(TOAY_OUTCOME) TOAY_OUTCOME,
        sum(TODAY_INCOME) TODAY_INCOME,
        sum(TODAY_REVICE) TODAY_REVICE,
        sum(TODAY_DAY_BALANCE) TODAY_DAY_BALANCE
        from 
        CHK_CUSTOMER_DAILY_BALANCE t 
                where t.task_date >= '********'
                and t.task_date <= '********'
         group by CUSTOMER_CODE;

select * from acc_accounttype order by code;

select * from cum_business t where name like '%微信%';
select t.institution_id, round(avg(EXTRACT(SECOND FROM (t.end_time - t.create_time))),2) as seconds, count(0) as c from txs_pay_trade_order t where t.create_time > sysdate - 1 and t.state='00' and t.business_code='WxMicro' group by t.institution_id;

select * from nts_asynnotify t where t.createdatetime > sysdate - 30;
select * from pas_time_task t where t.ip_addres in('rc','acc');
select * from rc_risk_event_rule t where t.rule_desc like '%日%';

select * from meta_tables t where t.table_name like 'CAS%';

select * from sms_send_record order by time desc;

select * from acc_account t where t.customercode='***************';

select * from rc_audit_record t order by t.id desc;


select * from acc_account;
select * from chk_customer_daily_balance t where t.task_date = '********';
select * from rc_archive;
select * from acc_cancel_record;
select * from acc_cancel_withdraw;

select * from pas_time_task t where t.ip_addres IN('cust','CUST') and job_status='0';
select * from CUST_AUTH_CHECK where epsp_trade_no='202503190319457908';
select * from meta_columns t where t.field_name='SIGN_CURRENT_STATE';

select t.sign_current_state, count(0) from cust_customer_draft t where t.create_time > timestamp '2024-01-01 00:00:00' group by t.sign_current_state;
select * from cust_customer_draft t where t.create_time > timestamp '2025-01-01 00:00:00' order by t.customer_id desc;

select t.service_id,t.uri,t.authtype,t.needauth,t.needauthorize,t.supportauthtype from uaa_service t where t.uri like '%institutionCert/queryInstitutionCert%';

select * from pas_time_task t where t.ip_addres='report';


  select CUSTOMER_CODE,CUSTOMER_NAME, TRUNC(t.TRANSACTION_TIME) as BILL_DATE, '0' AS COST_TYPE, sum(PROCEDURE_FEE) as AMOUNT,
      null as INVOICE_TIME, '2' as INVOICE_STATUS, SYSDATE as CREATE_TIME
      from SETT_BILL_WITHDRAW_DETAIL t
      where  t.TRANSACTION_TIME >= to_date('2025031700000','yyyyMMddHH24MISS') 
      and t.TRANSACTION_TIME < to_date('2025031800000','yyyyMMddHH24MISS')
      group by t.CUSTOMER_CODE, t.CUSTOMER_NAME,  TRUNC(t.TRANSACTION_TIME);
      
select CUSTOMER_CODE,CUSTOMER_NAME,CLEAR_DATE as BILL_DATE, '0' AS COST_TYPE, PROCEDURE_FEE as AMOUNT,
          null as INVOICE_TIME, '2' as INVOICE_STATUS, SYSDATE
          from REPORT_SETT_RECORD
          where CLEAR_DATE  >= to_date('2025030100000','yyyyMMddHH24MISS') 
          and CLEAR_DATE  < to_date('2025040100000','yyyyMMddHH24MISS');

select * from report_cost_bill;

select * from cum_pay_method t where t.pay_method_name like '%互联%';
select * from pas_time_task t where t.remark like '%控制%';
select * from PAS_LOGIN_LOG t where t.USERNAME='***********' and login_time > timestamp '2025-05-09 14:00:00';

select * from txs_pay_trade_order t where t.business_code is null order by t.create_time desc;
select * from cum_business t where t.code like '%SF%';

select * from txs_withdraw_trade_order;

select * from cust_static_param t where t.param_type='EPSP_REGION_CODE';

select * from sms_supplier_info;
select * from txs_withdraw_trade_order t where t.create_time > sysdate - 1 order by t.create_time desc;

select * from pas_business order by id desc;
