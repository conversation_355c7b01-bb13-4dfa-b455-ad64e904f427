import cx_Oracle
import requests
import csv

DB_CONNECT_STR = 'efps01/efps01@**********/testdb'
ENCRYPT_URL = 'http://***********:8089/SecurityNew/symmetricEncryptData?data={}'

# DB_CONNECT_STR = 'info/INfo#2018@***********:1521/epsp'
# ENCRYPT_URL = 'http://************:8089/SecurityNew/symmetricEncryptData?data={}'

def sm3(data):
    # pip install cryptography
    from cryptography.hazmat.primitives import hashes  
    from cryptography.hazmat.backends import default_backend  
    
    sm3_hash = hashes.Hash(hashes.SM3(), backend=default_backend())
    sm3_hash.update(data.encode('utf-8'))
    return sm3_hash.finalize().hex() 

def sm4(data):
    resp = requests.get(ENCRYPT_URL.format(data))
    return resp.text

def query_settle_card(cardno):
    db = cx_Oracle.connect(DB_CONNECT_STR)
    cursor = db.cursor()
    cursor.execute('SELECT customer_code FROM cum_customer_settle_info WHERE bank_acc_hash=:a', a='$a001$'+sm3(cardno))
    result = cursor.fetchall()
    for row in result:
        output(cardno, row[0])
    db.close()
    return result

def query_withdraw_card(card_no, amount = 0, trans_date = None):
    db = cx_Oracle.connect(DB_CONNECT_STR)
    cursor = db.cursor()
    sql = 'SELECT transaction_no,business_code,actual_fee/100 as amount,to_char(create_time,\'yyyy-mm-dd hh24:mi:ss\') as create_time,customer_code,customername FROM txs_withdraw_trade_order WHERE card_no_sm3=:a'
    if amount > 0:
        sql += ' AND actual_fee={}'.format(amount * 100)
    if trans_date != None:
        sql += ' AND create_time > to_date(\'{}\',\'yyyy-mm-dd\')'.format(trans_date)
        sql += ' AND create_time < to_date(\'{}\',\'yyyy-mm-dd\')+1'.format(trans_date)
    cursor.execute(sql, a=sm3(card_no))
    result = cursor.fetchall()
    for row in result:
        output(card_no, row[0], row[1],row[2],row[3],row[4],row[5])
    db.close()
    return result

def query_pay_card(card_no, amount = 0, trans_date = None):
    db = cx_Oracle.connect(DB_CONNECT_STR)
    cursor = db.cursor()
    sql = 'SELECT transaction_no,business_code,amount/100 as amount,to_char(create_time,\'yyyy-mm-dd hh24:mi:ss\') as create_time,customer_code,customername FROM txs_pay_trade_order WHERE card_no_enc=:a'
    if amount > 0:
        sql += ' AND amount={}'.format(amount * 100)
    if trans_date != None:
        sql += ' AND create_time > to_date(\'{}\',\'yyyy-mm-dd\')'.format(trans_date)
        sql += ' AND create_time < to_date(\'{}\',\'yyyy-mm-dd\')+1'.format(trans_date)
    cursor.execute(sql, a=sm4(card_no))
    result = cursor.fetchall()
    for row in result:
        output(card_no, row[0], row[1],row[2],row[3],row[4],row[5])
    db.close()
    return result

def query_pos_card(card_no, amount = 0, trans_date = None):
    db = cx_Oracle.connect(DB_CONNECT_STR)
    cursor = db.cursor()
    sql = 'SELECT ref_no,business_code,amount/100 as amount,to_char(create_time,\'yyyy-mm-dd hh24:mi:ss\') as create_time,customer_code,customer_name FROM TXS_POSP_THREE WHERE bank_card_no_enc=:a'
    if amount > 0:
        sql += ' AND amount={}'.format(amount * 100)
    if trans_date != None:
        sql += ' AND create_time > to_date(\'{}\',\'yyyy-mm-dd\')'.format(trans_date)
        sql += ' AND create_time < to_date(\'{}\',\'yyyy-mm-dd\')+1'.format(trans_date)
    cursor.execute(sql, a=sm4(card_no))
    result = cursor.fetchall()
    for row in result:
        output(card_no, row[0], row[1],row[2],row[3],row[4],row[5])
    db.close()
    return result

def read_csv(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        render = csv.reader(f)
        header = next(render)
        print(header)
        for row in render:
            # print(row)
            # query_settle_card(row[0])
            if row[2] == '收入':
                query_withdraw_card(row[0], float(row[3]), row[4][0:10])
            elif row[2] == '支出':
                query_pay_card(row[0], float(row[3]), row[4][0:10])
                query_pos_card(row[0], float(row[3]), row[4][0:10])

output_csv = None
def output(card_no, transaction_no = None, business_code = None, amount = None, create_time = None, customer_code = None, customer_name = None):
    print(card_no, transaction_no, business_code, amount, create_time, customer_code, customer_name)
    if output_csv != None:
        output_csv.writerow([card_no, transaction_no, business_code, amount, create_time, customer_code, customer_name])

def build_output_csv(filename):
    global output_csv
    output_csv = csv.writer(open(filename, 'w', newline=''))

if __name__ == '__main__':
    build_output_csv('d:/tmp/cardno0809_2.csv')
    query_settle_card('6217857000073750318')
    query_withdraw_card('6217857000073750318', amount=0.09, trans_date='2023-08-07')
    query_pay_card('9558821911000196629',amount=0.1,trans_date='2023-08-08')
    query_pos_card('6224241100000048',300,trans_date='2022-11-09')
    # read_csv('d:/tmp/cardno0809.csv')
