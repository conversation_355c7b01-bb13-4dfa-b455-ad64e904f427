#!/usr/bin/python3

import os
import requests
import time
import shutil

from timeit import default_timer as timer

user_key = os.getenv("YOUR_KEY_ENV_VAR")
# user_key = YOUR_KEY

extraction_url = "https://api.dowjones.com/alpha/extractions/documents/"

headers = {
    "Content-Type": "application/json",
    "user-key": user_key
}

# Query
request_body = {
    "query": {
        "where": "language_code = 'en' AND publication_datetime >= '2019-01-01 00:00:00'"
    }
}

start = timer()
# Creating the job.
job_response = requests.post(
    extraction_url, headers=headers, json=request_body)

if job_response.status_code != 201:
    print("An error has occurred while creating an extraction job.")
    print(job_response.text)
else:
    job_url = job_response.json()["links"]["self"]
    print("Job started. ID: {}".format(job_response.json()["data"]["id"]))

    while True:
        job_response = requests.get(job_url, headers=headers)
        if job_response.status_code != 200:
            print("An error has occurred while checking job status.")
            print(job_response.text)
        else:
            # Monitoring the job status until it completes or fails.
            job_state = job_response.json(
            )["data"]["attributes"]["current_state"]
            if job_state == "JOB_STATE_DONE" or job_state == "JOB_STATE_FAILED":
                end = timer()
                break
            else:
                print("Extraction job in progress. Status: {}".format(job_state))
                time.sleep(30)

    if job_state == "JOB_STATE_DONE":
        # Downloading the files.
        print("Job finished. Elapsed time: {0:.0f}s".format(end - start))
        files = job_response.json()["data"]["attributes"]["files"]
        print("Starting file download...")

        for file in files:
            uri = file["uri"]
            file_name = uri.split("/")[-1]
            file_response = requests.get(
                uri, headers=headers, allow_redirects=True, stream=True)
            if len(file_name) > 0:
                with open(file_name, mode="wb") as local_file:
                    file_response.raw.decode_content = True
                    shutil.copyfileobj(file_response.raw, local_file)

        print("File download complete.")
    else:
        print("Extraction job failed.")
        print("Reason: {}".format(job_response.json()["errors"][0]["detail"]))
        print("Elapsed time: {0:.0f}s".format((end - start)))
