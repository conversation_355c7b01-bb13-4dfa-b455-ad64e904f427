#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的连接测试脚本
用于测试不同的连接方式
"""

import socket
import ssl
import sys

def test_plain_connection(host, port):
    """测试普通TCP连接"""
    print(f"=== 测试普通TCP连接到 {host}:{port} ===")
    try:
        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client.settimeout(10.0)
        client.connect((host, port))
        print("✓ 普通TCP连接成功")
        
        # 发送测试消息
        test_msg = b"Hello Server"
        client.send(test_msg)
        print(f"发送消息: {test_msg}")
        
        # 接收响应
        response = client.recv(1024)
        print(f"收到响应: {response}")
        
        client.close()
        return True
    except Exception as e:
        print(f"✗ 普通TCP连接失败: {e}")
        return False

def test_ssl_connection_basic(host, port):
    """测试基本SSL连接"""
    print(f"\n=== 测试基本SSL连接到 {host}:{port} ===")
    try:
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client.settimeout(10.0)
        
        secure_client = context.wrap_socket(client, server_hostname=host)
        secure_client.connect((host, port))
        
        print("✓ 基本SSL连接成功")
        print(f"TLS版本: {secure_client.version()}")
        print(f"加密套件: {secure_client.cipher()}")
        
        # 发送测试消息
        test_msg = b"Hello SSL Server"
        secure_client.send(test_msg)
        print(f"发送消息: {test_msg}")
        
        # 接收响应
        response = secure_client.recv(1024)
        print(f"收到响应: {response}")
        
        secure_client.close()
        return True
    except Exception as e:
        print(f"✗ 基本SSL连接失败: {e}")
        return False

def test_ssl_connection_custom(host, port):
    """测试自定义SSL连接（支持更多加密套件）"""
    print(f"\n=== 测试自定义SSL连接到 {host}:{port} ===")
    try:
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
        context.minimum_version = ssl.TLSVersion.TLSv1_2
        context.maximum_version = ssl.TLSVersion.TLSv1_3
        
        # 设置更宽松的加密套件
        context.set_ciphers('ALL:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!SRP:!CAMELLIA')
        
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client.settimeout(10.0)
        
        secure_client = context.wrap_socket(client, server_hostname=host)
        secure_client.connect((host, port))
        
        print("✓ 自定义SSL连接成功")
        print(f"TLS版本: {secure_client.version()}")
        print(f"加密套件: {secure_client.cipher()}")
        
        # 发送测试消息
        test_msg = b"Hello Custom SSL Server"
        secure_client.send(test_msg)
        print(f"发送消息: {test_msg}")
        
        # 接收响应
        response = secure_client.recv(1024)
        print(f"收到响应: {response}")
        
        secure_client.close()
        return True
    except Exception as e:
        print(f"✗ 自定义SSL连接失败: {e}")
        return False

def test_ssl_with_sm2_ciphers(host, port):
    """测试支持国密的SSL连接"""
    print(f"\n=== 测试支持国密的SSL连接到 {host}:{port} ===")
    try:
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
        context.minimum_version = ssl.TLSVersion.TLSv1_2
        context.maximum_version = ssl.TLSVersion.TLSv1_3
        
        # 尝试设置支持国密的加密套件
        sm2_ciphers = [
            'ECDHE+AESGCM',
            'ECDHE+CHACHA20', 
            'DHE+AESGCM',
            'ECDH+AESGCM',
            'RSA+AESGCM',
            'HIGH',
            '!aNULL',
            '!eNULL',
            '!EXPORT',
            '!DES',
            '!RC4',
            '!MD5',
            '!PSK',
            '!SRP',
            '!CAMELLIA'
        ]
        
        context.set_ciphers(':'.join(sm2_ciphers))
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client.settimeout(10.0)
        
        secure_client = context.wrap_socket(client, server_hostname=host)
        secure_client.connect((host, port))
        
        print("✓ 国密SSL连接成功")
        print(f"TLS版本: {secure_client.version()}")
        print(f"加密套件: {secure_client.cipher()}")
        
        # 发送测试消息
        test_msg = b"Hello SM2 SSL Server"
        secure_client.send(test_msg)
        print(f"发送消息: {test_msg}")
        
        # 接收响应
        response = secure_client.recv(1024)
        print(f"收到响应: {response}")
        
        secure_client.close()
        return True
    except Exception as e:
        print(f"✗ 国密SSL连接失败: {e}")
        return False

def main():
    """主测试函数"""
    host = '************'
    port = 9445
    
    print("开始连接测试...")
    print("=" * 60)
    
    tests = [
        lambda: test_plain_connection(host, port),
        lambda: test_ssl_connection_basic(host, port),
        lambda: test_ssl_connection_custom(host, port),
        lambda: test_ssl_with_sm2_ciphers(host, port)
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试完成: {passed}/{total} 成功")
    
    if passed > 0:
        print("✓ 至少有一种连接方式可用")
    else:
        print("✗ 所有连接方式都失败")
    
    return 0 if passed > 0 else 1

if __name__ == "__main__":
    sys.exit(main())
