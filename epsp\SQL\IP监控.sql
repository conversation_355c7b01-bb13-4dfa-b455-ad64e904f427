﻿--创建表
create table MO_CUSTOMER_ACCESS_IPS
(
  signsn       VARCHAR2(100),
  ip           VARCHAR2(100),
  url          VARCHAR2(200),
  access_count NUMBER,
  create_time  DATE default sysdate
);
COMMENT ON TABLE MO_CUSTOMER_ACCESS_IPS IS '商户访问IP统计';
COMMENT ON COLUMN MO_CUSTOMER_ACCESS_IPS.SIGNSN IS '签名序列号';
COMMENT ON COLUMN MO_CUSTOMER_ACCESS_IPS.IP IS '访问IP地址';
COMMENT ON COLUMN MO_CUSTOMER_ACCESS_IPS.ACCESS_COUNT IS '次数';
COMMENT ON COLUMN MO_CUSTOMER_ACCESS_IPS.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN MO_CUSTOMER_ACCESS_IPS.URL IS '接口URL';

select * from MO_CUSTOMER_ACCESS_IPS;

--监控SQL
select c.customer_code 商户编号, c.name 商户名称, m.ip 异动IP
from mo_customer_access_ips m 
  inner join uaa_certificate u on m.signsn=u.signsn 
  inner join cum_customer_info c on u.customercode=c.customer_code
where m.create_time >= trunc(sysdate - 1)
  and m.url like '%Withdraw%'
  and exists(
    select 1 from mo_customer_access_ips t 
    where m.signsn=t.signsn and m.ip=t.ip
      and m.url like '%Withdraw%'
      and t.create_time < trunc(sysdate - 1) and t.create_time > trunc(sysdate - 31)
    group by t.signsn,t.ip
    having count(0) < 5
  );

