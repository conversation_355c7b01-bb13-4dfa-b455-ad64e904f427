import pandas as pd
import logging
import os
from datetime import datetime

logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',  # 只包含日志消息本身
    handlers=[
        logging.StreamHandler()  # 控制台处理器
    ]
)
logger = logging.getLogger(__name__)

dest_dir = "D:/TMP/"

# 增加数据SQL输出
if not os.path.exists(dest_dir):
    os.makedirs(dest_dir)
output_filename = f'{dest_dir}/table_{datetime.now().strftime("%Y%m%d")}.sql'
if os.path.exists(output_filename):
    os.remove(output_filename)

formatter = logging.Formatter('%(message)s')
log_file_handler = logging.FileHandler(output_filename)  
log_file_handler.setFormatter(formatter)
logger.addHandler(log_file_handler)

def excel_to_sql(file_path, table_name, sheet_index=0):
    # 读取Excel文件
    df = pd.read_excel(file_path, sheet_name=sheet_index)
    
    # 获取字段名
    columns = df.columns.tolist()
    
    # 生成CREATE TABLE语句
    create_table = f"CREATE TABLE {table_name} (\n"
    create_table += ",\n".join([f"    {col} varchar2(1000)" for col in columns])
    create_table += "\n);"
    logger.info(create_table)
    
    # 生成INSERT语句
    for index, row in df.iterrows():
        values = "', '".join([str(value) for value in row.tolist()])
        insert_statement = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ('{values}');"
        logger.info(insert_statement)

if __name__ == '__main__':
    # file_path = input("请输入Excel文件路径：")
    # table_name = input("请输入表名：")
    # sheet_index = int(input("请输入Sheet索引（从0开始）："))
    # excel_to_sql(file_path, table_name, sheet_index)
    excel_to_sql("D:/Download/gj.xlsx", "country_risk_level", 0)
    excel_to_sql("D:/Download/gj.xlsx", "country_black_list", 1)
    excel_to_sql("D:/Download/gz.xlsx", "rule_bin_risk", 0)
    excel_to_sql("D:/Download/gz.xlsx", "rule_action_risk", 2)
