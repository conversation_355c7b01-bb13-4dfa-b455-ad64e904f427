import hashlib
from hashlib import sha3_256
import urllib.parse as up
import requests

host = "http://***********:8089"

def sm4(msg):
   resp = requests.post(f"{host}/SecurityNew/symmetricEncryptData", data={"data": msg})
   return resp.text

def aes(msg, algorithm="AES256"):
   resp = requests.post(f"{host}/Security/EncryptData", data={"data": msg, "algorithm": algorithm})
   return resp.text

def sha256(s):
   return hashlib.sha256(s.encode("utf-8")).hexdigest()

def sm3(data):
   return sha3_256(data.encode("utf-8")).hexdigest()

def sm3_gm(data):
   from gmssl import sm3,func
   return sm3.sm3_hash(func.bytes_to_list(data.encode("utf-8")))

def md5(text):
   md5_hash = hashlib.md5()
   md5_hash.update(text.encode('utf-8'))
   md5_hash_value = md5_hash.hexdigest()
   return md5_hash_value

############################################################################
def enc_file(txtfile, target_file):
   md5 = hashlib.md5()
   with open(txtfile, 'r') as f, open(target_file,'w') as w:
      lines = f.readlines()
      for line in lines:
         s = line.strip()
         md5.update(s.encode("utf-8"))
         sm4_result = sm4(s)
         s = s+","+sm4_result+","+md5.hexdigest()
         w.write(s+"\n")

def sm3_file(txtfile, target_file):
   with open(txtfile, 'r') as f, open(target_file,'w') as w:
      lines = f.readlines()
      for line in lines:
         s = line.strip()
         s = sm3_gm(s)
         w.write(s+"\n")
         


if __name__ == "__main__":
   # enc_file("D:\\TMP\\cards.txt", "D:\\TMP\\cards2.txt")
   print(aes("123456"))

   # print(md5("6235720200000033689").lower())
   # print(md5("6222620910050784887").lower())
   # print(md5("6217730717296329").lower())
   # print(sm3("1234"))
   # print(sm3_gm("1234").upper())
   # print(md5("wangshaobinwangshaobin10189ee0e8").lower())
   # sm3_file("D:\\TMP\\0112.txt", "D:\\TMP\\0112_2.txt")