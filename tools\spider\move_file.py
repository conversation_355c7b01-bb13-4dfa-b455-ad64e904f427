import os
import re
import shutil

dir1 = 'C:\\TMP\\Photo\\小亲亲'

def move_file(source_dir, prefix):
    # 遍历文件夹dir1下的所有文件
    for filename in os.listdir(source_dir):
        if filename.startswith(prefix) and filename.endswith('.jpg'):
            src = os.path.join(dir1, filename)
            dest_folder = os.path.join(source_dir, prefix)

            # 检查是否存在子文件夹abc，如果不存在则创建
            if not os.path.exists(dest_folder):
                os.makedirs(dest_folder)

            dest = os.path.join(dest_folder, filename)
            shutil.move(src, dest)
            print(f'Moved file {filename} to subfolder {prefix}')

    print('Operation complete.')
    
if __name__ == '__main__':
    prefixes = set()
    for filename in os.listdir(dir1):
        if re.match(r'(.*?)\(\d+\)\.jpg', filename):
            match = re.match(r'(.*?)\(\d+\)\.jpg', filename)
            if match:
                prefix = match.group(1).strip()
                if prefix not in prefixes:
                    prefixes.add(prefix)
                    print(f'Possible prefix for {filename}: "{prefix}"')
    for prefix in prefixes:
        move_file(dir1, prefix)