﻿select name,real_name,sum(c),'第1季度' as season from(
  select u.name,u.real_name,count(0) as c from rc_operate_log a inner join pas_user u on a.operator=u.user_id 
  where a.operate_time between to_date('2023-01-01','yyyy-mm-dd') and to_date('2023-04-01','yyyy-mm-dd') group by u.name,u.real_name
  union all
  select user_name,real_name,count(0) as c from operation_log t 
  where t.op_time between to_date('2023-01-01','yyyy-mm-dd') and to_date('2023-04-01','yyyy-mm-dd') group by user_name,real_name
) t group by name,real_name
union all
select name,real_name,sum(c),'第2季度' as season from(
  select u.name,u.real_name,count(0) as c from rc_operate_log a inner join pas_user u on a.operator=u.user_id 
  where a.operate_time between to_date('2023-04-01','yyyy-mm-dd') and to_date('2023-07-01','yyyy-mm-dd') group by u.name,u.real_name
  union all
  select user_name,real_name,count(0) as c from operation_log t 
  where t.op_time between to_date('2023-04-01','yyyy-mm-dd') and to_date('2023-07-01','yyyy-mm-dd') group by user_name,real_name
) t group by name,real_name
union all
select name,real_name,sum(c),'第3季度' as season from(
  select u.name,u.real_name,count(0) as c from rc_operate_log a inner join pas_user u on a.operator=u.user_id 
  where a.operate_time between to_date('2023-07-01','yyyy-mm-dd') and to_date('2023-10-01','yyyy-mm-dd') group by u.name,u.real_name
  union all
  select user_name,real_name,count(0) as c from operation_log t 
  where t.op_time between to_date('2023-07-01','yyyy-mm-dd') and to_date('2023-10-01','yyyy-mm-dd') group by user_name,real_name
) t group by name,real_name
union all
select name,real_name,sum(c),'第4季度' as season from(
  select u.name,u.real_name,count(0) as c from rc_operate_log a inner join pas_user u on a.operator=u.user_id 
  where a.operate_time between to_date('2023-10-01','yyyy-mm-dd') and to_date('2024-01-01','yyyy-mm-dd') group by u.name,u.real_name
  union all
  select user_name,real_name,count(0) as c from operation_log t 
  where t.op_time between to_date('2023-10-01','yyyy-mm-dd') and to_date('2024-01-01','yyyy-mm-dd') group by user_name,real_name
) t group by name,real_name
