﻿--导入银行数据
create table tmp_bank_info(bank_name varchar2(1000), 
       d varchar2(10), d_per_limit varchar2(10), d_day_limit varchar2(10),
       c varchar2(10), c_per_limit varchar2(10), c_day_limit varchar2(10),
       batch_no varchar2(1), bank_id number, bank_code varchar2(40));

select * from tmp_bank_info where batch_no is null for update;
update tmp_bank_info set batch_no='2' where batch_no is null;

select t.bank_name,count(0) from tmp_bank_info t group by t.bank_name having count(0) > 1;

--匹配标准银行
merge into tmp_bank_info t
using (select min(id) as id,bank_code,full_name from bk_bank group by bank_code, full_name) b 
      on (b.full_name=t.bank_name)
when matched then 
  update set t.bank_id=b.id,t.bank_code=b.bank_code where t.bank_id is null;

--创建临时表视图
create view v_bank_info as
select 
  t.bank_name,
  t.d,
  to_number(trim((case when d is not null then substr(d_per_limit,1,length(d_per_limit)-1) else null end)))*10000*100 as d_per_limit,
  to_number(trim((case when d is not null then substr(d_day_limit,1,length(d_day_limit)-1) else null end)))*10000*100 as d_day_limit,
  t.c,
  to_number(trim((case when c is not null then substr(c_per_limit,1,length(c_per_limit)-1) else null end)))*10000*100 as c_per_limit,
  to_number(trim((case when c is not null then substr(c_day_limit,1,length(c_day_limit)-1) else null end)))*10000*100 as c_day_limit,
  t.bank_id,t.bank_code,
  t.batch_no
from tmp_bank_info t;

--创建临时表
create table CUM_PAY_CHANNEL_RECORD_A as select * from CUM_PAY_CHANNEL_RECORD where id=0;
alter table CUM_PAY_CHANNEL_RECORD_A add OP varchar2(10);
create table CUM_PAY_CHANNEL_TIERED_A as select * from CUM_PAY_CHANNEL_TIERED where parent_id=0;
alter table CUM_PAY_CHANNEL_TIERED_A add OP varchar2(10);

--业务参数
select * from cum_pay_channel where channel_name like '%记卡快捷协议支付%';
select * from cum_pay_channel_category where name like '%快捷支付%';

--准备数据存储过程
create or replace procedure p_update_channel_bank_fee(V_BATCH_NO number, V_PAY_CHANNEL_ID number, IN_RATE number) IS
  V_INSTITUTION_ID number;
  V_PAY_CHANNEL_CATEGORY number;
  V_RECORD_ID number;
  V_RECORD_COUNT NUMBER;
  V_RATE NUMBER;
begin
  V_INSTITUTION_ID := 18;        --网联支付机构
  V_PAY_CHANNEL_CATEGORY := 11;  --支付大类，快捷支付
  V_RATE := IN_RATE * 10000;
  
  for bank in (select * from V_BANK_INFO WHERE bank_code is not null and batch_no=V_BATCH_NO) loop
    select count(0) INTO v_RECORD_COUNT from CUM_PAY_CHANNEL_RECORD r 
      where r.institution_id=V_INSTITUTION_ID 
            and r.pay_channel_category=V_PAY_CHANNEL_CATEGORY 
            and r.pay_channel_id=V_PAY_CHANNEL_ID
            and r.bank_code=bank.bank_code;
    if v_RECORD_COUNT >0 then --存在则更新费率
      
      select ID into V_RECORD_ID 
      from CUM_PAY_CHANNEL_RECORD r 
      where r.institution_id=V_INSTITUTION_ID 
            and r.pay_channel_category=V_PAY_CHANNEL_CATEGORY 
            and r.pay_channel_id=V_PAY_CHANNEL_ID
            and r.bank_code=bank.bank_code;
      
      insert into CUM_PAY_CHANNEL_RECORD_A(id, pay_type, pay_channel_id, rate, rate_mode, fee_per, create_time, 
        creator_id, update_time, updator_id, pay_channel_category, institution_id, channel_sort, state, limit_per, 
        day_total_limit, priority, channel_inst_code, service_id, account_type, bank_code, remark, attachment_url, 
        stop_start_time, stop_end_time, amount_min, amount_max, ip_support_type,OP)
      select id, pay_type, pay_channel_id, V_RATE, 2, 0, create_time, 
        creator_id, sysdate, 0, pay_channel_category, institution_id, channel_sort, state, limit_per, 
        day_total_limit, priority, channel_inst_code, service_id, account_type, bank_code, remark, attachment_url, 
        stop_start_time, stop_end_time, amount_min, amount_max, ip_support_type,'MOD'
      from CUM_PAY_CHANNEL_RECORD where id=V_RECORD_ID;
      
      insert into CUM_PAY_CHANNEL_TIERED_A(tiered_id, parent_id, fee_from, fee_to, fee_mode, fee_rate, fee_per, 
        fee_from_mode, fee_to_mode, orderno, create_time, creator_id, update_time, updator_id, mcht_cat, ladder_type,Op)
      select tiered_id, parent_id, fee_from, fee_to, 2, V_RATE, 0, 
        fee_from_mode, fee_to_mode, orderno, create_time, creator_id, sysdate, 0, mcht_cat, ladder_type,'MOD' 
      from CUM_PAY_CHANNEL_TIERED d where parent_id=V_RECORD_ID;
    else
      select SEQ_CUM_PAY_CHANNEL_RECORD.nextval into V_RECORD_ID from dual;
      insert into CUM_PAY_CHANNEL_RECORD_A(id, pay_type, pay_channel_id, rate, rate_mode, fee_per, create_time, 
        creator_id, update_time, updator_id, pay_channel_category, institution_id, channel_sort, state, limit_per, 
        day_total_limit, priority, channel_inst_code, service_id, account_type, bank_code, remark, attachment_url, 
        stop_start_time, stop_end_time, amount_min, amount_max, ip_support_type,OP)
      select 
        V_RECORD_ID as ID,
        1 as PAY_TYPE,
        V_PAY_CHANNEL_ID as PAY_CHANNEL_ID, --借记卡快捷协议支付
        V_RATE as RATE,
        2 as RATE_MODE,
        0 as FEE_PER,
        sysdate as CREATE_TIME,
        0 as CREATOR_ID,
        null as UPDATE_TIME,
        null as updator_id,
        V_PAY_CHANNEL_CATEGORY as PAY_CHANNEL_CATEGORY, 
        V_INSTITUTION_ID as INSTITUTION_ID, 
        null as channel_sort,
        1 as state, --正常状态
        t.d_per_limit  as LIMIT_PER,
        t.d_day_limit as DAY_TOTAL_LIMIT,
        1 as PRIORITY,
        'Z2001944000017' as CHANNEL_INST_CODE, --网联机构号
        null as SERVICE_ID,
        null as ACCOUNT_TYPE,
        t.BANK_CODE,
        '********导入' as REMARK,
        null as ATTACHMENT_URL,
        null as STOP_START_TIME,
        null as STOP_END_TIME,
        0 as AMOUNT_MIN,
        null as AMOUNT_MAX,
        2 as IP_SUPPORT_TYPE,
        'ADD' as OP
      FROM V_BANK_INFO  t
      WHERE batch_no=V_BATCH_NO and t.bank_name=bank.bank_name;
      --构造费率数据
      INSERT INTO CUM_PAY_CHANNEL_TIERED_A(tiered_id, parent_id, fee_from, fee_to, fee_mode, fee_rate, fee_per, 
        fee_from_mode, fee_to_mode, orderno, create_time, creator_id, update_time, updator_id, mcht_cat, ladder_type,Op)
      select 
        SEQ_CHANNEL_TIERED.Nextval as TIERED_ID,
        V_RECORD_ID as parent_id,
        0 as FEE_FROM,
        *********** as FEE_TO,
        2 as FEE_MODE,
        V_RATE as FEE_RATE,
        0 as FEE_PER,
        2 as FEE_FROM_MODE,
        4 as FEE_TO_MODE,
        1 as ORDERNO,
        sysdate as CREATE_TIME,
        0 as CREATOR_ID,
        null as UPDATE_TIME,
        null as UPDATOR_ID,
        null as MCHT_CAT,
        1 as LADDER_TYPE,
        'ADD' as OP
      FROM DUAL;
    end if;
  end loop;
  commit;
end;

--准备数据
/*
渠道：
借记卡快捷协议支付 42,
贷记卡快捷协议支付 41
truncate table CUM_PAY_CHANNEL_RECORD_A;
truncate table CUM_PAY_CHANNEL_TIERED_A;
begin
  --第一批
  p_update_channel_bank_fee(1, 42, 0.1);
  p_update_channel_bank_fee(1, 41, 0.2);
  --第二批
  p_update_channel_bank_fee(2, 42, 0.15);
  p_update_channel_bank_fee(2, 41, 0.3);
end;
*/
truncate table CUM_PAY_CHANNEL_RECORD_A;
truncate table CUM_PAY_CHANNEL_TIERED_A;
begin
  --第一批
  p_update_channel_bank_fee(1, 42, 0.1);
  p_update_channel_bank_fee(1, 41, 0.2);
  --第二批
  p_update_channel_bank_fee(2, 42, 0.15);
  p_update_channel_bank_fee(2, 41, 0.3);
end;
select * from CUM_PAY_CHANNEL_RECORD_A;
select * from CUM_PAY_CHANNEL_TIERED_A;

--数据备份
--drop table CUM_PAY_CHANNEL_RECORD_0627;
create table CUM_PAY_CHANNEL_RECORD_0627 as 
select id, pay_type, pay_channel_id, rate, rate_mode, fee_per, create_time, 
        creator_id, update_time, updator_id, pay_channel_category, institution_id, channel_sort, state, limit_per, 
        day_total_limit, priority, channel_inst_code, service_id, account_type, bank_code, remark, attachment_url, 
        stop_start_time, stop_end_time, amount_min, amount_max, ip_support_type,op
from CUM_PAY_CHANNEL_RECORD_A
union all
select id, pay_type, pay_channel_id, rate, rate_mode, fee_per, create_time, 
        creator_id, update_time, updator_id, pay_channel_category, institution_id, channel_sort, state, limit_per, 
        day_total_limit, priority, channel_inst_code, service_id, account_type, bank_code, remark, attachment_url, 
        stop_start_time, stop_end_time, amount_min, amount_max, ip_support_type,'INIT'
from CUM_PAY_CHANNEL_RECORD where id in(select id from CUM_PAY_CHANNEL_RECORD_A where op='MOD');

--drop table CUM_PAY_CHANNEL_TIERED_0627;
create table CUM_PAY_CHANNEL_TIERED_0627 as
select tiered_id, parent_id, fee_from, fee_to, fee_mode, fee_rate, fee_per, 
        fee_from_mode, fee_to_mode, orderno, create_time, creator_id, update_time, updator_id, mcht_cat, ladder_type,op
from CUM_PAY_CHANNEL_TIERED_A
union all
select tiered_id, parent_id, fee_from, fee_to, fee_mode, fee_rate, fee_per, 
        fee_from_mode, fee_to_mode, orderno, create_time, creator_id, update_time, updator_id, mcht_cat, ladder_type,'INIT'
from CUM_PAY_CHANNEL_TIERED where tiered_id in(select tiered_id from CUM_PAY_CHANNEL_TIERED_A where op='MOD');

--合并数据
begin
  merge into CUM_PAY_CHANNEL_RECORD t
  using (select * from CUM_PAY_CHANNEL_RECORD_A where op='MOD') s on (t.id=s.id)
  when matched then
    update set t.rate_mode=s.rate_mode,t.rate=s.rate,t.fee_per=s.fee_per;

  insert into CUM_PAY_CHANNEL_RECORD(id, pay_type, pay_channel_id, rate, rate_mode, fee_per, create_time, 
          creator_id, update_time, updator_id, pay_channel_category, institution_id, channel_sort, state, limit_per, 
          day_total_limit, priority, channel_inst_code, service_id, account_type, bank_code, remark, attachment_url, 
          stop_start_time, stop_end_time, amount_min, amount_max, ip_support_type)
  select id, pay_type, pay_channel_id, rate, rate_mode, fee_per, create_time, 
          creator_id, update_time, updator_id, pay_channel_category, institution_id, channel_sort, state, limit_per, 
          day_total_limit, priority, channel_inst_code, service_id, account_type, bank_code, remark, attachment_url, 
          stop_start_time, stop_end_time, amount_min, amount_max, ip_support_type
  from CUM_PAY_CHANNEL_RECORD_A where op='ADD';

  --
  merge into CUM_PAY_CHANNEL_TIERED t
  using (select * from CUM_PAY_CHANNEL_TIERED_A where op='MOD') s
  on (t.tiered_id=s.tiered_id)
  when matched then
    update set t.fee_mode=s.fee_mode, t.fee_rate=s.fee_rate, t.fee_per=s.fee_per;

  insert into CUM_PAY_CHANNEL_TIERED(tiered_id, parent_id, fee_from, fee_to, fee_mode, fee_rate, fee_per, 
          fee_from_mode, fee_to_mode, orderno, create_time, creator_id, update_time, updator_id, mcht_cat, ladder_type)
  select tiered_id, parent_id, fee_from, fee_to, fee_mode, fee_rate, fee_per, 
          fee_from_mode, fee_to_mode, orderno, create_time, creator_id, update_time, updator_id, mcht_cat, ladder_type
  from CUM_PAY_CHANNEL_TIERED_A where op='ADD';

  commit;
end;

--数据恢复-------------------需要恢复的时候才执行
begin
  delete from CUM_PAY_CHANNEL_RECORD where id in(select id from CUM_PAY_CHANNEL_RECORD_0627 where op='ADD');

  merge into CUM_PAY_CHANNEL_RECORD t
  using (select * from CUM_PAY_CHANNEL_RECORD_0627 where op='INIT') s on (t.id=s.id)
  when matched then
    update set t.rate_mode=s.rate_mode,t.rate=s.rate,t.fee_per=s.fee_per;

  --
  delete from CUM_PAY_CHANNEL_TIERED where tiered_id in(select tiered_id from CUM_PAY_CHANNEL_TIERED_0627 where OP='ADD');

  merge into CUM_PAY_CHANNEL_TIERED t
  using (select * from CUM_PAY_CHANNEL_TIERED_0627 where op='INIT') s
  on (t.tiered_id=s.tiered_id)
  when matched then
    update set t.fee_mode=s.fee_mode, t.fee_rate=s.fee_rate, t.fee_per=s.fee_per;
  
  commit;
end;

--清理对象
--drop table tmp_bank_info;
drop view v_bank_info;

drop table CUM_PAY_CHANNEL_RECORD_A;
drop table CUM_PAY_CHANNEL_TIERED_A;

drop procedure p_update_channel_bank_fee;

drop table CUM_PAY_CHANNEL_RECORD_0627;
drop table CUM_PAY_CHANNEL_TIERED_0627;

-------
create table CUM_PAY_CHANNEL_RECORD_0630 as
select * from cum_pay_channel_record where remark='********导入';

create table CUM_PAY_CHANNEL_TIERED_0630 as
select * from CUM_PAY_CHANNEL_TIERED where parent_id in(select id from cum_pay_channel_record where remark='********导入');

delete from CUM_PAY_CHANNEL_TIERED where parent_id in(select id from cum_pay_channel_record where remark='********导入');
delete from cum_pay_channel_record where remark='********导入';
-----------------------------------------------------------------------

--代码和名称一致
select t.id, t.bank_code,a.bank_code as old_bank_code,b.bank_code as new_bank_code,a.bank_name old_bank_name,b.bank_name as new_bank_name from CUM_PAY_CHANNEL_RECORD t, cum_bank_info a,bk_bank b where t.bank_code=a.bank_code and a.bank_code=b.bank_code and a.bank_name=b.bank_name;

--代码相同和名称不同
select t.id, t.bank_code,a.bank_code as old_bank_code,b.bank_code as new_bank_code,a.bank_name old_bank_name,b.bank_name as new_bank_name from CUM_PAY_CHANNEL_RECORD t, cum_bank_info a,bk_bank b where t.bank_code=a.bank_code and a.bank_code=b.bank_code and a.bank_name!=b.bank_name;

--代码不同和名称相同
select t.id, t.bank_code,a.bank_code as old_bank_code,b.bank_code as new_bank_code,a.bank_name old_bank_name,b.bank_name as new_bank_name from CUM_PAY_CHANNEL_RECORD t, cum_bank_info a,bk_bank b where t.bank_code=a.bank_code and a.bank_code!=b.bank_code and a.bank_name=b.bank_name;

--代码不同和名称不同
select t.id, t.bank_code,a.bank_code as old_bank_code,a.bank_name old_bank_name from CUM_PAY_CHANNEL_RECORD t, cum_bank_info a where t.bank_code=a.bank_code and not exists(select 1 from bk_bank b where a.bank_code=b.bank_code or a.bank_name=b.bank_name);

--代码不存在
select t.id, t.bank_code from CUM_PAY_CHANNEL_RECORD t where t.bank_code not in(select a.bank_code from cum_bank_info a);

---------------------------------------------------------------------------
--补充银行数据
insert into cum_bank_info(id, bank_code, bank_name, create_time, is_area_bank, institution_code, new_bank_code, flag)
select id,b.bank_code,b.bank_name,b.create_time,b.is_area_bank,b.institution_code,b.new_bank_code,b.flag
from bk_bank b where b.bank_code is not null and b.bank_code not in('DBSCN','SHRCB','BOCFCB') and b.bank_code in(select bank_code from tmp_bank_info) and b.bank_code not in(select bank_code from cum_bank_info);

--如下银行代码重复，单独处理，生产数据
insert into cum_bank_info(id, bank_code, bank_name, create_time, is_area_bank, institution_code, new_bank_code, flag)
select id,b.bank_code,b.bank_name,b.create_time,b.is_area_bank,b.institution_code,b.new_bank_code,b.flag
from bk_bank b where b.bank_code in('DBSCN','SHRCB','BOCFCB') and b.id in(3005426, 3005080, 3005708);


--补充网联渠道银行编码
insert into clr_channel_bankcode(id, bank_code, institution_id, channel_bank_code,support_card_type,create_time)
select id,b.bank_code,'18' as institution_id,b.institution_code,(case when a.c is null then '0' else '2' end) as support_card_type, sysdate
from bk_bank b inner join tmp_bank_info a on b.bank_code=a.bank_code
where b.bank_code is not null and b.bank_code not in('SHRCB','HANABANK','DBSCN','BOCFCB') and not exists(select bank_code from clr_channel_bankcode c where c.institution_id='18' and c.bank_code=b.bank_code and c.support_card_type=(case when a.c is null then '0' else '2' end));

insert into clr_channel_bankcode(id, bank_code, institution_id, channel_bank_code,support_card_type,create_time)
select id,b.bank_code,'18' as institution_id,b.institution_code,(case when a.c is null then '0' else '2' end) as support_card_type, sysdate
from bk_bank b inner join tmp_bank_info a on b.bank_code=a.bank_code
where b.bank_code in('SHRCB','HANABANK','DBSCN','BOCFCB') and b.id in(3005426, 3005080, 3005125, 3005708);


---
create table reco_db.cum_bank_info_0705 as select * from cum_bank_info;
create table reco_db.clr_channel_bankcode_0705 as select * from clr_channel_bankcode;

create table reco_db.cum_pay_channel_record_0705 as select * from cum_pay_channel_record;
create table reco_db.cum_pay_channel_tiered_0705 as select * from cum_pay_channel_tiered;

---
select institution_id, bank_code,support_card_type, count(0) from (
select id,b.bank_code,'18' as institution_id,b.institution_code,(case when a.c is null then '0' else '2' end) as support_card_type, sysdate
from bk_bank b inner join tmp_bank_info a on b.bank_code=a.bank_code
where b.bank_code is not null and b.bank_code not in('SHRCB','HANABANK','DBSCN','BOCFCB') and not exists(select bank_code from clr_channel_bankcode c where c.institution_id='18' and c.bank_code=b.bank_code and c.support_card_type=(case when a.c is null then '0' else '2' end))
) t group by institution_id, bank_code,support_card_type having count(0) > 1;


select * from clr_channel_bankcode;

select bank_code,count(0) from(
select distinct id,b.bank_code,b.bank_name,b.create_time,b.is_area_bank,b.institution_code,b.new_bank_code,b.flag
from bk_bank b where b.bank_code is not null and b.bank_code in(select bank_code from tmp_bank_info) and b.bank_code not in(select bank_code from cum_bank_info)
) t group by bank_code having count(0) > 1;

select * from bk_bank b where b.bank_code in('DBSCN','SHRCB','BOCFCB') order by b.bank_code desc;

insert into cum_bank_info(id, bank_code, bank_name, create_time, is_area_bank, institution_code, new_bank_code, flag)
select id,b.bank_code,b.bank_name,b.create_time,b.is_area_bank,b.institution_code,b.new_bank_code,b.flag
from bk_bank b where b.bank_code in('DBSCN','SHRCB','BOCFCB') and b.id in(3001026, 3001080, 3001757);

select * from tmp_bank_info t where t.bank_name in(select bank_name from bk_bank b where b.is_area_bank='1');
select * from tmp_bank_info t where t.bank_code in('DBSCN','SHRCB','BOCFCB');

select * from bk_bank t where t.bank_id is not null;

insert into clr_channel_bankcode(id, bank_code, institution_id, channel_bank_code,support_card_type,create_time)
select id,b.bank_code,'18' as institution_id,b.institution_code,(case when a.c is null then '0' else '2' end) as support_card_type, sysdate
from bk_bank b inner join tmp_bank_info a on b.bank_code=a.bank_code
where b.bank_code in('SHRCB','HANABANK','DBSCN','BOCFCB') and b.id in(3005426, 3005080, 3005125, 3005708);
-----------------------------------------------------------------------
create table reco_db.cum_pay_channel_record0804 as 
select * from cum_pay_channel_record t where t.remark='********导入' and t.update_time < timestamp '2023-08-01 00:00:00';
update cum_pay_channel_record t set t.rate_mode=1,fee_per=0 where t.remark='********导入' and t.update_time < timestamp '2023-08-01 00:00:00';

create table reco_db.cum_pay_channel_tiered0804 as 
select * from cum_pay_channel_tiered r where r.parent_id in(select t.id from cum_pay_channel_record t where t.remark='********导入' and t.update_time < timestamp '2023-08-01 00:00:00');
update cum_pay_channel_tiered r set r.fee_mode=2,r.fee_per=0 where r.parent_id in(select t.id from cum_pay_channel_record t where t.remark='********导入' and t.update_time < timestamp '2023-08-01 00:00:00');
--=====================================================================
--1、数据入库
create table tmp_bank_0825(bank_name varchar2(100), card_type varchar2(20), per_limit number, day_limit number,protocol_pay varchar2(2),bank_code varchar2(20));
select * from tmp_bank_0825 t for update;
--2、银行匹配
begin
  for s in (select distinct bank_name from tmp_bank_0825 where bank_code is null) loop
    update tmp_bank_0825 
    set bank_code=(select listagg(b.bank_code,',') within group (order by b.bank_code) from bk_bank b where b.bank_name=s.bank_name) 
    where bank_name=s.bank_name;
    commit;
  end loop;
end;
--1、执行Python脚本
--readcsv4UnionProtocolChannel(filename="G:/Python/ESQuery/bank20230825.csv")
--2、搜索新增ID，提供为启用
select listagg(id,',') within group(order by id) as ids from cum_pay_channel_record where create_time > sysdate -1;
--3、执行Python脚本
--enablePayChannel(channelIds="Step2的输出", remark="********导入")
--4、银行数据同步
insert into cum_bank_info(id, bank_code, bank_name, new_bank_code, institution_code, is_area_bank,create_time, flag)
select seq_cum_bank_info_id.nextval,bank_code, bank_name, bank_code, institution_code, 1, sysdate, '1'
from bk_bank where bank_code in(
select bank_code from cum_pay_channel_record where create_time > sysdate -1 and bank_code not in(select bank_code from cum_bank_info)
);

--5、数据回滚
delete from cum_pay_channel_record where create_time > sysdate -1;
delete from cum_pay_channel_tiered where create_time > sysdate -1;

-----------------------------------------------------------------
--0、执行如下SQL
alter table cum_pay_channel_record_his modify bank_code varchar2(100);
--1、执行Python脚本
--readcsv4EpccProtocolChannel(filename="G:/Python/ESQuery/bank-epcc-********.csv")
--2、搜索新增ID，提供为启用
select listagg(id,',') within group(order by id) as ids from cum_pay_channel_record where create_time > sysdate -1;
--3、执行Python脚本
--enablePayChannel(channelIds="Step2的输出", remark="********导入")
--4、银行数据同步
insert into cum_bank_info(id, bank_code, bank_name, new_bank_code, institution_code, is_area_bank,create_time, flag)
select seq_BANK_INFO_ID.nextval,bank_code, bank_name, bank_code, institution_code, 1, sysdate, '1'
from bk_bank where bank_code in(
select bank_code from cum_pay_channel_record where create_time > sysdate -1 and bank_code not in(select bank_code from cum_bank_info)
);

--5、数据回滚
delete from cum_pay_channel_record where create_time > sysdate -1;
delete from cum_pay_channel_tiered where create_time > sysdate -1;
delete from cum_pay_channel_record_his where create_time > sysdate -1;

--================================================================
select * from cum_pay_channel_record where create_time > sysdate -1;
select * from cum_pay_channel_tiered where create_time > sysdate -1;
select * from cum_pay_channel_record_his where create_time > sysdate -1;


select * from cum_bank_info;
select * from bk_bank where bank_code in(
select bank_code from cum_pay_channel_record where create_time > sysdate -1 and bank_code not in(select bank_code from cum_bank_info)
);


select * from posp_txn t where t.create_time > sysdate -1 order by create_time desc;
