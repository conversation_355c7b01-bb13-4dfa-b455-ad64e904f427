﻿select t.customer_code as 商户编号,c.name as 商户名称, 
	c.plat_customer_no as 所属平台商号, 
	(select name from cust_customer c1 where c1.customer_no=c.plat_customer_no) AS 所属平台名称,
	c.service_customer_no 所属服务商号,
  (select name from cust_customer c1 where c1.customer_no=c.service_customer_no) AS 所属服务商名称,
  t.tran_mon as 统计月份, t.total_sum as 总计金额, t.total_count as 总计笔数
from(
  select p.customer_code,to_char(p.create_time, 'yyyy-mm') tran_mon, sum(p.amount) / 100 total_sum, count(p.transaction_no) as total_count
  from clr_pay_record t
    inner join txs_pay_trade_order p on t.transaction_no=p.transaction_no
  where t.create_time >= timestamp'2023-09-01 00:00:00'
    and t.create_time < timestamp'2024-01-01 00:00:00'
    and t.pay_method in('75','76','4','7','14','77','66','1','6','9','10','13','35')  --微信&支付宝
    and t.card_type='D'
    and t.amount > 10000
    and t.channel_extend like '%PSBC%'
    and (p.customer_code in (select customer_code from cum_customer_info c 
                     where c.customer_path like '%5651300003037538%' or c.customer_path like '%5651300003028376%' 
                        or c.customer_path like '%562000003553804%' or c.customer_path like '%562194003599345%' 
                        or c.customer_path like '%562537003623224%' or c.customer_path like '%562110003967680%' 
                        or c.customer_path like '%562074003730293%' or c.customer_path like '%562309003279845%' 
                        or c.customer_path like '%562817003857625%' or c.customer_path like '%562578003765962%' 
                        or c.customer_path like '%562658004369900%' or c.customer_path like '%562479004119028%')
       OR p.customer_code in('****************','***************','***************','562375003712647','562607003644467'))
  group by p.customer_code,to_char(p.create_time, 'yyyy-mm')
  union all 
  select p.customer_code,to_char(p.create_time, 'yyyy-mm') tran_mon, sum(p.amount) / 100 total_sum, count(p.transaction_no) as total_count
  from clr_pay_record t
    inner join txs_pay_trade_order p on t.transaction_no=p.transaction_no
  where t.create_time >= timestamp'2023-09-01 00:00:00'
    and t.create_time < timestamp'2024-01-01 00:00:00'
    and t.pay_method in('49','72','73','40','41','24','25') --银联二维码
    and t.card_type='D'
    and t.amount > 10000
    and t.channel_extend like '%PSBC%'
    and p.customer_code ='562712003718992'
  group by p.customer_code,to_char(p.create_time, 'yyyy-mm')
  union all
  select p.customer_code,to_char(p.create_time, 'yyyy-mm') tran_mon, sum(p.amount) / 100 total_sum, count(p.transaction_no) as total_count
  from clr_pay_record t
    inner join txs_pay_trade_order p on t.transaction_no=p.transaction_no
  where t.create_time >= timestamp'2023-09-01 00:00:00'
    and t.create_time < timestamp'2024-01-01 00:00:00'
    and t.pay_method in('49','72','73','40','41','24','25') --银联二维码
    and t.card_type='D'
    and t.amount > 1000
    and t.channel_extend like '%PSBC%'
    and p.customer_code IN('562152003714912','562574004419118','562983004419109')
  group by p.customer_code,to_char(p.create_time, 'yyyy-mm')
) t inner join cust_customer c on t.customer_code=c.customer_no
order by t.customer_code,t.tran_mon
;
------------------------------------------------------------------------------------------------
select t.customer_code as 商户编号,c.name as 商户名称, 
	c.plat_customer_no as 所属平台商号, 
	(select name from cust_customer c1 where c1.customer_no=c.plat_customer_no) AS 所属平台名称,
	c.service_customer_no 所属服务商号,
  (select name from cust_customer c1 where c1.customer_no=c.service_customer_no) AS 所属服务商名称,
  t.tran_mon as 统计月份, t.total_sum as 总计金额, t.total_count as 总计笔数
from(
  select p.customer_code,to_char(p.create_time, 'yyyy-mm') tran_mon, sum(p.amount) / 100 total_sum, count(p.transaction_no) as total_count
  from clr_pay_record t
    inner join txs_pay_trade_order p on t.transaction_no=p.transaction_no
  where t.create_time >= timestamp'2023-09-01 00:00:00'
    and t.create_time < timestamp'2024-01-01 00:00:00'
    and t.pay_method in('8','26','29','28')  --快捷支付
    and p.card_type='D'
    and p.amount > 10000
    and p.bank_code like '%PSBC%'
    and p.customer_code in('****************','***************','***************')
  group by p.customer_code,to_char(p.create_time, 'yyyy-mm')
) t inner join cust_customer c on t.customer_code=c.customer_no
order by t.customer_code,t.tran_mon
;

------------------------------------------------------------------------------------------------
select t.customer_code as 商户编号,c.name as 商户名称, 
	c.plat_customer_no as 所属平台商号, 
	(select name from cust_customer c1 where c1.customer_no=c.plat_customer_no) AS 所属平台名称,
	c.service_customer_no 所属服务商号,
  (select name from cust_customer c1 where c1.customer_no=c.service_customer_no) AS 所属服务商名称,
  t.tran_mon as 统计月份, t.total_sum as 总计金额, t.total_count as 总计笔数
from(
  select p.customer_code,to_char(p.create_time, 'yyyy-mm') tran_mon, sum(p.amount) / 100 total_sum, count(p.transaction_no) as total_count
  from clr_pay_record t
    inner join txs_pay_trade_order p on t.transaction_no=p.transaction_no
  where t.create_time >= timestamp'2023-01-01 00:00:00'
    and t.create_time < timestamp'2024-01-01 00:00:00'
    and t.pay_method in('8','26','29','28')  --快捷支付
    and p.card_type='D'
    and p.amount > 10000
    and p.bank_code like '%PSBC%'
    and p.customer_code in('****************')
  group by p.customer_code,to_char(p.create_time, 'yyyy-mm')
) t inner join cust_customer c on t.customer_code=c.customer_no
order by t.customer_code,t.tran_mon
;
