﻿/*
CREATE OR REPLACE VIEW v_customer_accountflow AS 
SELECT b.customercode, accountcode, REPLACE(accountcode, b.customercode, '') AS accounttype, 
       direction, balancetype, transactiontype, flowtype, businesscode,
       sum(amount) AS total_amount, count(0) AS total_count,
       min(accountdatetime) as first_time, max(accountdatetime) as last_time
FROM(
  SELECT t.accountcode,(case when t.amount >= 0 then 1 else -1 end) AS direction, t.balancetype,nvl(t.transactiontype,'SETT') as transactiontype,
         t.type AS flowtype, nvl(t.business_code,'EMPTY') AS businesscode,t.amount, t.accountdatetime
  FROM acc_accountflow t 
  WHERE t.accountdatetime >= timestamp '2023-01-18 00:00:00' AND t.accountdatetime < timestamp '2024-01-19 00:00:00'
)a inner join acc_account b on a.accountcode=b.code 
GROUP BY b.customercode, accountcode, direction, balancetype, transactiontype, flowtype, businesscode
ORDER BY b.customercode;
*/

CREATE OR REPLACE VIEW v_customer_money_category AS 

SELECT f.*, '收单/线上收单' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='ZF'
UNION ALL
SELECT f.*, '收单/间连收单' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='JL'
UNION ALL
SELECT f.*, '收单/间连收单服务费' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='JL'
UNION ALL
SELECT f.*, '收单/间连收单撤销' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='JLCX'
UNION ALL
SELECT f.*, '收单/间连收单服务费撤销' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='JLCX'
UNION ALL
SELECT f.*, '收单/直连收单' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='XX'
UNION ALL
SELECT f.*, '收单/授权支付' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='SQZF'
UNION ALL
SELECT f.*, '收单/分账收单' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='FZ' AND f.accounttype = 'BJ-B' 
UNION ALL

SELECT f.*, '分账/分账交易' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='FZJY' AND f.accounttype = 'BJ-B' 
UNION ALL
SELECT f.*, '分账/分账交易服务费' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='FZJY' AND f.accounttype = 'JY-A'  AND f.balancetype=1
UNION ALL
SELECT f.*, '分账/分账收款' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='FZJY' AND f.accounttype = 'JY-A' 
UNION ALL
SELECT f.*, '分账/账户分账交易' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='ZHFZ' AND f.accounttype = 'JY-A' 
UNION ALL
SELECT f.*, '分账/账户分账撤销（付款方）' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='ZHFZCX' AND f.accounttype = 'JY-A' 
UNION ALL
SELECT f.*, '分账/账户分账撤销（收款方）' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='ZHFZCX' AND f.accounttype = 'JY-A' 
UNION ALL
SELECT f.*, '分账/账户分账收款' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='ZHFZ' AND f.accounttype = 'JY-A' 
UNION ALL
SELECT f.*, '分账/费差分账付款' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='FCFZ' AND f.accounttype = 'JY-A' 
UNION ALL
SELECT f.*, '分账/费差分账收款' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='FCFZ' AND f.accounttype = 'JY-A' 
UNION ALL

SELECT f.*, '余额支付/付款，付款金额和付款手续费2条记录' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='IT' AND f.accounttype = 'JY-A' 
UNION ALL
SELECT f.*, '余额支付/收款' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='IT'
UNION ALL

SELECT f.*, '退款/可用余额退款' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='TK' AND f.accounttype = 'JY-A' AND f.balancetype = 1 AND f.flowtype=1
UNION ALL
SELECT f.*, '退款/可用余额退款回滚' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='TK' AND f.accounttype = 'JY-A' AND f.balancetype = 1 AND f.flowtype=2
UNION ALL
SELECT f.*, '退款/在途余额退款' AS category FROM v_customer_accountflow f WHERE (f.direction=-1 OR f.total_amount=0) AND f.transactiontype='TK' AND f.accounttype = 'JY-A' AND f.balancetype = 2 AND f.flowtype=1
UNION ALL
SELECT f.*, '退款/在途余额退款回滚' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='TK' AND f.accounttype = 'JY-A' AND f.balancetype = 2 AND f.flowtype=2
UNION ALL
SELECT f.*, '退款/分账退款' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='TK' AND f.accounttype = 'BJ-B' AND f.balancetype = 1 AND f.flowtype=1
UNION ALL
SELECT f.*, '退款/分账退款回滚' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='TK' AND f.accounttype = 'BJ-B' AND f.balancetype = 1 AND f.flowtype=2
UNION ALL
SELECT f.*, '退款/退回手续费/服务费' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='TK' AND f.accounttype = 'JY-A' AND f.balancetype = 1 AND f.flowtype=1
UNION ALL
SELECT f.*, '退款/退回手续费/服务费回滚' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='TK' AND f.accounttype = 'JY-A' AND f.balancetype = 1 AND f.flowtype=2
UNION ALL
SELECT f.*, '退款/退款费' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='TKF' AND f.accounttype = 'JY-A' AND f.balancetype = 1 AND f.flowtype=1
UNION ALL
SELECT f.*, '退款/退款费回滚' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='TKF' AND f.accounttype = 'JY-A' AND f.balancetype = 1 AND f.flowtype=2
UNION ALL
SELECT f.*, '退款/线下直连退款' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='XX' AND f.accounttype = 'JY-A' AND f.balancetype = 1 AND f.flowtype=1
UNION ALL

SELECT f.*, '出金/提现|代付' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='TX' AND f.accounttype = 'JY-A' 
UNION ALL
SELECT f.*, '出金/提现|代付回滚' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='TX' AND f.accounttype = 'JY-A' 
UNION ALL
SELECT f.*, '出金/退汇' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='TH' AND f.accounttype = 'JY-A' 
UNION ALL

SELECT f.*, '充值/ACS充值' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='TZ' AND f.businesscode = 'AcsRecharge' 
UNION ALL
SELECT f.*, '充值/ACS充值手续费' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='TZ' AND f.businesscode = 'AcsRecharge' 
UNION ALL

SELECT f.*, '跨境/跨境资金映射' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='TZ' AND f.accounttype = 'BJ-B' 
UNION ALL
SELECT f.*, '跨境/跨境资金映射消耗' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='ZHFZ' AND f.accounttype = 'BJ-B' 
UNION ALL
SELECT f.*, '跨境/跨境资金映射消耗撤销' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='ZHFZCX' AND f.accounttype = 'BJ-B' AND (f.businesscode IN('GlobalBalancePay','GlobalCardPay') OR f.businesscode IS NULL)
UNION ALL
SELECT f.*, '跨境/跨境出金' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='KJ' AND f.accounttype = 'JY-A' 
UNION ALL
SELECT f.*, '跨境/跨境出金回滚' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='KJ' AND f.accounttype = 'JY-A' 
UNION ALL

SELECT f.*, '费用/收单服务费' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='ZF'
UNION ALL
SELECT f.*, '费用/服务费扣减' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='SF' AND f.flowtype=1
UNION ALL
SELECT f.*, '费用/服务费扣减回滚' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='SF' AND f.flowtype=2
UNION ALL
SELECT f.*, '费用/服务费收取' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='SF' AND f.flowtype=1
UNION ALL
SELECT f.*, '费用/服务费收取回滚' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='SF' AND f.flowtype=2
UNION ALL
SELECT f.*, '费用/服务付费（鉴权、银行卡核验、OCR、CF、支付服务费）' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype IN('JQ','YHKHY','OCR','CF','PS')
UNION ALL
SELECT f.*, '费用/服务收费（鉴权、银行卡核验、OCR、CF、支付服务费）' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype IN('JQ','YHKHY','OCR','CF','PS')
UNION ALL
SELECT f.*, '费用/风险管理费' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='FXF'
UNION ALL


SELECT f.*, '调账/可用余额调增' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='TZ' AND f.accounttype = 'JY-A' AND f.balancetype=1 AND f.businesscode NOT IN('AcsRecharge')
UNION ALL
SELECT f.*, '调账/可用余额调减' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='TZ' AND f.accounttype = 'JY-A' AND f.balancetype=1 AND f.businesscode NOT IN('AcsRecharge')
UNION ALL
SELECT f.*, '调账/可用余额调增' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='TZ' AND f.accounttype = 'JY-A' AND f.balancetype=2 AND f.businesscode NOT IN('AcsRecharge')
UNION ALL
SELECT f.*, '调账/可用余额调减' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='TZ' AND f.accounttype = 'JY-A' AND f.balancetype=2 AND f.businesscode NOT IN('AcsRecharge')
UNION ALL


SELECT f.*, '开票/分润加额（分润计算）' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='FRJS' AND f.accounttype = 'XF-B' AND f.balancetype=1 
UNION ALL
SELECT f.*, '开票/分润加额回滚（分润计算）' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='FRJS' AND f.accounttype = 'XF-B' AND f.balancetype=2 
UNION ALL
SELECT f.*, '开票/分润抵票转入' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='FRDP' AND f.accounttype = 'XF-B' AND f.balancetype=1 
UNION ALL
SELECT f.*, '开票/分润抵票转入' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='FRDP' AND f.accounttype = 'JY-A' AND f.balancetype=1 
UNION ALL
SELECT f.*, '开票/分润结算减额' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='FRJS' AND f.accounttype = 'XF-B' AND f.balancetype=1 
UNION ALL
SELECT f.*, '开票/增开发票' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='ZKFP' AND f.accounttype = 'FP-B' AND f.balancetype=1 
UNION ALL
SELECT f.*, '开票/冲抵预开票余额' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='DYKP' AND f.accounttype = 'XF-B' AND f.balancetype=1 
UNION ALL
SELECT f.*, '开票/冲抵待开票金额' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='DDKP' AND f.accounttype = 'FP-B' AND f.balancetype=1 
UNION ALL

SELECT f.*, '分润/分润加额' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='FR' AND f.accounttype = 'JY-A' AND f.balancetype=1 
UNION ALL
SELECT f.*, '分润/分润减额？' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='FR' AND f.accounttype = 'JY-A' AND f.balancetype=1 
UNION ALL
SELECT f.*, '分润/补增分润差额' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='BZFR' AND f.accounttype = 'XF-B' AND f.balancetype=1 
UNION ALL
SELECT f.*, '分润/扣减分润差额' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='KJFR' AND f.accounttype = 'XF-B' AND f.balancetype=1 
UNION ALL
SELECT f.*, '分润/扣除分润' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='KCFR' AND f.accounttype = 'XF-B' AND f.balancetype=1 
UNION ALL
SELECT f.*, '分润/扣减税点' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='KJSD' AND f.accounttype = 'XF-B' AND f.balancetype=1 
UNION ALL
SELECT f.*, '分润/冻结分润（可用减额）' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='DJFR' AND f.accounttype = 'XF-B' AND f.balancetype=1 
UNION ALL
SELECT f.*, '分润/冻结分润（冻结加额）' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='DJFR' AND f.accounttype = 'XF-B' AND f.balancetype=3 
UNION ALL
SELECT f.*, '分润/解冻分润（冻结减额）' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='JDFR' AND f.accounttype = 'XF-B' AND f.balancetype=3
UNION ALL
SELECT f.*, '分润/解冻分润（可用加额）' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='JDFR' AND f.accounttype = 'XF-B' AND f.balancetype=1
UNION ALL

SELECT f.*, '结算/在途减额' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='SETT' AND f.accounttype = 'JY-A' AND f.balancetype=2 
UNION ALL
SELECT f.*, '结算/可用加额' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='SETT' AND f.accounttype = 'JY-A' AND f.balancetype=1 
UNION ALL

SELECT f.*, '销户/销户转出（商户已结算金额）' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='XHZZ' AND f.accounttype = 'JY-A' AND f.balancetype=1
UNION ALL
SELECT f.*, '销户/销户转出（商户待结算金额）' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='XHZZ' AND f.accounttype = 'JY-A' AND f.balancetype=2
UNION ALL
SELECT f.*, '销户/销户转出（商户待分账金额）' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='XHZZ' AND f.accounttype = 'BJ-B' AND f.balancetype=1
UNION ALL
SELECT f.*, '销户/销户转入（中间商户）' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='XHZZ' AND f.accounttype = 'JY-A' AND f.balancetype=1 
UNION ALL

SELECT f.*, '保证金/手动加额（缴费登记）' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='TZ' AND f.accounttype = 'BZ-B' AND f.balancetype=1 
UNION ALL
SELECT f.*, '保证金/手动减额（提款登记）' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='TZ' AND f.accounttype = 'BZ-B' AND f.balancetype=1 
UNION ALL
SELECT f.*, '保证金/扣款减额' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='TX' AND f.accounttype = 'BZ-B' AND f.balancetype=1 AND f.flowtype=1
UNION ALL
SELECT f.*, '保证金/扣款回滚' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='TX' AND f.accounttype = 'BZ-B' AND f.balancetype=1 AND f.flowtype=2
UNION ALL
SELECT f.*, '保证金/结算加额' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='SETT' AND f.accounttype = 'BZ-B' AND f.balancetype=1 
UNION ALL
SELECT f.*, '保证金/扣款减额（外循环保证金）' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='TZ' AND f.accounttype = 'XB-B' AND f.balancetype=1
UNION ALL
SELECT f.*, '保证金/结算加额（外循环保证金）' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='SETT' AND f.accounttype = 'XB-B' AND f.balancetype=1 
UNION ALL

SELECT f.*, '钱包/充值（平台商簿记加额）' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='CZ' AND f.accounttype = 'BJ-B' AND f.balancetype=1 
UNION ALL
SELECT f.*, '钱包/充值（平台商簿记减额）' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='CZ' AND f.accounttype = 'BJ-B' AND f.balancetype=1 
UNION ALL
SELECT f.*, '钱包/充值（扣取手续费）' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='CZ' AND f.accounttype = 'JY-A' AND f.balancetype=1 
UNION ALL
SELECT f.*, '钱包/充值（用户加额）' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='CZ' AND f.accounttype = 'JY-A' AND f.balancetype=1 
UNION ALL

SELECT f.*, '其他/交易可用调减' AS category FROM v_customer_accountflow f WHERE f.direction=-1 AND f.transactiontype='SETT' AND f.accounttype = 'JY-A' AND f.balancetype=1 
UNION ALL
SELECT f.*, '其他/交易在途调增' AS category FROM v_customer_accountflow f WHERE f.direction=1 AND f.transactiontype='SETT' AND f.accounttype = 'JY-A' AND f.balancetype=2
;

/*
--验证完整性
select * from v_customer_accountflow t where not exists (select 1 from v_customer_money_category c where t.customercode=c.CUSTOMERCODE and t.accountcode=c.ACCOUNTCODE and t.direction=c.DIRECTION and t.balancetype=c.BALANCETYPE and t.transactiontype=c.TRANSACTIONTYPE and t.flowtype=c.FLOWTYPE and t.businesscode=c.BUSINESSCODE) order by accounttype;
--验证分类
select t.ACCOUNTCODE,t.DIRECTION,t.ACCOUNTTYPE,t.BALANCETYPE,t.TRANSACTIONTYPE,t.FLOWTYPE,t.BUSINESSCODE,count(0) from v_customer_money_category t group by t.ACCOUNTCODE,t.DIRECTION,t.ACCOUNTTYPE,t.BALANCETYPE,t.TRANSACTIONTYPE,t.FLOWTYPE,t.BUSINESSCODE having count(0) > 1;
*/
