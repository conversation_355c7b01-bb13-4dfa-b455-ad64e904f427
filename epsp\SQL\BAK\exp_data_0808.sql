select * from tmp_cust_0808 ;
---------------OUT------------------------
create table tmp_out_0808 as
SELECT t.CUSTOMER_CODE AS 商户编号,t.C<PERSON><PERSON>MERNAME AS 商户名称, 
       t.BUSINESS_CODE  AS 业务代码, b.NAME AS 业务名称 ,
       sum(t.TOTAL_FEE) / 100  AS 总金额, sum(t.PROCEDURE_FEE) / 100 AS 总手续费,
       count(t.TRANSACTION_NO) AS 总笔数,to_char(max(t.CREATE_TIME),'yyyy-mm-dd hh24:mi:ss') AS 最后一笔时间
FROM epsp.TXS_WITHDRAW_TRADE_ORDER t 
  INNER JOIN tmp_cust_0808 c ON t.CUSTOMER_CODE =c.CUSTOMER_code
	LEFT JOIN epsp.PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE 
WHERE t.PAY_STATE ='00' AND t.CREATE_TIME >= timestamp '2021-01-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-01-01 00:00:00'
GROUP BY t.CUSTOMER_CODE ,t.CUSTOMERNAME,t.BUSINESS_CODE ,b.NAME ;
---------------PAY------------------------
create table tmp_pay_0808 as
SELECT t.CUSTOMER_CODE AS 商户号,t.CUSTOMERNAME AS 商户名称, 
       t.BUSINESS_CODE  AS 业务代码, b.NAME AS 业务名称 ,
       sum(t.AMOUNT) / 100 AS 总金额, sum(t.PROCEDURE_FEE) / 100 AS 总手续费, count(t.TRANSACTION_NO) AS 总笔数, to_char(max(t.CREATE_TIME),'yyyy-mm-dd hh24:mi:ss') AS 最后一笔时间
FROM epsp.TXS_PAY_TRADE_ORDER t 
	INNER JOIN info.tmp_cust_0808 c ON t.CUSTOMER_CODE =c.CUSTOMER_CODE
	LEFT JOIN epsp.PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE 
WHERE t.STATE ='00' AND t.CREATE_TIME >= timestamp '2021-01-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-01-01 00:00:00'
GROUP BY t.CUSTOMER_CODE ,t.CUSTOMERNAME,t.BUSINESS_CODE ,b.NAME ;
-------------------POS-----------------------
create table tmp_pos_0808 as
select MERCHANT_NO, a.name, sum(AMOUNT) as total_amount, 0 as fee, count(1) as cnt
from epsp.ZHY_POSP_RECORD t inner join  tmp_cust_0808 a on t.merchant_no=a.customer_code
where CREATION_TIME >= timestamp '2021-01-01 00:00:00'
  and CREATION_TIME < timestamp '2022-01-01 00:00:00'
group by MERCHANT_NO,a.name;
-------------------ACS----------------------
create table tmp_acs_0808 as
SELECT t.CUSTOMER_CODE AS 商户号,c.name ,sum(t.AMOUNT) / 100 AS 总金额, sum(t.PROCEDURE_FEE) / 100 AS 总手续费,count(t.TRANSACTION_NO) AS 总笔数
FROM epsp.PAS_ACCT_QUOTA_RECORD t
	INNER JOIN info.tmp_cust_0808 c ON t.CUSTOMER_CODE =c.CUSTOMER_CODE
WHERE t.FUND_TYPE ='1' AND t.ACCT_STATE ='00' AND t.CREATE_TIME >= timestamp '2021-01-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-01-01 00:00:00'
GROUP BY t.CUSTOMER_CODE,c.name;
-------------------XX----------------------
create table tmp_xx_0808
select t.customer_code,t.customer_name,sum(t.amt) / 100 as total_amount, sum(t.procedure_fee) as total_fee, count(0)
from epsp.chk_xx_tran_record t 
     inner join tmp_cust_0808 c on t.customer_code=c.customer_code
where t.create_time > timestamp '2021-01-01 00:00:00' and t.create_time < timestamp '2022-01-01 00:00:00'
group by t.customer_code,t.customer_name;
-------------------FZ----------------------
create table tmp_fz_0808 as
select t.source_customer_code,t.source_customername,sum(t.amount) / 100 as total_amount, count(0) as cnt
from epsp.txs_split_record t 
     inner join  tmp_cust_0808 c on t.source_customer_code=c.customer_code 
where t.create_time > timestamp '2021-01-01 00:00:00' and t.create_time < timestamp '2022-01-01 00:00:00'  
      and t.state='3'
      and not exists(select 1 from tmp_cust_0808 a where t.customer_code=a.customer_code)
group by t.source_customer_code,t.source_customername;

--=========================--
select * from tmp_total_080901;
--insert into tmp_pay_0808
select t.customer_code,t.customer_name,'XX','XX',t.total_amount,t.fee,t.cnt,null from tmp_xx_0808 t;
--insert into tmp_pay_0808
select t.商户号,t.name,'ACS','ACS',t.总金额,t.总手续费,t.总笔数,null from tmp_acs_0808 t;

select * from tmp_cust_0808 where customer_code not in(select 商户编号 from tmp_out_0808)
select * from tmp_out_0808;
select * from tmp_pos_0808;
select * from tmp_pay_0808;
select * from tmp_bus_0808;
select * from tmp_fz_0808;

--insert into tmp_pay_0808
--select t.source_customer_code,t.source_customername,'FZ','FZ',t.total_amount,0,t.cnt,null from tmp_fz_0808 t;

insert into tmp_bus_0808
select distinct t.业务代码,t.业务名称 from  tmp_pay_0808 t where t.业务代码 not in(select 业务代码 from tmp_bus_0808);



select 'SUM(case when 业务代码=' ||''''||业务代码||'''' || ' then 总金额 else 0 end) as ' || name || ' , '
       || 'SUM(case when 业务代码=' ||''''||业务代码||'''' || ' then 总笔数 else 0 end) as ' ||name || ' , '  from tmp_bus_0808;
       
select sum(t.总笔数),sum(t.总金额) from tmp_pay_0808 t where t.业务代码 in('Withdraw','WithdrawToSettmentDebit','FZ');
select sum(t.总笔数),sum(t.总金额) from tmp_pay_0808 t where not t.业务代码 in('Withdraw','WithdrawToSettmentDebit','FZ');
