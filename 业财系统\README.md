# DBLINK建设

因业财系统DBLINK epsp使用了EPSP系统的info账号，info账号被锁，需要执行如下处理：
1、	EPSP库中建立新账号：ycinfo
2、	对ycinfo账号对如下的表/视图授权select权限；
v_chk_business_jingying_report
v_customer_all
3、	重建业财系统DBLINK epsp；

# 业财系统常见系统运维支撑

业财系统依赖EPSP经营报表数据，可能出现，业财系统同步数据定时任务已经启动，但是EPSP经营数据尚未生成完成（已经有告警）
1、	检查指定日期数据是否存在，下面SQL查询到有数据即是同步正确（在业财系统数据库中执行）
select * from mb_db.chk_business_jingying_report where task_date = to_date('20250303','yyyymmdd') + 1/2;
2、	如果Step1查询无数据，执行如下脚本，完成数据同步（在业财系统数据库中执行）：
begin
  p_sync_business_jingying('20250303');
end;

业财数据库：运营库(opdb)，用户：mb_db
