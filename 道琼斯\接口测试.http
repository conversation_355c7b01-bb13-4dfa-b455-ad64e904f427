###二、Risk & Compliance Search API（风险与合规性搜索API）
post https://api.dowjones.com/riskentities/search
Content-Type: application/vnd.dowjones.dna.riskentities.v_2.2+json
Accept: application/vnd.dowjones.dna.riskentities.v_2.2+json
Authorization: Bearer <token>

{
    "data": {
    "type": "RiskEntitySearch",
        "attributes": {
            "paging": {
                "offset": 0,
                "limit": 20
            },
            "sort": null,
            "filter_group_and": {
                "filters": {
                    "content_set": [
                        "WatchList",
                        "StateOwnedCompanies",
                        "AdverseMedia"
                    ],
                    "record_types": [
                        "Person"
                    ],
                    "search_keyword": {
                        "scope": [
                            "Name"
                        ],
                        "text": "<PERSON>",
                        "type": "Precise"
                    }
                },
                "group_operator": "And"
            },
            "filter_group_or": {
                "filters": {
                    "sanctions_list": {
                        "is_all_excluded": false,
                        "operator": "OR"
                    },
                    "content_category": {
                        "special_interest": {
                            "is_all_excluded": false,
                            "operator": "OR"
                        },
                        "adverse_media": {
                            "is_all_excluded": false,
                            "operator": "OR"
                        },
                        "location": {
                            "is_all_excluded": false,
                            "operator": "OR"
                        }
                    },
                    "other_official_list": {
                        "is_all_excluded": false,
                        "operator": "OR"
                    },
                    "other_exclusion_list": {
                        "is_all_excluded": false,
                        "operator": "OR"
                    },
                    "state_ownership": {
                        "is_all_excluded": false
                    },
                    "occupation_category": {
                        "is_all_excluded": false,
                        "operator": "Or"
                    },
                    "hrf_category": {
                        "is_all_excluded": false,
                        "operator": "OR"
                    }
                },
                "group_operator": "Or"
            }
        }
    }
}


###三、Risk & Compliance Profiles API（风险与合规概况API）——获取档案信息
get https://api.dowjones.com/riskentities/profiles/000000
Content-Type: application/vnd.dowjones.dna.riskentities.v_2.2+json
Accept: application/vnd.dowjones.dna.riskentities.v_2.2+json
Authorization: Bearer <token>

###三、Risk & Compliance Profiles API（风险与合规概况API）——获取档案关联信息(parts/watchlist)
get https://api.dowjones.com/riskentities/profiles/000000?parts=basic
Content-Type: application/vnd.dowjones.dna.riskentities.v_2.2+json
Accept: application/vnd.dowjones.dna.riskentities.v_2.2+json
Authorization: Bearer <token>

###四、Risk & Compliance Taxonomy API（风险与合规分类API）
get https://api.dowjones.com/taxonomy/riskentities/lists?id=CountryTerritory
Content-Type: application/vnd.dowjones.dna.riskentities.v_2.2+json
Accept: application/vnd.dowjones.dna.riskentities.v_2.2+json
Authorization: Bearer <token>