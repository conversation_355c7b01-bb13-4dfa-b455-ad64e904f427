﻿select
  ID, TRANSACTION_NO, CUSTOMER_CODE, STATE, AMOUNT, PROCEDUREFEE,PAY_CASH_AMOUNT,OUT_TRADE_NO,ORIG_AMOUNT,PAY_AMOUNT,COUPON_AMOUNT,
  CREATE_TIME,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Y<PERSON><PERSON>SACTION_NO,BUSINESS_INST_ID, PAY_AMOUNT, PAY_PROCEDUREFEE,SPLIT_RATIO,SPLIT_PROCEDURE_FEE,SPLIT_ATTR,
  SPLIT_MODEL,BUSINESS_MAN,BUSINESS_MAN_ID,COMPANY_NAME,COMPANY_ID, CASH_AMOUNT,COUPON_AMOUNT,REVOKE_TRANSACTION_NO,
  REFUND_FEE,REFUNDING_FEE,OUT_SPLIT_TRADE_NO,SETTLE_CYCLE,SETTLE_STATE,TRANSACTION_TYPE,SOURCE_CUSTOMER_CODE,SOURCE_CUSTOMERNAME, SETTLE_CYCLE_TYPE

from (
  select A.*, rownum RN
  from (
  select  /*+index(TXS_SPLIT_RECORD CREATE_TIME)*/  * from TXS_SPLIT_RECORD
   WHERE  CREATE_TIME  >= :1 AND CREATE_TIME  <=  :2
      AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=TXS_SPLIT_RECORD.CUSTOMER_CODE
                        and instr(T1.customer_path, :3) >0)
  order by CREATE_TIME desc
  ) A
  where rownum <= :4
  )
  where RN >= :5
;
-----------------------------------------------------

[oracle@epspdb1 scripts]$ ./ora sqlt 5qtuhcxwjxx4k
sqlt 75npxvkkkp1q5
./ora sqlt 9qvy2wft4hayx
./ora sqlt 548uxhgjh9pxk
./ora sqlt 5wx0a6vhdykts
./ora sqlt 4a14v52y9hjbb
./ora sqlt b06snkc60xy70
./ora sqlt 1rfupjzrp3msk
./ora sqlt 988w52bg3hgd7
./ora sqlt 0rxv8bbc89j5jSQL Text:

select /*+parallel(4)*/ * from (
  select A.*, rownum RN from (
  select

    CREATE_TIME, END_TIME,OUT_TRADE_NO,TRANSACTION_NO, BUSINESS_CODE, STATE,AMOUNT,CASH_AMOUNT,COUPON_AMOUNT,
    TXS_PAY_TRADE_ORDER.CUSTOMER_CODE, TERMINAL_NO, TRANSACTION_TYPE, TERMINAL_NAME, CUSTOMERNAME, REFUNDING_FEE,
    REFUND_FEE, IS_ORDER_SPLIT, DELAY_DAYS, PAYER_CODE, PAYER_NAME, SPLIT_MODEL,TICKET_AMOUNT,PAYER_AMOUNT,PROCEDURE_FEE
  

  from TXS_PAY_TRADE_ORDER
   WHERE  CREATE_TIME  >= :1 AND CREATE_TIME  < :2 AND CUSTOMER_CODE = :3 AND STATE = :4
  AND TRANSACTION_TYPE != 'ZHFZ'
  order by CREATE_TIME desc
  ) A
    where rownum <= :5
  )
  where RN >= :6

select COALESCE(SUM(AMOUNT),0) total_amount, COALESCE(SUM(PROCEDURE_FEE),0) total_procedure_fee, count(ORDER_ID) total
    from ((select * FROM TXS_PRE_ORDER
    WHERE 1=1 AND CREATE_TIME  >=  :1 AND CREATE_TIME  < :2
    AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=TXS_PRE_ORDER.CUSTOMER_CODE
    and instr(T1.customer_path, :3) >0) ) tmp
    INNER JOIN CUST_TERMINAL ct ON ct.TERMINAL_CODE = tmp.TERMINAL_NO)


[oracle@epspdb1 scripts]$ ./ora sqlt fchd614uxx39m
SQL Text:
select group_number,name,state,type,round(total_mb/1024,2) total_gb,round((total_mb-free_mb)/1024,2) use_gb,round(free_mb/1024,2) free_gb from v$asm_diskgroup

[oracle@epspdb1 scripts]$ ./ora sqlt 75npxvkkkp1q5
SQL Text:
select * from (
  SELECT a.*,rownum rn
  FROM (
  SELECT
  ID,CLEAR_DATE,CUSTOMER_CODE,CUSTOMER_NAME, AMOUNT, PROCEDURE_FEE,SETT_AMOUNT, CASH_AMOUNT, COUPON_AMOUNT,
  IP_PROCEDURE_FEE
  FROM report_sett_record
   WHERE 1 = 1

      AND CLEAR_DATE >= :1


      AND CLEAR_DATE < :2












      AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=report_sett_record.CUSTOMER_CODE
      and instr(T1.customer_path, :3) >0)
  ORDER BY ID ASC
  )a WHERE rownum <= :4
  ) where rn >= :5


[oracle@epspdb1 scripts]$ ./ora sqlt 988w52bg3hgd7
SQL Text:
select  /*+index(TXS_SPLIT_RECORD CREATE_TIME)*/  1
  from TXS_SPLIT_RECORD
   WHERE  CREATE_TIME  >=
      :1


      AND CREATE_TIME  <=
      :2
















      AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=TXS_SPLIT_RECORD.CUSTOMER_CODE
      and instr(T1.customer_path, :3) >0)


[oracle@epspdb1 scripts]$ ./ora sqlt 9qvy2wft4hayx
SQL Text:
select count(*)
  from report_sett_record
   WHERE 1 = 1

      AND CLEAR_DATE >= :1


      AND CLEAR_DATE < :2












      AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=report_sett_record.CUSTOMER_CODE
      and instr(T1.customer_path, :3) >0)


[oracle@epspdb1 scripts]$ ./ora sqlt 25xuxafjqs0mx
SQL Text:
select A.*
    from (
        select
          t.*
          from TXS_SPLIT_ORDER t left join txs_pre_order a on a.out_trade_no=t.out_trade_no and a.CUSTOMER_CODE=t.CUSTOMER_CODE
       where
    t.create_time BETWEEN :1 and sysdate - :2/1440
is null))) and a.pay_state = '1' and NVL(a.REFUNDING_FEE , 0) + NVL(a.REFUND_FEE , 0) = 0
    and a.SETTLEMENT_STATE = '00'
          and exists (
          select c.customer_no from cust_customer c  where c.status != 4 and c.customer_no=t.customer_code
          )
        AND state != '00'
        order by t.create_time desc
    ) A
    where rownum <= :3


[oracle@epspdb1 scripts]$ ./ora sqlt 4a14v52y9hjbb
SQL Text:
select count(*) as "total",
  COALESCE(SUM(amount),0) as "amount",
  COALESCE(SUM(procedure_fee),0) as "procedureFee",
  COALESCE(SUM(channel_cost),0) as "channelCost",
  COALESCE(SUM(sett_amount),0) as "settAmount",
  COALESCE(SUM(cash_amount),0) as "cashAmount",
  COALESCE(SUM(coupon_amount),0) as "couponAmount",
  COALESCE(SUM(ip_procedure_fee),0) as "ipProcedureFee",
  COALESCE(SUM(ip_merchant_cost),0) as "ipMerchantCost"
  from report_sett_record
   WHERE 1 = 1

      AND CLEAR_DATE >= :1


      AND CLEAR_DATE < :2












      AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=report_sett_record.CUSTOMER_CODE
      and instr(T1.customer_path, :3) >0)


[oracle@epspdb1 scripts]$ ./ora sqlt 1t54nq7rsfrqz
SQL Text:
select COALESCE(SUM(AMOUNT),0) total_amount, COALESCE(SUM(PROCEDURE_FEE),0) total_procedure_fee, count(ORDER_ID) total
    from ((select * FROM TXS_PRE_ORDER
       WHERE 1=1
  
    AND CREATE_TIME  >=
    :1
  
  
    AND CREATE_TIME  <
    :2
  
  
  
  
    AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=TXS_PRE_ORDER.CUSTOMER_CODE
    and instr(T1.customer_path, :3) >0) ) tmp
    INNER JOIN CUST_TERMINAL ct ON ct.TERMINAL_CODE = tmp.TERMINAL_NO)


[oracle@epspdb1 scripts]$ ./ora sqlt 69cnp9sgmsvmf
SQL Text:
select /*+ parallel(4) */
  COALESCE(SUM(AMOUNT),0) total_amount, COALESCE(SUM(PROCEDURE_FEE),0) total_procedure_fee, count(*)
  total,
  COALESCE(SUM(IP_PROCEDURE_FEE),0) total_ip_procedure_fee, COALESCE(SUM(CASH_AMOUNT),0) total_cash_amount
  from TXS_PAY_TRADE_ORDER
   WHERE  CREATE_TIME  >=
      :1


      AND CREATE_TIME  <
      :2








      AND STATE = :3


















  AND TRANSACTION_TYPE != 'ZHFZ'



      AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=TXS_PAY_TRADE_ORDER.CUSTOMER_CODE
      and instr(T1.customer_path, :4) >0)


[oracle@epspdb1 scripts]$ ./ora sqlt 75npxvkkkp1q5
SQL Text:
select * from (
  SELECT a.*,rownum rn
  FROM (
  SELECT
  ID,CLEAR_DATE,CUSTOMER_CODE,CUSTOMER_NAME, AMOUNT, PROCEDURE_FEE,SETT_AMOUNT, CASH_AMOUNT, COUPON_AMOUNT,
  IP_PROCEDURE_FEE
  FROM report_sett_record
   WHERE 1 = 1

      AND CLEAR_DATE >= :1


      AND CLEAR_DATE < :2












      AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=report_sett_record.CUSTOMER_CODE
      and instr(T1.customer_path, :3) >0)
  ORDER BY ID ASC
  )a WHERE rownum <= :4
  ) where rn >= :5


[oracle@epspdb1 scripts]$ ./ora sqlt 9qvy2wft4hayx
SQL Text:
select count(*)
  from report_sett_record
   WHERE 1 = 1

      AND CLEAR_DATE >= :1


      AND CLEAR_DATE < :2












      AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=report_sett_record.CUSTOMER_CODE
      and instr(T1.customer_path, :3) >0)


[oracle@epspdb1 scripts]$ ./ora sqlt 548uxhgjh9pxk
SQL Text:
select /*+parallel(4)*/ * from (
  select A.*, rownum RN from (
  select

    CREATE_TIME, END_TIME,OUT_TRADE_NO,TRANSACTION_NO, BUSINESS_CODE, STATE,AMOUNT,CASH_AMOUNT,COUPON_AMOUNT,
    TXS_PAY_TRADE_ORDER.CUSTOMER_CODE, TERMINAL_NO, TRANSACTION_TYPE, TERMINAL_NAME, CUSTOMERNAME, REFUNDING_FEE,
    REFUND_FEE, IS_ORDER_SPLIT, DELAY_DAYS, PAYER_CODE, PAYER_NAME, SPLIT_MODEL,TICKET_AMOUNT,PAYER_AMOUNT,PROCEDURE_FEE
  

  from TXS_PAY_TRADE_ORDER
   WHERE  CREATE_TIME  >=
      :1


      AND CREATE_TIME  <
      :2








      AND STATE = :3


















  AND TRANSACTION_TYPE != 'ZHFZ'



      AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=TXS_PAY_TRADE_ORDER.CUSTOMER_CODE
      and instr(T1.customer_path, :4) >0)
  order by CREATE_TIME desc
  ) A
    where rownum <= :5
  )
  where RN >= :6


[oracle@epspdb1 scripts]$ ./ora sqlt 5wx0a6vhdykts
SQL Text:
select  /*+index(TXS_SPLIT_RECORD CREATE_TIME)*/
  COALESCE(SUM(amount),0) total_amount, COALESCE(SUM(SPLIT_PROCEDURE_FEE),0) total_fz_procedure_fee,COALESCE(SUM(PAY_PROCEDUREFEE),0) totalIpProcedureFee,
  COALESCE(SUM(ORIG_AMOUNT),0) total_fz_amount, COALESCE(SUM(procedurefee),0) total_bfz_procedure_fee, count(1) total
  from TXS_SPLIT_RECORD
   WHERE  CREATE_TIME  >=
      :1


      AND CREATE_TIME  <=
      :2
















      AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=TXS_SPLIT_RECORD.CUSTOMER_CODE
      and instr(T1.customer_path, :3) >0)


[oracle@epspdb1 scripts]$ ./ora sqlt 4a14v52y9hjbb
SQL Text:
select count(*) as "total",
  COALESCE(SUM(amount),0) as "amount",
  COALESCE(SUM(procedure_fee),0) as "procedureFee",
  COALESCE(SUM(channel_cost),0) as "channelCost",
  COALESCE(SUM(sett_amount),0) as "settAmount",
  COALESCE(SUM(cash_amount),0) as "cashAmount",
  COALESCE(SUM(coupon_amount),0) as "couponAmount",
  COALESCE(SUM(ip_procedure_fee),0) as "ipProcedureFee",
  COALESCE(SUM(ip_merchant_cost),0) as "ipMerchantCost"
  from report_sett_record
   WHERE 1 = 1

      AND CLEAR_DATE >= :1


      AND CLEAR_DATE < :2












      AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=report_sett_record.CUSTOMER_CODE
      and instr(T1.customer_path, :3) >0)


[oracle@epspdb1 scripts]$ ./ora sqlt b06snkc60xy70
SQL Text:
select /*+parallel(4)*/ * from (
  select A.*, rownum RN from (
  select

    CREATE_TIME, END_TIME,OUT_TRADE_NO,TRANSACTION_NO, BUSINESS_CODE, STATE,AMOUNT,CASH_AMOUNT,COUPON_AMOUNT,
    TXS_PAY_TRADE_ORDER.CUSTOMER_CODE, TERMINAL_NO, TRANSACTION_TYPE, TERMINAL_NAME, CUSTOMERNAME, REFUNDING_FEE,
    REFUND_FEE, IS_ORDER_SPLIT, DELAY_DAYS, PAYER_CODE, PAYER_NAME, SPLIT_MODEL,TICKET_AMOUNT,PAYER_AMOUNT,PROCEDURE_FEE
  

  from TXS_PAY_TRADE_ORDER
   WHERE  CREATE_TIME  >=
      :1


      AND CREATE_TIME  <
      :2

























  AND TRANSACTION_TYPE != 'ZHFZ'



      AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=TXS_PAY_TRADE_ORDER.CUSTOMER_CODE
      and instr(T1.customer_path, :3) >0)
  order by CREATE_TIME desc
  ) A
    where rownum <= :4
  )
  where RN >= :5


[oracle@epspdb1 scripts]$ ./ora sqlt 1rfupjzrp3msk
SQL Text:
select

  ID, TRANSACTION_NO, CUSTOMER_CODE, STATE, AMOUNT, PROCEDUREFEE,PAY_CASH_AMOUNT,OUT_TRADE_NO,ORIG_AMOUNT,PAY_AMOUNT,COUPON_AMOUNT,
  CREATE_TIME,CUSTOMERNAME, PAYTRANSACTION_NO,BUSINESS_INST_ID, PAY_AMOUNT, PAY_PROCEDUREFEE,SPLIT_RATIO,SPLIT_PROCEDURE_FEE,SPLIT_ATTR,
  SPLIT_MODEL,BUSINESS_MAN,BUSINESS_MAN_ID,COMPANY_NAME,COMPANY_ID, CASH_AMOUNT,COUPON_AMOUNT,REVOKE_TRANSACTION_NO,
  REFUND_FEE,REFUNDING_FEE,OUT_SPLIT_TRADE_NO,SETTLE_CYCLE,SETTLE_STATE,TRANSACTION_TYPE,SOURCE_CUSTOMER_CODE,SOURCE_CUSTOMERNAME, SETTLE_CYCLE_TYPE

  from (
  select A.*, rownum RN
  from (
  select  /*+index(TXS_SPLIT_RECORD CREATE_TIME)*/  * from TXS_SPLIT_RECORD
   WHERE  CREATE_TIME  >=
      :1


      AND CREATE_TIME  <=
      :2
















      AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=TXS_SPLIT_RECORD.CUSTOMER_CODE
      and instr(T1.customer_path, :3) >0)
  order by CREATE_TIME desc
  ) A
  where rownum <= :4
  )
  where RN >=
  :5


[oracle@epspdb1 scripts]$ ./ora sqlt 988w52bg3hgd7
SQL Text:
select  /*+index(TXS_SPLIT_RECORD CREATE_TIME)*/  1
  from TXS_SPLIT_RECORD
   WHERE  CREATE_TIME  >=
      :1


      AND CREATE_TIME  <=
      :2
















      AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=TXS_SPLIT_RECORD.CUSTOMER_CODE
      and instr(T1.customer_path, :3) >0)


[oracle@epspdb1 scripts]$ ./ora sqlt 0rxv8bbc89j5j
SQL Text:
select /*+ parallel(4) */
  COALESCE(SUM(AMOUNT),0) total_amount, COALESCE(SUM(PROCEDURE_FEE),0) total_procedure_fee, count(*)
  total,
  COALESCE(SUM(IP_PROCEDURE_FEE),0) total_ip_procedure_fee, COALESCE(SUM(CASH_AMOUNT),0) total_cash_amount
  from TXS_PAY_TRADE_ORDER
   WHERE  CREATE_TIME  >=
      :1


      AND CREATE_TIME  <
      :2

























  AND TRANSACTION_TYPE != 'ZHFZ'



      AND EXISTS(select 1 from CUM_CUSTOMER_INFO t1 WHERE T1.CUSTOMER_CODE=TXS_PAY_TRADE_ORDER.CUSTOMER_CODE
      and instr(T1.customer_path, :3) >0)


