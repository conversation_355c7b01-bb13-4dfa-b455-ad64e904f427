import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression

# 假设您有一年的历史数据，包括交易量、收款笔数、付款次数和入网商户数量
# 请替换下面的示例数据为您的实际数据
data = {
    '月份': np.arange(1, 13),
    '交易量': [1.08, 1.09, 1.10, 1.11, 1.12, 1.13, 1.14, 1.15, 1.16, 1.17, 1.18, 1.19],  # 示例数据，单位：亿人民币
    '收款笔数': [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4],  # 示例数据，每月每客户收款笔数
    '付款次数': [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6],  # 示例数据，每月每客户付款次数
    '入网商户数量': [103, 206, 309, 412, 515, 618, 721, 824, 927, 1030, 1133, 1236]  # 示例数据，入网商户数量
}

# 将数据转换为 DataFrame 格式
df = pd.DataFrame(data)

# 准备回归所需的特征矩阵 X 和目标变量 y
X = df[['月份']]  # 使用月份作为特征
y = df[['交易量', '收款笔数', '付款次数', '入网商户数量']]  # 交易量、收款笔数、付款次数和入网商户数量作为目标变量

# 初始化线性回归模型
model = LinearRegression()

# 拟合模型
model.fit(X, y)

# 进行预测
future_months = np.arange(13, 25).reshape(-1, 1)  # 假设预测未来12个月的数据
predictions = model.predict(future_months)

# 打印预测结果
future_data = pd.DataFrame(predictions, columns=['交易量', '收款笔数', '付款次数', '入网商户数量'])
future_data['月份'] = np.arange(13, 25)
print(future_data)