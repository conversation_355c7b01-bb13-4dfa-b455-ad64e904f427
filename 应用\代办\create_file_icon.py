from PIL import Image, ImageDraw, ImageFont
import os

# 创建res目录
res_dir = os.path.join(os.path.dirname(__file__), "res")
os.makedirs(res_dir, exist_ok=True)

# 创建默认文件图标
def create_file_icon(name, color=(200, 200, 200)):
    img = Image.new('RGBA', (64, 64), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制文件形状
    draw.rectangle([(10, 5), (54, 59)], fill=color, outline=(100, 100, 100))
    draw.polygon([(10, 5), (44, 5), (54, 15), (54, 59), (10, 59)], fill=color, outline=(100, 100, 100))
    draw.polygon([(44, 5), (54, 15), (44, 15)], fill=color, outline=(100, 100, 100))
    
    # 保存图标
    img.save(os.path.join(res_dir, name))
    print(f"Created {name}")

# 创建默认文件图标
create_file_icon("file.png")

# 创建特定类型的图标
type_colors = {
    "text.png": (220, 220, 255),
    "word.png": (200, 220, 255),
    "excel.png": (200, 255, 200),
    "powerpoint.png": (255, 220, 200),
    "pdf.png": (255, 200, 200),
    "image.png": (220, 255, 220),
    "audio.png": (255, 255, 200),
    "video.png": (255, 220, 255),
    "zip.png": (220, 220, 220),
    "exe.png": (200, 200, 255),
    "python.png": (200, 255, 255),
    "html.png": (255, 220, 200),
    "css.png": (200, 220, 255),
    "js.png": (255, 255, 200),
}

for name, color in type_colors.items():
    create_file_icon(name, color)

print("All icons created successfully!")
