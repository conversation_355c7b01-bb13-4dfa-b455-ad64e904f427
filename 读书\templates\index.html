<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{ title }}</title>
    <style>
        .book-title {
            width: 380px;
            display: inline-block;
        }
        .read-link,.download-link {
            width: 50px;
            display: inline-block;
            text-align: right;
        }
        .chapter-count {
            width: 85px;
            display: inline-block;
            text-align: right;
        }
        .book-list {
            display: inline-block;
            width: 49%;
            vertical-align: top;
        }
        .body_style {
            padding: 0 20px; 
            font-size: 20px; 
            line-height: 30px;
            background-color: #a0a0a0;
        }
        input[type="text"] {
            width: 200px; /* 设置输入框宽度为300px */
        }
    </style>
</head>
<body class="body_style">
    <h1>我的书单
        <form action="/index/1/{{ page_size }}" method="get" style="display: inline;">
            <input type="text" name="q" style="display: inline;" placeholder="书名" value="{{ q }}">
            <input type="submit" value="搜索" style="display: inline;">
            <input type="reset" style="display: inline;" onclick="window.location.href='/index/1/{{ page_size }}'">
        </form>
    </h1>
    <div class="book-list">
        <ul>
        {% for book in books %}
            <li>
                <span class="book-title" href="/book/{{ book[1] }}" target="_blank">{{ book[0] }} </span> &nbsp;
                <span class="chapter-count">({{ book[2] }}章)</span>&nbsp;
                <a class="read-link" href="/book/{{ book[1] }}" target="_blank">阅读</a>&nbsp;
                <a class="download-link" href="/download/{{ book[1] }}">下载</a>&nbsp;
            </li>
        {% endfor %}
        </ul>
    </div>

    <div class="book-list">
        <ul>
        {% for book in books2 %}
            <li>
                <span class="book-title" href="/book/{{ book[1] }}" target="_blank">{{ book[0] }}  </span> &nbsp;
                <span class="chapter-count">({{ book[2] }}章)</span>&nbsp;
                <a class="read-link" href="/book/{{ book[1] }}" target="_blank">阅读</a>&nbsp;
                <a class="download-link" href="/download/{{ book[1] }}">下载</a>&nbsp;
            </li>
        {% endfor %}
        </ul>
    </div>

    <a href="/index/{{ page-1 }}/{{ page_size}}?q={{ q }}">&lt;&lt;上一页</a>
    &nbsp;&nbsp;
    <a href="/index/{{ page+1 }}/{{ page_size}}?q={{ q }}">下一页&gt;&gt;</a>

</body>
</html>
