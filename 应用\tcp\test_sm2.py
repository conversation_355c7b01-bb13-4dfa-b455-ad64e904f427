#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SM2功能测试脚本
用于测试gmssl库的SM2功能是否正常工作
"""

import sys
import os

def test_gmssl_import():
    """测试gmssl库导入"""
    print("=== 测试gmssl库导入 ===")
    try:
        import gmssl
        print("✓ gmssl库导入成功")
        
        # 尝试导入SM2相关模块
        from gmssl.sm2 import CryptSM2
        print("✓ SM2模块导入成功")
        
        return True
    except ImportError as e:
        print(f"✗ gmssl库导入失败: {e}")
        return False

def test_sm2_basic():
    """测试SM2基本功能"""
    print("\n=== 测试SM2基本功能 ===")
    try:
        from gmssl.sm2 import CryptSM2
        
        # 生成密钥对（用于测试）
        print("正在生成SM2密钥对...")
        
        # 注意：这里需要根据实际的gmssl版本调整
        # 不同版本的gmssl可能有不同的API
        
        # 创建SM2实例（使用空密钥进行测试）
        try:
            sm2_crypt = CryptSM2(public_key="", private_key="")
            print("✓ SM2对象创建成功（使用空密钥）")
        except Exception as e:
            print(f"✗ SM2对象创建失败: {e}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ SM2基本功能测试失败: {e}")
        return False

def test_sm3_hash():
    """测试SM3哈希功能"""
    print("\n=== 测试SM3哈希功能 ===")
    try:
        from gmssl import sm3, func
        
        # 测试SM3哈希
        test_data = "Hello, SM3!"
        data_bytes = test_data.encode('utf-8')
        data_list = func.bytes_to_list(data_bytes)
        
        hash_result = sm3.sm3_hash(data_list)
        print(f"✓ SM3哈希计算成功")
        print(f"  输入: {test_data}")
        print(f"  哈希: {hash_result}")
        
        return True
    except Exception as e:
        print(f"✗ SM3哈希功能测试失败: {e}")
        return False

def test_config_loading():
    """测试配置文件加载"""
    print("\n=== 测试配置文件加载 ===")
    try:
        from sm2_config import SERVER_CONFIG, CERT_CONFIG, SM2_CONFIG, get_sm2_keys, DEBUG_CONFIG
        
        print("✓ 配置文件加载成功")
        print(f"  服务器配置: {SERVER_CONFIG}")
        print(f"  SM2加密启用: {SM2_CONFIG.get('enable_sm2_encryption', False)}")
        
        # 测试密钥获取
        public_key, private_key = get_sm2_keys()
        if public_key and private_key:
            print("✓ SM2密钥配置存在")
            print(f"  公钥长度: {len(public_key)} 字符")
            print(f"  私钥长度: {len(private_key)} 字符")
        else:
            print("! SM2密钥未配置或为空")
        
        return True
    except ImportError as e:
        print(f"✗ 配置文件加载失败: {e}")
        return False

def test_certificate_loading():
    """测试证书加载"""
    print("\n=== 测试证书加载 ===")
    try:
        from sm2_config import CERT_CONFIG
        
        cert_path = CERT_CONFIG.get('sm2_cert_path', '')
        if os.path.exists(cert_path):
            print(f"✓ 证书文件存在: {cert_path}")
            
            with open(cert_path, 'r', encoding='utf-8') as f:
                cert_content = f.read()
            
            if '-----BEGIN CERTIFICATE-----' in cert_content:
                print("✓ 证书格式正确（PEM格式）")
            else:
                print("! 证书格式可能不正确")
            
            return True
        else:
            print(f"! 证书文件不存在: {cert_path}")
            return False
    except Exception as e:
        print(f"✗ 证书加载测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("SM2功能测试开始")
    print("=" * 50)
    
    tests = [
        test_gmssl_import,
        test_sm2_basic,
        test_sm3_hash,
        test_config_loading,
        test_certificate_loading
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过，SM2功能正常")
        return 0
    else:
        print("! 部分测试失败，请检查配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())
