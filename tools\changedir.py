import os

def remove_dirs_with_string(directory, string):
    for root, dirs, files in os.walk(directory, topdown=False):
        for name in dirs[:]:
            if string in name:
                full_path = os.path.join(root, name)
                for root2, dirs2, files2 in os.walk(full_path):
                    for file in files2:
                        os.remove(os.path.join(root2, file))
                os.rmdir(full_path)

# 指定目录路径
directory_path = r'E:\.AV\Photo\抓图'

# 要删除的字符
string_to_remove = '-今日头条'

remove_dirs_with_string(directory_path, string_to_remove)