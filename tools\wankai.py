
def read_large_file(file_path, from_row, to_row):
    cur_row = 0
    with open(file_path, "r") as f:
        while True:
            line = f.readline()
            cur_row += 1
            if cur_row < from_row:
                continue
            if cur_row > to_row:
                break
            print(line)
            
if __name__ == "__main__":
    read_large_file("F:\SBSTMP\CCPS_CREDITINFO.csv", 0, 10)