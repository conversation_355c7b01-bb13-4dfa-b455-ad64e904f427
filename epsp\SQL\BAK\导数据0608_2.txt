--线上收单交易
SELECT /*+parallel(5)*/ t.CUSTOMER_CODE AS 商户编号 ,t.C<PERSON>TOMERNAME AS 商户名称 ,
t.BUSINESS_CODE AS 业务代码 ,b.NAME AS 业务名称,
t.OUT_TRADE_NO AS 商户单号, t.TRANSACTION_NO AS 易票联单号,
to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 下单时间, to_char(t.END_TIME,'yyyy-mm-dd hh24:mi:ss') AS 完成时间 ,
(CASE t.STATE WHEN '00' THEN '成功' WHEN '01' THEN '失败' WHEN '03' THEN '处理中' ELSE t.STATE END) AS 订单状态,
t.AMOUNT / 100 AS 订单金额元 , t.ACTUAL_PAY_AMOUNT / 100 AS 应付金额元, t.CASH_AMOUNT / 100 AS 实付金额元, t.DISCOUNTABLE_AMOUNT AS 优惠金额元 , t.PROCEDURE_FEE / 100 AS 手续费金额元, t.REFUND_FEE / 100 AS 退款金额元,
(CASE t.CARD_TYPE WHEN 'D' THEN '贷记卡' WHEN 'C' THEN '借记卡' ELSE null END)AS 付款卡类型, t.BANK_CODE AS 付款卡开户行,
t.CHANNEL_ORDER AS 上游单号, c.INSTITUTION_NAME AS 交易机构,
t.CLIENT_IP AS 交易IP,
t.REMARK AS 交易摘要
FROM TXS_PAY_TRADE_ORDER t
LEFT JOIN CUM_INSTITUTION c ON t.INSTITUTION_ID =c.ID
LEFT JOIN PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE
WHERE t.state='00'
and t.create_time >=timestamp'2020-05-14 00:00:00'
and t.create_time < timestamp'2023-05-26 00:00:00'
and t.CUSTOMER_CODE in (select customer_code from info.tmp_cust_0608)
and t.TRANSACTION_TYPE != 'ZHFZ'


--分账数据
SELECT /*+parallel(5)*/ t.CREATE_TIME as 创建时间, t.CUSTOMER_CODE AS 商户编号 ,cc.NAME AS 商户名称 , tsr.CUSTOMER_CODE as 被分账商户编号, tsr.CUSTOMERNAME as 被分账商户名称,
t.OUT_TRADE_NO AS 商户单号, t.TRANSACTION_NO AS 易票联分账单号, tsr.AMOUNT/100 as 分账金额元, tsr.SPLIT_PROCEDURE_FEE/100 as 分账手续费元,
(case when t.procedure_customer_code=tsr.customer_code then '是' else '否' end) as 是否手续费商户
FROM TXS_SPLIT_ORDER t
INNER JOIN CUST_CUSTOMER cc ON t.CUSTOMER_CODE = cc.CUSTOMER_NO
INNER JOIN TXS_SPLIT_RECORD tsr ON t.TRANSACTION_NO = tsr.TRANSACTION_NO
where t.STATE = '00'
and t.create_time >=timestamp'2020-05-14 00:00:00'
and t.create_time < timestamp'2023-05-26 00:00:00'
and t.CUSTOMER_CODE in (select customer_code from info.tmp_cust_0608)


--缴费登记数据
SELECT t.TRANSACTION_NO as 易票联流水号, t.CUSTOMER_CODE as 商户编号, cc.NAME as 商户名称, decode(t.FUND_TYPE, '1','银行转账','2','商户赔付','3','商户退款','4','商户补款','6','结汇','7','收单充值') as 调整类型, t.AMOUNT/100 as 缴费金额元,
t.DEBTOR_ACCOUNT as 付款人账号, t.DEBTOR_ACCOUNT_NAME as 付款人名称, t.CREATE_TIME as 创建时间
FROM PAS_ACCT_QUOTA_RECORD t
INNER JOIN CUST_CUSTOMER cc ON t.CUSTOMER_CODE = cc.CUSTOMER_NO
where t.CUSTOMER_CODE in (select customer_code from info.tmp_cust_0608)
and t.ACCT_STATE = '00' and t.AUDIT_STATE = '00' and t.CHANGE_TYPE = '1'


--商户被分账数据
SELECT /*+parallel(5)*/ t.TRANSACTION_NO AS 易票联分账单号, t.SOURCE_CUSTOMER_CODE as 交易商户编号, t.SOURCE_CUSTOMERNAME as 交易商户名称, t.CUSTOMER_CODE AS 商户编号 ,t.CUSTOMERNAME AS 商户名称, t.OUT_TRADE_NO AS 商户单号, t.CREATE_TIME as 创建时间, t.PAYTRANSACTION_NO as 原交易单号,
t.AMOUNT/100 as 分账金额元, t.pay_procedurefee/100 as 收单手续费元,t.split_procedure_fee/100 as 分账手续费元,(case when tso.procedure_customer_code=t.customer_code then '是' else '否' end) as 是否手续费商户
FROM TXS_SPLIT_RECORD t
INNER JOIN TXS_SPLIT_ORDER tso ON t.TRANSACTION_NO = tso.TRANSACTION_NO
where t.create_time >=timestamp'2020-05-14 00:00:00'
and t.create_time < timestamp'2023-05-26 00:00:00'
and t.CUSTOMER_CODE in (select customer_code from info.tmp_cust_0608)
 and t.STATE = 3 ORDER BY t.transaction_no

--出金交易
SELECT t.CUSTOMER_CODE AS 商户编号, t.CUSTOMERNAME AS 商户名称, t.OUT_TRADE_NO AS 商户单号, t.TRANSACTION_NO AS 易票联单号,
to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 下单时间, to_char(t.END_TIME,'yyyy-mm-dd hh24:mi:ss') AS 完成时间 ,
(CASE WHEN t.PAY_STATE='00' THEN '成功' WHEN t.PAY_STATE='01' THEN '失败' WHEN t.PAY_STATE='03' THEN '处理中' ELSE t.PAY_STATE END) AS 订单状态,
t.TOTAL_FEE / 100 AS 订单金额元 , t.PROCEDURE_FEE / 100 AS 手续费金额元,
t.CARD_NO_CIPHER AS enc_card_no,
t.CARD_NO AS 提现卡, t.BANK_USER_NAME_FULL AS 持卡人,
t.BANK_NAME AS 银行名称,t.REMARK AS 备注,
t.CHANNEL_ORDER AS 上游单号,t.BATCH_NO AS 批量代付批号
FROM TXS_WITHDRAW_TRADE_ORDER t
INNER JOIN CUST_CUSTOMER cc ON t.CUSTOMER_CODE =cc.CUSTOMER_NO
WHERE t.pay_state='00' and t.create_time >=timestamp'2020-05-14 00:00:00'
and t.create_time < timestamp'2023-05-26 00:00:00'
and t.CUSTOMER_CODE in (select customer_code from info.tmp_cust_0608)

--退款订单
SELECT t.CUSTOMER_CODE AS 商户编号 ,t.CUSTOMERNAME AS 商户名称 ,
b.NAME AS 业务名称,t.OUT_REFUND_NO as 商户退款单号,
 t.TRANSACTION_NO AS 易票联退款单号,
to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 退款时间, to_char(t.END_TIME,'yyyy-mm-dd hh24:mi:ss') AS 完成时间 ,
'成功' AS 退款状态,
t.REFUND_FEE / 100 AS 退款金额元 , t.BACKPAY_PROCEDUREFEE / 100 AS 退回手续费金额元,
t.CHANNEL_TRADE_NO AS 上游单号, t.CHANNEL_NAME as 上游渠道名称
FROM TXS_REFUND_PRE_ORDER t
LEFT JOIN PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE
WHERE t.PAY_STATE='00'
and t.create_time >=timestamp'2020-05-14 00:00:00'
and t.create_time < timestamp'2023-05-26 00:00:00'
and t.CUSTOMER_CODE in (select customer_code from info.tmp_cust_0608)
