import requests
import ssl
from bs4 import BeautifulSoup
import re
import os
import urllib.request
import mysql.connector
from threading import Lock
import threading
import logging

logging.basicConfig(encoding='utf-8')
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# log_file_handler = logging.FileHandler('C:/TMP/output.log', encoding='utf-8')  # 创建日志处理器
# log_file_handler.setFormatter(formatter)
# logger.addHandler(log_file_handler)
log_console_handler = logging.StreamHandler()  # 创建控制台处理器
log_console_handler.setFormatter(formatter)
logger.addHandler(log_console_handler)

username = 'root'
password = ''
host = 'localhost'
database = 'book2'

cnx_pool = mysql.connector.pooling.MySQLConnectionPool(
    pool_name="mypool",
    pool_size=32,
    user=username,
    password=password,
    host=host,
    database=database,
    connection_timeout=300  # 设置超时时间为5分钟
)

def execute_select_sql(sql, params=None):
    db = cnx_pool.get_connection()
    cursor = db.cursor()
    try:
        cursor.execute(sql, params)
        if sql.startswith('SELECT'):
            result = cursor.fetchall()
            return result
        else:
            db.commit()
            return cursor.rowcount

    finally:
        cursor.close()
        db.close()

####################################################################
#-----ltxswu.org-----
def ltxswu_chapter_parser(soup):
    pb_next = None
    title = None
    content = None
    pb_next_a = soup.find('a', id='pb_next')
    if pb_next_a is not None:
        pb_next = 'http://m.ltxswu.org'+ pb_next_a.attrs['href']
        
    div = soup.find('div', id='nr_title')
    if div is not None:
        title = div.get_text()
        
    div = soup.find('div', id='nr1')
    if div is not None:
        content = div.get_text()
    return pb_next, title, content

def ltxswu_book_parser(soup):
    book_name = None
    first_chapter_url = None
    
    h1 = soup.find('h1', id='_52mb_h1')
    if h1 is not None:
        book_name = h1.get_text()
        
    uls = soup.find_all('ul', class_='chapter')
    second_ul = uls[1]
    first_li = second_ul.find('li')
    a_tag = first_li.find('a')
    if a_tag is not None:
        first_chapter_url = 'http://m.ltxswu.org/'+ a_tag.get('href')
    
    return book_name, first_chapter_url

def ltxswu_book_url(book_id):
    return 'http://m.ltxswu.org/book/{}/'.format(book_id)

#-----fxxs2.com-----
def fxxs2_chapter_parser(soup):
    pb_next = None
    title = None
    content = None
    
    pb_next_a = soup.find('a', id='next_url')
    if pb_next_a is not None:
        pb_next = pb_next_a.get('href')
        if pb_next.startswith('/'):
            pb_next = 'https://fxxs2.com'+pb_next
        else:
            pb_next = None
            
    h1 = soup.find('h1', class_='bookname')
    if h1 is not None:
        title = h1.get_text()
        
    div = soup.find('div', id='booktxt')
    if div is not None:
        content = ''
        for child in div.children:
            content += str(child)
        # content = div.get_text()
    
    logger.debug(f'Chapter: {title}， {pb_next}， {len(content) if content is not None else 0}')
    return pb_next, title, content

def fxxs2_book_parser(soup):
    book_name = None
    first_chapter_url = None

    h1 = soup.find('h1')
    if h1 is not None:
        book_name = h1.get_text()

    div_content_1 = soup.find('div', id='content_1')
    first_chapter_url = div_content_1.find('a').get('href')
    
    if book_name is not None and first_chapter_url is not None:
        return book_name, 'https://fxxs2.com'+first_chapter_url
    else:
        return None, None
    
def fxxs2_book_url(book_id):
    return 'https://fxxs2.com/index_{}/'.format(book_id)

#-----n998745.com-----
def n998745_chapter_parser(soup):
    pb_next = None
    title = None
    content = None

    pb_next_a = soup.find('a', class_='btn btn-danger pull-right col-md-2 col-xs-3 col-sm-3')
    if pb_next_a is not None:
        pb_next = pb_next_a.get('href')
        if not pb_next.startswith('http'):
            pb_next = None

    h1 = soup.find('h1')
    if h1 is not None:
        title = h1.get_text()

    div = soup.find('div', id='content')
    if div is not None:
        content = ''
        for child in div.children:
            content += str(child)

    return pb_next, title, content

def n998745_book_parser(soup):
    book_name = None
    first_chapter_url = None

    h1 = soup.find('h1')
    if h1 is not None:
        book_name = h1.get_text()

    li = soup.find('li', class_='list-group-item col-md-4')
    if li is not None:
        a = li.find('a')
        if a is not None:
            first_chapter_url = a.get('href')

    if book_name is not None and first_chapter_url is not None:
        return book_name, 'http://www.998745.com:443'+first_chapter_url
    else:
        return None, None    

def n998745_book_url(book_id):
    return 'http://www.998745.com:443/{}/'.format(book_id)

####################################################################
user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36'
spiders = {
    'ltxswu': {
        'encoding': 'GBK',
        'book_url': ltxswu_book_url,
        'book_parser': ltxswu_book_parser,
        'chapter_parser': ltxswu_chapter_parser
    },
    'fxxs2': {
        'encoding': 'utf-8',
        'book_url': fxxs2_book_url,
        'book_parser': fxxs2_book_parser,
        'chapter_parser': fxxs2_chapter_parser
    },
    'n998745': {
        'encoding': 'utf-8',
        'book_url': n998745_book_url,
        'book_parser': n998745_book_parser,
        'chapter_parser': n998745_chapter_parser
    }
}

def fetch_chapter(url, web_site):
    response = requests.get(url, headers={'User-Agent': user_agent}, verify=False)
    response.encoding = spiders[web_site]['encoding']
    soup = BeautifulSoup(response.text, 'html.parser')
    
    chapter_parser = spiders[web_site]['chapter_parser']
    pb_next,title, content = chapter_parser(soup)
    
    logger.info(f'fetch chapter: {url}， {title}， {len(content) if content is not None else 0}， {pb_next}')
    
    return title, content, pb_next

query = "INSERT INTO bk_chapter (book_id, book, title, content, url) VALUES (%s, %s, %s, %s, %s)"

def save_chapter(book_id, book_name, chapter_url, web_site, get_url_only=False):
    cnx = cnx_pool.get_connection()
    try:
        cursor = cnx.cursor()
        title, content, next_url = fetch_chapter(chapter_url, web_site)

        if get_url_only:
            return next_url
        
        if title is not None or content is not None:
            data = (book_id, book_name, title, content, chapter_url)
            cursor.execute(query, data)
            cnx.commit()
    finally:
        cursor.close()
        cnx.close()
    return next_url

def fetch_book(book_id, web_site):
    
    book_url = spiders[web_site]['book_url'](book_id)
    book_parser = spiders[web_site]['book_parser']
    logger.debug(f'fetch_book: {book_url}')


    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    response = requests.get(book_url, headers={'User-Agent': user_agent}, verify=False)

    response.encoding = spiders[web_site]['encoding']
    soup = BeautifulSoup(response.text, 'html.parser')

    book_name, first_chapter_url = book_parser(soup)
    logger.debug(f'book_parser>>: {book_name}， {first_chapter_url}')
    
    #get book name
    if book_name is None:
        return book_name, False
    
    get_url_only = False
    qs = execute_select_sql("SELECT book_id, state FROM bk_book WHERE book='{}'".format(book_name))
    if len(qs) > 0:
        if qs[0][1] == 1:   #存在一本完成的书，跳过
            logger.debug("BOOK: 《{}》, 已经完成".format(book_name))
            return book_name, True
        book_id = qs[0][0]
        
        qs = execute_select_sql("SELECT url FROM bk_chapter WHERE book_id='{}' order by id desc limit 1".format(book_id))
        if len(qs) > 0:
            first_chapter_url = qs[0][0]
            get_url_only = True
    else:
        execute_select_sql("INSERT INTO bk_book(book_id, book, url, state) VALUES ({}, '{}', '{}', 1)".format(book_id, book_name, book_url))
    
    logger.info("BOOK: {}, {} fetched".format(book_name, first_chapter_url))
    
    last_url = first_chapter_url
    next_url = save_chapter(book_id, book_name, first_chapter_url, web_site, get_url_only=get_url_only)
    while next_url is not None:
        last_url = next_url
        next_url = save_chapter(book_id, book_name, next_url, web_site, get_url_only=get_url_only)
    execute_select_sql("UPDATE bk_book SET state=1, url='{}' where book_id='{}'".format(last_url, book_id))
    #
    merge_chapters(book_id)
    #
    return book_name, True

def merge_chapters(book_id):
    cnx = cnx_pool.get_connection()
    try:
        sql = f"select id,title,content,url from bk_chapter bc where book_id='{book_id}' and url not like '%\_%' order by id"
        cursor = cnx.cursor()
        cursor.execute(sql)
        chapters = cursor.fetchall()
        for chapter in chapters:
            title = chapter[1]
            content = chapter[2]
            #
            title = title[0: title.rfind('(第')]
            title = re.sub(r'\s', '', title)
            #
            url = chapter[3]
            if url.endswith('.html'):
                url = url[:-5]
            if url.endswith('.htm'):
                url = url[:-4]
            parts = url.split('/')
            chapter_id = parts[-1]
            ##
            sql = f"select id,title,content,url from bk_chapter bc where book_id='{book_id}' and url like '%\_%' and url like '%{book_id}%{chapter_id}%' order by id"
            cursor.execute(sql)
            chapter_parts = cursor.fetchall()
            content += "".join([part[2].strip() for part in chapter_parts])
            content = fix_content(content)

            sql = f"update bk_chapter set title=%s, content=%s,book_id={book_id},chapter_id={chapter_id} where id={chapter[0]}"
            cursor.execute(sql, (title, content,))
            if len(chapter_parts) > 0:
                sql = f"delete from bk_chapter where id in ({','.join([str(chapter_part[0]) for chapter_part in chapter_parts])})"
                cursor.execute(sql)
        if len(chapters) > 0:
            sql = f"update bk_book set merged=1,chapter_count={len(chapters)} where book_id='{book_id}'"
            cursor.execute(sql)
        cnx.commit()
    finally:
        cursor.close()
        cnx.close()

def remove_book(book_id):
    cnx = cnx_pool.get_connection()
    cursor = cnx.cursor()
    sql = f"delete from bk_book where book_id='{book_id}'"
    cursor.execute(sql)
    sql = f"delete from bk_chapter where book_id='{book_id}'"
    cursor.execute(sql)
    cnx.commit()
    cursor.close()
    cnx.close()

####################################################################
books = []
bklock = threading.Lock()

def start_download_task():
    while True:
        with bklock:
            if len(books) == 0:
                break
            book_info = books.pop(0)
        try: 
            book_name, success = fetch_book(book_info[0], book_info[1])
            if success:
                logger.info("download book {}, {}".format(book_name, book_info[0]))
        except Exception as e:
            logger.error("download book {} {}, Reason()".format(book_info[0], e))

def start_download_books():
    for i in range(1, 35000):
        books.append((i, 'ltxswu'))

    threads = []
    for i in range(30):
        t = threading.Thread(target=start_download_task)
        t.start()
        threads.append(t)
    for t in threads:
        t.join()

####################################################################
keywords= []
def fix_content(content):
    if len(keywords) == 0:
        cnx = cnx_pool.get_connection()
        try:
            sql = "select keyword from bk_keywords"
            cursor = cnx.cursor()
            cursor.execute(sql)
            keyword_rows = cursor.fetchall()
            keywords.extend([r''+keyword[0] for keyword in keyword_rows])
        finally:
            cursor.close()
            cnx.close()
    for keyword in keywords:
        content = re.sub(keyword, '', content)
    return content

####################################################################
if __name__ == '__main__':
    # start_download_books()
    # remove_book(125532)
    # fetch_book(1, 'ltxswu')
    fetch_book(130590, 'fxxs2')
    # fetch_book('guanzi', 'n998745')
    # requests.get('http://www.998745.com:443/guanzi/', verify=False)