<mxfile host="65bd71144e">
    <diagram id="4qAuooWk1PmUcOpyLhhR" name="第 1 页">
        <mxGraphModel dx="906" dy="607" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="22" value="" style="rounded=0;whiteSpace=wrap;html=1;dashed=1;" parent="1" vertex="1">
                    <mxGeometry x="200" y="170" width="570" height="290" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="224" y="187" width="150" height="180" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="客户" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="88" y="197" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="运营" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="88" y="296" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="H5门户&lt;br&gt;（纯页面）" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="240" y="197" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="运营门户&lt;br&gt;（纯页面）" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="240" y="296" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="AdminServer&lt;br&gt;（Tomcat）" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="434" y="250" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="Oracle" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="659" y="188" width="60" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="FastDFS" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" parent="1" vertex="1">
                    <mxGeometry x="659" y="284" width="90" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="" style="endArrow=classic;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="2" target="4" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="130" y="267" as="sourcePoint"/>
                        <mxPoint x="180" y="217" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="填写/上传资料" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="10" vertex="1" connectable="0">
                    <mxGeometry x="0.2" y="1" relative="1" as="geometry">
                        <mxPoint x="-14" y="1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="" style="endArrow=classic;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fillColor=#a20025;strokeColor=#6F0000;strokeWidth=3;" parent="1" source="3" target="5" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="140" y="416" as="sourcePoint"/>
                        <mxPoint x="190" y="366" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="查看&amp;amp;审核资料" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="11" vertex="1" connectable="0">
                    <mxGeometry x="0.3286" y="-1" relative="1" as="geometry">
                        <mxPoint x="-23" y="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="12" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fillColor=#a20025;strokeColor=#6F0000;strokeWidth=3;" parent="1" source="5" target="7" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="410" y="410" as="sourcePoint"/>
                        <mxPoint x="460" y="360" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="13" value="" style="endArrow=classic;startArrow=classic;html=1;" parent="1" source="7" target="9" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="574" y="410" as="sourcePoint"/>
                        <mxPoint x="624" y="360" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="15" value="上传下载文件" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="13" vertex="1" connectable="0">
                    <mxGeometry x="0.1854" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" value="" style="endArrow=classic;startArrow=classic;html=1;" parent="1" source="7" target="8" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="584" y="250" as="sourcePoint"/>
                        <mxPoint x="634" y="200" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="数据库读写" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="14" vertex="1" connectable="0">
                    <mxGeometry x="0.1814" y="3" relative="1" as="geometry">
                        <mxPoint y="1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="20" value="" style="endArrow=classic;html=1;" parent="1" source="4" target="7" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="410" y="220" as="sourcePoint"/>
                        <mxPoint x="460" y="170" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="Nginx" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="260" y="363" width="50" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="易兑信息收集系统" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="217" y="139" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="阿里云短信服务" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;dashPattern=1 2;" parent="1" vertex="1">
                    <mxGeometry x="644" y="60" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="" style="endArrow=classic;html=1;strokeWidth=1;exitX=0.75;exitY=0;exitDx=0;exitDy=0;" parent="1" source="7" target="24" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="550" y="170" as="sourcePoint"/>
                        <mxPoint x="600" y="120" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="26" value="Redis" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="1">
                    <mxGeometry x="659" y="367" width="60" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="" style="endArrow=classic;startArrow=classic;html=1;" edge="1" parent="1" source="7" target="26">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="564" y="274" as="sourcePoint"/>
                        <mxPoint x="669" y="246" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="28" value="MEM IO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="27">
                    <mxGeometry x="0.1814" y="3" relative="1" as="geometry">
                        <mxPoint y="1" as="offset"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>