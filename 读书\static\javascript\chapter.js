// 监听键盘事件
document.addEventListener('keydown', function(event) {
    if (event.key === 'ArrowRight') {
        event.preventDefault();
        document.getElementById("chapter_next").click();
    } else if (event.key === 'ArrowLeft') {
        event.preventDefault();
        document.getElementById("chapter_prev").click();
    } else if (event.key === 'ArrowUp') {
        event.preventDefault();
        if(currentParagraphIndex > 0) {
            currentParagraphIndex--;
            var paragraphs = document.querySelectorAll("#content-column1 p, #content-column2 p");
            paragraphs[currentParagraphIndex].click();
            paragraphs[currentParagraphIndex].scrollIntoView({ behavior: "smooth", block: "center" });
        } else {
            document.getElementById("chapter_prev").click();
        }
    } else if (event.key === 'ArrowDown') {
        event.preventDefault();
        var paragraphs = document.querySelectorAll("#content-column1 p, #content-column2 p");
        if(currentParagraphIndex < paragraphs.length - 1) {
            currentParagraphIndex++;
            paragraphs[currentParagraphIndex].click();
            paragraphs[currentParagraphIndex].scrollIntoView({ behavior: "smooth", block: "center" });
        } else {
            document.getElementById("chapter_next").click();
        }
    }
});

// 监听鼠标滚轮事件
document.addEventListener('wheel', function(event) {
    if (event.deltaY > 0) {
        // 模拟 ArrowDown 键点击事件
        const keyEvent = new KeyboardEvent('keydown', {
            key: 'ArrowDown',
            code: 'ArrowDown',
            which: 40,
            keyCode: 40,
            bubbles: true,
            cancelable: true,
            composed: true
        });

        event.preventDefault();
        event.target.dispatchEvent(keyEvent);
    } else if (event.deltaY < 0) {
        // 模拟 ArrowUp 键点击事件
        const keyEvent = new KeyboardEvent('keydown', {
            key: 'ArrowUp',
            code: 'ArrowUp',
            which: 38,
            keyCode: 38,
            bubbles: true,
            cancelable: true,
            composed: true
        });

        event.preventDefault();
        event.target.dispatchEvent(keyEvent);
    }
});

// 监听触摸屏滑动事件
document.addEventListener('touchstart', handleTouchStart);
document.addEventListener('touchmove', handleTouchMove);

let startX = 0;
let startY = 0;

function handleTouchStart(event) {
    startX = event.touches[0].clientX;
    startY = event.touches[0].clientY;
}

function handleTouchMove(event) {
    const deltaX = event.touches[0].clientX - startX;
    const deltaY = event.touches[0].clientY - startY;

    // 检测右滑
    if (deltaX > 50 && Math.abs(deltaY) < 50) {
        // 模拟 ArrowRight 键点击事件
        const keyEvent = new KeyboardEvent('keydown', {
            key: 'ArrowLeft',
            code: 'ArrowLeft',
            which: 39,
            keyCode: 39,
            bubbles: true,
            cancelable: true,
            composed: true
        });

        event.preventDefault();
        event.target.dispatchEvent(keyEvent);
    }
    // 检测左滑
    else if (deltaX < -50 && Math.abs(deltaY) < 50) {
        // 模拟 ArrowLeft 键点击事件
        const keyEvent = new KeyboardEvent('keydown', {
            key: 'ArrowRight',
            code: 'ArrowRight',
            which: 37,
            keyCode: 37,
            bubbles: true,
            cancelable: true,
            composed: true
        });

        event.preventDefault();
        event.target.dispatchEvent(keyEvent);
    }
}

// 分屏显示
function toggleSplitScreen(checked) {
    var container = document.getElementById("content-container");
    if (checked) {
        container.style.display = "flex";
    } else {
        container.style.display = "block";
    }
}

// 段落阅读状态
lastest_read_column = null;
var currentParagraphIndex = 0;

function toggleReadStatus(p) {
    var found_read = false;
    if (p.parentNode.id === "content-column1") {
        var paragraphs1 = document.getElementById("content-column1").getElementsByTagName("p");
        for (var i = 0; i < paragraphs1.length; i++) {
            var paragraph = paragraphs1[i];
            if (paragraph === p) {
                paragraph.classList.add("reading");
                paragraph.classList.remove("read");
                paragraph.classList.remove("unread");
                found_read = true;
                currentParagraphIndex = i;
            } else {
                paragraph.classList.remove("reading");
                if(found_read){
                    paragraph.classList.remove("read");
                    paragraph.classList.add("unread");
                } else {
                    paragraph.classList.add("read");
                    paragraph.classList.remove("unread");
                }
            }
        }
        //
        if(lastest_read_column == "content-column2"){
            var paragraphs2 = document.getElementById("content-column2").getElementsByTagName("p");
            for (var i = 0; i < paragraphs2.length; i++) {
                var paragraph2 = paragraphs2[i];
                paragraph2.classList.remove("reading");
                paragraph2.classList.remove("read");
                paragraph2.classList.add("unread");
            }
        }
        //
        lastest_read_column = "content-column1";
    } else if (p.parentNode.id === "content-column2") {
        var paragraphs1 = document.getElementById("content-column1").getElementsByTagName("p");
        for (var i = 0; i < paragraphs1.length; i++) {
            var paragraph = paragraphs1[i];
            paragraph.classList.remove("reading");
            paragraph.classList.remove("unread");
            paragraph.classList.add("read");
        }
        //
        var paragraphs2 = document.getElementById("content-column2").getElementsByTagName("p");
        for (var i = 0; i < paragraphs2.length; i++) {
            var paragraph = paragraphs2[i];
            if (paragraph === p) {
                paragraph.classList.add("reading");
                paragraph.classList.remove("read");
                paragraph.classList.remove("unread");
                found_read = true;
                currentParagraphIndex = i + paragraphs1.length;
            } else {
                paragraph.classList.remove("reading");
                if(found_read){
                    paragraph.classList.remove("read");
                    paragraph.classList.add("unread");
                } else {
                    paragraph.classList.add("read");
                    paragraph.classList.remove("unread");
                }
            }
        }
        //
        lastest_read_column = "content-column2";
    }
}

var scrollScreenIntervalId = null;
function toggleScrollScreen(checked) {
    if(!checked) {
        clearInterval(scrollScreenIntervalId);
        scrollScreenIntervalId = null;
        stopSpeak();
        return;
    }
    var paragraphs = document.querySelectorAll("#content-column1 p, #content-column2 p");

    var paragraphPlanTime = 0;      // 每段文字需要的时间，单位为毫秒
    var paragraphActTime = 0;       // 每段文字已经耗时，单位为毫秒
    var interval = 600;             // 定时器间隔，单位为毫秒
    var speakStatus = 0;            // 0: 未开始，1: 正在说话，2: 说话结束
    scrollScreenIntervalId = setInterval(function() {
        if (currentParagraphIndex < paragraphs.length) {
            if(speakStatus == 1) {
                paragraphActTime += interval;
            } else if(paragraphActTime > paragraphPlanTime || speakStatus == 2) {
                var p = paragraphs[currentParagraphIndex];
                var intervalWords = 12 * getCookie("speech_rate",1);         // 每段文字需要的字数，单位为字
                p.click();
                p.scrollIntoView({ behavior: "smooth", block: "center" });
                paragraphPlanTime = getParagraphWordCount(p) * interval / intervalWords;
                paragraphActTime = 0;
                //
                speakStatus = 0;
                if(getCookie("options_speak") == "yes") {
                    speakStatus = 1;
                    speak(p.textContent.trim(), {
                    onend: function() {
                        speakStatus = 2;
                    }, 
                    onstart: function() {
                        speakStatus = 1;
                    }
                    });
                }
                currentParagraphIndex++;
            } else {
                paragraphActTime += interval;
            }
        } else {
            clearInterval(scrollScreenIntervalId);
            scrollScreenIntervalId = null;
            document.getElementById("chapter_next").click();
        }
    }, interval);
}