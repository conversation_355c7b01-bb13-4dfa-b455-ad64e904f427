# 清算例外处理

【描述】对账，授权交易成功，但上游清算例外（exception)，该批记录需重新提交上游清算，现需通过后台修改状态并重新提交清算。

【处理】

update t_master_issuer_account_range_info i , t_clear_info c
set i.activeCode = 'I',
    c.clear_status = 'WAIT'
where i.issuerAccountRangeLow < c.pan
  AND i.issuerAccountRangeHigh > c.pan
  and i.gcmsProductId <> substr(c.network_data, 1, 3)
  AND i.activeCode = 'A'
  AND i.gcmsProductId != 'CIR'
  and c.clear_status = 'EXCEPTION';
