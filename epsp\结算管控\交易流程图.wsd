@startuml 交易流程图
    title 交易流程图

    actor 用户 as u
    actor 商户 as m
    box "收单机构" #LightBlue
    actor 运营 as o
    participant 商户模块 as c
    participant 交易模块 as t
    participant 记账模块 as a
    participant 核算模块 as k
    participant 清算模块 as s
    endbox
    participant 两联 as b
    participant 账户机构 as p

    ==交易支付==
    m -> t: 收单支付
    t -[#red]> t: 校验是否受控交易
    note right of t
        条件：
        商户号+APPID存在于受控清单中
    end note
    t -> b: 两联受理支付
    b -> p: 账户机构支付
    p --> b: 完成支付
    b --> t: 完成支付
    t -> a: 调用记账接口
    a -> a: 账户记账
    alt 非受控交易
        a -> a: 汇总当日非受控交易金额
        note right of a
        登记到Redis中的用于退款的额度
        end note
    end
    t -> m: 支付成功

    ==管控通知==
    p -> c: 受控通知
    alt APPID受控
        c -> c: 商户APPID受控登记
    else 交易解冻
        c -> t: 调用解冻接口
        t -> t: 交易解冻（修改状态）
    end

    ==退款==
    m -> t: 申请退款
    t -> t: 判断交易是否冻结交易
    alt 冻结交易
        t -> a: 从在途余额减额\n（Redis中额度不变）
    else 非冻结交易
        t -> a: 同现有退款流程
        note left of a
        范围包含：
        ①非受控交易；
        ②受控已解冻交易
        处理：
        ①Redis中额度扣减
        ②Redis额度不够，从可用余额退款
        end note
    end
    ==账单==
    k -> b: 调用两联接口，获取账单
    k -> k: 账单入库
    k -[#red]> t: 对账户机构的退款补单
@enduml