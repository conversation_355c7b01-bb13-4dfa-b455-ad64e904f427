@startuml 客户对象图
entity 客户 {
    客户ID
    编号
    中文名称
    英文名称
    联系邮箱
    证件类型（营业执照）
    证件编号
    证件失效日期
    行业分类（电商、外贸B2B）
    注册资本
    注册地址ID（参考地址）
    经营地址ID（参考地址）
    注册文件影像件
    是否有网址
    网址
    证明材料类型
    证明材料
    资金来源材料类型
    资金来源材料
    实控证明类型
    实控证明
    客户状态
    国家地区
    注册时间
    提交时间
    审核时间
    审核人
    审核备注
    停用时间
    注销时间
}
note left of 客户::证明材料类型
    营销材料、产品清单、发票、合同、
    货物报关单、货物运单、其他
end note
note left of 客户::资金来源材料类型
    投资证明、继承证明、银行对账单、
    资产负债/损益表、退税单、
    年度财务报表、
    股东/合伙人/委托人资金证明
end note
note left of 客户::实控证明类型
    股权结构图、股东名册、年度财务报表、
    合伙注册确认书、合伙协议、公司章程、
    股权证、公司决议、懂事名册、
    运营协议、其他
end note
note left of 客户::客户状态
    待提交资料、审核中、启用、停用、冻结
end note
entity 用户
{
    用户ID
    客户ID
    状态（正常、禁用）
    类型
    邮箱
    密码
    最近登录时间
    最近登录IP
    密码过期时间
    最近密码修改时间
    创建时间
}
note bottom of 用户
    登录用户、
    支付用户
end note
客户 ||--o{ 用户 : 1个用户

entity 人员
{
    人员ID
    客户ID
    类型
    名
    姓
    证件类型
    证件编号
    证件生效日期
    证件失效日期
    证件影像件正面
    证件影像件反面
    授权文件
    授权联系人电话
}
note left of 人员::类型
    法定代表人、
    受益人（股东）、
    董事、
    联系人
end note
note left of 人员::证件类型
    临时身份证、
    港澳居民来往内地通行证、
    台湾居民来往大陆通行证、
    外国公民护照
end note
客户 }|-- 人员 : 多个人员
entity 地址
{
    地址ID
    客户ID
    类型（）
    国家/地区（中国、香港）
    省
    市
    区
    街道
    邮编
    详细地址
    详细地址英文
}
客户 ||--o{ 地址 : 1-N 地址
note bottom of 地址
    注册地址、经营地址、联系地址
end note
entity 操作日志
{
    客户ID
    操作类型
    操作时间
    操作人
    操作备注
}
note bottom of 操作日志
    注册、
    修改、
    停用、
    启用、
    冻结、
    注销、
    高风险、
    中风险、
    低风险、
    审核通过、
    审核拒绝
end note

entity 业务
{
    业务ID
    业务名称
    业务代码
    业务描述
}
note bottom of 业务
    收款、
    换汇、
    付款
end note
entity 客户业务
{
    客户ID
    业务代码
    业务状态
    业务备注
    收费比例
    单笔收费
    保底收费
    封顶收费
    币种（美元）
    开通时间
    停用时间
    审核人
    审核备注
}
业务 ||--o{ 客户业务: 业务ID关联
客户 ||--o{ 客户业务: 客户ID关联

entity VA账户{
    VA账户号
    客户号
    上游渠道
    其他字段
}
note top of VA账户: 表VA_MCH_INFO
客户 ||--o{ VA账户: 1-N VA账户

entity VA账户注册{
    VA账户号
    客户号
    上游渠道
    上游客户号
    其他字段
}
note bottom of VA账户注册: 表VA_MCH_CHANNEL
VA账户 ||--o{ VA账户注册: 1-1注册信息

entity VA收款账户{
    收款账户ID
    VA账户号
    客户号
    上游渠道
    其他收款账户信息
}
note bottom of VA收款账户: 表VA_ACC_INFO
VA账户 ||--o{ VA收款账户: 1-n收款账户

entity VA账户币种{
    币种卡号
    VA账户号
    币种
}
note bottom of VA账户币种: 表VA_ACC_CARD
VA账户 ||--o{ VA账户币种: 1-n本地币种

@enduml

@startuml 名单筛查对象图
entity 筛查客户
{
    客户ID
    请求唯一标识
    客户编码
    客户名称
    名单类型
    筛查状态
    是否监控
    是否通知（MQ）
    创建时间
    请求筛查时间
    筛查更新时间
}
note right of 筛查客户::请求唯一标识
    用于标识一次请求
    对于交易请求，请求唯一标识为交易流水号
end note
note right of 筛查客户::名单类型
    客户、
    收款人、
    付款人
end note
note right of 筛查客户::筛查状态
    待筛查（尚未发起筛查）、
    筛查中、
    确定可疑、
    排除可疑
end note

entity 筛查名单
{
    名单ID
    所属客户ID
    名单名称
    名单类型
    名单内容
    名单状态
    证件类型
    证件号码
    所属国家地区
    性别
    出生日期
    筛查结果/档案
    请求筛查时间
    名单更新时间
    状态
    失效时间
}
note right of 筛查名单
    客户资料变更后，先“删除名单检索”删除，
    并重新发起检索
end note
note right of 筛查名单::名单类型
    公司、
    个人
end note
note right of 筛查名单::名单状态
    待审核、
    审核通过、
    审核拒绝
end note
note right of 筛查名单::名单更新时间
    取通知报文中的timestamp，
    并以此决定数据新旧
    如果数据更旧，就不要更新了
end note
note right of 筛查名单::状态
    有效、
    无效，调用删除名单检索后
end note

筛查客户 *-- 筛查名单 : 一个客户包含多个名单

entity 筛查名单历史记录
{
    历史记录ID
    所属名单ID
    所属客户ID
    名单名称
    筛查结果/档案
    创建时间
}

note right of 筛查名单历史记录
    筛查结果/档案与上一次不一样时
    记录新纪录，并且更新筛查名单
    首次通知时，需要记录历史记录
end note

筛查名单 *-- 筛查名单历史记录 : 一个名单包含多个记录

@enduml