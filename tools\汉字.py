import os
from datetime import datetime
import datetime as dt
import cx_Oracle
import pandas as pd
import shutil
import logging
import math

logging.basicConfig(encoding='utf-8')
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log_console_handler = logging.StreamHandler()  # 创建控制台处理器
log_console_handler.setFormatter(formatter)
logger.addHandler(log_console_handler)

def import_to_oracle():
    conn = cx_Oracle.connect('efps01/efps01@**********/testdb') 
    cursor = conn.cursor()
    with open('D:/GitHub/pytools/tools/汉字拼音.txt', 'r', encoding='utf-8') as f:
        a_line = f.readline()
        while a_line:
            a_line = a_line.strip()
            parts = a_line.split('=')
            pinyin = parts[0]
            hanzi_list = list(parts[1])
            for hanzi in hanzi_list:
                sql = "insert into hanzi_pinyin (pinyin, hanzi) values ('{}', '{}')".format(pinyin, hanzi)
                cursor.execute(sql)
            a_line = f.readline()
    conn.commit()
    cursor.close()
    conn.close()

def translate_to_pinyin(hanzi_str):
    conn = cx_Oracle.connect('efps01/efps01@**********/testdb') 
    cursor = conn.cursor()
    hanzi_list = list(hanzi_str)
    pinyin_list = []
    for hanzi in hanzi_list:
        sql = "select pinyin from hanzi_pinyin where hanzi = '{}'".format(hanzi)
        cursor.execute(sql)
        res = cursor.fetchone()
        if is_none(res):
            pinyin_list.append(hanzi)
            continue
        pinyin = res[0]
        pinyin_list.append(pinyin)
    pinyin_str = ' '.join(pinyin_list)
    cursor.close()
    conn.close()
    return pinyin_str

def is_none(obj):
    return isinstance(obj, type(None))

if __name__ == '__main__':
    # import_to_oracle()
    print(translate_to_pinyin('广东省广州市海珠区广州大道中368大厦'))