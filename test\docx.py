import test.docx as docx

def readDocx(docxfilename, xlsfilename):
    print("docx file is %s, xlsx file is %s" % (docxfilename, xlsfilename))
    
    from openpyxl import Workbook
    
    # 打开Word文件
    doc = docx.Document(docxfilename)

    # 创建Excel文件
    wb = Workbook()
    ws = wb.active

    # 遍历所有表格
    for table in doc.tables:
        # 遍历表格中的所有行和单元格
        for i, row in enumerate(table.rows):
            # 如果是第一行，则创建Excel中的表头
            if i == 0:
                header = [cell.text for cell in row.cells]
                ws.append(header)
            # 否则将行中的值写入Excel文件中
            else:
                values = [cell.text for cell in row.cells]
                ws.append(values)

    # 保存Excel文件
    wb.save(xlsfilename)
    
if __name__ == "__main__":
    readDocx("D:/EPSP/设计文档/04.对外接口/易票联跨境支付业务接口规范.docx", "D:/TMP/output.xlsx")