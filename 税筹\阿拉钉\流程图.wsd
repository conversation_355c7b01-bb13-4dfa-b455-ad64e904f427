@startuml 易灵工

title 易灵工接口流程

actor 客户 as C
participant 易灵工 as Y

==签约==
C -> Y: 1.1 灵工签约，参数：身份证号、姓名、手机号
Y --> C: 返回签约状态
C -> Y: 1.2 签约查询，参数：身份证号、姓名、手机号
Y --> C: 返回签约状态
C -> Y: 1.3 批量签约，参数：批号、身份证号、姓名、手机号
Y --> C: 返回多条签约状态
C -> Y : 1.4 批量签约查询，参数：批号、身份证号、姓名、手机号
Y --> C: 返回多条签约状态

==证件照片==
C -> Y: 2.1 证件照片上传，参数：身份证号、姓名、手机号码、身份证正面、身份证反面
Y --> C: 返回证件照片上传状态
C -> Y: 2.2 证件照片查询，参数：身份证号
Y --> C: 返回证件照片上传状态


==任务==
group 任务发布
    C -> Y: 3.1.1 标签查询，参数：任务类型
    Y --> C: 返回标签列表
    C -> Y: 3.1.2 创建任务，参数：标签、任务类型、名称、etc
    Y --> C: 返回任务列表
    C -> Y: 3.1.3 任务关联人员，参数：任务id、person_list
    Y --> C: 返回关联人员状态
end group

group 任务交付
    C -> Y: 3.2.1 上传文件，参数：文件类型、文件内容
    Y --> C: 返回凭证文件ID
    C -> Y: 3.2.2 完成任务，参数：任务id、凭证文件ID
    Y --> C: 返回任务状态
    C -> Y: 3.2.3 上传任务交付成果，参数：任务ID、成果文件(参考上传文件)
    Y --> C: 返回任务交付成果状态
end group

group 任务查询
    C -> Y: 3.3.1 任务查询，参数：任务id
    Y --> C: 返回任务状态、委托方信息
    C -> Y: 3.3.2 任务信息查询，参数：任务id
    Y --> C: 返回任务详细信息：任务信息、任务人员信息、委托方信息
end group
==结算==
group 结算查询
    C -> Y: 4.1.1 任务人员查询，参数：任务id
    Y --> C: 返回任务人员信息
    C -> Y: 4.1.2 个人收入明细查询，参数：身份证号
    Y --> C: 返回任务人员信息
end group 
group 代征单
    C -> Y: 4.2.1 创建代征单，参数：纳税人信息、银行卡号、金额、开票模式
    Y --> C: 返回代征单ID
    C -> Y: 4.2.2 确认代征单，参数：代征单ID、处理操作（继续/取消）
    Y --> C: 确认执行结果
    C -> Y: 4.2.3 上传个人代付凭证
    Y --> C: 返回上传状态
end group
group 结算单
    C -> Y: 4.3.1 生成结算单，参数：任务ID
    Y --> C: 返回结算单ID、状态
    C -> Y: 4.3.2 结算单信息查询，参数：结算单ID
    Y --> C: 结算单详细信息（企业信息、任务信息、关联人员信息等）
end group

==发票==
group 开票信息查询
    C -> Y: 5.1.1 查询可开票类目，参数：
    Y --> C: 返回可开票类目
    C -> Y: 5.1.2 查询可开票批次，参数：企业ID
    Y --> C: 返回可开票批次列表
    C -> Y: 5.1.2 查询代付批次订单明细，参数：批次ID
    Y --> C: 返回代付订单明细列表
end group
group 开票申请
    C -> Y: 5.2.1 发票申请，参数：批次ID、开票金额、开票备注、开票抬头、邮寄信息
    Y --> C: 返回发票申请ID
    C -> Y: 5.2.2 查询发票申请记录，参数：发票申请ID
    Y --> C: 返回发票申请记录列表
    Y -\ C: 5.2.3 发票审核结果通知（异步），参数：发票申请ID、状态
    Y -\ C: 5.2.4 发票开具通知（异步），参数：发票申请ID、状态、发票详细信息
    C -> Y: 5.2.5 发票信息查询，参数：发票申请ID
    Y --> C: 返回发票详细信息
end group
@enduml

@startuml 易灵工第2版

title 易灵工接口流程

actor 客户 as C
participant 易灵工 as Y

==签约==
C -> Y: 1.1 灵工签约，参数：身份证号、姓名、手机号
Y --> C: 返回签约状态
C -> Y: 1.2 证件照片上传，参数：身份证号、姓名、手机号码、身份证正面、身份证反面
Y --> C: 返回证件照片上传状态

C -> Y: 1.3 签约查询，参数：身份证号、姓名、手机号
Y --> C: 返回签约状态


==任务==
C -> Y: 2.1 标签查询，参数：任务类型
Y --> C: 返回标签列表
C -> Y: 2.2 创建任务，参数：标签、任务类型、名称、etc
Y --> C: 返回任务列表
C -> Y: 2.3 任务关联人员，参数：任务id、person_list
Y --> C: 返回关联人员状态
C -> Y: 2.4 任务人员查询，参数：任务id
Y --> C: 返回任务人员信息

==结算==
C -> Y: 3.1 创建代征单，参数：纳税人信息、银行卡号、金额、开票模式
Y --> C: 返回代征单ID
Y -> Y: 上传代发明细&付款代发
C -> Y: 3.2 确认代征单，参数：代征单ID、处理操作（继续/取消）
Y --> C: 确认执行结果
C -> Y: 3.3 上传个人代付凭证
Y --> C: 返回上传状态

==发票==
group 开票信息查询
    C -> Y: 4.1.1 查询可开票类目，参数：
    Y --> C: 返回可开票类目
    C -> Y: 4.1.2 查询可开票批次，参数：企业ID
    Y --> C: 返回可开票批次列表
    C -> Y: 4.1.2 查询代付批次订单明细，参数：批次ID
    Y --> C: 返回代付订单明细列表
end group
group 开票申请
    C -> Y: 4.2.1 发票申请，参数：批次ID、开票金额、开票备注、开票抬头、邮寄信息
    Y --> C: 返回发票申请ID
    C -> Y: 4.2.2 查询发票申请记录，参数：发票申请ID
    Y --> C: 返回发票申请记录列表
    Y -\ C: 4.2.3 发票审核结果通知（异步），参数：发票申请ID、状态
    Y -\ C: 4.2.4 发票开具通知（异步），参数：发票申请ID、状态、发票详细信息
    C -> Y: 4.2.5 发票信息查询，参数：发票申请ID
    Y --> C: 返回发票详细信息
end group
@enduml

@startuml 易灵工第3版

title 易灵工接口流程

actor 委托方 as C
participant 易票联 as Y
participant 受托方 as S
actor 任务人员 as R

==开户（线下）==
C -> Y: 提交开户资料
Y -> Y: 进件商户&开通业务
Y -> Y: 开户&绑定受托方ID（资质地ID）
Y --> C: 返回商户号、受托方ID（资质地ID）

==签约==
C -> Y: 签约
Y --> C: 返回受理结果
Y -\ R: 发送签约短信
R -> Y: 提交签约信息
Y -\ C: 异步通知签约状态

==任务==
C -> Y: 标签列表查询
Y --> C: 返回标签列表
C -> Y: 创建任务
Y --> C: 返回任务列表
C -> Y: 关联人员
Y --> C: 返回关联人员状态

==结算==
C -> Y: 任务人员查询
Y --> C: 返回任务人员信息
C -> C: 制作代付明细表
C -> Y: 上传代付明细
Y -> Y: 筛选任务人员
Y -> Y: 创建代征单
Y -> Y: 筛出超额人员
Y --> C: 返回符合代发人员明细

C -> Y: 申请支付代发款
Y --> C: 返回收银台URL
C --[#red]> C: 完成支付(PC或H5收银台中完成)
alt 支付成功
    Y -> S: D0结算代发款
    S --> Y: 委托代发明细
    Y -> Y: 发起批量代付
    Y -\ R: 资金入账异步通知
    Y -\ C: 代发结果异步通知
end
C -> Y: 代发单查询
Y --> C: 返回代发单详情

==发票==
group 开票信息查询
    C -> Y: 查询可开票类目
    Y --> C: 返回可开票类目
    C -> Y: 查询可开票批次
    Y --> C: 返回可开票批次列表
    C -> Y: 查询代付批次订单明细
    Y --> C: 返回代付订单明细列表
end group
group 开票申请
    C -> Y: 发票申请
    Y --> C: 返回发票申请ID
    C -> Y: 查询发票申请记录
    Y --> C: 返回发票申请记录列表
    Y -\ C: 发票审核结果通知（异步）
    Y -\ C: 发票开具通知（异步）
    C -> Y: 发票信息查询
    Y --> C: 返回发票详细信息
end group
Y -> Y: 定期（3个月）生成结算单\n结束任务
@enduml

