<%@ page import="java.sql.*" %>
<%@ page import="javax.servlet.http.HttpServletRequest" %>
<%@ page import="javax.servlet.http.HttpServletResponse" %>
<%
    // 获取表单提交的参数
    String param1 = request.getParameter("param1");
    String result = "";
    // 连接数据库
    if("C".equalsIgnoreCase(param1) || "T".equalsIgnoreCase(params1)) {
        Connection conn = null;
        try {
            // 使用JDBC连接到Oracle数据库
            String url = "*************************************";
            String username = "mb_db";
            String password = "mb_db";
            conn = DriverManager.getConnection(url, username, password);

            // 调用存储过程
            String sql = "{call p_sync_customer_today()}";
            if("T".equalsIgnoreCase(param1)) {
                sql = "{call p_sync_business_jingying_today()}";
            }
            CallableStatement stmt = conn.prepareCall(sql);
            stmt.execute();
            result = "Success!";
        } catch (SQLException e) {
            // 处理数据库错误
            result = e.getMessage();
        } finally {
            // 关闭连接
            if (conn != null) {
                conn.close();
            }
        }
    }
%>

<!DOCTYPE html>
<html>
<head>
    <title>调用存储过程</title>
</head>
<body>
    <h1>调用存储过程</h1>
    <p><font color='red'><%= result %></font></p>
    <form method="post" action="">
        <label for="param1">刷新数据:</label>
        <input type="text" name="param1" id="param1">
        <span>C：商户数据；T：交易数据</span><br>
        <input type="submit" value="提交">
    </form>
</body>
</html>