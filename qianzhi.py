# -*- python3.7 -*-
# -*- coding: utf-8 -*-
import datetime
import csv

def readcsv(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        render = csv.reader(f)
        header = next(render)
        for row in render:
            sql = "INSERT INTO CHK_QRA_BUSINESS_MAN (CUSTOMER_CODE, BUSINESS_MAN, COMPANY_NAME) VALUES ('{}', '{}', '{}');".format(row[1].strip(), row[2].strip(), row[3].strip())
            print(sql)
            
if __name__ == "__main__":
    print("start......")
    readcsv(filename="D:/TMP/QZ0706.csv")
    fromdate = datetime.datetime.strptime("20230630", "%Y%m%d")
    todate = datetime.datetime.strptime("20230706", "%Y%m%d")
    while fromdate <= todate:
        cmd = "curl -X GET 'http://************:8197/report/updateQraBusinessMan?taskDate={}' -H 'accept: */*';".format(fromdate.strftime("%Y%m%d"))
        print(cmd)
        fromdate += datetime.timedelta(days=1)
    