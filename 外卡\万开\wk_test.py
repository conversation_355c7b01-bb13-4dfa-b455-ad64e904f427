import json
from datetime import datetime
import random
from Crypto.PublicKey import RSA
from Crypto.Signature import PKCS1_v1_5
from Crypto.Hash import SHA256
from Crypto.Hash import SHA
from base64 import b64decode
import hashlib
import rsa
import urllib.parse
import requests
import base64

private_key_base64 = "MIIEpQIBAAKCAQEAu9glwIsZFL7kXdevtxhl8h+iR00JvZVhuhz5G6HVH6cVUhCxs2Y0AIlUcDPO0ShZ3CeB4rcuJM8YPWqADpraWqN91QSkPTCOj3vEwdIdn41xK4sNo2EnK2tAdTg+oEch/H59ZofCWPokqpWzx1FLFvQYbuXbDJbT1wdn+KK+yLFFP5+cjcpqulpRhMAfsxpETnuVVU8wjqahHky3Ma51d1Ul5Q1RLDeBxtmyVjJqbPugZS/43HkotYxa3HQiiZs720I+ORczcCrfz0Q3qZ+Ud0zAy5gOBfgEUlDdcFFDlfATKPPKbfgtPHFq9Mmk0QVNTAuBQpt098MCXq2HY3XtiQIDAQABAoIBAQCBs+m9XU5gKL7+nSNTRaF+4aDnoqYLJc5o+AB5t/C0/mWWWLiaqwNVK34GS3+OpFIH1a+1n85Y1DZkkdkCPKJw21rTb3UIWzLUmB1vx2l09fEio80y9q5ZKKFFTyTO/s7UzXJBDZSAY5hVwTB1fAhhPQJx8Hyj4LE9VHvSDm21nKHJMx4LtbHscBlNrEmZ1jbxjUi3hXjIHb+WlUnr3r8/fiXp7nHTjkyb33t0tAFL8urKx5nwOMjkwV79/FDq0+wDgW/TYVsvAM+I8k0E/n3KrFNMtBIrZtKtBUGhelXgU4wp57MLJQ/mioLcTY5tkvMVM6qFQu0VueTv7Ql0LI9RAoGBAO7GChmaooy7Qq2I39toq7RohVBBb+3d0oq/6kFkbKU0iPb+UhpN5LLwcI3lJTiTQ8ZCz1EmQ2CAz0WXlsd69Xm3wKbKlh/zGWv5rnitaDrB1moDuwyFff+wCFgNcilg3R5whdRhiOHXK6+Auv54LnVAFNLnugnqsCerxP2qyBgfAoGBAMllfAklPBxZdeE66TWR6tudncc773YGzcDDDio3BT25/5aErbTPKVQQVwsHQ8lrLNPhDqToIvLUfEC+HScPnaQi136O1Yj5tTP3Pws5ysLy7s3B3sb8lxWbPwB+hiy5q8QD1F+1iherF2FltUuzaidnuwh1MI+An0vgxA1v3uVXAoGADwk7k5cwS0pzjXgEGM4Dmg3G72HfiYwheQlS2CFZ0iF3yo4DX5KuJzKK8/SNHn3vk6mbYpHVRlt2Mfy0ywUMNkkDAb3aLFLjs+/6M7E/0gto1C499jDl2Vcdf2YBXWezJPqTAufNdeNV5exmWiX5TvrlFM7w4TT9ONJRSmQZy6MCgYEAilBBVEKXOt0RwSJlYZ4aDsP7xQXbmlJn4lfTlZh95/uilev/JGsV9h7WuMM/gcgyXD13gjfPMLhPsWMfwGDQIhavqsJL0qu0D8FhMcN6BlMzQGpCJqT42iTpqtw6J+NIHPGIXEbgVQrynrhsP1YztGZgJBnfxN5QCJuP6a6IBTcCgYEAyNUykocRpkDpcJS1Fz4NnKqATEwRwmv9cxYQGZ/hJhXlxkEJHxwKeemOH2paWUfchvtPOL5DN9p37TqPPQgliSS3sTmRJfy6ouBSVKlqWc5jhQZQ6muDypuFcN2LKSmrUFijL91Z0J4JB2H0f0HCmsnH/LQ2Vs6z6eTRR8bn1mM="

def get_private_key(key):
    key = b64decode(key)
    return RSA.import_key(key)

def get_sign(data):
    key = rsa.PrivateKey.load_pkcs1(base64.b64decode(private_key_base64))
    message_bytes = data.encode('utf-8')
    message_digest = hashlib.sha1(message_bytes).digest()
    signature = rsa.sign(message_digest, key, 'SHA-1')
    return signature

def get_sign2(data):
    key = get_private_key(private_key_base64)
    message_bytes = data.encode('utf-8')
    # signer = PKCS1_v1_5.new(key)
    # digest = SHA256.new()
    # digest.update(message_bytes)
    # return signer.sign(digest)

    h = SHA.new(message_bytes)
    signer = PKCS1_v1_5.new(key)
    signature = signer.sign(h)
    return signature

def pay():
    wk_url = "https://service.epaylinks.cn/snc-gate/gateway/api"
    dt_str = datetime.now().strftime("%Y%m%d%H%M%S")
    data={
        "agentId": "100004",
        "merNo": "10000176",
        "productId": "0102",
        "phoneNo": "18665889336",
        "custIp": "127.0.0.1",
        
        "acctNo": "5187183123527307",
        "cvn": "123",
        "expDate": "0724",
        "currencyCode": "156",
        "orderDate": "f{dt_str}",
        "orderNo": f"{dt_str}{random.randint(1000, 9999)}",
        "requestNo": f"{dt_str}{random.randint(1000, 9999)}",
        "notifyUrl": "localhost",
        "returnUrl": "localhost",
        "goodsList": '[{"goodsName":"test","goodsPrice":"11.11","quantity":"1"}]',
        
        "transType": "SALES",
        "subTransType": "00",
        "transAmt": "12",
        "version": "1.0",
        "webSite": "baidu.com",
        
        "billingAddress": "US",
        "billingCity": "US",
        "billingCountry": "US",
        "billingEmail": "<EMAIL>",
        "billingFirstName": "luo",
        "billingLastName": "yuan",
        "billingState": "US",
        "billingZip": "23456",
        
        "shippingAddress": "US",
        "shippingCity": "US",
        "shippingCountry": "US",
        "shippingFirstName": "luo",
        "shippingLastName": "yuan",
        "shippingState": "US",
        "shippingZip": "23221"
    }
    sorted_keys = sorted(data.keys())
    content = '&'.join([f'{key}={data[key]}' for key in sorted_keys])
    # content = '&'.join([f'{key}={urllib.parse.quote(data[key])}' for key in sorted_keys])
    sign = get_sign(content)
    content += f"&signature={urllib.parse.quote(sign)}"
    print("request content >>>>", content)
    resp = requests.post(wk_url, data = content.encode('utf8'), headers={'Content-Type': 'application/x-www-form-urlencoded'})
    print("response content >>>>",resp.content.decode('utf8'))
    json_object = json.loads(resp.content.decode('utf8'))
    print("response code >>>>", json_object.get("respCode"), json_object.get("respMsg"))

def query():
    wk_url = "https://service.epaylinks.cn/snc-gate/gateway/api"
    dt_str = datetime.now().strftime("%Y%m%d%H%M%S")
    data={
        "requestNo": f"{dt_str}{random.randint(1000, 9999)}",
        "version": "1.0",
        "productId": "0401",
        "transType": "TRANS_QUERY",
        
        "merNo": "10000176",
        "agentId": "100004",
        "orderNo": f"{dt_str}{random.randint(1000, 9999)}"
    }
    sorted_keys = sorted(data.keys())
    content = '&'.join([f'{key}={data[key]}' for key in sorted_keys])
    sign = get_sign(content)
    content += f"&signature={urllib.parse.quote(sign)}"
    print("request content >>>>", content)
    resp = requests.post(wk_url, data = content.encode('utf8'), headers={'Content-Type': 'application/x-www-form-urlencoded'})
    print("response content >>>>",resp.content.decode('utf8'))
    json_object = json.loads(resp.content.decode('utf8'))
    print("response code >>>>", json_object.get("respCode"), json_object.get("respMsg"))
    

if __name__ == '__main__':
    query()