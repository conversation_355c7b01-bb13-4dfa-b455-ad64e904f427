@startuml 付款单状态图
title 付款单状态图

state "待提交" as DTJ
state "待确认" as DQR
state "待审核" as DSH
state "审核结果" as SHJG <<choice>>
state "审核失败" as SHSB
state "已审核" as YSH
state "付款中" as FKZ
state "付款结果" as FKJG <<choice>>
state "已完成" as YWC
state "已取消" as YQX

[*] -> DTJ: 申请付款
DTJ --> DQR: 提交付款申请
DQR -> DSH: 确认付款申请
DSH -> SHJG: 审核付款申请
SHJG --> SHSB: 审核失败
SHJG --> YSH: 审核成功
SHSB --> DQR: 重新提交

YSH --> FKZ: 付款中
FKZ -> FKJG: 付款结果
FKJG --> YWC: 成功
FKJG --> YQX: 失败
YWC --> [*]: 成功
YQX --> [*]: 失败

@enduml

@startuml 付款流程图
title 付款流程图

actor "客户" as KH
actor "运营人员" as YY
box 汇通平台
    participant "汇通门户" as HT
    participant "汇通账户" as ACC
end box
participant "上游渠道" as QD

note right of QD 
全球付(默认)
Skyee
CC
end note

KH -> HT: 提交付款申请
KH -> HT: 确认付款申请, 提交付款凭证（支付密码）
YY -> HT: 审核付款申请
alt 审核成功
    HT -> ACC: 发起扣款
    HT -> QD: 发起付款
    YY -> QD: 提交付款材料（线上或线下）
    QD -[#red]\ HT: 通知付款结果
    alt 成功
        HT -\ KH: 付款成功
    else 失败
        HT -> ACC: 退款
        HT -\ KH: 付款失败
    end
else 审核失败
    HT -\ KH: 审核失败
end

@enduml