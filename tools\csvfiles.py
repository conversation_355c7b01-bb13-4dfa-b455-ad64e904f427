import csv

def readcsv(filename):
    with open(filename) as f:
        render = csv.reader(f)
        header = next(render)
        print(header)
        for row in render:
            print(row)
            
def groupby(filename, groupby_column, column_to_aggregate, agg):
    import pandas as pd

    # 从CSV文件中读取数据
    data = pd.read_csv(filename)
    # data.filter(items=[groupby_column, column_to_aggregate])

    # 使用group by对数据进行分组
    grouped_data = data.groupby(groupby_column)

    # 对分组后的数据执行聚合操作（例如求和、平均值等）
    aggregated_data = grouped_data.agg({column_to_aggregate: agg})
    if isinstance(groupby_column, list):
        aggregated_data.sort_values(by=groupby_column, inplace=True)
    else:
        aggregated_data.sort_values(by=[groupby_column], inplace=True)
    # 打印聚合结果
    print(aggregated_data)
            
filename = "D:/TMP/EPSP当前版本BUG (Your Company Jira) 2023-07-19T11_48_23+0800.csv"
if __name__ == "__main__":
    # readcsv(filename)
    groupby(filename, "经办人", "问题ID", "count")
    groupby(filename, "状态", "问题ID", "count")
    groupby(filename, ["经办人","状态"], "问题ID", "count")