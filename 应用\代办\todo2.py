import sqlite3
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, PhotoImage, filedialog
from tkcalendar import DateEntry
from dataclasses import dataclass
import os  # 添加 os 模块
from PIL import Image, ImageTk  # 添加 PIL 模块
import shutil  # 用于文件操作
import tempfile  # 添加临时文件模块
import subprocess  # 用于打开文件
import locale  # 添加 locale 模块
import configparser  # 添加配置文件模块
import time  # 添加 time 模块

def find_or_create_config():
    """优先查找当前目录下的todo.ini，否则查找用户目录，没有则在用户目录创建"""
    app_dir = os.path.dirname(__file__)
    user_dir = os.path.expanduser("~")
    app_config = os.path.join(app_dir, "todo.ini")
    user_config = os.path.join(user_dir, "todo.ini")
    if os.path.exists(app_config):
        return app_config
    elif os.path.exists(user_config):
        return user_config
    else:
        # 创建用户目录下的todo.ini
        config = configparser.ConfigParser()
        config["Settings"] = {"db_path": "todo.db"}
        with open(user_config, "w", encoding="utf-8") as f:
            config.write(f)
        return user_config

config_file = find_or_create_config()

def read_config(key, group = "Settings", default = None, type=str):
    config = configparser.ConfigParser()
    config.read(config_file, encoding="utf-8")
    if type == int:
        return config.getint(group, key, fallback=default)
    elif type == float:
        return config.getfloat(group, key, fallback=default)
    elif type == bool:
        return config.getboolean(group, key, fallback=default)
    elif type == list:
        return config.get(group, key, fallback=default).split(",")
    elif type == dict:
        return dict(config.items(group))
    else:
        return config.get(group, key, fallback=default)

def write_config(key, value, group = "Settings"):
    config = configparser.ConfigParser()
    config.read(config_file, encoding="utf-8")
    if group not in config:
        config[group] = {}
    config[group][key] = value
    with open(config_file, "w", encoding="utf-8") as f:
        config.write(f)
        
@dataclass
class Todo:
    id: int
    title: str
    description: str
    status: str
    created_time: str
    planned_time: str
    completed_time: str
    parent_id: int
    tag: str
    reminder_time: str  # 新增字段
    star_rating: int = 1  # 星级评分，默认1星

class TodoDatabase:
    def __init__(self, db_path="todo.db"):
        self.db_path = db_path
        self.connection = sqlite3.connect(db_path)
        self.create_table()
        self.migrate_database()

    def create_table(self):
        query = """
        CREATE TABLE IF NOT EXISTS todos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            status TEXT CHECK(status IN ('待处理', '已完成', '延迟')) DEFAULT '待处理',
            created_time TEXT NOT NULL,
            planned_time TEXT,
            completed_time TEXT,
            parent_id INTEGER,
            tag TEXT,
            reminder_time TEXT,
            star_rating INTEGER CHECK(star_rating BETWEEN 1 AND 5) DEFAULT 1,
            FOREIGN KEY(parent_id) REFERENCES todos(id)
        )
        """
        self.connection.execute(query)
        self.connection.commit()

    def add_todo(self, title, description="", planned_time=None, parent_id=None, tag=None):
        query = """
        INSERT INTO todos (title, description, created_time, planned_time, parent_id, tag)
        VALUES (?, ?, ?, ?, ?, ?)
        """
        created_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.connection.execute(query, (title, description, created_time, planned_time, parent_id, tag))
        self.connection.commit()

    def get_todos(self, status=None):
        query = "SELECT * FROM todos"
        if status:
            query += " WHERE status = ?"
            return self.connection.execute(query, (status,)).fetchall()
        return self.connection.execute(query).fetchall()

    def get_children_todos(self, parent_id):
        query = "SELECT * FROM todos WHERE parent_id =?"
        return self.connection.execute(query, (parent_id,)).fetchall()
    
    def get_todo(self, id):
        query = "SELECT * FROM todos WHERE id = ?"
        return self.connection.execute(query, (id,)).fetchone()
    
    def search_todos(self, keyword = None, title = None, tag = None, start_date = None, end_date = None, show_delayed = False, show_all = False):
        query = "SELECT * FROM todos WHERE 1=1"
        params = []
        
        if keyword:
            query += " AND (title LIKE ? OR description LIKE ?)"
            params.extend([f"%{keyword}%", f"%{keyword}%"])
        if title:
            query += " AND title LIKE ?"
            params.append(f"%{title}%")
        if tag:
            query += " AND tag LIKE ?"
            params.append(f"%{tag}%")
        if not show_all:
            if not start_date:
                start_date = datetime.now().strftime("%Y-%m-%d")
            allowed_status = ["待处理"]
            if show_delayed:
                allowed_status.append("延迟")
            query += " AND ((completed_time >= ? OR planned_time >= ?) AND status IN ('待处理', '已完成') OR status IN ({}))".format(",".join(["?"] * len(allowed_status)))
            params.extend([start_date, start_date])
            params.extend(allowed_status)
        if end_date:
            end_date = end_date + timedelta(days=1)  # 包含当天
            query += " AND (completed_time <= ? OR planned_time <= ?)"
            params.extend([end_date, end_date])
        query += " ORDER BY parent_id, status DESC, star_rating DESC, planned_time DESC, created_time DESC"
        todos = self.connection.execute(query, params).fetchall()
        # 如果todo对应的parent不存在，则根据ID加载这些todo，并放到该todo的队列所在位置的前面，如果达到最大尝试次数还是找不到，则删除该todo
        if todos:
            max_try_times = 100
            try_times = 0
            while try_times < max_try_times:
                try_times += 1
                found = False
                for todo in todos:
                    if todo[7]:  # 如果有父任务ID
                        # 在todos中查找父任务
                        parent_todo = next((t for t in todos if t[0] == todo[7]), None)
                        if not parent_todo:  # 如果在现有列表中找不到父任务
                            # 从数据库加载父任务
                            parent_todo = self.get_todo(todo[7])
                            if parent_todo:
                                # 确保父任务在子任务之前
                                insert_pos = todos.index(todo)
                                todos.insert(insert_pos, parent_todo)
                                found = True
                        elif todos.index(todo) < todos.index(parent_todo):
                            todos.remove(parent_todo)
                            insert_pos = todos.index(todo)
                            todos.insert(insert_pos, parent_todo)
                            found = True
                if not found or try_times >= max_try_times - 1:
                    break
        return todos

    def migrate_database(self):
        """确保数据库结构是最新的"""
        # 检查tag列是否存在
        cursor = self.connection.cursor()
        cursor.execute("PRAGMA table_info(todos)")
        columns = [column[1] for column in cursor.fetchall()]

        # 如果tag列不存在，添加它
        if 'tag' not in columns:
            cursor.execute("ALTER TABLE todos ADD COLUMN tag TEXT")
            self.connection.commit()

        # 新增reminder_time字段
        if 'reminder_time' not in columns:
            cursor.execute("ALTER TABLE todos ADD COLUMN reminder_time TEXT")
            self.connection.commit()

        # 添加星级评分字段
        if 'star_rating' not in columns:
            cursor.execute("ALTER TABLE todos ADD COLUMN star_rating INTEGER DEFAULT 1 CHECK(star_rating BETWEEN 1 AND 5)")
            self.connection.commit()

        # 创建附件表
        attachment_table_query = """
        CREATE TABLE IF NOT EXISTS attachments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            todo_id INTEGER NOT NULL,
            file_path TEXT,
            file_name TEXT NOT NULL,
            file_content BLOB NOT NULL,
            created_time TEXT NOT NULL,
            FOREIGN KEY(todo_id) REFERENCES todos(id) ON DELETE CASCADE
        )
        """
        cursor.execute(attachment_table_query)
        self.connection.commit()

        # 检查是否需要迁移旧的附件表
        cursor.execute("PRAGMA table_info(attachments)")
        columns = [column[1] for column in cursor.fetchall()]

        # 如果file_content列不存在，添加它
        if 'file_content' not in columns:
            try:
                # 尝试添加新列
                cursor.execute("ALTER TABLE attachments ADD COLUMN file_content BLOB")
                self.connection.commit()

                # 更新现有记录，读取文件内容并存储
                attachments = cursor.execute("SELECT id, file_path FROM attachments WHERE file_path IS NOT NULL").fetchall()
                for attachment_id, file_path in attachments:
                    if file_path and os.path.exists(file_path):
                        try:
                            with open(file_path, 'rb') as f:
                                file_content = f.read()
                                cursor.execute("UPDATE attachments SET file_content = ? WHERE id = ?",
                                              (file_content, attachment_id))
                        except Exception as e:
                            print(f"Error reading file {file_path}: {e}")
                self.connection.commit()
            except Exception as e:
                print(f"Error migrating attachments table: {e}")

    def add_attachment(self, todo_id, file_path):
        """添加附件到任务，并存储文件内容"""
        # 从文件路径中提取文件名
        file_name = os.path.basename(file_path)
        created_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 读取文件内容
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read()
        except Exception as e:
            print(f"读取文件失败: {e}")
            return None

        query = """
        INSERT INTO attachments (todo_id, file_path, file_name, file_content, created_time)
        VALUES (?, ?, ?, ?, ?)
        """
        self.connection.execute(query, (todo_id, file_path, file_name, file_content, created_time))
        self.connection.commit()
        return self.connection.execute("SELECT last_insert_rowid()").fetchone()[0]

    def get_attachments(self, todo_id):
        """获取任务的所有附件"""
        query = "SELECT * FROM attachments WHERE todo_id = ?"
        return self.connection.execute(query, (todo_id,)).fetchall()

    def delete_attachment(self, attachment_id):
        """删除附件"""
        query = "DELETE FROM attachments WHERE id = ?"
        self.connection.execute(query, (attachment_id,))
        self.connection.commit()

    def close(self):
        self.connection.close()

class TodoApp:
    def __init__(self, root):
        self.db_path = self.load_db_config()
        self.db = TodoDatabase(self.db_path)
        self.root = root
        self.root.title("待办事项管理")

        # 设置窗口图标
        icon_path = os.path.join(os.path.dirname(__file__), "res", "todo.png")
        self.root.iconphoto(True, tk.PhotoImage(file=icon_path))
        
        # 设置窗口透明度
        self.root.attributes('-alpha', read_config("alpha", "Settings", 0.9, float))

        # 设置背景图片
        self.bg_image_path = None
        self.bg_image = None
        self.bg_label = None
        self.set_background_image()

        # 初始化上次搜索条件的存储变量
        self.search_condition = {}

        self.details_initialized = False  # 标记详情控件是否已初始化

        locale.setlocale(locale.LC_TIME, "C")  # 设置日期区域为通用格式

        self.theme = read_config("theme", "Settings", "dark")
        self.apply_theme()  # 应用主题
        self.create_menu()  # 创建菜单
        self.create_toolbar()  # 创建工具栏
        self.create_widgets()

        self.reminder_ahead_minutes = read_config("reminder_ahead_minutes", "Settings", 5, int)
        self.reminded_tasks = set()  # 已提醒的任务ID
        self.reminder_delay_map = {}  # 任务ID: 下次提醒时间
        self.check_reminders()  # 启动提醒检测

        # 延迟设置任务栏图标，避免影响控件显示
        def set_iconbitmap_later():
            try:
                ico_path = os.path.join(os.path.dirname(__file__), "res", "todo.ico")
                import sys
                if sys.platform.startswith("win") and os.path.exists(ico_path):
                    self.root.iconbitmap(ico_path)
            except Exception:
                pass
        self.root.after(100, set_iconbitmap_later)

    def load_db_config(self):
        db_path = read_config("db_path", "Settings", "todo.db")
        # 如果配置文件中的db_path不存在，尝试查找应用目录下的todo.db
        if not os.path.exists(db_path):
            app_dir = os.path.dirname(__file__)
            app_db = os.path.join(app_dir, "todo.db")
            # 在应用目录创建todo.db
            if not os.path.exists(app_db):
                open(app_db, "w").close()
            db_path = app_db
            # 更新配置文件指向新创建的todo.db
            write_config("db_path", db_path)
        return db_path

    def create_menu(self):
        """创建菜单栏"""
        menu_bar = tk.Menu(self.root, activeforeground="white", relief=tk.FLAT)

        # 任务菜单
        task_menu = tk.Menu(menu_bar, tearoff=0, relief=tk.FLAT)
        task_menu.add_command(label="新增", command=self.add_todo)
        task_menu.add_command(label="删除", command=self.delete_todo)
        task_menu.add_command(label="刷新", command=self.refresh_todos)
        task_menu.add_command(label="搜索", command=self.toggle_search_frame)
        menu_bar.add_cascade(label="任务", menu=task_menu)

        # 设置菜单
        settings_menu = tk.Menu(menu_bar, tearoff=0, relief=tk.FLAT)
        settings_menu.add_command(label="设置背景图片", command=self.set_bg_image_menu)
        settings_menu.add_command(label="选择任务库", command=self.select_database)
        settings_menu.add_command(label="设置字体", command=self.set_font_menu)
        # 新增主题子菜单
        theme_menu = tk.Menu(settings_menu, tearoff=0, relief=tk.FLAT)
        theme_menu.add_command(label="深色主题", command=lambda: self.set_theme("dark"))
        theme_menu.add_command(label="浅色主题", command=lambda: self.set_theme("light"))
        theme_menu.add_command(label="自定义主题", command=lambda: self.set_theme("theme:custome"))
        settings_menu.add_cascade(label="设置主题", menu=theme_menu)
        menu_bar.add_cascade(label="设置", menu=settings_menu)

        # 帮助菜单
        help_menu = tk.Menu(menu_bar, tearoff=0, relief=tk.FLAT)
        help_menu.add_command(label="关于", command=self.show_about)
        menu_bar.add_cascade(label="帮助", menu=help_menu)

        self.root.config(menu=menu_bar)

    def set_theme(self, theme):
        """切换主题"""
        self.theme = theme
        write_config("theme", theme)
        self.apply_theme()

    def apply_theme(self):
        """根据当前主题应用样式"""
        if getattr(self, "theme", None) is None:
            self.theme = read_config("theme", "Settings", "dark")

        theme_obj = THEMES.get(self.theme, THEMES["dark"])
        bg = theme_obj.bg
        fg = theme_obj.fg
        entry_bg = theme_obj.entry_bg
        entry_fg = theme_obj.entry_fg
        button_bg = theme_obj.button_bg
        button_fg = theme_obj.button_fg
        tree_bg = theme_obj.tree_bg
        tree_fg = theme_obj.tree_fg
        tree_sel_bg = theme_obj.tree_sel_bg
        tree_sel_fg = theme_obj.tree_sel_fg
        odd_bg = theme_obj.odd_bg
        even_bg = theme_obj.even_bg
        overdue_fg = theme_obj.overdue_fg
        completed_fg = theme_obj.completed_fg
        scrollbar_bg = theme_obj.scrollbar_bg
        scrollbar_trough = theme_obj.scrollbar_trough
        font_name = read_config("font_name", "Settings", "微软雅黑")
        font_size = read_config("font_size", "Settings", 10, int)

        self.root.configure(bg=bg)
        style = ttk.Style()
        style.theme_use("clam")
        style.configure("Treeview",
                        background=tree_bg,
                        foreground=tree_fg,
                        fieldbackground=tree_bg,
                        bordercolor=tree_bg,
                        rowheight=40)
        style.configure("Treeview.Heading", font=(font_name, font_size), background=tree_bg, foreground=tree_fg, rowheight=40)
        style.map("Treeview", background=[("selected", tree_sel_bg)], foreground=[("selected", tree_sel_fg)])
        style.configure("TButton", background=button_bg, foreground=button_fg, borderwidth=1, font=(font_name, font_size))
        style.map("TButton", background=[("active", tree_sel_bg)])
        style.configure("TLabel", background=bg, foreground=fg, font=(font_name, font_size))
        style.configure("TEntry", fieldbackground=entry_bg, foreground=entry_fg, bordercolor=bg, font=(font_name, font_size))
        style.configure("TCombobox", fieldbackground=entry_bg, foreground=entry_fg, bordercolor=bg, font=(font_name, font_size))
        style.map("TCombobox", fieldbackground=[("readonly", entry_bg)],
                    background=[("readonly", entry_bg)], foreground=[("readonly", entry_fg)])
        style.configure("TScrollbar", background=scrollbar_bg, troughcolor=scrollbar_trough)
        # Treeview行颜色和特殊颜色
        try:
            if hasattr(self, "tree"):
                self.tree.tag_configure("completed", font=(font_name, font_size, "overstrike"), foreground=completed_fg)
                self.tree.tag_configure("delayed", font=(font_name, font_size), foreground="#888888")  # 延迟状态显示为灰色
                self.tree.tag_configure("overdue", font=(font_name, font_size), foreground=overdue_fg)
                self.tree.tag_configure("odd", font=(font_name, font_size), background=odd_bg, foreground=tree_fg)
                self.tree.tag_configure("even", font=(font_name, font_size), background=even_bg, foreground=tree_fg)
                self.tree.tag_configure("alternate", font=(font_name, font_size), background="#5a5a5a")
        except Exception:
            pass
        def set_control_theme(control):
            try:
                if isinstance(control, ttk.Treeview):
                    control.tag_configure("odd", background=odd_bg, foreground=tree_fg)
                    control.tag_configure("even", background=even_bg, foreground=tree_fg)
                    control.tag_configure("completed", font=("Arial", font_size, "overstrike"), foreground=completed_fg)
                    control.tag_configure("overdue", foreground=overdue_fg)
                elif isinstance(control, tk.Text):
                    control.config(bg=entry_bg, fg=entry_fg, font=(font_name, font_size))
                # elif isinstance(control, tk.Scrollbar):
                #     control.config(background=scrollbar_bg, troughcolor=scrollbar_trough)
                elif isinstance(control, ttk.Combobox):
                    control.config(background=entry_bg, foreground=entry_fg)
                elif isinstance(control, DateEntry):
                    pass
                elif isinstance(control, tk.Entry):
                    control.config(bg=entry_bg, fg=entry_fg, font=(font_name, font_size))
                elif isinstance(control, tk.Listbox):
                    control.config(bg=entry_bg, fg=entry_fg, font=(font_name, font_size))
                elif isinstance(control, tk.Button):
                    control.config(bg=button_bg, fg=button_fg, activebackground=tree_sel_bg, activeforeground=tree_sel_fg, font=(font_name, font_size))
                elif isinstance(control, tk.Label):
                    control.config(bg=bg, fg=fg, font=(font_name, font_size))
                elif isinstance(control, tk.Checkbutton):
                    control.config(bg=bg, fg=fg, selectcolor=bg, activebackground=bg, activeforeground=fg, font=(font_name, font_size))
                elif isinstance(control, ttk.Checkbutton):
                    style = ttk.Style()
                    style.configure("Custom.TCheckbutton", background=bg, foreground=fg)
                    control.config(style="Custom.TCheckbutton")
                elif isinstance(control, tk.Frame):
                    control.config(bg=bg)
                    for child in control.winfo_children():
                        set_control_theme(child)
                elif isinstance(control, tk.Menu):
                    def set_menu_theme(menu):
                        menu.config(bg=bg, fg=fg, activebackground=button_bg, activeforeground=button_fg)
                        for i in range(menu.index("end") or 0):
                            try:
                                submenu = menu.entrycget(i, "menu")
                                if submenu:
                                    set_menu_theme(menu.nametowidget(submenu))
                            except Exception:
                                pass
                    set_menu_theme(control)
            except Exception as e:
                print(control, e)  # 调试输出异常信息
                pass
        # 其他控件背景
        for f in self.root.winfo_children():
            set_control_theme(f)
        # 设置窗口背景图片（可选：刷新背景以适配主题）
        if hasattr(self, "set_background_image"):
            self.set_background_image()

    def select_database(self):
        """选择任务库"""
        file_path = filedialog.askopenfilename(
            title="选择任务库",
            filetypes=[("SQLite 数据库", "*.db"), ("所有文件", "*.*")]
        )
        if not file_path:
            return

        if os.path.isdir(file_path):
            # 如果选择的是文件夹，则在该文件夹中创建 todo.db
            file_path = os.path.join(file_path, "todo.db")
            if not os.path.exists(file_path):
                open(file_path, "w").close()  # 创建空文件

        elif not file_path.endswith(".db"):
            messagebox.showerror("错误", "请选择一个有效的 SQLite 数据库文件或文件夹")
            return

        # 更新数据库路径并保存到配置文件
        write_config("db_path", file_path)

        # 重新加载数据库
        self.db.close()
        self.db = TodoDatabase(file_path)
        self.refresh_todos()
        messagebox.showinfo("成功", f"任务库已切换到: {file_path}")

    def create_toolbar(self):
        """创建工具栏"""
        toolbar = tk.Frame(self.root, relief=tk.RAISED, bd=1)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        self.toolbar = toolbar  # 保存工具栏引用

        # 获取图片目录路径
        res_dir = os.path.join(os.path.dirname(__file__), "res")

        # 加载并调整图片大小
        image_size = (30, 30)
        add_icon = ImageTk.PhotoImage(Image.open(os.path.join(res_dir, "add.png")).resize(image_size, Image.Resampling.LANCZOS))
        delete_icon = ImageTk.PhotoImage(Image.open(os.path.join(res_dir, "del.png")).resize(image_size, Image.Resampling.LANCZOS))
        refresh_icon = ImageTk.PhotoImage(Image.open(os.path.join(res_dir, "refresh.png")).resize(image_size, Image.Resampling.LANCZOS))
        search_icon = ImageTk.PhotoImage(Image.open(os.path.join(res_dir, "search.png")).resize(image_size, Image.Resampling.LANCZOS))

        # 第一区：新增和刷新按钮
        add_button = tk.Button(toolbar, image=add_icon, command=self.add_todo, relief=tk.FLAT)
        add_button.image = add_icon  # 防止图片被垃圾回收
        add_button.pack(side=tk.LEFT, padx=5, pady=5)
        add_button.bind("<Enter>", lambda e: self.show_tip("新增"))
        add_button.bind("<Leave>", lambda e: self.hide_tip())

        del_button = tk.Button(toolbar, image=delete_icon, command=self.delete_todo, relief=tk.FLAT)
        del_button.image = delete_icon
        del_button.pack(side=tk.LEFT, padx=5, pady=5)
        del_button.bind("<Enter>", lambda e: self.show_tip("删除"))
        del_button.bind("<Leave>", lambda e: self.hide_tip())

        refresh_button = tk.Button(toolbar, image=refresh_icon, command=self.refresh_todos, relief=tk.FLAT)
        refresh_button.image = refresh_icon
        refresh_button.pack(side=tk.LEFT, padx=5, pady=5)
        refresh_button.bind("<Enter>", lambda e: self.show_tip("刷新"))
        refresh_button.bind("<Leave>", lambda e: self.hide_tip())

        search_button = tk.Button(toolbar, image=search_icon, command=self.toggle_search_frame, relief=tk.FLAT)
        search_button.image = search_icon
        search_button.pack(side=tk.LEFT, padx=5)
        search_button.bind("<Enter>", lambda e: self.show_tip("搜索"))
        search_button.bind("<Leave>", lambda e: self.hide_tip())

    def toggle_search_frame(self):
        """打开高级搜索对话框"""
        self.open_search_dialog()

    def open_search_dialog(self):
        """打开高级搜索对话框"""
        theme = THEMES.get(self.theme, THEMES["dark"])
        search_dialog = tk.Toplevel(self.root)
        search_dialog.title("高级搜索")
        search_dialog.configure(bg=theme.bg)

        # 设置窗口在主窗口的中间
        self.root.update_idletasks()
        root_x = self.root.winfo_x()
        root_y = self.root.winfo_y()
        root_width = self.root.winfo_width()
        root_height = self.root.winfo_height()

        search_dialog.update_idletasks()
        width = 400  # 设置固定宽度
        height = 300  # 设置固定高度
        x = root_x + (root_width // 2) - (width // 2)
        y = root_y + (root_height // 2) - (height // 2)
        search_dialog.geometry(f"{width}x{height}+{x}+{y}")

        # 确保窗口在主窗口上面
        search_dialog.transient(self.root)
        search_dialog.grab_set()

        # 关键字搜索
        tk.Label(search_dialog, text="关键字:", bg=theme.bg, fg=theme.fg).grid(row=0, column=0, padx=10, pady=10, sticky="w")
        keyword_entry = tk.Entry(search_dialog, bg=theme.entry_bg, fg=theme.entry_fg, width=30)
        keyword_entry.insert(0, self.search_condition["keyword"] if "keyword" in self.search_condition else "")  # 填充上次搜索的关键字
        keyword_entry.grid(row=0, column=1, padx=10, pady=10, sticky="w")

        # 标签搜索
        tk.Label(search_dialog, text="标签:", bg=theme.bg, fg=theme.fg).grid(row=1, column=0, padx=10, pady=10, sticky="w")
        tag_entry = tk.Entry(search_dialog, bg=theme.entry_bg, fg=theme.entry_fg, width=30)
        tag_value = self.search_condition["tag"] if "tag" in self.search_condition else ""
        tag_entry.insert(0, tag_value if tag_value else "")  # 填充上次搜索的标签
        tag_entry.grid(row=1, column=1, padx=10, pady=10, sticky="w")

        # 日期范围
        tk.Label(search_dialog, text="开始日期:", bg=theme.bg, fg=theme.fg).grid(row=2, column=0, padx=10, pady=10, sticky="w")
        start_date_frame = tk.Frame(search_dialog, bg=theme.bg)
        start_date_frame.grid(row=2, column=1, padx=10, pady=10, sticky="w")

        # 创建日期选择器，并填充上次搜索的日期
        start_date_entry = DateEntry(start_date_frame, width=12, background="darkblue", foreground="white", borderwidth=2, date_pattern="yyyy-mm-dd")
        start_date_entry.delete(0, tk.END)  # 先清空默认日期

        # 如果有上次的开始日期，则填充
        if "start_date" in self.search_condition:
            start_date_entry.set_date(self.search_condition["start_date"] if "start_date" in self.search_condition else "")

        start_date_entry.pack(side=tk.LEFT, padx=(0, 5))

        # 添加清除按钮
        tk.Button(start_date_frame, text="清除", command=lambda: start_date_entry.delete(0, tk.END),
                 bg=theme.button_bg, fg=theme.button_fg, width=4).pack(side=tk.LEFT)
        tk.Label(search_dialog, text="结束日期:", bg=theme.bg, fg=theme.fg).grid(row=3, column=0, padx=10, pady=10, sticky="w")
        end_date_frame = tk.Frame(search_dialog, bg=theme.bg)
        end_date_frame.grid(row=3, column=1, padx=10, pady=10, sticky="w")

        # 创建日期选择器，并填充上次搜索的日期
        end_date_entry = DateEntry(end_date_frame, width=12, background="darkblue", foreground="white", borderwidth=2, date_pattern="yyyy-mm-dd")
        end_date_entry.delete(0, tk.END)  # 先清空默认日期

        # 如果有上次的结束日期，则填充
        if "end_date" in self.search_condition:
            end_date_entry.set_date(self.search_condition["end_date"] if "end_date" in self.search_condition else "")

        end_date_entry.pack(side=tk.LEFT, padx=(0, 5))

        # 添加清除按钮
        tk.Button(end_date_frame, text="清除", command=lambda: end_date_entry.delete(0, tk.END),
                 bg=theme.button_bg, fg=theme.button_fg, width=4).pack(side=tk.LEFT)

        button_frame = tk.Frame(search_dialog, bg=theme.bg)
        button_frame.grid(row=4, column=0, columnspan=2, pady=20)

        # 搜索按钮
        def do_advanced_search():
            keyword = keyword_entry.get().strip().lower()
            tag = tag_entry.get().strip().lower()

            # 获取日期值，如果日期字段为空，则返回 None
            start_date = None
            end_date = None

            # 检查开始日期是否有值
            if start_date_entry.get().strip():
                try:
                    start_date = start_date_entry.get_date()
                except ValueError:
                    pass

            # 检查结束日期是否有值
            if end_date_entry.get().strip():
                try:
                    end_date = end_date_entry.get_date()
                except ValueError:
                    pass

            self.advanced_search(keyword, tag, start_date, end_date)
            search_dialog.destroy()

        tk.Button(button_frame, text="搜索", command=do_advanced_search, bg=theme.button_bg, fg=theme.button_fg).pack(side=tk.LEFT, padx=10)
        tk.Button(button_frame, text="取消", command=search_dialog.destroy, bg=theme.button_bg, fg=theme.button_fg).pack(side=tk.LEFT, padx=10)

    def show_tip(self, text):
        """显示提示文本"""
        if not hasattr(self, "tip_label"):
            self.tip_label = tk.Label(self.root, text="", relief=tk.FLAT)
        self.tip_label.config(text=text)
        self.tip_label.place(x=10, y=self.root.winfo_height() - 30)

    def hide_tip(self):
        """隐藏提示文本"""
        if hasattr(self, "tip_label"):
            self.tip_label.place_forget()

    def set_font_menu(self):
        """设置字体大小对话框"""
        theme = THEMES.get(self.theme, THEMES["dark"])
        font_dialog = tk.Toplevel(self.root)
        font_dialog.title("设置字体")
        font_dialog.configure(bg=theme.bg)
        font_dialog.geometry("300x200")

        # 设置窗口在主窗口的中间
        self.root.update_idletasks()
        root_x = self.root.winfo_x()
        root_y = self.root.winfo_y()
        root_width = self.root.winfo_width()
        root_height = self.root.winfo_height()

        x = root_x + (root_width // 2) - 150
        y = root_y + (root_height // 2) - 100
        font_dialog.geometry(f"300x200+{x}+{y}")

        # 确保窗口在主窗口上面
        font_dialog.transient(self.root)
        font_dialog.grab_set()

        import tkinter.font as tkfont
        available_fonts = sorted(list(tkfont.families()))
        font_name_var = tk.StringVar()
        font_name_var.set(read_config("font_name", "Settings", "微软雅黑"))
        font_size_var = tk.StringVar()
        font_size_var.set(read_config("font_size", "Settings"))

        # 字体名称行
        font_name_frame = tk.Frame(font_dialog, bg=theme.bg)
        font_name_frame.pack(pady=(20, 5), fill=tk.X)
        tk.Label(font_name_frame, text="字体名称:", bg=theme.bg, fg=theme.fg, width=8, anchor="w").pack(side=tk.LEFT, padx=(10, 0))
        font_name_combo = ttk.Combobox(font_name_frame, textvariable=font_name_var, values=available_fonts, state="readonly", width=18)
        font_name_combo.pack(side=tk.LEFT, padx=(5, 0))

        # 字体大小行
        font_size_frame = tk.Frame(font_dialog, bg=theme.bg)
        font_size_frame.pack(pady=(5, 5), fill=tk.X)
        tk.Label(font_size_frame, text="字体大小:", bg=theme.bg, fg=theme.fg, width=8, anchor="w").pack(side=tk.LEFT, padx=(10, 0))
        font_size_entry = tk.Entry(font_size_frame, textvariable=font_size_var, bg=theme.entry_bg, fg=theme.entry_fg, width=10)
        font_size_entry.pack(side=tk.LEFT, padx=(5, 0))

        button_frame = tk.Frame(font_dialog, bg=theme.bg)
        button_frame.pack(pady=20)

        def apply_font():
            try:
                new_size = int(font_size_var.get())
                if new_size < 6 or new_size > 72:
                    messagebox.showerror("错误", "字体大小必须在6-72之间")
                    return
                new_font = font_name_var.get()
                if not new_font:
                    messagebox.showerror("错误", "请选择字体名称")
                    return
                # 更新配置文件中的字体大小和字体名称
                write_config("font_size", str(new_size), "Settings")
                write_config("font_name", new_font, "Settings")
                # 应用新主题
                self.apply_theme()
                font_dialog.destroy()
                messagebox.showinfo("成功", f"字体已设置为 {new_font} {new_size}")
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字")

        tk.Button(button_frame, text="确定", command=apply_font, bg=theme.button_bg, fg=theme.button_fg).pack(side=tk.LEFT, padx=10)
        tk.Button(button_frame, text="取消", command=font_dialog.destroy, bg=theme.button_bg, fg=theme.button_fg).pack(side=tk.LEFT, padx=10)

    def show_about(self):
        """显示关于信息"""
        messagebox.showinfo("关于", "待办事项管理\n版本: 1.0\n作者: 示例用户")

    def create_widgets(self):
        # 搜索框架
        self.search_frame = tk.Frame(self.root)  # 修改为直接放在 root 上
        self.search_frame.pack(fill=tk.X, padx=5, pady=5)  # 默认显示

        tk.Label(self.search_frame, text="搜索:").pack(side=tk.LEFT, padx=5)
        self.search_entry = tk.Entry(self.search_frame)
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.search_entry.bind("<Return>", lambda event: self.search_todos())  # 绑定回车事件

        self.show_delayed_var = tk.BooleanVar(value=False)  # 默认不选中"显示延迟任务"
        show_delayed_checkbox = tk.Checkbutton(
            self.search_frame, text="显示延迟任务", variable=self.show_delayed_var,
            command=self.search_todos
        )
        show_delayed_checkbox.pack(side=tk.LEFT, padx=5)

        self.show_all_var = tk.BooleanVar(value=False)  # 默认不选中“显示历史任务”
        show_all_checkbox = tk.Checkbutton(
            self.search_frame, text="显示所有任务", variable=self.show_all_var,
            command=self.search_todos
        )
        show_all_checkbox.pack(side=tk.LEFT, padx=5)

        tk.Button(self.search_frame, text="搜索", command=self.search_todos).pack(side=tk.LEFT, padx=5)

        # 中间框架
        middle_frame = tk.Frame(self.root)
        middle_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧Treeview框架
        self.tree_frame = tk.Frame(middle_frame, width=int(self.root.winfo_screenwidth() * 0.7))
        self.tree_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Treeview
        self.tree = ttk.Treeview(self.tree_frame, columns=("title", "status", "star_rating", "planned_time", "completed_time"),
                                 show="tree headings")
        # 设置表头
        self.tree.heading("#0", text="ID")
        self.tree.heading("title", text="标题")
        self.tree.heading("status", text="状态")
        self.tree.heading("planned_time", text="计划日期")
        self.tree.heading("star_rating", text="星级")
        self.tree.heading("completed_time", text="完成时间")

        # 设置表头字体大小为16
        self.tree.column("#0", width=60, stretch=False)
        self.tree.column("title", width=250, stretch=True)
        self.tree.column("status", width=60, stretch=False)
        self.tree.column("star_rating", width=80, stretch=False)
        self.tree.column("planned_time", width=90, stretch=False)
        self.tree.column("completed_time", width=140, stretch=False)

        self.tree.pack(fill=tk.BOTH, expand=True)

        # 展开选中任务的子任务
        self.tree.bind("<<TreeviewOpen>>", lambda e: self.expand_selected_task())

        # 配置标签样式 - 这些会在apply_theme中重新设置，这里只是初始化
        self.tree.tag_configure("alternate", background="#5a5a5a")  # 添加数据行间的淡灰色线条
        self.tree.tag_configure("completed", foreground="white", font=("Arial", 10, "overstrike"))  # 删除线字体
        self.tree.tag_configure("overdue", foreground="yellow")  # 红色文字

        # 滚动条
        tree_scroll_y = ttk.Scrollbar(self.tree, orient=tk.VERTICAL, command=self.tree.yview)
        tree_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.configure(yscrollcommand=tree_scroll_y.set)

        # 分隔条
        self.splitter = tk.Frame(middle_frame, width=5, cursor="sb_h_double_arrow", bg="gray")
        self.splitter.pack(side=tk.LEFT, fill=tk.Y)
        self.splitter.bind("<B1-Motion>", self.resize_frames)

        # 右侧详情框架
        self.details_frame = tk.Frame(middle_frame, width=300)
        self.details_frame.pack(side=tk.LEFT, fill=tk.Y)
        self.details_frame.pack_propagate(False)

        # 在 details_frame 中直接添加内容框架
        self.details_content = tk.Frame(self.details_frame)
        self.details_content.pack(fill=tk.BOTH, expand=True)

        # 底部操作按钮框架
        frame = tk.Frame(self.root)
        frame.pack(fill=tk.X)

        self.tree.bind("<<TreeviewSelect>>", self.show_details)  # 绑定选中事件
        self.refresh_todos()

    def resize_frames(self, event):
        """调整TreeView和任务详情的宽度"""

        min_width = 100  # 设置最小宽度
        max_width = self.root.winfo_width() - 200  # 确保右侧留出足够空间

        # 获取当前树形框架的宽度
        current_width = self.tree_frame.winfo_reqwidth()
        # 计算新的宽度
        new_width = current_width + event.x

        # 限制宽度范围
        if new_width < min_width:
            new_width = min_width
        elif new_width > max_width:
            new_width = max_width

        # 更新宽度配置
        self.tree_frame.configure(width=new_width)
        self.tree_frame.pack_propagate(False)  # 禁止自动调整
        self.tree.pack(expand=True, fill=tk.BOTH)  # 确保树形视图填满框架

        # 更新详情框架
        details_width = self.root.winfo_width() - new_width - self.splitter.winfo_width()
        self.details_frame.configure(width=details_width)
        self.details_frame.pack_propagate(False)

        # 强制更新布局
        self.root.update_idletasks()

    def set_bg_image(self, image_path):
        """设置背景图片，并自动拉伸铺满全屏"""
        if not os.path.exists(image_path):
            return  # 图片文件不存在，无法设置背景

        def update_bg(event=None):
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            if width < 2 or height < 2:
                return
            img = Image.open(image_path).resize((width, height), Image.Resampling.LANCZOS)
            self.bg_image = ImageTk.PhotoImage(img)
            if self.bg_label is None:
                self.bg_label = tk.Label(self.root, image=self.bg_image, borderwidth=0)
                self.bg_label.place(x=0, y=0, relwidth=1, relheight=1)
                self.bg_label.lower()
            else:
                self.bg_label.config(image=self.bg_image)
            self.bg_label.lower()

        self.root.update_idletasks()
        update_bg()
        self.root.bind("<Configure>", update_bg)

    def set_bg_image_menu(self):
        """菜单操作：选择背景图片并保存到配置文件"""
        file_path = filedialog.askopenfilename(
            title="选择背景图片",
            filetypes=[("图片文件", "*.jpg;*.jpeg;*.png;*.bmp;*.gif"), ("所有文件", "*.*")]
        )
        if not file_path or not os.path.exists(file_path):
            return
        # 保存到配置文件
        write_config("bg_image", file_path)
        self.bg_image_path = file_path
        self.set_background_image()

    def set_background_image(self):
        """设置窗口背景图片，仅在窗口上半部分显示"""
        # 优先从配置文件读取
        config_bg_path = read_config("bg_image", "Settings", None)
        if config_bg_path and os.path.exists(config_bg_path):
            self.bg_image_path = config_bg_path
        elif not (self.bg_image_path and os.path.exists(self.bg_image_path)):
            # 没有背景图片则跳过
            if self.bg_label:
                self.bg_label.destroy()
                self.bg_label = None
            return

        def update_bg(event=None):
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            if width < 2 or height < 2 or not (self.bg_image_path and os.path.exists(self.bg_image_path)):
                return
            # 只显示上半部分
            half_height = height // 2
            img = Image.open(self.bg_image_path).resize((width, half_height), Image.Resampling.LANCZOS)
            # 创建一张全窗口大小的纯色图片，将背景图粘贴到上半部分，剩下部分用深色填充
            full_img = Image.new("RGB", (width, height), "#2e2e2e")
            img = img.convert("RGB")
            full_img.paste(img, (0, 0))
            self.bg_image = ImageTk.PhotoImage(full_img)
            if self.bg_label is None:
                self.bg_label = tk.Label(self.root, image=self.bg_image, borderwidth=0)
                self.bg_label.place(x=0, y=0, relwidth=1, relheight=1)
                self.bg_label.lower()
            else:
                self.bg_label.config(image=self.bg_image)
            self.bg_label.lower()

        self.root.update_idletasks()
        update_bg()
        self.root.bind("<Configure>", update_bg)

    def expand_selected_task(self):
        """仅展开选中任务的子任务"""
        selected_item = self.tree.focus()  # 获取当前选中的任务
        if selected_item:
            self.tree.item(selected_item, open=True)  # 展开选中的任务

    def refresh_todos(self, select_id=None):
        if self.search_condition.get("type") == "simple":
            keyword = self.search_condition.get("keyword")
            show_all = self.search_condition.get("show_all")
            show_delayed = self.search_condition.get("show_delayed")
            self.load_todos(select_id, keyword = keyword, show_all = show_all, show_delayed = show_delayed)
        elif self.search_condition.get("type") == "advanced":
            keyword = self.search_condition.get("title")
            tag = self.search_condition.get("tag")
            start_date = self.search_condition.get("start_date")
            end_date = self.search_condition.get("end_date")
            show_all = self.search_condition.get("show_all")
            show_delayed = self.search_condition.get("show_delayed")
            self.load_todos(select_id, title = keyword, tag = tag, start_date = start_date, end_date = end_date, show_all = show_all, show_delayed = show_delayed)
        else:
            self.load_todos(select_id)

    def search_todos(self, select_id=None):
        keyword = self.search_entry.get().strip().lower()
        show_all = self.show_all_var.get()
        show_delayed = self.show_delayed_var.get()
        # 保存搜索条件以便下次使用
        self.search_condition = {
            "type": "simple",
            "keyword": keyword,
            "tag": None,
            "start_date": None,
            "end_date": None,
            "show_delayed": show_delayed,
            "show_all": show_all
        }
        self.load_todos(select_id, keyword = keyword, show_all = show_all, show_delayed = show_delayed)

    def advanced_search(self, keyword, tag, start_date, end_date):
        # 保存搜索条件以便下次使用
        self.search_condition = {
            "type": "advanced",
            "title": keyword,
            "tag": tag,
            "start_date": start_date,
            "end_date": end_date,
            "show_delayed": True,
            "show_all": True
        }
        self.load_todos(title = keyword, tag = tag, start_date = start_date, end_date = end_date, show_all = True, show_delayed = True)
        
    def load_todos(self, select_id=None, keyword = None, title = None, tag = None, start_date = None, end_date = None, show_all = False, show_delayed = False):
        # 保存当前选中和展开状态
        selected_items = self.tree.selection()
        selected_id = selected_items[0] if selected_items else select_id

        expanded_items = set()
        for item in self.tree.get_children():
            if self.tree.item(item, "open"):
                expanded_items.add(item)

        # 清空并重新加载数据
        for row in self.tree.get_children():
            self.tree.delete(row)
        
        todos = self.db.search_todos(keyword = keyword, title = title, tag = tag, start_date = start_date, end_date = end_date, 
                                     show_delayed = show_delayed, show_all = show_all)
        todos = [Todo(*todo) for todo in todos]  # 将元组转换为 Todo 对象
        current_time = datetime.now()

        def parse_date(date_str):
            """尝试解析多种日期格式"""
            for fmt in ("%Y-%m-%d %H:%M:%S", "%Y/%m/%d", "%Y-%m-%d"):
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            return None  # 无法解析时返回 None

        # 找到所有匹配的任务及其父级任务
        matched_todos = set()
        todo_dict = {todo.id: todo for todo in todos}  # 构建任务字典，便于查找父级任务

        def add_with_parents(todo):
            """递归添加任务及其父级任务"""
            if todo.id in matched_todos:
                return
            matched_todos.add(todo.id)
            if todo.parent_id and todo.parent_id in todo_dict:
                add_with_parents(todo_dict[todo.parent_id])

        for todo in todos:
            add_with_parents(todo)

        # 过滤出匹配的任务
        todos = [todo for todo in todos if todo.id in matched_todos]

        for index, todo in enumerate(todos):
            parent_id = int(todo.parent_id) if todo.parent_id is not None else None
            parent = "" if parent_id is None else parent_id
            # 显示星级评分（1-5颗星）
            stars = '⭐️' * todo.star_rating
            row_id = self.tree.insert(parent, tk.END, iid=todo.id, text=todo.id, values=(todo.title, todo.status, stars, todo.planned_time, todo.completed_time, todo.tag))

            # 设置字体样式和背景色
            if todo.status == "已完成":
                self.tree.item(row_id, tags=("completed", "odd" if index % 2 == 0 else "even"))
            elif todo.status == "延迟":
                self.tree.item(row_id, tags=("delayed", "odd" if index % 2 == 0 else "even"))
            elif todo.status == "待处理" and todo.planned_time and parse_date(todo.planned_time) < current_time:
                self.tree.item(row_id, tags=("overdue", "odd" if index % 2 == 0 else "even"))
            else:
                tag = "odd" if index % 2 == 0 else "even"
                self.tree.item(row_id, tags=(tag,))

        # 恢复展开状态
        for item in expanded_items:
            if self.tree.exists(item):
                self.tree.item(item, open=True)

        # 恢复选中状态
        if selected_id and self.tree.exists(selected_id):
            self.tree.selection_set(selected_id)
            self.tree.see(selected_id)  # 滚动到选中的任务
        elif not selected_id and todos:  # 如果没有选中任务且有数据，默认选中第一条
            first_item_id = todos[0].id
            self.tree.selection_set(first_item_id)
            self.tree.see(first_item_id)
            self.show_details()  # 显示详情框架
        # 刷新主题，确保TreeView样式同步
        self.apply_theme()
        
    def create_details_widgets(self):
        """创建任务详情控件，仅调用一次"""
        if self.details_initialized:
            return

        # 标题
        self.title_var = tk.StringVar()
        title_frame = tk.Frame(self.details_content)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        tk.Label(title_frame, text="标题:").pack(side=tk.LEFT)
        self.title_entry = tk.Entry(title_frame, textvariable=self.title_var)
        self.title_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        # 说明
        self.description_var = tk.StringVar()
        description_frame = tk.Frame(self.details_content)
        description_frame.pack(fill=tk.X, padx=10, pady=5)
        tk.Label(description_frame, text="说明:").pack(side=tk.LEFT, anchor="n")
        self.description_text = scrolledtext.ScrolledText(description_frame, wrap=tk.WORD, height=10)
        self.description_text.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        # 标签
        self.tag_var = tk.StringVar()
        tag_frame = tk.Frame(self.details_content)
        tag_frame.pack(fill=tk.X, padx=10, pady=5)
        tk.Label(tag_frame, text="标签:").pack(side=tk.LEFT)
        self.tag_entry = tk.Entry(tag_frame, textvariable=self.tag_var)
        self.tag_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        # 附件管理
        attachment_frame = tk.Frame(self.details_content)
        attachment_frame.pack(fill=tk.X, padx=10, pady=5)
        tk.Label(attachment_frame, text="附件:").pack(side=tk.LEFT)

        # 星级评分
        star_frame = tk.Frame(self.details_content)
        star_frame.pack(fill=tk.X, padx=10, pady=5)
        tk.Label(star_frame, text="星级:").pack(side=tk.LEFT)
        self.star_var = tk.StringVar()
        self.star_combobox = ttk.Combobox(star_frame, textvariable=self.star_var, values=["⭐️" * i for i in range(1, 4)], width=15, state='readonly')
        self.star_combobox.pack(side=tk.LEFT, padx=2)
        self.star_combobox.current(0)
            
        # 附件列表框和滚动条
        attachment_list_frame = tk.Frame(attachment_frame)
        attachment_list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0))
        self.attachment_listbox = tk.Listbox(attachment_list_frame, height=2)
        self.attachment_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        attachment_scrollbar = ttk.Scrollbar(attachment_list_frame, orient=tk.VERTICAL, command=self.attachment_listbox.yview)
        attachment_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.attachment_listbox.config(yscrollcommand=attachment_scrollbar.set)

        # 按钮框架
        attachment_button_frame = tk.Frame(attachment_frame)
        attachment_button_frame.pack(side=tk.LEFT, padx=5)

        # 加载按钮图片
        res_dir = os.path.join(os.path.dirname(__file__), "res")
        add_icon = ImageTk.PhotoImage(Image.open(os.path.join(res_dir, "add.png")).resize((20, 20), Image.Resampling.LANCZOS))
        del_icon = ImageTk.PhotoImage(Image.open(os.path.join(res_dir, "del.png")).resize((20, 20), Image.Resampling.LANCZOS))

        # 添加附件按钮
        add_button = tk.Button(attachment_button_frame, image=add_icon, command=self.add_attachment, relief=tk.FLAT)
        add_button.image = add_icon  # 防止图片被垃圾回收
        add_button.pack(fill=tk.X, pady=2)

        # 删除附件按钮
        del_button = tk.Button(attachment_button_frame, image=del_icon, command=self.delete_attachment, relief=tk.FLAT)
        del_button.image = del_icon  # 防止图片被垃圾回收
        del_button.pack(fill=tk.X, pady=2)

        # 计划时间
        planned_time_frame = tk.Frame(self.details_content)
        planned_time_frame.pack(fill=tk.X, padx=10, pady=5)
        tk.Label(planned_time_frame, text="计划日期:").pack(side=tk.LEFT)
        self.planned_time_entry = DateEntry(planned_time_frame, width=10, background="darkblue", foreground="white", borderwidth=2, date_pattern="yyyy-mm-dd")
        self.planned_time_entry.pack(side=tk.LEFT, padx=(5, 0))

        # 提醒时间（日期+时分）
        reminder_time_frame = tk.Frame(self.details_content)
        reminder_time_frame.pack(fill=tk.X, padx=10, pady=5)
        tk.Label(reminder_time_frame, text="提醒时间:").pack(side=tk.LEFT)
        self.reminder_time_var = tk.StringVar()
        self.reminder_time_entry = DateEntry(reminder_time_frame, width=10, background="darkblue", foreground="white", borderwidth=2, date_pattern="yyyy-mm-dd")
        self.reminder_time_entry.pack(side=tk.LEFT, padx=(5, 0))
        # 小时和分钟下拉框
        self.reminder_hour_var = tk.StringVar(value="")
        self.reminder_minute_var = tk.StringVar(value="")
        self.reminder_hour_box = ttk.Combobox(reminder_time_frame, width=3, textvariable=self.reminder_hour_var, values=[f"{i:02d}" for i in range(24)], state="readonly")
        self.reminder_hour_box.pack(side=tk.LEFT, padx=(5, 0))
        tk.Label(reminder_time_frame, text=":").pack(side=tk.LEFT)
        self.reminder_minute_box = ttk.Combobox(reminder_time_frame, width=3, textvariable=self.reminder_minute_var, values=[f"{i:02d}" for i in range(0, 60, 5)], state="readonly")
        self.reminder_minute_box.pack(side=tk.LEFT)
        # 删除提醒按钮
        del_icon = ImageTk.PhotoImage(Image.open(os.path.join(res_dir, "del.png")).resize((10, 20), Image.Resampling.LANCZOS))
        del_reminder_btn = tk.Button(reminder_time_frame, image=del_icon, command=self.clear_reminder_time, relief=tk.FLAT)
        del_reminder_btn.image = del_icon  # 防止图片被垃圾回收
        del_reminder_btn.pack(side=tk.LEFT, padx=5)

        # 状态和时间信息
        self.status_label = tk.Label(self.details_content)
        self.status_label.pack(anchor="w", padx=10, pady=5)
        self.completed_time_label = tk.Label(self.details_content)
        self.completed_time_label.pack(anchor="w", padx=10, pady=5)
        self.created_time_label = tk.Label(self.details_content)
        self.created_time_label.pack(anchor="w", padx=10, pady=5)

        # 操作按钮
        button_frame = tk.Frame(self.details_content)
        button_frame.pack(pady=2, fill=tk.X)
        self.complete_button = tk.Button(button_frame, text="标记完成")
        self.complete_button.pack(side=tk.LEFT, padx=5)
        self.uncomplete_button = tk.Button(button_frame, text="标记未完成")
        self.uncomplete_button.pack(side=tk.LEFT, padx=5)
        self.delay_button = tk.Button(button_frame, text="延迟处理")
        self.delay_button.pack(side=tk.LEFT, padx=5)

        self.attachment_listbox.bind("<Double-Button-1>", self.open_attachment)  # 绑定双击事件

        self.details_initialized = True

    def clear_reminder_time(self):
        """清除提醒时间"""
        self.reminder_time_entry.set_date(None)  # 清空日期
        self.reminder_time_var.set("")  # 清空日期
        self.reminder_hour_var.set("")  # 重置小时
        self.reminder_minute_var.set("")  # 重置分钟
        self._details_dirty = True  # 标记为脏数据
        selected_item = self.tree.selection()
        todo_id = self.tree.item(selected_item[0], "text")
        if self._details_dirty:
            self.save_details(todo_id)
            self._details_dirty = False

    def open_attachment(self, event):
        """双击附件以打开"""
        selected_attachment_index = self.attachment_listbox.curselection()
        if not selected_attachment_index:
            return

        selected_attachment = self.attachment_listbox.get(selected_attachment_index)
        attachments = self.db.get_attachments(self.tree.item(self.tree.selection()[0], "text"))
        attachment = next((att for att in attachments if att[3] == selected_attachment), None)
        
        if attachment:
            file_content = attachment[5]  # 获取文件内容
            file_name = attachment[3]  # 获取文件名

            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            temp_file_path = os.path.join(temp_dir, file_name)

            try:
                with open(temp_file_path, 'wb') as temp_file:
                    temp_file.write(file_content)
                # 使用默认应用打开文件
                subprocess.run(['start', temp_file_path], shell=True, check=False)
            except Exception as e:
                print(f"无法打开附件: {e}")
                messagebox.showerror("错误", f"无法打开附件: {e}")

    def save_details(self, todo_id):
        """保存任务详情中的修改内容并刷新Treeview"""
        title = self.title_var.get().strip()
        description = self.description_text.get("1.0", tk.END).strip()
        tag = self.tag_var.get().strip()
        planned_time = self.planned_time_entry.get_date() if self.planned_time_entry.get() else None
        star_rating = int(len(self.star_var.get()) / 2)
        # 组合提醒时间
        if self.reminder_time_entry.get():
            date_str = self.reminder_time_entry.get()
            hour = self.reminder_hour_var.get()
            minute = self.reminder_minute_var.get()
            reminder_time = f"{date_str} {hour}:{minute}:00"
        else:
            reminder_time = None

        if not title:
            messagebox.showerror("错误", "标题不能为空")
            return

        query = """
        UPDATE todos
        SET title = ?, description = ?, tag = ?, planned_time = ?, reminder_time = ?, star_rating = ?
        WHERE id = ?
        """
        self.db.connection.execute(query, (title, description, tag, planned_time, reminder_time, star_rating, todo_id))
        self.db.connection.commit()
        self.refresh_todos(select_id=todo_id)

    def show_details(self, event=None):
        """显示任务详情，仅更新数据"""
        selected_item = self.tree.selection()
        if not selected_item:
            return

        todo_id = self.tree.item(selected_item[0], "text")
        query = "SELECT * FROM todos WHERE id = ?"
        todo = self.db.connection.execute(query, (todo_id,)).fetchone()

        if not todo:
            return

        # 初始化控件
        self.create_details_widgets()

        # 更新数据
        if self.description_text.winfo_exists():  # 确保控件未被销毁
            self.title_var.set(todo[1])
            self.description_text.delete("1.0", tk.END)
            self.description_text.insert("1.0", todo[2] or "")
            self.tag_var.set(todo[8] or "")
            self.star_var.set("⭐️" * todo[10])
            if todo[5]:
                self.planned_time_entry.set_date(todo[5])
            else:
                self.planned_time_entry.delete(0, tk.END)
            # 加载提醒时间
            if len(todo) > 9 and todo[9]:
                try:
                    dt = datetime.strptime(todo[9], "%Y-%m-%d %H:%M:%S")
                    self.reminder_time_entry.set_date(dt.date())
                    self.reminder_hour_var.set(f"{dt.hour:02d}")
                    self.reminder_minute_var.set(f"{dt.minute:02d}")
                except Exception:
                    try:
                        dt = datetime.strptime(todo[9], "%Y-%m-%d")
                        self.reminder_time_entry.set_date(dt.date())
                        self.reminder_hour_var.set("")
                        self.reminder_minute_var.set("")
                    except Exception:
                        self.reminder_time_entry.delete(0, tk.END)
                        self.reminder_hour_var.set("")
                        self.reminder_minute_var.set("")
            else:
                self.reminder_time_entry.delete(0, tk.END)
                self.reminder_hour_var.set("")
                self.reminder_minute_var.set("")
            self.status_label.config(text=f"状态: {todo[3]}")
            self.created_time_label.config(text=f"创建时间: {todo[4]}")
            self.completed_time_label.config(text=f"完成时间: {todo[6] or 'None'}")

        # 更新附件列表
        attachments = self.db.get_attachments(todo_id)
        self.attachment_listbox.delete(0, tk.END)
        for attachment in attachments:
            self.attachment_listbox.insert(tk.END, attachment[3])  # 显示文件名

        # 更新按钮状态
        if todo[3] == "已完成":
            self.complete_button.config(state=tk.DISABLED, command=None)
            self.uncomplete_button.config(state=tk.NORMAL, command=lambda: self.mark_uncompleted(todo_id))
            self.delay_button.config(state=tk.DISABLED, command=None)
        elif todo[3] == "延迟":
            self.complete_button.config(state=tk.NORMAL, command=lambda: self.mark_completed(todo_id))
            self.uncomplete_button.config(state=tk.NORMAL, command=lambda: self.mark_uncompleted(todo_id))
            self.delay_button.config(state=tk.DISABLED, command=None)
        else:  # 待处理状态
            self.complete_button.config(state=tk.NORMAL, command=lambda: self.mark_completed(todo_id))
            self.uncomplete_button.config(state=tk.DISABLED, command=None)
            self.delay_button.config(state=tk.NORMAL, command=lambda: self.mark_delayed(todo_id))

        # --- 变更监控相关 ---
        # 标志变量
        self._details_dirty = False

        def on_change(event=None):
            self._details_dirty = True

        def on_change2(event=None):
            self._details_dirty = True
            # 自动补全小时和分钟，避免为空导致保存异常
            if not self.reminder_hour_var.get():
                self.reminder_hour_var.set("09")
            if not self.reminder_minute_var.get():
                self.reminder_minute_var.set("00")

        def on_focus_out(event=None):
            if self._details_dirty:
                self.save_details(todo_id)
                self._details_dirty = False

        # 绑定内容变更事件
        self.title_entry.bind("<KeyRelease>", on_change)
        self.description_text.bind("<KeyRelease>", on_change)
        self.tag_entry.bind("<KeyRelease>", on_change)
        if hasattr(self.planned_time_entry, "bind"):
            self.planned_time_entry.bind("<<DateEntrySelected>>", on_change)
        if hasattr(self.reminder_time_entry, "bind"):
            self.reminder_time_entry.bind("<<DateEntrySelected>>", on_change2)
        self.reminder_hour_box.bind("<<ComboboxSelected>>", on_change)
        self.reminder_minute_box.bind("<<ComboboxSelected>>", on_change)
        self.star_combobox.bind('<<ComboboxSelected>>', on_change)

        # 绑定失去焦点事件
        self.title_entry.bind("<FocusOut>", on_focus_out)
        self.description_text.bind("<FocusOut>", on_focus_out)
        self.tag_entry.bind("<FocusOut>", on_focus_out)
        self.planned_time_entry.bind("<FocusOut>", on_focus_out)
        self.reminder_time_entry.bind("<FocusOut>", on_focus_out)
        self.reminder_hour_box.bind("<FocusOut>", on_focus_out)
        self.reminder_minute_box.bind("<FocusOut>", on_focus_out)
        self.star_combobox.bind("<FocusOut>", on_focus_out)

        # 显示详情框架
        self.details_frame.pack(side=tk.RIGHT, fill=tk.Y)

    def hide_details(self):
        """隐藏任务详情"""
        self.details_frame.pack_forget()

    def add_todo(self):
        """添加新任务"""
        theme = THEMES.get(self.theme, THEMES["dark"])
        def save_todo():
            title = title_entry.get().strip()
            description = description_text.get("1.0", tk.END).strip()
            planned_time = planned_time_entry.get()
            tag = tag_entry.get().strip()
            parent_title = parent_id_var.get()
            reminder_time = None

            if not title:
                messagebox.showerror("错误", "标题不能为空")
                return
            
            # 根据标题获取上级任务的 ID
            parent_id = None if parent_title == "无" else title_to_id.get(parent_title)

            self.db.add_todo(title, description, planned_time, parent_id, tag=tag)
            # 新增提醒时间
            if reminder_time:
                query = "UPDATE todos SET reminder_time = ? WHERE id = (SELECT id FROM todos WHERE title = ? ORDER BY id DESC LIMIT 1)"
                self.db.connection.execute(query, (reminder_time, title))
                self.db.connection.commit()
            
            # 获取新添加的任务ID
            query = "SELECT id FROM todos WHERE title = ? ORDER BY id DESC LIMIT 1"
            new_todo_id = self.db.connection.execute(query, (title,)).fetchone()[0]

            # 如果是子级任务，记录父级任务ID，以便展开
            parent_to_expand = parent_id

            # 刷新任务列表，并传递新任务ID以便选中
            self.refresh_todos(new_todo_id)

            # 如果是子级任务，展开父级任务
            if parent_to_expand:
                # 确保父任务展开
                self.tree.item(parent_to_expand, open=True)

            # 再次确保选中新添加的任务
            self.tree.selection_set(new_todo_id)
            self.tree.see(new_todo_id)  # 滚动到新任务的位置
            
            self.refresh_todos()
            add_window.destroy()

        add_window = tk.Toplevel(self.root)
        add_window.title("新增任务")
        add_window.configure(bg=theme.bg)
        add_window.resizable(False, False)  # 禁止调整窗口大小
        add_window.bind("<Escape>", lambda e: add_window.destroy())  # 响应 ESC 按钮关闭窗口

        # 设置窗口在主窗口的中间
        self.root.update_idletasks()
        root_x = self.root.winfo_x()
        root_y = self.root.winfo_y()
        root_width = self.root.winfo_width()
        root_height = self.root.winfo_height()

        add_window.update_idletasks()
        width = add_window.winfo_width()
        height = add_window.winfo_height()
        x = root_x + (root_width // 2) - (width // 2)
        y = root_y + (root_height // 2) - (height // 2)
        add_window.geometry(f"+{x}+{y}")

        # 确保窗口在主窗口上面
        add_window.transient(self.root)
        add_window.grab_set()

        tk.Label(add_window, text="标题:", bg=theme.bg, fg=theme.fg).grid(row=0, column=0, padx=5, pady=5, sticky="w")
        title_entry = tk.Entry(add_window, bg=theme.entry_bg, fg=theme.entry_fg, width=40)
        title_entry.grid(row=0, column=1, padx=5, pady=5, sticky="w")

        tk.Label(add_window, text="说明:", bg=theme.bg, fg=theme.fg).grid(row=1, column=0, padx=5, pady=5, sticky="w")
        description_text = scrolledtext.ScrolledText(add_window, wrap=tk.WORD, height=5, bg=theme.entry_bg, fg=theme.entry_fg, width=40)
        description_text.grid(row=1, column=1, padx=5, pady=5, sticky="w")

        tk.Label(add_window, text="计划时间:", bg=theme.bg, fg=theme.fg).grid(row=2, column=0, padx=5, pady=5, sticky="w")
        planned_time_entry = DateEntry(add_window, width=10, background=theme.button_bg, foreground=theme.entry_fg, borderwidth=2, date_pattern="yyyy-mm-dd")
        planned_time_entry.grid(row=2, column=1, padx=5, pady=5, sticky="w")

        tk.Label(add_window, text="标签:", bg=theme.bg, fg=theme.fg).grid(row=3, column=0, padx=5, pady=5, sticky="w")
        tag_entry = tk.Entry(add_window, bg=theme.entry_bg, fg=theme.entry_fg)
        tag_entry.grid(row=3, column=1, padx=5, pady=5, sticky="w")

        tk.Label(add_window, text="上级任务:", bg=theme.bg, fg=theme.fg).grid(row=4, column=0, padx=5, pady=5, sticky="w")
        parent_id_var = tk.StringVar(value="无")
        todos = self.db.search_todos()
        title_to_id = {todo[1]: todo[0] for todo in todos if todo[3] != "已完成"}
        parent_options = ["无"] + list(title_to_id.keys())
        parent_menu = ttk.Combobox(add_window, textvariable=parent_id_var, values=parent_options, state="readonly")
        parent_menu.grid(row=4, column=1, padx=5, pady=5, sticky="w")

        # 如果有选中的任务，默认设置为父级任务
        selected_item = self.tree.selection()
        if selected_item:
            selected_todo_id = self.tree.item(selected_item[0], "text")
            for title, todo_id in title_to_id.items():
                if todo_id == int(selected_todo_id):
                    parent_id_var.set(title)
                    break

        tk.Button(add_window, text="保存", command=save_todo, bg=theme.button_bg, fg=theme.button_fg).grid(row=5, column=0, columnspan=5, pady=10)

    def delete_todo(self):
        """删除选中的任务"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showerror("错误", "请选择一个任务")
            return

        todo_id = self.tree.item(selected_item[0], "text")

        # 确认删除
        if not messagebox.askyesno("确认", "确定要删除该任务吗？"):
            return

        # 删除任务
        delete_query = "DELETE FROM todos WHERE id = ?"
        self.db.connection.execute(delete_query, (todo_id,))
        self.db.connection.commit()

        # 刷新任务列表
        self.refresh_todos()
        
        # 清除选中状态，确保下次点击能正确触发选中事件
        self.tree.selection_remove(self.tree.selection())
        self.hide_details()

    def mark_completed(self, todo_id=None):
        """标记任务为已完成"""
        if not todo_id:
            selected_item = self.tree.selection()
            if not selected_item:
                messagebox.showerror("错误", "请选择一个任务")
                return
            todo_id = self.tree.item(selected_item[0], "text")

        def mark_todos_as_completed(todo_id):
            """递归标记子任务为已完成"""
            completed_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            query = """
            UPDATE todos
            SET status = '已完成', completed_time = ?
            WHERE id = ?
            """
            self.db.connection.execute(query, (completed_time, todo_id))
            self.db.connection.commit()
        
            # 查找子任务
            subtask_query = "SELECT id FROM todos WHERE status='待处理' AND parent_id = ?"
            subtasks = self.db.connection.execute(subtask_query, (todo_id,)).fetchall()
            for subtask in subtasks:
                mark_todos_as_completed(subtask[0])  # 递归标记子任务

        mark_todos_as_completed(todo_id)
        # 刷新任务列表
        self.refresh_todos()

    def mark_uncompleted(self, todo_id=None):
        """标记任务为未完成"""
        if not todo_id:
            selected_item = self.tree.selection()
            if not selected_item:
                messagebox.showerror("错误", "请选择一个任务")
                return
            todo_id = self.tree.item(selected_item[0], "text")

        def mark_todos_as_uncompleted(todo_id):
            query = """
            UPDATE todos
            SET status = '待处理', completed_time = NULL
            WHERE id = ?
            """
            self.db.connection.execute(query, (todo_id,))
            self.db.connection.commit()
            
            # 查找父级任务，并标记为未完成
            parent_query = "SELECT parent_id FROM todos WHERE id =?"
            parent_id = self.db.connection.execute(parent_query, (todo_id,)).fetchone()
            if parent_id:
                parent_id = parent_id[0]
                mark_todos_as_uncompleted(parent_id)  # 递归标记父级任务

        mark_todos_as_uncompleted(todo_id)
        # 刷新任务列表
        self.refresh_todos()

    def mark_delayed(self, todo_id=None):
        """标记任务及其所有待处理子任务为延迟状态"""
        if not todo_id:
            selected_item = self.tree.selection()
            if not selected_item:
                messagebox.showerror("错误", "请选择一个任务")
                return
            todo_id = self.tree.item(selected_item[0], "text")

        def mark_todos_as_delayed(todo_id):
            """递归标记子任务为延迟状态"""
            query = """
            UPDATE todos
            SET status = '延迟', reminder_time = NULL
            WHERE id = ?
            """
            self.db.connection.execute(query, (todo_id,))
            self.db.connection.commit()
        
            # 查找待处理的子任务
            subtask_query = "SELECT id FROM todos WHERE status='待处理' AND parent_id = ?"
            subtasks = self.db.connection.execute(subtask_query, (todo_id,)).fetchall()
            for subtask in subtasks:
                mark_todos_as_delayed(subtask[0])  # 递归标记子任务
                
        mark_todos_as_delayed(todo_id)
        self.refresh_todos()

    def add_attachment(self):
        """添加附件到任务"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showerror("错误", "请选择一个任务")
            return

        todo_id = self.tree.item(selected_item[0], "text")
        file_path = filedialog.askopenfilename(title="选择文件")
        if not file_path:
            return

        # 从文件路径中读取文件内容
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read()
        except Exception as e:
            messagebox.showerror("错误", f"读取文件失败: {e}")
            return

        # 保存附件到数据库
        query = """
        INSERT INTO attachments (todo_id, file_path, file_name, file_content, created_time)
        VALUES (?, ?, ?, ?, ?)
        """
        file_name = os.path.basename(file_path)
        created_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.db.connection.execute(query, (todo_id, file_path, file_name, file_content, created_time))
        self.db.connection.commit()

        # messagebox.showinfo("成功", "附件添加成功")
        self.show_details()  # 刷新任务详情

    def delete_attachment(self):
        """删除选中的附件"""
        selected_attachment_index = self.attachment_listbox.curselection()
        if not selected_attachment_index:
            messagebox.showerror("错误", "请选择一个附件")
            return

        selected_attachment = self.attachment_listbox.get(selected_attachment_index)
        attachments = self.db.get_attachments(self.tree.item(self.tree.selection()[0], "text"))
        attachment_id = next((att[0] for att in attachments if att[3] == selected_attachment), None)

        if attachment_id:
            if messagebox.askyesno("确认", "确定要删除该附件吗？"):
                self.db.delete_attachment(attachment_id)
                # messagebox.showinfo("成功", "附件删除成功")
                self.show_details()  # 刷新任务详情

    def on_close(self):
        self.db.close()

        self.root.destroy()

    def check_reminders(self):
        """定时检查提醒任务"""
        now = datetime.now()
        ahead = self.reminder_ahead_minutes
        todos = self.db.get_todos()
        for todo in todos:
            todo_id = todo[0]
            title = todo[1]
            status = todo[3]
            reminder_time_str = todo[9] if len(todo) > 9 else None
            # 只对“待处理”状态的任务处理提醒
            if status != "待处理" or not reminder_time_str:
                continue
            try:
                reminder_time = datetime.strptime(reminder_time_str, "%Y-%m-%d %H:%M:%S")
            except Exception:
                try:
                    reminder_time = datetime.strptime(reminder_time_str, "%Y-%m-%d")
                except Exception:
                    continue
            # 只提醒未完成且未被延迟的任务
            if todo_id in self.reminded_tasks:
                continue
            # 延迟提醒
            if todo_id in self.reminder_delay_map:
                if now < self.reminder_delay_map[todo_id]:
                    continue
                else:
                    del self.reminder_delay_map[todo_id]
            # 提前N分钟提醒
            delta = (reminder_time - now).total_seconds() / 60
            if 0 <= delta <= ahead:
                self.show_reminder_popup(todo_id, title, reminder_time)
        # 每30秒检查一次
        self.root.after(30000, self.check_reminders)

    def show_reminder_popup(self, todo_id, title, reminder_time):
        """弹出提醒窗口并闪烁任务栏"""
        def on_confirm():
            self.reminded_tasks.add(todo_id)
            popup.destroy()
        def on_delay():
            self.reminder_delay_map[todo_id] = datetime.now() + timedelta(minutes=10)
            popup.destroy()
        popup = tk.Toplevel(self.root)
        popup.title("任务提醒")
        popup.geometry("350x150")
        popup.attributes("-topmost", True)
        tk.Label(popup, text=f"任务：{title}\n提醒时间：{reminder_time.strftime('%Y-%m-%d %H:%M')}", font=("Arial", 12)).pack(pady=20)
        btn_frame = tk.Frame(popup)
        btn_frame.pack(pady=10)
        tk.Button(btn_frame, text="确定", width=10, command=on_confirm).pack(side=tk.LEFT, padx=10)
        tk.Button(btn_frame, text="延迟10分钟", width=12, command=on_delay).pack(side=tk.LEFT, padx=10)
        # 任务栏闪烁
        try:
            import ctypes
            hwnd = int(self.root.frame(), 16) if hasattr(self.root, 'frame') else self.root.winfo_id()
            FLASHW_ALL = 3
            class FLASHWINFO(ctypes.Structure):
                _fields_ = [("cbSize", ctypes.c_uint),
                            ("hwnd", ctypes.c_void_p),
                            ("dwFlags", ctypes.c_uint),
                            ("uCount", ctypes.c_uint),
                            ("dwTimeout", ctypes.c_uint)]
            flash = FLASHWINFO(ctypes.sizeof(FLASHWINFO), hwnd, FLASHW_ALL, 5, 0)
            ctypes.windll.user32.FlashWindowEx(ctypes.byref(flash))
        except Exception:
            pass

class Theme:
    def __init__(self, name, **kwargs):
        self.name = name
        self.bg = kwargs.get("bg")
        self.fg = kwargs.get("fg")
        self.entry_bg = kwargs.get("entry_bg")
        self.entry_fg = kwargs.get("entry_fg")
        self.button_bg = kwargs.get("button_bg")
        self.button_fg = kwargs.get("button_fg")
        self.tree_bg = kwargs.get("tree_bg")
        self.tree_fg = kwargs.get("tree_fg")
        self.tree_sel_bg = kwargs.get("tree_sel_bg")
        self.tree_sel_fg = kwargs.get("tree_sel_fg")
        self.odd_bg = kwargs.get("odd_bg")
        self.even_bg = kwargs.get("even_bg")
        self.overdue_fg = kwargs.get("overdue_fg")
        self.completed_fg = kwargs.get("completed_fg")
        self.scrollbar_bg = kwargs.get("scrollbar_bg")
        self.scrollbar_trough = kwargs.get("scrollbar_trough")
        self.delayed_fg = kwargs.get("delayed_fg")  # 添加延迟状态颜色配置
        self.font_size = kwargs.get("font_size", 10)  # 添加字体大小配置，默认为10

# 主题定义
THEMES = {
    "dark": Theme(
        "dark",
        bg="#2e2e2e",
        fg="white",

        entry_bg="#3e3e3e",
        entry_fg="white",
        button_bg="#4e4e4e",
        button_fg="white",
        tree_bg="#3a3a5a",
        tree_fg="white",
        tree_sel_bg="#5e5e5e",
        tree_sel_fg="white",
        odd_bg="#4a4a4a",
        even_bg="#3e3e3e",
        overdue_fg="yellow",
        completed_fg="white",
        scrollbar_bg="#3e3e3e",
        scrollbar_trough="#2e2e2e",
        delayed_fg="#90EE90"
    ),
    "light": Theme(
        "light",
        bg="#f5f5f5",
        fg="#222",
        entry_bg="#ffffff",
        entry_fg="#222",
        button_bg="#e0e0e0",
        button_fg="#222",
        tree_bg="#f5f5f5",
        tree_fg="#222",
        tree_sel_bg="#b3d1ff",
        tree_sel_fg="#222",
        odd_bg="#f0f0f0",
        even_bg="#e9e9e9",
        overdue_fg="red",
        completed_fg="#888",
        scrollbar_bg="#e0e0e0",
        scrollbar_trough="#f5f5f5",
        delayed_fg="#98FB98"
    ),
    "theme:custome": Theme(
        "theme:custome",
        bg = read_config("bg", 'theme:custome'),
        fg = read_config("fg", 'theme:custome'),
        entry_bg = read_config("entry_bg", 'theme:custome'),
        entry_fg = read_config("entry_fg", 'theme:custome'),
        button_bg = read_config("button_bg", 'theme:custome'),
        button_fg = read_config("button_fg", 'theme:custome'),
        tree_bg = read_config("tree_bg", 'theme:custome'),
        tree_fg = read_config("tree_fg", 'theme:custome'),
        tree_sel_bg = read_config("tree_sel_bg", 'theme:custome'),
        tree_sel_fg = read_config("tree_sel_fg", 'theme:custome'),
        odd_bg = read_config("odd_bg", 'theme:custome'),
        even_bg = read_config("even_bg", 'theme:custome'),
        overdue_fg = read_config("overdue_fg", 'theme:custome'),
        completed_fg = read_config("completed_fg", 'theme:custome'),
        scrollbar_bg = read_config("scrollbar_bg", 'theme:custome'),
        scrollbar_trough = read_config("scrollbar_trough", 'theme:custome'),
        delayed_fg = read_config("delayed_fg", 'theme:custome', "#90EE90")
    )
}

if __name__ == "__main__":
    #  主题定义和Theme类需要放在所有类定义之前或主程序之前
    root = tk.Tk()
    root.state('zoomed')  # 启动时最大化窗口
    app = TodoApp(root)
    root.protocol("WM_DELETE_WINDOW", app.on_close)
    root.mainloop()