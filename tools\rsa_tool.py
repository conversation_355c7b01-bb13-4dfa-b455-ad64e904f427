from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.primitives import serialization

from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives import hashes

def generate_keys(key_size=2048, out_dir=None):
    # Generate an RSA private key
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=key_size
    )

    # Get the public key
    public_key = private_key.public_key()

    # Serialize the private key
    pem_private = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.TraditionalOpenSSL,
        encryption_algorithm=serialization.NoEncryption()
    )

    # Serialize the public key
    pem_public = public_key.public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo
    )

    # Print the private key in PKCS#8 format
    print("Private Key (PKCS#1):")
    print(pem_private.decode('utf-8'))

    # Print the public key in PKCS#8 format
    print("Public Key (PKCS#1):")
    print(pem_public.decode('utf-8'))

    if out_dir:
        # Save the keys to files
        with open(f'{out_dir}/private_key.pem', 'wb') as f:
            f.write(pem_private)

        with open(f'{out_dir}/public_key.pem', 'wb') as f:
            f.write(pem_public)

def generate_pkcs8_keypair(key_size=2048, out_dir=None):
    # 生成RSA密钥对
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=key_size
    )

    # 将私钥转换为PKCS#8格式
    private_key_bytes = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption()
    )

    # 将公钥转换为PKCS#8格式
    public_key_bytes = private_key.public_key().public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.PKCS1
    )

    # 打印私钥和公钥
    print("Private Key (PKCS#8):")
    print(private_key_bytes.decode('utf-8'))

    print("Public Key (PKCS#8):")
    print(public_key_bytes.decode('utf-8'))

    if out_dir:
        # 保存私钥和公钥到文件
        with open(f'{out_dir}/private_key.pem', 'wb') as f:
            f.write(private_key_bytes)

        with open(f'{out_dir}/public_key.pem', 'wb') as f:
            f.write(public_key_bytes)
            
    return private_key_bytes.decode('utf-8'), public_key_bytes.decode('utf-8')


def read_pem_cert(file_path):
    from cryptography import x509
    from cryptography.hazmat.backends import default_backend
    
    with open(file_path, 'rb') as f:
        cert_data = f.read()

    cert = x509.load_pem_x509_certificate(cert_data, default_backend())

    # 获取证书信息
    issuer = cert.issuer
    subject = cert.subject
    serial_number = cert.serial_number
    not_before = cert.not_valid_before
    not_after = cert.not_valid_after
    public_key = cert.public_key()

    # 打印证书信息
    print("Issuer:", issuer)
    print("Subject:", subject)
    print("Serial Number:", serial_number)
    print("Not Before:", not_before)
    print("Not After:", not_after)
    print("Public Key:", public_key)

def read_pem_cert2(file_path):
    from cryptography.hazmat.primitives import serialization

    with open(file_path, 'rb') as f:
        private_key_data = f.read()

    private_key = serialization.load_pem_private_key(
        private_key_data,
        password=None,  # 如果私钥文件有密码，需要提供密码
    )

    print(private_key)

# 检查PEM文件格式
def check_pem_format(pem_file):
    from cryptography.hazmat.primitives import serialization
    from cryptography.hazmat.primitives.asymmetric import rsa
    
    with open(pem_file, 'rb') as f:
        pem_data = f.read()

    try:
        # 尝试解析为PKCS#8私钥
        private_key = serialization.load_pem_private_key(
            pem_data,
            password=None
        )
        return "PKCS#8"
    except ValueError:
        pass

    try:
        # 尝试解析为PKCS#1私钥
        private_key = serialization.load_pem_private_key(
            pem_data,
            password=None,
            backend=None
        )
        return "PKCS#1"
    except ValueError:
        pass

    try:
        # 尝试解析为证书
        cert = serialization.load_pem_x509_certificate(pem_data)
        return "证书"
    except ValueError:
        pass
    
    with open(pem_file, 'r') as file:
        header = file.readline().strip()
        footer = file.readline().strip()

    if header.startswith(b'-----BEGIN RSA PRIVATE KEY-----') and \
        footer.startswith(b'-----END RSA PRIVATE KEY-----'):
        return "PKCS#1"
    elif header.startswith(b'-----BEGIN PRIVATE KEY-----') and \
            footer.startswith(b'-----END PRIVATE KEY-----'):
        return "PKCS#8"
    else:
        return "Unknown format"
    
    return "未知格式"

############################################################################
# 加载公钥
def load_public_key_from_pem(pem_file):
    with open(pem_file, 'rb') as f:
        pem_data = f.read()
    public_key = serialization.load_pem_public_key(pem_data)
    return public_key

# 加密数据
def encrypt_data(public_key, data):
    encrypted_data = public_key.encrypt(
        data,
        padding.OAEP(
            mgf=padding.MGF1(algorithm=hashes.SHA256()),
            algorithm=hashes.SHA256(),
            label=None
        )
    )
    return encrypted_data

# 验证签名
def verify_signature(public_key, signature, data):
    try:
        public_key.verify(
            signature,
            data,
            padding.PSS(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        return True
    except:
        return False

# 加载私钥
def load_private_key_from_pem(pem_file):
    with open(pem_file, 'rb') as f:
        pem_data = f.read()
    private_key = serialization.load_pem_private_key(pem_data, password=None)
    return private_key

# 解密数据
def decrypt_data(private_key, encrypted_data):
    decrypted_data = private_key.decrypt(
        encrypted_data,
        padding.OAEP(
            mgf=padding.MGF1(algorithm=hashes.SHA256()),
            algorithm=hashes.SHA256(),
            label=None
        )
    )
    return decrypted_data

# 签名数据
def sign_data(private_key, data):
    signature = private_key.sign(
        data,
        padding.PSS(
            mgf=padding.MGF1(algorithm=hashes.SHA256()),
            salt_length=padding.PSS.MAX_LENGTH
        ),
        hashes.SHA256()
    )
    return signature

if __name__ == '__main__':
    # 读取PEM证书文件
    # read_pem_cert2('D:/TMP/a.pem')
    # generate_keys(key_size=1024)
    # print(check_pem_format('D:/TMP/a.pem'))
    
    # 测试
    # private_key, public_key = generate_pkcs8_keypair()
    # print("私钥:")
    # print(private_key)
    # print("公钥:")
    # print(public_key)
    private_key = load_private_key_from_pem('D:/TMP/私钥.pem')
    public_key = load_public_key_from_pem('D:/TMP/公钥.pem')
    data = b'Hello World'
    #加解密
    encrypted_data = encrypt_data(public_key, data)
    print(encrypted_data)
    decrypted_data = decrypt_data(private_key, encrypted_data)
    print(decrypted_data)
    #签名验签
    signature = sign_data(private_key, data)
    print(signature)
    print(verify_signature(public_key, signature, data))
    
    public_key = load_public_key_from_pem('D:/TMP/公钥2.pem')