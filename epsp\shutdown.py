import requests

def shutdown(url):
    print("Shutting down server:"+url)
    response = requests.post(url)

    if response.status_code != 200:
        print("Error shutting down server")
        print(response.text)
        print(response.content)

def pause(url):
    print("Pausing server:"+url)
    response = requests.post(url)

    if response.status_code != 200:
        print("Error pausing server")
        print(response.text)
        print(response.content)
        
if __name__ == "__main__":
    shutdown("http://localhost:8320/shutdown")
    # pause("http://localhost:8320/pause")
    # shutdown("http://localhost:8120/shutdown")