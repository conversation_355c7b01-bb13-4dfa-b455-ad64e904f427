from haystack.document_stores import ElasticsearchDocumentStore

document_store = ElasticsearchDocumentStore(host="localhost", port=9200, username="", password="", index="document")

def pre_process_text(text):
    from haystack.nodes import PreProcessor

    preprocessor = PreProcessor(
        clean_empty_lines=True,
        clean_whitespace=True,
        clean_header_footer=True,
        split_by="word",
        split_length=100,
        split_respect_sentence_boundary=True
    )

    docs = [{"content": text, "meta": {"name": "example"}}]
    preprocessed_docs = preprocessor.process(docs)
    document_store.write_documents(preprocessed_docs)

def get_answer(question):
    from haystack.retrievers import ElasticsearchRetriever
    retriever = ElasticsearchRetriever(document_store=document_store)
    
    from haystack.nodes import FARMReader
    reader = FARMReader(model_name_or_path="deepset/roberta-base-squad2", use_gpu=True)

    from haystack.pipelines import ExtractiveQAPipeline
    pipe = ExtractiveQAPipeline(reader, retriever)
    
    prediction = pipe.run(query=question, params={"Retriever": {"top_k": 5}, "Reader": {"top_k": 3}})
    print(prediction)

import sys
print(sys.prefix == sys.base_prefix)
