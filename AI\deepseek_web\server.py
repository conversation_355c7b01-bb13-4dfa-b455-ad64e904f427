from flask import Flask, request, jsonify
import sys
import os

# 添加deepseek路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../AI/deepseek'))
from demo1 import DeepSeekQA  # 假设demo1.py中有DeepSeekQA类

app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False  # 确保中文正常显示

# 初始化问答系统
qa_system = DeepSeekQA()

@app.route('/ask', methods=['POST'])
def ask():
    data = request.json
    question = data.get('question', '')
    
    if not question:
        return jsonify({'answer': '请输入有效的问题'})
    
    try:
        # 调用deepseek获取答案
        answer = qa_system.get_answer(question)
        return jsonify({'answer': answer})
    except Exception as e:
        print(f"Error: {str(e)}")
        return jsonify({'answer': '系统处理问题出错，请稍后重试'})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
