@startuml transaction
title "Glocal card Amazon Payment Flow"

actor "Card Owner"  as c
actor "Merchant"  as m
participant "ePaylinks"  as e
participant "Amazon"  as a
participant "Master Card、 Visa、JCB"  as i

c -> m: Request Payment
m -> e: Request Payment
e -> e: Store Payment Request in Database
e -> a: Request Assessment
a --> e: Assessment Result
e -> e: Verify Assessment Result
e -> e: Store Assessment Result in Database
alt Assessment Result is Approved
    e -> i: Request Payment
    i -> e: Payment Approved
    e -> m: Payment Result
    m -> c: Payment Result
else
    e -> m: Payment Rejected
    m -> c: Payment Rejected
end

@enduml