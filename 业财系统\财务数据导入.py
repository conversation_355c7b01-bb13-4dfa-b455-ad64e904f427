import os
from datetime import datetime
import datetime as dt
import cx_Oracle
import pandas as pd
import shutil
import logging
import math

logging.basicConfig(encoding='utf-8')
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log_file_handler = logging.FileHandler('D:/TMP/output.log')  # 创建日志处理器
log_file_handler.setFormatter(formatter)
logger.addHandler(log_file_handler)
log_console_handler = logging.StreamHandler()  # 创建控制台处理器
log_console_handler.setFormatter(formatter)
logger.addHandler(log_console_handler)

field_file_name = 'batch_no'
base_dir = "D:/TMP"  # 文件存放目录
use_scheduled_job = True  # 是否使用定时任务

def import_to_oracle(ftp_dir, table_name, column_names, converts={}, skiprows=0) :
    # 1. 连接数据库
    conn = cx_Oracle.connect('epspchk/epspchk@************/ypldb')  # 连接测试环境数据库
    # conn = cx_Oracle.connect('mb_db/mb_db@*********/opdb')
    cursor = conn.cursor()

    # 2. 获取最近10分钟内更新的Excel文件
    file_list = [f for f in os.listdir(ftp_dir) if f.endswith('.xls') or f.endswith('.xlsx')]
    column_names.append(field_file_name)

    # 3. 遍历文件，执行delete和insert操作
    for file in file_list:
        # 获取文件名
        file = os.path.join(ftp_dir, file)
        file_name = os.path.basename(file)

        # 删除操作
        sql = f'delete from {table_name} where {field_file_name} = :file_name'
        cursor.execute(sql, {'file_name': file_name})

        # 读取Excel文件内容前5列
        data = read_excel_file(file, skiprows=skiprows)
        
        # insert操作
        sql = f'insert into {table_name}({",".join(column_names)}) values ({",".join([":" + str(i+1) for i in range(len(column_names))])})'
        cursor.prepare(sql)
        row_count = 0
        for row in data:
            row2 = row +[''] * (len(column_names) - 1 - len(row))
            row2 = row2[:len(column_names)]
            row2.append(file_name)
            for i in range(len(column_names)):
                if column_names[i] in converts:
                    row2[i] = converts[column_names[i]](row2[i])
            logger.info(row2)
            cursor.execute(None, row2)
            row_count += 1
            if row_count % 10 == 0:
                logger.info(f'{file} 插入 {row_count} 行')
                conn.commit()
        logger.info(f'{file} 插入 {row_count} 行')
        conn.commit()
        
        # 重命名并移动文件
        new_file_path = rename_and_move_file(file, ftp_dir+'/backup')
        logger.info(f'{file} 移动到 {new_file_path}')

    # 关闭连接
    cursor.close()
    conn.close()

def rename_and_move_file(file_path, backup_dir):
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
        
    current_datetime = datetime.now().strftime('%Y%m%d%H%M%S')
    file_name = os.path.basename(file_path)
    file_name, file_extension = os.path.splitext(file_name)
    new_file_name = f"{file_name}.{current_datetime}{file_extension}"
    
    new_file_path = os.path.join(backup_dir, new_file_name)
    shutil.move(file_path, new_file_path)
    
    return new_file_path

def read_excel_file(file_path, skiprows=0):
    df = pd.read_excel(file_path, skiprows=skiprows)
    # df = df.fillna(0)
    data = []
    rows_list = df.values.tolist()
    row_index = 0
    for row in rows_list:
        if row_index > 0:
            row = rows_list[row_index]
            if len(data) > 0 and row[0] is not None:
                pre_row = data[-1]
                row = fill_empty_values(pre_row, row)
        data.append(row)
        row_index += 1
    logger.info(file_path)
    logger.info(data)
    return data

def fill_empty_values(row1, row2):
    filled_row = []
    for i, value in enumerate(row2):
        if value is None or value == "" or str(value).strip() == "" or str(value).strip() == "nan":
            filled_row.append(row1[i])
        else:
            filled_row.append(value)
    return filled_row

def to_date(value):
    if isinstance(value, float) or isinstance(value, int):
        return float_to_date(value)
    if isinstance(value, datetime):
        return value.date()
    return datetime.strptime(value, '%Y-%m-%d')

def float_to_date(value):
    base_date = datetime(1899, 12, 30)
    num_days = int(value)
    fraction_of_day = value - num_days
    delta_days = dt.timedelta(days=num_days)

    result_date = base_date + delta_days

    if fraction_of_day:
        seconds_in_day = int(fraction_of_day * 86400)  # 86400 seconds in a day
        result_date += dt.timedelta(seconds=seconds_in_day)
        
    return result_date

def to_int(value):
    return int(value) if value and not math.isnan(value) else 0

def to_float(value):
    return float(value) if value and not math.isnan(value) else 0.0

################ schedule ################
from schedule import run_pending
import schedule
import time

def job():
    logger.info(f"{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}, {'start import data to oracle...'}")
    ####### 部署成本费用 #######
    import_to_oracle(os.path.join(base_dir,'财务报表'),'cost_statement',
                        ['department','produce_date','course_code','course_name','cost_type', 'amount'],
                        converts={'produce_date': to_date})
    try:
        import_to_oracle(os.path.join(base_dir,'财务报表'),'cost_statement',
                         ['department','produce_date','course_code','course_name','cost_type', 'amount'],
                         converts={'produce_date': to_date})
    except Exception as e:
        logger.error(e)
    ####### 部署部门人数 #######
    try:
        import_to_oracle(os.path.join(base_dir,'部门人数'),'depart_headcount',
                         ['department','count_date','count_depart'],
                         converts={'count_date': to_date, 'count_depart': to_int})
    except Exception as e:
        logger.error("FAIL", e)
    ####### 部署提成数据 #######
    try:
        import_to_oracle(os.path.join(base_dir,'提成数据'),'commission_statement',
                         ['department','calculate_date','gross_profit_month', 
                          'cost_month', 'net_profit_month', 'cumulative_net_profit',
                          'cumulative_net_profit_target','okr', 'performance_const', 'stock_performance_index',
                          'increase_performance_index','excess_performance_index','business_commission_issue',
                          'stock_commission_percent', 'stock_commission', 'increase_commission_percent', 'increase_commission',
                          'excess_commission_percent', 'excess_commission', 'sum_commission'],
                         converts={'calculate_date': to_date, 'gross_profit_month': to_float,
                                   'cost_month': to_float, 'net_profit_month': to_float, 'cumulative_net_profit': to_float,
                                   'cumulative_net_profit_target': to_float, 'performance_const': to_float, 'stock_performance_index': to_float,
                                   'increase_performance_index': to_float, 'business_commission_issue': to_float,
                                   'stock_commission': to_float, 'increase_commission': to_float, 'excess_commission': to_float,
                                   'sum_commission': to_float}, skiprows=1)
    except Exception as e:
        logger.error(e)

if __name__ == '__main__':
    job()
    # if use_scheduled_job:
    #     schedule.every(120).seconds.do(job)
    #     while True:
    #         run_pending()
    #         time.sleep(1)
