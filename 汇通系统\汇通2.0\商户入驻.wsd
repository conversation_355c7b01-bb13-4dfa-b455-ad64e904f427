@startuml 商户入网
title 商户入网

actor 商户 as M
actor 运营 as O
box 汇通系统 #LightBlue
participant 商户门户 as MP
participant 风控模块 as RC
end box
participant 渠道 as C

autonumber 1.1
M -> MP: 商户注册
MP -> M: 发送验证码
M -> MP: 输入验证码
MP --> M: 商户注册成功

autonumber 2.1
M -> MP: 商户提交认证资料
MP -> RC: 名单筛查&风控检查
RC --> MP: 返回结果
MP -> M: 商户资料待审核
O -> MP: 审核商户资料并指定渠道&业务
alt 审核通过
    MP -> C: 通知渠道商户入网
    C --> MP: 渠道返回结果
    alt 渠道入网成功
        MP --> M: 商户入网成功
    else 渠道入网失败
        MP --> M: 商户入网失败
    end
else 审核不通过
    MP --> M: 商户入网失败
end
MP -\ M: 通知入网结果

@enduml