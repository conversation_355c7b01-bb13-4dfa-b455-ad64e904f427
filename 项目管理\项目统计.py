from datetime import datetime, timedelta
import logging
from 代码行统计 import stat_code
from JIRA统计 import stat_workorder, stat_bugs
from TFS统计 import stat_task

logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',  # 只包含日志消息本身
    handlers=[
        logging.StreamHandler()  # 控制台处理器
    ]
)
logger = logging.getLogger(__name__)

BASE_DIR = "C:/Users/<USER>/OneDrive/work/项目/统计&考核"
# BASE_DIR = "C:/TMP"

PROJECT_REPOS = [
    {'type': 'SVN', 'name': 'EPSP',  'path': 'C:/work/EPSP/JavaSource'},
    {'type': 'SVN', 'name': 'EPSP',  'path': 'C:/work/EPSP/PortalSource'},
    {'type': 'SVN', 'name': '汇通',  'path': 'C:/work/HT/JavaSource'},
    {'type': 'SVN', 'name': '汇通',  'path': 'C:/work/HT/PortalSource'},
    {'type': 'GIT', 'name': '易分账','path': 'C:/work/HUIET/epay-epl'},
    {'type': 'GIT', 'name': '易分账','path': 'C:/work/HUIET/epay-m'},
    {'type': 'GIT', 'name': '易分账','path': 'C:/work/HUIET/epay-ui-epl'},
    {'type': 'GIT', 'name': '易分账','path': 'C:/work/HUIET/epay-epl-sdk'},
    {'type': 'SVN', 'name': '外卡',  'path': 'C:/work/WK/JavaSource'},
    {'type': 'SVN', 'name': '框架',  'path': 'C:/work/EPL-Framework/010-Source'},
    # {'type': 'SVN', 'name': '跨境',  'path': 'D:/KJ/092-acq_manageWeb_epl'},
    # {'type': 'SVN', 'name': '跨境',  'path': 'D:/KJ/093-acq_busiapi_epl'},
    # {'type': 'SVN', 'name': '跨境',  'path': 'D:/KJ/095-scheduledTask-epl'},
    # {'type': 'SVN', 'name': '跨境',  'path': 'D:/KJ/merchantWeb-epl'},
    # {'type': 'SVN', 'name': '聚合',  'path': 'D:/aps/Source'},
    {'type': 'GIT', 'name': '工具',  'path': 'C:/work/pytools'}
]

PROJECT_ITERATIONS = [
    {'name': 'EPSP\\迭代 077（24.01.10上线）', 'end_date': '2024-01-10'},
    {'name': 'EPSP\\迭代 078（24.01.31上线）', 'end_date': '2024-01-31'},
    {'name': 'EPSP\\迭代 079（24.02.28上线）', 'end_date': '2024-02-28'},
    {'name': 'EPSP\\迭代 080（24.03.27上线）', 'end_date': '2024-03-27'},
    {'name': 'EPSP\\迭代 081（24.04.24上线）', 'end_date': '2024-04-24'},
    {'name': 'EPSP\\迭代 082（24.05.22上线）', 'end_date': '2024-05-22'},
    {'name': 'EPSP\\迭代 083（24.06.25上线）', 'end_date': '2024-06-25'},
    {'name': 'EPSP\\迭代 084（24.07.24上线）', 'end_date': '2024-07-24'},
    {'name': 'EPSP\\迭代 085（24.08.26上线）', 'end_date': '2024-08-26'},
    {'name': 'EPSP\\迭代 086（24.09.27上线）', 'end_date': '2024-09-27'},
    {'name': 'EPSP\\迭代 087（24.10.31上线）', 'end_date': '2024-10-31'},
    {'name': 'EPSP\\迭代 088（24.11.27上线）', 'end_date': '2024-11-27'},
    {'name': 'EPSP\\迭代 089（24.12.25上线）', 'end_date': '2024-12-25'},
    {'name': 'EPSP\\迭代 090（25.01.22上线）', 'end_date': '2025-01-22'},
    {'name': 'EPSP\\迭代 091（25.02.26上线）', 'end_date': '2025-02-26'},
    {'name': 'EPSP\\迭代 092（25.03.26上线）', 'end_date': '2025-03-26'},
    {'name': 'EPSP\\迭代 093（25.04.24上线）', 'end_date': '2025-04-24'},
    {'name': 'EPSP\\迭代 094（25.05.28上线）', 'end_date': '2025-05-28'},
    {'name': 'EPSP\\迭代 095（25.06.25上线）', 'end_date': '2025-06-30'},
    {'name': 'EPSP\\迭代 096（25.07.30上线）', 'end_date': '2025-07-30'}
]

def get_iteration_obj(iteration_path):
    if not iteration_path:
        iteration_path = PROJECT_ITERATIONS[-1]['name']

    def get_next_day(date_str):
        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
        return (date_obj + timedelta(days=1)).strftime('%Y-%m-%d')

    try:
        iter_index = next(i for i, it in enumerate(PROJECT_ITERATIONS) if iteration_path in it['name'])
        end_date = get_next_day(PROJECT_ITERATIONS[iter_index]['end_date'])
        start_date = get_next_day(PROJECT_ITERATIONS[iter_index - 1]['end_date'])
        excel_file_path = PROJECT_ITERATIONS[iter_index]['name'].replace("\\", "-")
        return {'start_date': start_date, 'end_date': end_date, 'excel_file_path': excel_file_path, 'index': iter_index, 'name': PROJECT_ITERATIONS[iter_index]['name']}
    except StopIteration:
        return None

def stat_all_iterations():
    list(map(lambda it: stat_iteration(it['name']), PROJECT_ITERATIONS[1:]))

def stat_iteration(iteration_path):
    import pandas as pd
    iteration_obj = get_iteration_obj(iteration_path)
    if iteration_obj:
        excel_file_path = f'{BASE_DIR}/迭代统计_{iteration_obj["excel_file_path"]}.xlsx'
        output_excel_writer = pd.ExcelWriter(excel_file_path, engine='xlsxwriter')
        stat_task(iteration_path, output_excel_writer)
        stat_bugs(iteration_path=iteration_path, output_excel_writer=output_excel_writer)
        stat_workorder(iteration_path=iteration_path, output_excel_writer=output_excel_writer)
        stat_code(iteration_path=iteration_path, output_excel_writer=output_excel_writer)
        output_excel_writer.close()
    else:
        stat_task(None)
        stat_bugs(iteration_path=iteration_path)
        stat_workorder(iteration_path=iteration_path)

if __name__ == '__main__':
    iteration_path = input("请输入迭代路径：")
    stat_iteration(iteration_path)