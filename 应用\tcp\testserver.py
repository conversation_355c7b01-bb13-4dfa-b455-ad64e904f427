import socket
import threading

class TestServer:
    def __init__(self, host='0.0.0.0', port=9999):
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.bind((host, port))
        self.server_socket.listen(5)
        print(f"服务器启动成功，监听地址：{host}:{port}")

    def handle_client(self, client_socket, addr):
        print(f"客户端连接：{addr}")
        while True:
            try:
                data = client_socket.recv(1024)
                if not data:
                    break
                
                # 显示接收数据长度
                print(f"接收内容长度: {len(data)} bytes")
                
                # 将接收到的数据转换为ASCII码并打印
                ascii_values = [f"{byte}({chr(byte)})" for byte in data]
                print(f"收到来自 {addr} 的数据：")
                print("ASCII码：", " ".join(ascii_values))
                print("完成！")
                
                # 向客户端发送响应消息
                response = f"receive content length {len(data)}, done!"
                client_socket.send(response.encode())
                
            except Exception as e:
                print(f"处理客户端数据时出错：{e}")
                break
        
        client_socket.close()
        print(f"客户端断开连接：{addr}")

    def start(self):
        try:
            while True:
                client_socket, addr = self.server_socket.accept()
                client_thread = threading.Thread(
                    target=self.handle_client,
                    args=(client_socket, addr)
                )
                client_thread.start()
        except KeyboardInterrupt:
            print("\n服务器正在关闭...")
        finally:
            self.server_socket.close()

if __name__ == "__main__":
    server = TestServer()
    server.start()