# 测试环境

## 易票联参数

测试环境出口IP：***********
回调地址：https://sandbox-merchant.waltranx.com/api/busiapi/yeahpay/notify
证书文件：/files/waltranx-public-rsa-1024.cer

## 移卡参数

商户id：1753482757
接口地址：[https://yeahpay-collect.yuque.com/org-wiki-yeahpay-collect-nx7atx/pwfm31](https://yeahpay-collect.yuque.com/org-wiki-yeahpay-collect-nx7atx/pwfm31) 密码：ibd8
证书公钥：/files/yeahpay-1753482757-public-rsa.cer

# 业务

## 收款&凭证顺序图

```mermaid
sequenceDiagram
    participant c as 客户
    participant o as 运营
    participant ht as 汇通系统
    participant y as 移卡系统

    autonumber
    y -->> ht: 收款到账通知
    ht ->> ht: 创建收款单
    ht ->> c: 通知收款到账
    c ->> ht: 新建交易订单
    c ->> ht: 绑定收款单，提交待审核
    o ->> ht: 审核收款单
    alt 审核通过
        ht ->> y: 接口“订单申请”
        y ->> y: 订单审核
        loop 等待移卡审核结果
            y -->> ht: 接口“订单审核通知”
            ht ->> y: 接口“订单详情”查询结果
        end
        alt 状态=申报成功
            ht ->> ht: 更新订单审核状态“审核通过”
        else 状态=审核驳回
            ht ->> ht: 更新订单审核状态“驳回”
        end
    else 审核驳回
        ht ->> ht: 更新订单审核状态“驳回”
    end


```