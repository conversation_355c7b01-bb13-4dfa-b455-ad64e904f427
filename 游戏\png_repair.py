from PIL import Image
import os

def remove_icc_profile(image_path):
    img = Image.open(image_path)
    if 'icc_profile' in img.info:
        del img.info['icc_profile']
    img.save(image_path)

# 批量处理目录下的所有PNG图像，包括子目录
def batch_remove_icc_profiles(directory):
    for root, _, files in os.walk(directory):
        for filename in files:
            if filename.endswith('.png'):
                file_path = os.path.join(root, filename)
                remove_icc_profile(file_path)

# 批量处理当前目录下的PNG图像
batch_remove_icc_profiles('.')