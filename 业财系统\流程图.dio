<mxfile host="65bd71144e">
    <diagram id="OzBE1MsY4XST4IhSvIJi" name="第 1 页">
        <mxGraphModel dx="669" dy="607" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="用户" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="100" y="130" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="财务" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="100" y="340" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="FTP Server" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" parent="1" vertex="1">
                    <mxGeometry x="270" y="330" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="文件上传" style="endArrow=classic;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" target="4" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="130" y="370" as="sourcePoint"/>
                        <mxPoint x="180" y="320" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="6" value="文件处理" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="507" y="340" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="" style="endArrow=classic;html=1;" parent="1" source="6" target="4" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="390" y="360" as="sourcePoint"/>
                        <mxPoint x="440" y="450" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="8" value="文件提取" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="7" vertex="1" connectable="0">
                    <mxGeometry x="-0.4643" y="-1" relative="1" as="geometry">
                        <mxPoint x="-35" y="1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="9" value="运营库&lt;br&gt;Oracle 19" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="537" y="120" width="60" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="Metabase" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="270" y="130" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="数据入库" style="endArrow=classic;html=1;" parent="1" source="6" target="9" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="527" y="280" as="sourcePoint"/>
                        <mxPoint x="577" y="230" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="13" value="数据查看" style="endArrow=classic;html=1;exitX=0.5;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="2" target="10" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="180" as="sourcePoint"/>
                        <mxPoint x="210" y="130" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" value="数据提取" style="endArrow=classic;html=1;" parent="1" source="10" target="9" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="520" y="200" as="sourcePoint"/>
                        <mxPoint x="570" y="150" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="15" value="业务库&lt;br&gt;Oracle 11" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="1">
                    <mxGeometry x="727" y="120" width="60" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="业务数据同步" style="endArrow=classic;html=1;" edge="1" parent="1" source="15" target="9">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="637" y="260" as="sourcePoint"/>
                        <mxPoint x="687" y="210" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>