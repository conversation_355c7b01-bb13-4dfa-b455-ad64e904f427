import os
import time
import requests
from datetime import datetime

class AnythingLLMClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.api_key = api_key

    def get_thread(self, workspace_id, thread_id):
        url = f"{self.base_url}/workspaces/{workspace_id}/threads/{thread_id}"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return response.json()
        else:
            response.raise_for_status()

    def get_all_workspaces(self):
        url = f"{self.base_url}/workspaces"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return response.json()
        else:
            response.raise_for_status()

    def get_workspace(self, workspace_id):
        url = f"{self.base_url}/workspace/{workspace_id}"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return response.json()
        else:
            response.raise_for_status()
            
    def chat(self, workspace_id, thread_id, message):
        url = f"{self.base_url}/workspace/{workspace_id}/thread/{thread_id}/chat"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        body = {
            "message": message,
            "mode": "chat"
        }
        response = requests.post(url, headers=headers, json=body)
        if response.status_code == 200:
            return response.json()
        else:
            response.raise_for_status()

    def upload_file(self, workspace_id, file_path):
        url = f"{self.base_url}/workspace/{workspace_id}/upload"
        headers = {
            "Authorization": f"Bearer {self.api_key}"
        }
        files = {'file': open(file_path, 'rb')}
        response = requests.post(url, headers=headers, files=files)
        if response.status_code == 200:
            return response.json()
        else:
            response.raise_for_status()

    def monitor_directory(self, directory, workspace_id, interval=60):
        last_checked = datetime.now()
        while True:
            current_time = datetime.now()
            for root, _, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.getmtime(file_path) > last_checked.timestamp():
                        print(f"Uploading {file_path}")
                        self.upload_file(workspace_id, file_path)
            last_checked = current_time
            time.sleep(interval)

# 示例用法
if __name__ == "__main__":
    base_url = "http://localhost:3001/api/v1"
    # api_key = "YV56R7Z-JE74AN5-MFJN5DY-SQ413BY"
    workspace_id = "049da0e1-51e3-46d9-b4ba-ad86c8714b70"
    thread_id = "479e7ff7-b8e7-49fa-b4aa-820d46ac3c17"
    
    api_key = "GDYEWGP-AJQ4T93-H36V407-25ZM2G9"
    workspace_id = "ai"
    thread_id = "927e8360-1582-466e-8f59-d6aa5f1e6176"
    directory_to_monitor = "C:/Work/知识库"

    client = AnythingLLMClient(base_url, api_key)
    
    # 获取所有workspace
    # workspaces = client.get_all_workspaces()
    # print(workspaces)
    
    # workspace = client.get_workspace(workspace_id)
    # print(workspace)
    
    # message = "介绍易票联"
    # thread = client.chat(workspace_id, thread_id, message)
    # print(thread)
    
    # 监控目录并上传文件
    # client.monitor_directory(directory_to_monitor, workspace_id)
    client.upload_file(workspace_id, "C:/Work/知识库/易票联/易票联介绍.md")
