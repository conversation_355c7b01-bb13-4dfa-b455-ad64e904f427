import MySQLdb
import cx_Oracle

# MySQL数据库连接信息
mysql_host = '***********'
mysql_user = 'jira'
mysql_password = 'JiraDB@368'
mysql_database = 'jiradb'

# Oracle数据库连接信息
oracle_host = '**********'
oracle_port = '1521'
oracle_user = 'wkproj'
oracle_password = 'wkproj@123'
oracle_service_name = 'TESTDB'

# 连接到MySQL数据库
mysql_conn = MySQLdb.connect(host=mysql_host, user=mysql_user, passwd=mysql_password, db=mysql_database, charset='utf8')
mysql_cursor = mysql_conn.cursor()

# 连接到Oracle数据库
oracle_dsn = cx_Oracle.makedsn(oracle_host, oracle_port, service_name=oracle_service_name)
oracle_conn = cx_Oracle.connect(oracle_user, oracle_password, dsn=oracle_dsn)
oracle_cursor = oracle_conn.cursor()

def migrate_data(table_name, page_num, page_size=10000):
    # 获取MySQL表结构信息
    mysql_cursor.execute('DESCRIBE '+table_name)
    mysql_columns = [column[0] for column in mysql_cursor.fetchall()]
    
    insert_sql = 'INSERT INTO %s (%s) VALUES (%s)' % (table_name, ', '.join(mysql_columns),':'+ ', :'.join(mysql_columns))
    print(insert_sql)
    prepared_statement = oracle_cursor.prepare(insert_sql)
    
    mysql_cursor.execute(f'SELECT * FROM {table_name} LIMIT {(page_num-1) * page_size}, {page_size}')
    data = mysql_cursor.fetchall()
    row_count = 0
    for row in data:
        params = {}
        for i, column in enumerate(mysql_columns):
            params[column] = row[i] if row[i] is not None else ''
        oracle_cursor.execute(prepared_statement, params)
        row_count += 1
        if row_count % 1000 == 0:
            print('Rows inserted:', row_count, 'Page:', page_num)
    oracle_conn.commit()
    return row_count

def migrate_table(table_name):
    page_num = 1
    page_size = 10000
    total_row_count = 0
    while True:
        row_count = migrate_data(table_name, page_num, page_size)
        total_row_count += row_count
        page_num += 1
        if row_count < page_size:
            break
    print('Migrated table', table_name, 'Successfully, Total Rows:', total_row_count)
        
if __name__ == '__main__':
    migrate_table('jiraissue2')