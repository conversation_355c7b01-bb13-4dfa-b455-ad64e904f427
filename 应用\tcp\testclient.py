import socket
import sys

def main():
    # 创建TCP socket
    client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    
    # 服务器地址和端口
    # SERVER_HOST = '************'
    # SERVER_PORT = 9443
    # AD 国密
    SERVER_HOST = '*************'
    SERVER_PORT = 9999
    
    try:
        # 连接服务器
        client.connect((SERVER_HOST, SERVER_PORT))
        print(f"已连接到服务器 {SERVER_HOST}:{SERVER_PORT}")
        
        while True:
            # 获取用户输入
            message = input("请输入要发送的消息 (输入'quit'退出): ")
            
            if message.lower() == 'quit':
                break
                
            # 发送消息到服务器
            client.send(message.encode())
            
            # 接收服务器响应
            response = client.recv(1024)
            
            # 将响应转换为ASCII并打印
            print("服务器响应:")
            for byte in response:
                print(f"ASCII: {byte} -> 字符: {chr(byte)}")
                
    except ConnectionRefusedError:
        print("无法连接到服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"发生错误: {str(e)}")
    finally:
        client.close()
        print("连接已关闭")

if __name__ == "__main__":
    main()
