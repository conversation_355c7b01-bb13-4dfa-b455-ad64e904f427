import sys
import pandas as pd
import pdfminer
from pdfminer.high_level import extract_tables
from pdfminer.layout import LAParams

def read_pdf_tables(pdf_file, excel_file):
    # 解析PDF文件并提取表格
    tables = extract_tables(pdf_file, laparams=LAParams())

    # 提取第一个表格并将其转换为pandas DataFrame
    df = pd.DataFrame(tables[0])

    # 将数据存储到Excel文件中
    df.to_excel(excel_file, index=False)

if __name__ == "__main__":
    read_pdf_tables('C:/Users/<USER>/Desktop/2023年物理类.pdf', 'C:/Users/<USER>/Desktop/2023年物理类.xlsx')
