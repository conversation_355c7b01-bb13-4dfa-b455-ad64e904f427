import pandas as pd
import os

input_file = "d:/TMP/s.xlsx"
output_dir = "D:/TMP/output"

if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 读取Excel文件，所有列按文本处理
df = pd.read_excel(input_file, dtype=str)

# 根据第一列进行分组
for split_value, group in df.groupby(df.columns[0]):
    # 移除第一列
    output_data = group.iloc[:, 1:]
    
    # 创建输出文件名
    output_file = os.path.join(output_dir, f"{split_value}.xlsx")
    
    # 使用ExcelWriter保存并设置格式
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        output_data.to_excel(writer, index=False)
        worksheet = writer.sheets['Sheet1']
        
        # 设置列宽和格式
        for idx, col in enumerate(worksheet.columns):
            # 设置列宽
            max_length = max(len(str(cell.value)) for cell in col if cell.value)
            worksheet.column_dimensions[col[0].column_letter].width = max_length + 2
            
            # 设置所有单元格为文本格式，并禁用科学计数法
            for cell in col:
                cell.number_format = '@'
                if cell.value and str(cell.value).replace('.','').isdigit():
                    cell.value = str(cell.value)
