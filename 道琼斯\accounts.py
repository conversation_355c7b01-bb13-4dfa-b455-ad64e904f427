#!/usr/bin/python3

import os
import requests

user_key = os.getenv("YOUR_KEY_ENV_VAR")
# user_key = YOUR_KEY

accounts_url = "https://api.dowjones.com/alpha/accounts/{}".format(user_key)

headers = {
    "Content-Type": "application/json",
    "user-key": user_key
}

accounts_response = requests.get(accounts_url, headers=headers)

if accounts_response.status_code != 200:
    print("An error has occurred.\n" + accounts_response.text)
else:
    accounts_data = accounts_response.json()["data"]["attributes"]

    # What has been retrieved so far
    snapshots_so_far = accounts_data["tot_extracts"]
    documents_downloaded = accounts_data["tot_document_extracts"]
    bytes_downloaded = accounts_data["current_downloaded_amount"]
    streams_so_far = accounts_data["tot_topics"]
    subscriptions_so_far = accounts_data["tot_subscriptions"]
    snapshots_currently_running = accounts_data["cnt_curr_ext"]

    # Account limits
    max_documents = accounts_data["max_allowed_document_extracts"]
    max_snapshots = accounts_data["max_allowed_extracts"]
    max_concurrent_snapshots = accounts_data["max_allowed_concurrent_extracts"]

    # Snapshots
    output = "Snapshots created: {}/{}\n".format(snapshots_so_far, max_snapshots)
    output += "Documents downloaded: {}/{}\n".format(documents_downloaded, max_documents)
    output += "Snapshots running: {}/{}\n".format(snapshots_currently_running, max_concurrent_snapshots)
    output += "Total download size: {}\n\n".format(bytes_downloaded)

    # Streams
    output += "Streams created: {}\n".format(streams_so_far)
    output += "Subscriptions created: {}\n".format(subscriptions_so_far)
    
    print(output)
