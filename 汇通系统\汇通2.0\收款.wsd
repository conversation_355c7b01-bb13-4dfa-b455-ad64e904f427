@startuml CC收款流程
title CC收款流程
actor 商户 as M
actor 运营 as O
actor 付款人 as P
box 汇通系统 #LightBlue
    participant 商户门户 as MP
    participant 交易服务 as TX
    participant 账户服务 as AC
    participant 风控服务 as RC
    participant 渠道服务 as CH
end box
participant "CurrencyCloud" as CC #Grey

==获取收款账户==
autonumber 1.1
M -> MP: 登录
MP --> M: 进入汇通全球
M -> MP: 查询收款账户
MP -> CC: 查询收款账户
note left of CC: Find Funding Accounts接口\n参数account_id和currency
CC --> MP: 收款账户
MP --> M: 展现收款账户

M ---\ P: 提供收款账户(线下)
P -[#red]\ CC: 完成付款（线下银行转款等）
note right of P: 银行转账等方式，demo环境使用Emulate inbound funds接口模拟

==收款异步通知==
autonumber 2.1
CC ->> CH: 收款异步通知
CH ->> TX: 待处理收款单

TX -> RC: 申请交易风控核查
RC --> TX: 返回结果
alt 是黑名单
    TX -> TX: 更新状态：收款失败
    note left of TX: 交易风控核查失败
    ...手动安排退款...
else 非黑名单
    TX -> RC: 请求名单筛查
    RC -[#red]\ TX: 异步通知筛查结果
    alt 名单筛查结果可疑
        TX -> TX: 状态：未通过风控检查
        ...手动安排退款...
    end
    group 调用风控限额接口
        TX -> RC: 调用交易风控接口（限额、限次）
        RC --> TX: 返回风控结果
        alt 风控未通过，标记风控结果
            TX -> TX: 状态：未通过风控检查
            ...手动安排退款...
        end 
    end group 
    TX -> TX: 更新订单状态：待提交资料

    autonumber 3.1
    loop 提交收款资料
        M -> MP: 提交收款资料
        TX -> TX: 订单状态：待审核
        O -> TX: 审核
        alt 审核通过
            TX -> AC: 加额（充值接口）
            AC --> TX: 返回充值结果+手续费
            note right of AC: 手续费=实际收款金额*商户的收款费率\n手续费内扣，通知收款金额=手续费+商户加额
            TX -> TX: 状态：成功，登记手续费
            TX -[#red]> CC: 转存手续费，接口Transfer
            CC --[#red]> MP: 返回转存结果
        else 审核未通过
            TX -> TX: 更新订单状态：待提交资料
            ...继续提交资料...
        end
    end
end
@enduml