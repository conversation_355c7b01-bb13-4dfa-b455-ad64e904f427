﻿SELECT wm_concat(t.column_name || ' AS ' || (CASE WHEN instr(t.COMMENTS,'(') > 0 THEN substr(t.COMMENTS, 0,instr(t.COMMENTS,'(') - 1) WHEN instr(t.COMMENTS,'（') > 0 THEN substr(t.COMMENTS, 0,instr(t.COMMENTS,'（') - 1) ELSE t.COMMENTS END) ) 
FROM USER_COL_COMMENTS t 
  INNER JOIN USER_TAB_COLS c ON t.TABLE_NAME =c.TABLE_NAME AND t.COLUMN_NAME =c.COLUMN_NAME 
WHERE t.TABLE_NAME ='RISK_CONTROL_RESULT' 
ORDER BY c.INTERNAL_COLUMN_ID ;

SELECT t.COLUMN_NAME, t.COMMENTS 
FROM USER_COL_COMMENTS t 
  INNER JOIN USER_TAB_COLS c ON t.TABLE_NAME =c.TABLE_NAME AND t.COLUMN_NAME =c.COLUMN_NAME 
WHERE t.TABLE_NAME ='TXS_PAY_TRADE_ORDER' 
ORDER BY c.INTERNAL_COLUMN_ID ;

--导出市区数据，20220627
SELECT PARAM_NAME AS 编码, PARAM_VALUE AS 名称, PARENT_PARAM_NAME  AS 上级编码, PARAM_LEVEL AS 层级 
FROM CUST_STATIC_PARAM csp 
WHERE PARAM_TYPE ='EPSP_REGION_CODE'  AND FLAG ='1' START WITH PARENT_PARAM_NAME = '0' CONNECT BY PRIOR PARAM_ID =PARENT_PARAM_ID ;

-----------------------交易类-----------------------
--线上交易
SELECT /*+parallel(t,5)*/CUSTOMER_CODE AS 商户编号 ,CUSTOMERNAME AS 商户名称 , 
  BUSINESS_CODE AS 业务代码 ,b.NAME AS 业务名称, 
  OUT_TRADE_NO AS 商户单号, TRANSACTION_NO AS 易票联单号, 
  to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 下单时间, to_char(t.END_TIME,'yyyy-mm-dd hh24:mi:ss') AS 完成时间 , 
  (CASE t.STATE WHEN '00' THEN '成功' WHEN '01' THEN '失败' WHEN '03' THEN '处理中' ELSE t.STATE END) AS 订单状态, 
  AMOUNT / 100 AS 订单金额 , ACTUAL_PAY_AMOUNT / 100 AS 应付金额, CASH_AMOUNT / 100 AS 实付金额, DISCOUNTABLE_AMOUNT AS 优惠金额 , PROCEDURE_FEE / 100 AS 手续费金额, SETTLEMENT_AMOUNT / 100 AS 应结算金额, t.REFUND_FEE / 100 AS 退款金额, 
  /*t.CARD_NO_ENC AS enc_card_no, t.CARD_OWNER AS enc_card_owner, */
  (CASE t.CARD_TYPE WHEN 'D' THEN '贷记卡' WHEN 'C' THEN '借记卡' ELSE null END)AS 付款卡类型, t.BANK_CODE AS 付款卡开户行,
  CHANNEL_ORDER AS 上游单号, c.INSTITUTION_NAME AS 交易机构,
  t.CLIENT_IP AS 交易IP,
  t.REMARK AS 交易摘要,
  t.CHANNEL_RESP_CODE AS 渠道响应码,t.CHANNEL_RESP_MSG AS 渠道响应消息
FROM TXS_PAY_TRADE_ORDER t 
  LEFT JOIN CUM_INSTITUTION c ON t.INSTITUTION_ID =c.ID
  LEFT JOIN PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE 
WHERE t.state='00' 
  AND t.CUSTOMER_CODE IN('***************','***************','***************')
  AND t.CREATE_TIME >= timestamp'2022-10-24 00:00:00';
 
--间连流水
SELECT c.NAME ,t.MERCHANT_NO ,to_char(t.CREATION_TIME,'yyyymmddhh24miss') AS 交易时间,
	t.AMOUNT AS 交易金额 , /*0 AS 余额, */
	t.ACCOUNT AS 卡号, 
	' ' AS 交易对手开户行代码, ' ' AS 交易对手开户行,
	' ' AS 交易摘要, t.ACC_TRANSACTION_NO AS 交易流水号
FROM ZHY_POSP_RECORD t
	LEFT JOIN cum_customer_info c ON t.MERCHANT_NO = c.CUSTOMER_CODE 
WHERE t.CREATION_TIME >= timestamp'2022-10-24 00:00:00'
	AND t.MERCHANT_NO in('***************','***************','***************');

--直连流水
SELECT t.CUSTOMER_CODE AS 商户号,t.CUSTOMER_NAME AS 商户名称 ,t.AMT AS 交易金额,t.PROCEDURE_FEE 手续费金额,
	to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 创建时间 ,
	t.PAN AS 卡号
FROM CHK_XX_TRAN_RECORD t 
WHERE t.CUSTOMER_CODE in('***************','***************','***************');

--ACS充值
SELECT c.NAME AS 商户名称 , t.CUSTOMER_CODE AS 商户编号 ,to_char(t.CREATE_TIME,'yyyymmddhh24miss') AS 交易时间, 
	t.AMOUNT / 100 AS 交易金额 , 0 AS 余额, 
	t.DEBTOR_ACCOUNT, t.DEBTOR_ACCOUNT_NAME , 
	' ' AS 交易对手开户行,
	t.REMARK AS 交易摘要, TRANSACTION_NO AS 交易流水号, ' ' AS 交易IP
FROM PAS_ACCT_QUOTA_RECORD t
	INNER JOIN CUM_CUSTOMER_INFO c ON t.CUSTOMER_CODE =c.CUSTOMER_CODE
WHERE t.FUND_TYPE ='1' AND t.ACCT_STATE ='00' AND t.CREATE_TIME >= timestamp '2021-01-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-01-01 00:00:00'
	AND t.CUSTOMER_CODE in('IZHYTJ008','IZHYHN001','***************','***************','IZHYSH006','IZHYJS008','TSCZHY001','***************','***************','***************','***************','***************','***************','***************');

--分账数据
SELECT t.SOURCE_CUSTOMER_CODE AS 商户号 ,t.AMOUNT /100 AS 交易金额, to_char(t.CREATE_TIME,'yyyymmddhh24miss') AS 交易时间, '分出' AS 方向
FROM TXS_SPLIT_RECORD t 
WHERE t.SOURCE_CUSTOMER_CODE <>t.CUSTOMER_CODE AND t.STATE = '3'
	AND t.SOURCE_CUSTOMER_CODE IN('IZHYTJ008','IZHYHN001','***************','***************','IZHYSH006','IZHYJS008','TSCZHY001','***************','***************','***************','***************','***************','***************','***************')
	AND t.CREATE_TIME >= timestamp '2021-01-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-07-01 00:00:00'
UNION ALL 
SELECT t.CUSTOMER_CODE AS 商户号 ,t.AMOUNT /100 AS 交易金额, to_char(t.CREATE_TIME,'yyyymmddhh24miss') AS 交易时间, '分入' AS 方向
FROM TXS_SPLIT_RECORD t 
WHERE t.SOURCE_CUSTOMER_CODE <>t.CUSTOMER_CODE  AND t.STATE = '3'
	AND t.CUSTOMER_CODE IN('IZHYTJ008','IZHYHN001','***************','***************','IZHYSH006','IZHYJS008','TSCZHY001','***************','***************','***************','***************','***************','***************','***************')
	AND t.CREATE_TIME >= timestamp '2021-01-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-07-01 00:00:00';

------------------------------------------------------------------------------------
--跨境商户记账流水
SELECT ACCOUNTCODE ,to_char(a.ACCOUNTDATETIME,'yyyymmddhh24miss') AS accountdatetime,
	DECODE(BALANCETYPE,1,'可用金额',2,'在途金额',3,'冻结金额') AS BALANCETYPE,TRANSACTIONTYPE ,TRANSACTIONNO ,
	amount/100 AS amount,AFTERBALANCE / 100 AS AFTERBALANCE ,
	DECODE(a."TYPE",1,'正常流水',2,'回滚产生流水') AS TYPE, 
	DECODE(a.ISROLLBACK,0,'未回滚',1,'已回滚') AS ISROLLBACK
FROM ACC_ACCOUNTFLOW a 
WHERE a.ACCOUNTDATETIME >= timestamp '2021-01-01 00:00:00' AND a.ACCOUNTDATETIME < timestamp '2022-01-01 00:00:00'
	AND ACCOUNTCODE IN()
ORDER BY a.ACCOUNTDATETIME desc;

 --出金交易
SELECT /*+parallel(5)*/ t.CUSTOMER_CODE AS 商户编号, t.CUSTOMERNAME AS 商户名称, t.OUT_TRADE_NO AS 商户单号, t.TRANSACTION_NO AS 易票联单号, 
 to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 下单时间, to_char(t.END_TIME,'yyyy-mm-dd hh24:mi:ss') AS 完成时间 , 
 (CASE WHEN t.PAY_STATE='00' THEN '成功' WHEN t.PAY_STATE='01' THEN '失败' WHEN t.PAY_STATE='03' THEN '处理中' ELSE t.PAY_STATE END) AS 订单状态, 
 t.TOTAL_FEE / 100 AS 订单金额 , t.PROCEDURE_FEE / 100 AS 手续费金额, 
 t.CARD_NO AS 提现卡, /*t.CARD_NO_CIPHER AS enc_card_no,*/ t.BANK_USER_NAME_FULL AS 持卡人,
 t.BANK_NAME,t.REMARK ,
 t.CHANNEL_ORDER AS 上游单号,t.BATCH_NO AS 批量代付批号
FROM TXS_WITHDRAW_TRADE_ORDER t
WHERE t.CUSTOMER_CODE IN('***************','***************','***************','***************','***************') 
  and t.pay_state='00';
 
--退款交易记录
SELECT t.CUSTOMER_CODE as 商户号, t.CUSTOMERNAME as 商户名称, t.OUT_REFUND_NO as 退款申请单号, t.transaction_no as 退款交易单号, t.REFUND_FEE/100 as 退款申请金额
FROM TXS_REFUND_PRE_ORDER t
WHERE t.CUSTOMER_CODE ='****************' AND t.PAY_STATE ='00';

--===============入金交易===============
SELECT t2.月份, c.NAME AS 商户名称, t2.customer_code AS 商户编号,  
	c.plat_customer_no as 所属平台商号, (select name from cust_customer c1 where c1.customer_no=c.plat_customer_no) AS 所属平台名称,
	c.service_customer_no 所属服务商号, (select name from cust_customer c1 where c1.customer_no=c.service_customer_no) AS 所属服务商名称,
	u.REAL_NAME as 业务员,decode(c.OPEN_ACCOUNT,1,'是',0,'否') AS 是否开通账户, decode(c.ACCEPT_ORDER,1,'是',0,'否') AS 是否开通收单,
	t2.business AS 业务,
	t2.笔数, t2.交易金额, t2.手续费
FROM (
	SELECT /*+ parallel (t,4) */ t.CUSTOMER_CODE, b.NAME AS business ,to_char(t.CREATE_TIME ,'yyyymm') AS 月份,
		sum(t.AMOUNT) / 100 AS 交易金额, sum(t.PROCEDURE_FEE) / 100 AS 手续费, count(t.TRANSACTION_NO) AS 笔数
	FROM TXS_PAY_TRADE_ORDER t 
		INNER JOIN PAS_BUSINESS b ON t.business_code=b.CODE
	WHERE t.STATE ='00' AND t.CREATE_TIME >= timestamp '2022-01-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-08-01 00:00:00'
	GROUP BY t.CUSTOMER_CODE, b.NAME, to_char(t.CREATE_TIME ,'yyyymm')
	UNION ALL 
	SELECT /*+ parallel (zpr,2) */ zpr.MERCHANT_NO ,'POS',to_char(zpr.CREATION_TIME,'yyyymm'),
		sum(zpr.AMOUNT),0 AS fee,count(0) 
	FROM ZHY_POSP_RECORD zpr 
	WHERE zpr.CREATION_TIME >= timestamp '2022-01-01 00:00:00' AND zpr.CREATION_TIME < timestamp '2022-08-01 00:00:00'
	GROUP BY zpr.MERCHANT_NO ,to_char(zpr.CREATION_TIME,'yyyymm')
	UNION ALL 
	SELECT a.CUSTOMER_CODE, 'ACS充值', to_char(a.CREATE_TIME ,'yyyymm'),
		sum(a.AMOUNT) / 100, sum(a.PROCEDURE_FEE) / 100,  count(0)
	FROM PAS_ACCT_QUOTA_RECORD a
	WHERE a.FUND_TYPE ='1' AND a.ACCT_STATE ='00'
		AND a.CREATE_TIME >= timestamp '2022-01-01 00:00:00' AND a.CREATE_TIME < timestamp '2022-08-01 00:00:00'
	GROUP BY a.CUSTOMER_CODE, to_char(a.CREATE_TIME ,'yyyymm')
	UNION ALL 
	SELECT x.CUSTOMER_CODE , '线下直连', to_char(x.CREATE_TIME,'yyyymm'),
		sum(x.AMT) / 100, sum(x.PROCEDURE_FEE) / 100, count(0)
	FROM CHK_XX_TRAN_RECORD x 
	WHERE x.CREATE_TIME >= timestamp '2022-01-01 00:00:00' AND x.CREATE_TIME < timestamp '2022-08-01 00:00:00'
	GROUP BY x.CUSTOMER_CODE ,to_char(x.CREATE_TIME,'yyyymm')
) t2 INNER JOIN CUST_CUSTOMER c ON t2.customer_code= c.CUSTOMER_NO
	LEFT JOIN PAS_USER u ON c.BUSINESS_MAN_ID =u.USER_ID ;

--===============分账交易===============
SELECT t2.月份, c.NAME AS 商户名称, t2.customer_code AS 商户编号,  
	c.plat_customer_no as 所属平台商号, (select name from cust_customer c1 where c1.customer_no=c.plat_customer_no) AS 所属平台名称,
	c.service_customer_no 所属服务商号, (select name from cust_customer c1 where c1.customer_no=c.service_customer_no) AS 所属服务商名称,
	u.REAL_NAME as 业务员,decode(c.OPEN_ACCOUNT,1,'是',0,'否') AS 是否开通账户, decode(c.ACCEPT_ORDER,1,'是',0,'否') AS 是否开通收单,
	t2.business AS 业务,
	t2.笔数, t2.交易金额, t2.手续费
FROM (
	SELECT /*+ parallel (t,4) */ t.SOURCE_CUSTOMER_CODE as CUSTOMER_CODE, '分账' AS business ,to_char(t.CREATE_TIME ,'yyyymm') AS 月份,
		sum(t.AMOUNT) / 100 AS 交易金额, sum(t.PROCEDUREFEE) / 100 AS 手续费, count(t.TRANSACTION_NO) AS 笔数
	FROM TXS_SPLIT_RECORD t 
	WHERE t.STATE ='3' AND t.SOURCE_CUSTOMER_CODE <> CUSTOMER_CODE
		AND t.CREATE_TIME >= timestamp '2022-01-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-08-01 00:00:00'
	GROUP BY t.SOURCE_CUSTOMER_CODE, to_char(t.CREATE_TIME ,'yyyymm')
) t2 INNER JOIN CUST_CUSTOMER c ON t2.customer_code= c.CUSTOMER_NO
	LEFT JOIN PAS_USER u ON c.BUSINESS_MAN_ID =u.USER_ID ;

--===============提现&代付交易===============
SELECT t2.月份, c.NAME AS 商户名称, t2.customer_code AS 商户编号,  
	c.plat_customer_no as 所属平台商号, (select name from cust_customer c1 where c1.customer_no=c.plat_customer_no) AS 所属平台名称,
	c.service_customer_no 所属服务商号, (select name from cust_customer c1 where c1.customer_no=c.service_customer_no) AS 所属服务商名称,
	u.REAL_NAME as 业务员,decode(c.OPEN_ACCOUNT,1,'是',0,'否') AS 是否开通账户, decode(c.ACCEPT_ORDER,1,'是',0,'否') AS 是否开通收单,
	t2.business AS 业务,
	t2.笔数, t2.交易金额, t2.手续费
FROM (
	SELECT /*+ parallel (t,4) */ t.CUSTOMER_CODE, b.NAME AS business ,to_char(t.CREATE_TIME ,'yyyymm') AS 月份,
		sum(t.TOTAL_FEE) / 100 AS 交易金额, sum(t.PROCEDURE_FEE) / 100 AS 手续费, count(t.TRANSACTION_NO) AS 笔数
	FROM TXS_WITHDRAW_TRADE_ORDER t 
		INNER JOIN PAS_BUSINESS b ON t.business_code=b.CODE
	WHERE t.PAY_STATE ='00' 
		AND t.CREATE_TIME >= timestamp '2022-01-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-08-01 00:00:00'
	GROUP BY t.CUSTOMER_CODE, b.NAME, to_char(t.CREATE_TIME ,'yyyymm')
) t2 INNER JOIN CUST_CUSTOMER c ON t2.customer_code= c.CUSTOMER_NO
	LEFT JOIN PAS_USER u ON c.BUSINESS_MAN_ID =u.USER_ID ;

--------------------------------------------------------------------------
SELECT t.CUSTOMER_CODE AS 商户号,t.CUSTOMERNAME AS 商户名称, t.BUSINESS_CODE  AS 业务代码, b.NAME AS 业务名称 ,sum(t.AMOUNT) / 100 AS 总金额, 
  sum(t.PROCEDURE_FEE) / 100 AS 总手续费, count(t.TRANSACTION_NO) AS 总笔数, to_char(max(t.CREATE_TIME),'yyyy-mm-dd hh24:mi:ss') AS 最后一笔时间
FROM TXS_PAY_TRADE_ORDER t 
	INNER JOIN info.tmp_cust_0808 c ON t.CUSTOMER_CODE =c.CUSTOMER_CODE
	LEFT JOIN PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE 
WHERE t.STATE ='00' AND t.CREATE_TIME >= timestamp '2021-01-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-01-01 00:00:00'
GROUP BY t.CUSTOMER_CODE ,t.CUSTOMERNAME,t.BUSINESS_CODE ,b.NAME ;

--充值交易统计
SELECT t.CUSTOMER_CODE AS 商户号 ,sum(t.AMOUNT) / 100 AS 总金额, sum(t.PROCEDURE_FEE) / 100 AS 总手续费,count(t.TRANSACTION_NO) AS 总笔数
FROM PAS_ACCT_QUOTA_RECORD t
WHERE t.FUND_TYPE ='1' AND t.ACCT_STATE ='00' AND t.CREATE_TIME >= timestamp '2021-01-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-01-01 00:00:00'
  AND exists(SELECT 1 FROM CUST_CUSTOMER c WHERE t.CUSTOMER_CODE=c.CUSTOMER_NO AND c.CATEGORY =0 AND c.CREATE_TIME < timestamp '2022-01-01 00:00:00')
GROUP BY t.CUSTOMER_CODE;

--出金交易统计
SELECT t.CUSTOMER_CODE AS 商户编号,t.CUSTOMERNAME AS 商户名称, 
	sum(t.TOTAL_FEE) / 100  AS 总金额, count(t.TRANSACTION_NO) AS 总笔数, 
	sum(CASE WHEN t.BUSINESS_CODE in('WithdrawToSettmentCredit','WithdrawToSettmentDebit') THEN t.TOTAL_FEE ELSE 0 END) / 100  AS 总提现金额,  
	sum(CASE WHEN t.BUSINESS_CODE in('WithdrawToSettmentCredit','WithdrawToSettmentDebit') THEN 1 ELSE 0 END)   AS 总提现笔数, 
	sum(CASE WHEN NOT t.BUSINESS_CODE in('WithdrawToSettmentCredit','WithdrawToSettmentDebit') THEN t.TOTAL_FEE ELSE 0 END) / 100  AS 总代付金额,  
	sum(CASE WHEN NOT t.BUSINESS_CODE in('WithdrawToSettmentCredit','WithdrawToSettmentDebit') THEN 1 ELSE 0 END)  AS 总代付笔数, 
	max(t.CREATE_TIME) AS 最后一笔时间
FROM TXS_WITHDRAW_TRADE_ORDER t 
	INNER JOIN info.tmp_cust_0808 c ON t.CUSTOMER_CODE =c.CUSTOMER_CODE
WHERE t.PAY_STATE ='00' AND t.CREATE_TIME >= timestamp '2021-01-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-01-01 00:00:00'
GROUP BY t.CUSTOMER_CODE ,t.CUSTOMERNAME ;


SELECT t.CUSTOMER_CODE AS 商户编号,t.CUSTOMERNAME AS 商户名称, sum(t.TOTAL_FEE) / 100  AS 总金额, sum(t.PROCEDURE_FEE) / 100 AS 总手续费, count(t.TRANSACTION_NO) AS 总笔数, max(t.CREATE_TIME) AS 最后一笔时间
FROM TXS_WITHDRAW_TRADE_ORDER t 
WHERE t.PAY_STATE ='00' AND t.CREATE_TIME >= timestamp '2021-01-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-01-01 00:00:00'
  AND t.BUSINESS_CODE ='Withdraw'
GROUP BY t.CUSTOMER_CODE ,t.CUSTOMERNAME ;

 ---------------------导出商户信息-----------------------
SELECT c.CLIENT_NO as 客户号,c.name as 商户名称, c.CUSTOMER_NO AS 商户号, 
	to_char(c.CREATE_TIME ,'yyyy-mm-dd') as 创建日期,
	decode(c.CATEGORY,0,'商户',2,'平台商户',3,'服务商',4,'个人',5,'结算商户') AS 角色,
	decode(c."TYPE", 10, '个体工商户', 20, '企业', 30, '境外商户', 50, '小微', 60, '个人客户', 70, '政府/事业单位') AS 性质,
	c.plat_customer_no as 所属平台商号, 
	(select name from cust_customer c1 where c1.customer_no=c.plat_customer_no) AS 所属平台名称,
	c.service_customer_no 所属服务商号,
	(select name from cust_customer c1 where c1.customer_no=c.service_customer_no) AS 所属服务商名称,
	u.REAL_NAME as 业务员,
	to_char(c.REGISTER_TIME, 'yyyy-mm-dd hh24:mi:ss') AS 账户开通时间,
	decode(c.STATUS, 1, '正常', 2, '冻结', 3, '注销', 4, '止付', 5, '禁止入金') AS 账户状态,
	(CASE WHEN c.STATUS =2 THEN to_char(c.UPDATE_TIME, 'yyyy-mm-dd hh24:mi:ss') ELSE NULL END) as 账户冻结时间, 
	(CASE WHEN c.STATUS =3 THEN to_char(c.UPDATE_TIME, 'yyyy-mm-dd hh24:mi:ss') ELSE NULL END) as 账户注销时间, 
	(CASE WHEN c.STATUS =4 THEN to_char(c.UPDATE_TIME, 'yyyy-mm-dd hh24:mi:ss') ELSE NULL END) as 账户止付时间,
	(CASE WHEN c.STATUS =5 THEN to_char(c.UPDATE_TIME, 'yyyy-mm-dd hh24:mi:ss') ELSE NULL END) as 账户禁止入金时间,
	(CASE WHEN ccd.SIGN_STATUS = 1 THEN '已签约' ELSE '未签约'END)AS 协议书状态,
	decode(ccd.AUDIT_STATUS, '00', '待初审', '01', '初审未通过', '02', '待复审', '03', '复审未通过', '04', '审核成功', '05', '草稿', '06', '待预审', '07', '预审未通过') AS 审核状态, 
	decode(c.rc_status, '0','正常','1','冻结') as 风控状态,
	(CASE WHEN c.RC_STATUS = '1' THEN to_char(c.UPDATE_TIME, 'yyyy-mm-dd hh24:mi:ss') ELSE NULL END) as 风控冻结时间, 
	(SELECT param_value FROM CUST_STATIC_PARAM csp 
		WHERE csp.PARAM_TYPE='BUSINESS_ROLE' AND csp.PARAM_NAME=c.BUSINESS_ROLE) AS 业务角色,
	e.BUSINESS_LICENSE_NAME as 营业执照名称,
	e.BUSINESS_LICENSE_NO as 营业执照号,
	c.ADDRESS as 注册地址,
	e.LEGAL_REPRESENTATIVE_NAME as 法人代表姓名,
	(SELECT param_value FROM CUST_STATIC_PARAM csp 
		WHERE csp.PARAM_TYPE='CERTIFICATE_TYPE' AND csp.PARAM_NAME=e.CERTIFICATE_TYPE) AS 证件类型,
	e.CERTIFICATE_NO  as 证件号码,e.EN_CERTIFICATE_NO as ENC_CERTIFICATE_NO,
	c.phone as 联系电话,c.EN_MOBILE as ENC_MOBILE,
	e.ICP_RECORD_NO as ICP备案号,
	e.SITE_URL as 网址,
	decode(ccsi.TARGET, 1, '自动结算', 2, '手动结算') AS 结算方法,
	decode(ccsi.BANK_ACCOUNT_TYPE, 1,'对公', 2, '对私', 5, '存折') AS 结算卡类型,
	ccsi.BANK_ACCOUNT_NO  AS 结算卡号,
	ccsi.CUSTOMER_NAME_IN_BANK AS 结算卡户名,
	ccsi.OPEN_ACCOUNT_BANK_NAME AS 开户行名称,
	to_char(c.UPDATE_TIME, 'yyyy-mm-dd hh24:mi:ss') AS 最近更新时间,
	aa.AVAILABLEBALANCE/100 as 可用余额,aa.FLOATBALANCE/100 as 在途余额,
	(aa.FROZENBALANCE+aa.RCBALANCE)/100 as 冻结余额,
	ab.AVAILABLEBALANCE/100 as 待分账余额
FROM CUST_CUSTOMER c 
	INNER JOIN CUST_CUSTOMER_DRAFT ccd ON c.CUSTOMER_ID =ccd.CUSTOMER_ID 
	LEFT JOIN CUST_CUSTOMER_ENTERPRISE e on c.customer_id=e.customer_id
	LEFT JOIN PAS_USER u ON c.BUSINESS_MAN_ID =u.USER_ID 
	LEFT JOIN CUST_SIGN_AGREEMENT a ON c.CUSTOMER_ID = a.CUSTOMER_ID 
	LEFT JOIN CUM_CUSTOMER_SETTLE_INFO ccsi ON c.CUSTOMER_NO =ccsi.CUSTOMER_CODE 
	LEFT JOIN ACC_ACCOUNT aa on 'JY-A'||c.customer_no=aa.code
	LEFT JOIN ACC_ACCOUNT ab on 'BJ-B'||c.customer_no=ab.code
ORDER BY c.CREATE_TIME  desc;

--商户审核记录
select c.customer_no as 商户编号,decode(c.STATUS, 1, '正常', 2, '冻结', 3, '注销', 4, '止付', 5, '禁止入金') AS 账户状态, decode(c.rc_status, '0','正常','1','冻结') as 风控状态,to_char(c.register_time,'yyyy-mm-dd hh24:mi:ss') as 入驻时间, 
  to_char(a.create_time,'yyyy-mm-dd hh24:mi:ss') as 申请时间, to_char(a.audit_time,'yyyy-mm-dd hh24:mi:ss') as 审批时间, 
  decode(a.audit_type,0,'预审',1,'初审',2,'复审') as 审核类型,decode(a.audit_result,0,'待审',1,'通过',2,'不通过') as 审核结果, a.remarks as 审核备忘 
from CUST_CUSTOMER_AUDIT_RECORD a 
  inner join cust_customer c on a.customer_id=c.customer_id where a.create_time > sysdate - 365 
order by a.customer_id,a.create_time;

--********以前开通代付到储蓄卡，并出金免报备
SELECT c.CUSTOMER_NO AS 商户编号, c.NAME  AS 商户名称
FROM CUST_CUSTOMER c
WHERE c.CREATE_TIME < timestamp '2022-01-01 00:00:00'
  AND EXISTS(SELECT 1 FROM CUST_BUSINESS b WHERE b.BUSINESS_CODE ='Withdraw' AND b.CUSTOMER_ID =c.CUSTOMER_ID )
  AND EXISTS(SELECT 1 FROM RC_ARCHIVE r WHERE r.ARCHIVE_CODE =c.CUSTOMER_NO AND r.BIND_CARD ='0');


--导出开通“代付到储蓄卡”业务的商户
SELECT DISTINCT cc.CUSTOMER_NO ,cc.NAME 
FROM CUST_CUSTOMER cc INNER JOIN CUST_BUSINESS cb ON cc.CUSTOMER_ID =cb.CUSTOMER_ID 
WHERE cb.BUSINESS_CODE ='Withdraw' AND cb.CREATE_TIME < timestamp '2022-01-01 00:00:00';
SELECT * FROM CUST_STATIC_PARAM csp WHERE PARAM_TYPE LIKE '%STATUS%';

--导出终端
select t.customer_code as 商户编号,c.name as 商户名称,t.terminal_code as 终端号, 
(SELECT p.PARAM_VALUE FROM CUST_STATIC_PARAM p WHERE p.PARAM_TYPE='DEV_TYPE' AND p.PARAM_NAME=t."TYPE"||'')AS 终端类型, 
decode(t.status,1,'启用',2,'停用',3,'预录入',4,'已退机',5,'入库',6,'待审核') as 终端状态, 
t.machine_code as 终端序列号,to_char(t.activate_time,'yyyy-mm-dd hh24:mi:ss') as 布放时间,
decode(t.CHANNEL_TYPE,'01','全渠道平台','03','收单平台') AS 取号平台,
(select param_value from cust_static_param where param_type = 'EPSP_REGION_CODE' and param_name = t.province) || '-' || 
  (select param_value from cust_static_param where param_type = 'EPSP_REGION_CODE' and param_name = t.city) || '-' || 
  (select param_value from cust_static_param where param_type = 'EPSP_REGION_CODE' and param_name = t.area) || '-' ||t.address as 终端地址
from cust_terminal t inner join cust_customer c on t.customer_code=c.customer_no;

--商户对公卡
SELECT DISTINCT t.CUSTOMER_NO AS 商户号 , CARD_NO AS enc_card_no , CARD_OWNER AS enc_card_owner, (CASE WHEN t.BANK_ACCOUNT_TYPE ='1' THEN '对公卡' ELSE '对私卡'END ) as bank_account_type,t.BANK_CODE ,t.OPEN_BANK ,b.BANK_NAME 
FROM CUST_BANK_CARD t 
	INNER JOIN CUST_CUSTOMER c ON t.CUSTOMER_ID =c.CUSTOMER_ID 
	LEFT JOIN CUM_BANK_INFO b ON t.BANK_CODE =b.BANK_CODE 
WHERE t.BANK_ACCOUNT_TYPE =1 AND t.CARD_NO IS NOT NULL AND c.CREATE_TIME < timestamp '2022-01-01 00:00:00';

--默认结算账户
SELECT t.CUSTOMER_CODE AS 商户号, t.TARGET AS 是否结算到银行卡, t.BANK_ACCOUNT_TYPE AS 账户类型, t.BANK_ACCOUNT_NO AS 结算账户,
	t.CUSTOMER_NAME_IN_BANK AS 结算户名, t.BANK_CODE AS 银行代码, t.OPEN_ACCOUNT_BANK_NAME AS 银行名称 
FROM CUM_CUSTOMER_SETTLE_INFO t INNER JOIN CUST_CUSTOMER c ON t.CUSTOMER_CODE =c.CUSTOMER_NO 
WHERE c.CREATE_TIME < timestamp '2022-01-01 00:00:00' AND c.OPEN_ACCOUNT=1 AND c.ACCEPT_ORDER=1 ;

--导出商户证书及对应业务员，********
SELECT cc.CUSTOMER_NO AS 商户编号 , cc.NAME AS 商户名称, to_char(uc.NOTBEFORE,'yyyy-mm-dd') AS 证书有效起始时间, to_char(uc.NOTAFTER ,'yyyy-mm-dd') AS 证书有效结束时间, (CASE WHEN uc.status=0 THEN '正常' ELSE '无效' END) AS 证书状态,
  (CASE WHEN uc.TYPE IN('5','3') THEN '国密' ELSE '国际' END) AS 证书类型,pu.REAL_NAME AS 业务员, pu.MOBILE AS 业务员电话
FROM UAA_CERTIFICATE uc 
  INNER JOIN CUST_CUSTOMER cc ON uc.CUSTOMERCODE =cc.CUSTOMER_NO 
  LEFT JOIN PAS_USER pu ON cc.BUSINESS_MAN_ID = pu.USER_ID 
WHERE CUSTOMERCODE in('***************');

----------------导出登录日志-----------------
SELECT LOGIN_IP AS 登录IP,USERNAME AS 登录名 , REALNAME AS 用户名 , CUSTOMERCODE AS 商户号, 
	to_char(LOGIN_TIME,'yyyy-mm-dd hh24:mi:ss') AS 登录时间,
	CASE WHEN state=1 THEN '成功' ELSE '失败'END AS 状态
FROM PAS_LOGIN_LOG pll
WHERE LOGIN_IP NOT LIKE '10.%' AND 
	CUSTOMERCODE IN('***************') 
ORDER BY CUSTOMERCODE, LOGIN_TIME DESC ;

--根据卡号分析数据并导出交易
select c.card_no as c_card, t.card_no as t_card, c.card_user as c_user, t.bank_user_name_full as t_user,
       c.trans_amount as c_amnt, (t.total_fee - t.procedure_fee) / 100 as t_amnt, c.trans_no as c_trans, t.channel_order as t_trans,
       t.customer_code as 商户号, t.customername as 商户名, c.transaction_no as 易票联单号,
       to_char(a.createdatetime, 'yyyy-mm-dd hh24:mi:ss') as 创建时间, a.availablebalance / 100 as 可用余额, a.floatbalance / 100 as 在途余额, a.frozenbalance / 100 as 冻结金额,
       u.rc_status as 风控状态, e.legal_representative_name as 法人, e.certificate_no as 法人证件
  from tmp_card_0629 c
  left join epsp.acc_account a on 'JY-A' || c.customer_code = a.code
  left join txs_withdraw_trade_order t on c.transaction_no = t.transaction_no
  left join cust_customer u on t.customer_code = u.customer_no
  inner join cust_customer_enterprise e on u.customer_id = e.customer_id;

--账户状态查询
select c.client_no as 客户号,t.customercode as 商户号,c.name as 商户名称,
       decode(t.attribute , 0,'单位支付账户', 1,'个人支付账户') as 账户属性,
       decode(t.lv,1,'Ⅰ类', 2,'Ⅱ类', 3,'Ⅲ类') as 账户等级,
       (t.availablebalance+t.floatbalance+t.frozenbalance)/100 as 资金总额,
       t.availablebalance/100 as 已结算金额,
       t.floatbalance/100 as 待结算金额,
       t.frozenbalance/100 as 冻结金额,
       (t.availablebalance+t.floatbalance-t.frozenbalance)/100 as 可出金总额,
       decode(c.STATUS, 1, '正常', 2, '冻结', 3, '注销', 4, '止付', 5, '禁止入金') AS 状态,
       to_char(c.update_time,'yyyy-mm-dd hh24:mi:ss') as 更新时间
from acc_account t inner join cust_customer c on t.customercode=c.customer_no
where t.code like 'JY-A%' and 
t.customercode in('***************','***************','***************');


SELECT CUSTOMER_CODE as 商户号, INLET_SEQ_NO as 进件标志号,CHANNEL_MCHT_NO as 上游商户号,  
	CHANNEL_CATEGORY_ID as 支付渠道大类ID,INSTITUTION_ID as 上游机构, CHANNEL_INST_CODE as 机构号,  
	CHANNEL_ID as 渠道号,APPID as 机构APPID, INLET_STATE as 取号状态, 
	INLET_TYPE as 取号类型, INLET_STYLE as 取号方式, BUSINESS_CATEGORY as 经营类目, 
	CONTACT_NAME as 联系人姓名, CONTACT_TAG as 联系人职责, CONTACT_TYPE as 联系人类型, 
	MERCHANT_NAME as 商户名称, MERCHANT_SHORT_NAME as 商户简称, SERVICE_PHONE as 客服电话, 
	TERMINAL_CODE as 终端号, MCC_LEVEL as 商户类别层级, REGION_LEVEL as 经营地区层级, 
	REGION_CODE as 地区码, PROVINCE_CODE as 省份编码, CITY_CODE as 城市编码, DISTRICT_CODE as 区县编码, 
	ADDRESS as 详细经营地址, BUSINESS_LICENSE_TYPE as 商户证件类型, BUSINESS_LICENSE as 商户证件编号, 
	CARD_NO as 银行卡号, CARD_NAME as 银行卡持卡人姓名, BANK_BRANCH_NAME as 银行开户行名称
FROM CUST_INLET_RECORD t
WHERE t.INSTITUTION_ID != '18' AND t.USE_STATE = 1 AND t.INLET_STATE ='00';


-------------------出入金增加记账金额

--分账记录
SELECT /*+parallel(t,5)*/t.CUSTOMER_CODE AS 商户编号 ,t.CUSTOMERNAME AS 商户名称 ,
t.OUT_TRADE_NO AS 商户单号, t.TRANSACTION_NO AS 易票联单号,
t.AMOUNT / 100 AS 订单金额 , t.PROCEDURE_FEE / 100 AS 手续费金额, 
t2.customer_code AS 分账商户编号,t2.customername AS 分账商户名称,
(case when t1.procedure_customer_code=t2.customer_code then '是' else '否' end) as 是否手续费商户,
t2.amount/100 as 分账金额,t2.pay_procedurefee/100 as 收单手续费,t2.split_procedure_fee/100 as 分账手续费
FROM TXS_PAY_TRADE_ORDER t
INNER JOIN CUST_CUSTOMER cc ON t.CUSTOMER_CODE =cc.CUSTOMER_NO
LEFT JOIN TXS_SPLIT_ORDER t1 on t.out_trade_no=t1.out_trade_no and t.customer_code=t1.customer_code AND t1.state='00'
LEFT JOIN TXS_SPLIT_RECORD t2 on t1.transaction_no=t2.transaction_no
WHERE t.state='00' AND cc.CUSTOMER_NO IN ('562671003747487','562809003751059','562725003751054','562497003750557','562780003750559','562933003405898','562514003405913','562917003395584','562379003405703','562323003405936','562047003384869')
ORDER BY t.CUSTOMER_CODE, t.transaction_no;

--商户的交易的各分账对象金额
SELECT t.CUSTOMER_CODE AS 收单商户,sum(t.AMOUNT)/100 AS 收单交易金额, sum(t.PROCEDURE_FEE)/100 AS 收单交易手续费, count(t.TRANSACTION_NO) AS 收单交易笔数,
	sum(CASE WHEN t3.CUSTOMER_CODE=t.CUSTOMER_CODE THEN t3.AMOUNT ELSE 0 END)/100 AS 分账给交易商户金额,
	sum(CASE WHEN t3.CUSTOMER_CODE<>t.CUSTOMER_CODE THEN t3.AMOUNT ELSE 0 END)/100 AS 分账给其他商户金额
FROM TXS_PAY_TRADE_ORDER t 
	INNER JOIN TXS_SPLIT_ORDER t2 ON t.OUT_TRADE_NO = t2.OUT_TRADE_NO AND t.CUSTOMER_CODE = t2.CUSTOMER_CODE 
	INNER JOIN TXS_SPLIT_RECORD t3 ON t2.TRANSACTION_NO = t3.TRANSACTION_NO 
WHERE t.STATE ='00' AND t2.STATE = '00' 
GROUP BY t.CUSTOMER_CODE;

SELECT t.CUSTOMER_CODE AS 收单商户, t3.CUSTOMER_CODE AS 分账商户,sum(t3.AMOUNT)/100 AS 分账金额
FROM TXS_PAY_TRADE_ORDER t 
	INNER JOIN TXS_SPLIT_ORDER t2 ON t.OUT_TRADE_NO = t2.OUT_TRADE_NO AND t.CUSTOMER_CODE = t2.CUSTOMER_CODE 
	INNER JOIN TXS_SPLIT_RECORD t3 ON t2.TRANSACTION_NO = t3.TRANSACTION_NO 
WHERE t.STATE ='00' AND t2.STATE = '00' 
GROUP BY t.CUSTOMER_CODE, t3.CUSTOMER_CODE;

SELECT /*+parallel(t3,4)*/ t.CUSTOMER_CODE AS 分账商户编号,t.CUSTOMERNAME AS 分账商户名称,
  t.OUT_TRADE_NO AS 商户单号, t.TRANSACTION_NO AS 交易单号, t.BUSINESS_CODE AS 业务代码, 
  to_char(t.END_TIME ,'yyyy-mm-dd hh24:mi:ss') AS 交易时间, t.AMOUNT/100 AS 交易金额, t.PROCEDURE_FEE/100 AS 交易手续费, 
	t3.CUSTOMER_CODE AS 分账目标商户,t2.TRANSACTION_NO AS 分账单号, 
  t2.PROCEDURE_FEE/100 AS 分账手续费, t3.AMOUNT  / 100 AS 分账金额
FROM TXS_PAY_TRADE_ORDER t 
	INNER JOIN TXS_SPLIT_ORDER t2 ON t.OUT_TRADE_NO = t2.OUT_TRADE_NO AND t.CUSTOMER_CODE = t2.CUSTOMER_CODE 
	INNER JOIN TXS_SPLIT_RECORD t3 ON t2.TRANSACTION_NO = t3.TRANSACTION_NO 
WHERE t.STATE ='00' AND t2.STATE = '00' AND t3.AMOUNT > 0 
  AND t3.CUSTOMER_CODE IN ('562078003820340','562888003829333')
ORDER BY t.CUSTOMER_CODE , t.TRANSACTION_NO ;

--资金冻结解冻操作日志
SELECT rol.code AS 商户号,rol.name AS 商户名, to_char(rol.OPERATE_TIME ,'yyyy-mm-dd hh24:mi:ss') AS 操作时间, pu.REAL_NAME AS 操作人,
	(CASE WHEN TO_NUMBER(NEW_VALUE)-TO_NUMBER(ORIG_VALUE) >0 THEN '冻结' ELSE '解冻' END) AS 操作类型, 
	(TO_NUMBER(NEW_VALUE)-TO_NUMBER(ORIG_VALUE))/100 AS 操作金额,
	(SELECT NEW_VALUE FROM RC_OPERATE_LOG rl WHERE rl.CODE=rol.CODE AND rl.OPERATE_TIME=rol.OPERATE_TIME AND rl.OPERATE_CONTENT='风控冻结金额修改原因') AS 原因
FROM RC_OPERATE_LOG rol INNER JOIN PAS_USER pu ON rol.OPERATOR =pu.USER_ID 
WHERE OPERATE_TIME > timestamp'2022-01-01 00:00:00' AND OPERATE_TIME < timestamp'2023-01-01 00:00:00' AND OPERATE_CONTENT ='风控冻结金额'
ORDER BY rol.CODE ,rol.OPERATE_TIME ;

--风控冻结解冻操作日志
SELECT rol.code AS 商户号,rol.name AS 商户名, to_char(rol.OPERATE_TIME ,'yyyy-mm-dd hh24:mi:ss') AS 操作时间, pu.REAL_NAME AS 操作人,
	(CASE WHEN NEW_VALUE = '冻结' THEN '冻结' ELSE '解冻' END) AS 操作类型, 
	(SELECT DISTINCT NEW_VALUE FROM RC_OPERATE_LOG rl WHERE rl.CODE=rol.CODE AND rl.OPERATE_TIME=rol.OPERATE_TIME AND rl.OPERATE_CONTENT='风控状态修改原因') AS 原因
FROM RC_OPERATE_LOG rol INNER JOIN PAS_USER pu ON rol.OPERATOR =pu.USER_ID 
WHERE OPERATE_TIME > timestamp'2022-01-01 00:00:00' AND OPERATE_TIME < timestamp'2023-01-01 00:00:00' AND OPERATE_CONTENT ='风控状态'
ORDER BY rol.CODE ,rol.OPERATE_TIME ;

--导出菜单
select * from (
  select 
    p.perm_id 权限ID,p.parent_id 上级权限ID,
    sys_connect_by_path(alias,'/') as 权限名称,
    decode(p.PERM_TYPE,1,'目录',2,'页面',3,'按钮',4,'外部跳转') as 类型,
    URL,
    (case when exists(select 1 from cust_role_perm where role_id=100001 and perm_id=p.perm_id) then '是' else '-'end) as 是否代理商权限,
    (case when exists(select 1 from cust_role_perm where role_id=100002 and perm_id=p.perm_id) then '是' else '-'end) as 是否平台商权限 
  from cust_perm p 
  where p.perm_id in(select perm_id from cust_role_perm where role_id in(100001,100002))
  START WITH parent_id = 0
  CONNECT BY PRIOR perm_id = parent_id
) 
where 权限名称 in('/商户中心/下级商户信息/商户信息管理/商户信息管理-新增','/商户中心/下级商户信息/商户信息管理/商户信息管理-修改','/商户中心/下级商户信息/商户信息管理/业务管理/新增','/商户中心/下级商户信息/商户信息管理/业务管理/修改','/商户中心/下级商户信息/商户信息管理/业务管理/停用')
order by 权限名称;

--====================资金分析====================
--商户提款和充值
SELECT t.CUSTOMER_CODE AS 商户号, t.TRANSACTION_NO AS 易票联单号, r.TRANSACTION_NO AS 上游订单号, r.OUT_TRADE_NO AS 商户订单号, decode(t.CHANGE_TYPE,'1','1-充值','2','2-提款') AS 操作类型,  
  (CASE WHEN t.CHANGE_TYPE ='1' THEN decode(t.FUND_TYPE,'1','1-银行转账','2','2-商户赔付','3','3-商户退款','4','4-商户补款','7','7-企业网银充值') 
              WHEN t.CHANGE_TYPE ='2' THEN decode(t.FUND_TYPE,'1','1-商户扣款','2','2-银联退款','3','3-商户扣罚','4','4-跨境结算','5','5-跨境手续费') ELSE t.FUND_TYPE END) AS 业务类型,
  t.DEBTOR_ACCOUNT AS 付款人账号, t.DEBTOR_ACCOUNT_NAME AS 付款人名称,r.DEBTOR_BANK_NAME AS 付款银行,
  t.AMOUNT/100 AS 金额 , t.PROCEDURE_FEE / 100 AS 手续费  , 
  t.AMOUNT * (CASE WHEN CHANGE_TYPE='1' THEN 1 ELSE -1 END) / 100 AS 记账金额, 
  f.Amount/100 AS 记账金额2,
  FEE_CUSTOMER_CODE AS 手续费商户号, to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 操作时间, REMARK
FROM PAS_ACCT_QUOTA_RECORD t 
  LEFT JOIN ACS_HVPS_TXS_RECORD r ON t.TRANSACTION_NO = 'ACS'||r.MSG_ID 
  LEFT JOIN ACC_ACCOUNTFLOW f ON T.TRANSACTION_NO=f.Transactionno
WHERE t.ACCT_STATE ='00' 
ORDER BY t.CHANGE_TYPE, FUND_TYPE, t.CREATE_TIME desc;
 --出金交易，包括提现、代付和余额转账付款方
SELECT /*+parallel(5)*/ t.CUSTOMER_CODE AS 商户编号, t.OUT_TRADE_NO AS 商户单号, t.TRANSACTION_NO AS 交易单号, t.BUSINESS_CODE AS 业务代码,
  to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 下单时间, to_char(t.END_TIME,'yyyy-mm-dd hh24:mi:ss') AS 完成时间 , 
  decode(t.PAY_STATE,'00','成功','01','失败','03','处理中') AS 订单状态, 
  t.ACTUAL_FEE / 100 AS 订单金额 , t.PROCEDURE_FEE / 100 AS 手续费金额, t.PROCEDURE_CUSTOMERCODE AS 手续费商户号, 
  t.SERVICE_FEE / 100 AS 服务费, t.PAY_SERVICE_FEE / 100 as 支付服务费,
  (CASE WHEN ID > 0 THEN (t.ACTUAL_FEE   --ID<0为补单，不记账
	    + (CASE WHEN PROCEDURE_CUSTOMERCODE IS NOT NULL AND PROCEDURE_CUSTOMERCODE <> CUSTOMER_CODE THEN 0 ELSE nvl(t.PROCEDURE_FEE,0) END) )  --指定手续费商户时，不计手续费
	  ELSE 0 END) / 100 * -1 AS 记账金额,
  --aa.AMOUNT/100 AS 记账金额2,aa.AFTERBALANCE/100 AS 账户余额, aa.accountcode,
  t.CARD_NO_CIPHER AS enc_card_no, t.CARD_NO AS 提现卡, t.BANK_USER_NAME_FULL AS 持卡人,
  t.BANK_NAME AS 银行名称,t.REMARK AS 备注, t.CHANNEL_ORDER AS 上游单号,t.BATCH_NO AS 批量代付批号,
  (CASE WHEN th_state='1' THEN '是' ELSE '否' END) 是否退汇,
  (CASE WHEN ID < 0 THEN '是' ELSE '否' END) AS 是否补单
FROM TXS_WITHDRAW_TRADE_ORDER t
  --LEFT JOIN ACC_ACCOUNTFLOW aa ON t.TRANSACTION_NO=aa.TRANSACTIONNO AND aa.accountcode='JY-A'||t.customer_code
WHERE t.pay_state='00' AND t.BUSINESS_CODE!='BalanceTrans' 
ORDER BY t.CUSTOMER_CODE , t.CREATE_TIME;
--出金交易——收取手续费商户的手续费
--出金交易——收取服务费

select *from cum_split_business

--余额转账收款方
SELECT /*+parallel(5)*/ t.CARD_NO AS 商户编号, t.OUT_TRADE_NO AS 商户单号, t.TRANSACTION_NO AS 交易单号, t.BUSINESS_CODE AS 业务代码,
  to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 下单时间, to_char(t.END_TIME,'yyyy-mm-dd hh24:mi:ss') AS 完成时间 , 
  decode(t.PAY_STATE,'00','成功','01','失败','03','处理中') AS 订单状态, 
  t.ACTUAL_FEE / 100 AS 订单金额 , t.PROCEDURE_FEE / 100 AS 手续费金额,  
  t.ACTUAL_FEE / 100 AS 记账金额,
  aa.AMOUNT/100 AS 记账金额2,aa.AFTERBALANCE/100 AS 账户余额, aa.accountcode,
  t.SERVICE_FEE / 100 AS 服务费, t.PAY_SERVICE_FEE / 100 as 支付服务费,
  t.REMARK AS 备注, t.CHANNEL_ORDER AS 上游单号,
  (CASE WHEN ID < 0 THEN '是' ELSE '否' END) AS 是否补单
FROM TXS_WITHDRAW_TRADE_ORDER t
  LEFT JOIN ACC_ACCOUNTFLOW aa ON t.TRANSACTION_NO=aa.TRANSACTIONNO AND aa.accountcode='JY-A'||t.CARD_NO
WHERE t.pay_state='00' AND t.BUSINESS_CODE='BalanceTrans' 
ORDER BY t.CUSTOMER_CODE , t.CREATE_TIME;

--退汇交易——退回余额
SELECT CUSTOMERCODE AS 商户号, TRANSACTION_NO AS 退汇单号, ORGI_TXN_NO AS 原提现单号,
  to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 退汇时间,  
  ORGI_ACTUAL_FEE/100 AS 原提现单金额, ORGI_PROCEDURE_FEE/100 AS 原提现单手续费, 
  (t.ORGI_ACTUAL_FEE + nvl(t.ORGI_PROCEDURE_FEE,0)) / 100 AS 记账金额,
  aa.AMOUNT/100 AS 记账金额2,aa.AFTERBALANCE/100 AS 账户余额, aa.accountcode
FROM TXS_TUIHUI_RECORD t 
  INNER JOIN ACC_ACCOUNTFLOW aa ON t.TRANSACTION_NO=aa.TRANSACTIONNO AND aa.accountcode='JY-A'||t.customercode
WHERE t.STATE ='00'
ORDER BY CUSTOMERCODE, CREATE_TIME;

select t.procedure_customercode,t.service_fee_transaction_no,t.* from txs_withdraw_trade_order t where t.transaction_no='44202303085745068959832';
select * from acc_accountflow f where f.transactionno='44202303085745068959832';
--退汇交易——退回手续费（有手续费商户）
--退汇交易——退回服务费
--支付服务费汇总
select customer_code as 商户编号, account_voucher_no as 记账凭证, sett_transaction_no as 结算交易单号, clear_date as 清算日期, service_fee as 服务费, service_fee * -1 as 记账金额
  , aa.amount as 记账金额2, to_char(aa.accountdatetime, 'yyyy-mm-dd hh24:mi:ss') as 记账时间
from(
  select t.customer_code,t.account_voucher_no,sett_transaction_no, t.clear_date, sum(t.service_fee) as service_fee
  from txs_pay_service_order t
  group by t.customer_code, t.account_voucher_no, t.sett_transaction_no, t.clear_date
) t inner join acc_accountflow aa on t.sett_transaction_no=aa.transactionno
order by t.customer_code,aa.accountdatetime desc;

--支付服务费明细
SELECT CUSTOMER_CODE AS 商户号,decode(FEE_TYPE,'1','首笔','2','每笔')AS 类型, 
  t.BUSINESS_CODE AS 业务代码, t.ORIG_TRANSACTION_NO AS 原交易号,to_char(t.TXN_TIME,'yyyy-mm-dd hh24:mi:ss') AS 交易时间,  
  SERVICE_FEE / 100 AS 支付服务费,SERVICE_FEE / 100 * -1 AS 记账金额,
  t.CLEAR_DATE AS 清算日期
FROM TXS_PAY_SERVICE_ORDER t
WHERE ACC_STATE ='00';

--服务费（出金交易中的服务费）
select r.transaction_no as 出金交易单号, r.customer_code as 出金商户编号,to_char(r.create_time, 'yyyy-mm-dd hh24:mi:ss') as 出金单时间,
  t.transaction_no as 服务费交易编号, to_char(t.create_time, 'yyyy-mm-dd hh24:mi:ss') as 服务费订单时间, 
  t.customer_code as 服务费收款方,t.amount /100 as 收款金额,t.payer as 服务费付款方,t.amount / 100 * -1 as 付款金额
  ,aa.amount,aa.sourcecustomercode,aa.targetcustomercode,aa.type,aa.isrollback
from txs_pay_trade_order t 
  inner join txs_withdraw_trade_order r on t.transaction_no=r.service_fee_transaction_no and r.pay_state='00'
  inner join acc_accountflow aa on t.transaction_no=aa.transactionno
where t.transaction_type='SF' and t.state='00'
order by t.create_time desc;

--优惠券优惠金额
select t.transaction_no as 交易单号, t.coupon_code as 优惠券码, decode(t.type, '1', '优惠券', '2','满减') as 优惠券类型,
  t.contributor as 出资方编号, t.amount/100 as 出资金额, t.amount/100*-1 as 出资方记账金额,to_char(t.end_time,'yyyy-mm-dd hh24:mi:ss') as 完成时间
from txs_coupon_detail t
where t.state='01';

--线上收单交易
SELECT /*+parallel(t,5)*/t.CUSTOMER_CODE AS 商户编号 , t.BUSINESS_CODE AS 业务代码 ,t.TRANSACTION_TYPE,t.OUT_TRADE_NO AS 商户单号, t.TRANSACTION_NO AS 交易单号, 
  to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 下单时间, to_char(t.END_TIME,'yyyy-mm-dd hh24:mi:ss') AS 完成时间 , 
  decode(t.STATE, '00' , '成功' , '01' , '失败' , '03' , '处理中') AS 订单状态, decode(t.SPLIT_MODEL,'0','全额分账','1','普通分账','2','其他分账','3','费差分账','4','拆单分账','5','快捷分账') AS 分账模式,
  t.AMOUNT / 100 AS 订单金额 , t.ACTUAL_PAY_AMOUNT / 100 AS 应付金额, t.CASH_AMOUNT / 100 AS 支付金额, t.DISCOUNTABLE_AMOUNT AS 优惠金额,  
  t.COUPON_AMOUNT/100 AS 上游优惠, decode(t.IS_SETT_WITH_CASH_AMOUNT,'1','商户优惠') AS 上游优惠模式 , 
  t.LOCAL_COUPON AS 易票联优惠金额, t.COUPONS AS 易票联优惠券,
  t.PROCEDURE_FEE / 100 AS 手续费金额,t.PROCEDURE_CUSTOMERCODE AS 手续费商户号,
  ( CASE WHEN t.TRANSACTION_TYPE NOT IN('FZ','CZ') THEN t.AMOUNT  --分账和充值业务的出入金参考分账交易
     - (CASE WHEN t.PROCEDURE_CUSTOMERCODE IS NULL THEN t.PROCEDURE_FEE ELSE 0 END)  --有手续费商户时，手续费从手续费商户扣取
     - (CASE WHEN t.IS_SETT_WITH_CASH_AMOUNT='1' THEN nvl(t.COUPON_AMOUNT,0) ELSE 0 END )  --商户优惠，等同直接扣减
   ELSE 0 END ) / 100 AS 记账金额,
  --aa.AMOUNT/100 AS 记账金额2,aa.AFTERBALANCE/100 AS 账户余额, aa.accountcode,
  decode( t.CARD_TYPE , 'D' , '贷记卡' , 'C' , '借记卡' )AS 付款卡类型, t.BANK_CODE AS 付款卡开户行,
  t.CHANNEL_ORDER AS 上游单号, t.CLIENT_IP AS 交易IP, t.REMARK AS 交易摘要
FROM TXS_PAY_TRADE_ORDER t 
  --INNER JOIN ACC_ACCOUNTFLOW aa ON t.TRANSACTION_NO =aa.TRANSACTIONNO 
WHERE t.state='00' AND t.TRANSACTION_TYPE<>'ZHFZ' AND t.TRANSACTION_TYPE<>'SF' --排除服务费部分和账户分账
ORDER BY t.CUSTOMER_CODE , t.CREATE_TIME ;

--分账（分入）
SELECT /*+parallel(t,4)*/ t.CUSTOMER_CODE AS 分账商户编号, t.OUT_TRADE_NO AS 商户单号, t.TRANSACTION_NO AS 交易单号,   
  t.BUSINESS_CODE AS 业务代码, (CASE WHEN t.BUSINESS_CODE='AccountSplit' THEN '账户分账' ELSE decode(t.SPLIT_MODEL,'0','全额分账','1','普通分账','2','其他分账','3','费差分账','4','拆单分账','5','快捷分账') END) AS 分账模式, t2.TRANSACTION_NO AS 分账交易单号, t2.AMOUNT/100 AS 分账金额, 
  t3.PAY_PROCEDUREFEE / 100 AS 收单手续费,t2.PROCEDURE_CUSTOMER_CODE AS 收单手续费商户,
  t3.SPLIT_PROCEDURE_FEE / 100 AS 分账手续费,t2.SPLIT_PROCEDURE_CUSTOMER_CODE AS 分账手续费商户,
  t3.CUSTOMER_CODE AS 被分账商户,t3.AMOUNT  / 100 AS 被分账金额,t3.PROCEDUREFEE/100 AS 被分账手续费, 
  t3.AMOUNT  / 100 AS 记账金额,
  --aa.AMOUNT/100 AS 记账金额2,aa.AFTERBALANCE/100 AS 账户余额, aa.accountcode,  
  to_char(t2.END_TIME ,'yyyy-mm-dd hh24:mi:ss') AS 分账时间  
FROM TXS_PAY_TRADE_ORDER t 
  INNER JOIN TXS_SPLIT_ORDER t2 ON t.CUSTOMER_CODE=t2.CUSTOMER_CODE AND t.OUT_TRADE_NO=t2.OUT_TRADE_NO  
	INNER JOIN TXS_SPLIT_RECORD t3 ON t2.TRANSACTION_NO = t3.TRANSACTION_NO 
  --INNER JOIN ACC_ACCOUNTFLOW aa ON t2.Transaction_No=aa.transactionno AND aa.ACCOUNTCODE='JY-A'||t3.CUSTOMER_CODE
WHERE t.state='00' AND t2.STATE='00' AND t3.state='3' AND t.CREATE_TIME > sysdate - 360
ORDER BY t.CUSTOMER_CODE , t.TRANSACTION_NO ;

--账户分账（分出），手续费外扣
SELECT /*+parallel(t,4)*/ t.CUSTOMER_CODE AS 商户编号, t.OUT_TRADE_NO AS 商户单号, t.TRANSACTION_NO AS 交易单号,   
  t.BUSINESS_CODE AS 业务代码, (CASE WHEN t.BUSINESS_CODE='AccountSplit' THEN '账户分账' ELSE decode(t.SPLIT_MODEL,'0','全额分账','1','普通分账','2','其他分账','3','费差分账','4','拆单分账','5','快捷分账') END) AS 分账模式, t2.TRANSACTION_NO AS 分账交易单号,  
  t.AMOUNT/100 AS 交易金额, t.procedure_fee/100 as 交易手续费,t2.PROCEDURE_CUSTOMER_CODE AS 手续费商户,
  (t.AMOUNT + (CASE WHEN t2.PROCEDURE_CUSTOMER_CODE IS NULL OR t2.PROCEDURE_CUSTOMER_CODE=t.CUSTOMER_CODE THEN NVL(t.PROCEDURE_FEE,0) ELSE 0 END)) * -1  / 100 AS 记账金额,
  aa.AMOUNT/100 AS 记账金额2,aa.AFTERBALANCE/100 AS 账户余额, aa.accountcode,
  to_char(t2.END_TIME ,'yyyy-mm-dd hh24:mi:ss') AS 分账时间
FROM TXS_PAY_TRADE_ORDER t 
  INNER JOIN TXS_SPLIT_ORDER t2 ON t.CUSTOMER_CODE=t2.CUSTOMER_CODE AND t.OUT_TRADE_NO=t2.OUT_TRADE_NO  
  INNER JOIN ACC_ACCOUNTFLOW aa ON t2.Transaction_No=aa.transactionno AND aa.ACCOUNTCODE='JY-A'||t2.CUSTOMER_CODE AND aa.Sourcecustomercode=t2.customer_code
WHERE t.BUSINESS_CODE='AccountSplit' AND t.STATE='00' AND t2.STATE='00' AND t.CREATE_TIME > sysdate - 360
ORDER BY t.CUSTOMER_CODE , t.TRANSACTION_NO ;

--全球付款分账（分出），根据手续费承担方决定手续费归属
SELECT /*+parallel(t,4)*/ t.CUSTOMER_CODE AS 商户编号, t.OUT_TRADE_NO AS 商户单号, t.TRANSACTION_NO AS 交易单号,   
  t.BUSINESS_CODE AS 业务代码, '全球付款' AS 分账模式, t2.TRANSACTION_NO AS 分账交易单号, 
  t.AMOUNT/100 AS 交易金额, t.procedure_fee/100 as 交易手续费,decode(t2.PROCEDURE_TYPE,'0','付款方','1','收款方') AS 手续费承担方,
  (t.AMOUNT + CASE WHEN t2.PROCEDURE_TYPE = '0' THEN nvl(t.PROCEDURE_FEE,0) ELSE 0 END ) * -1  / 100 AS 记账金额,
  --aa.AMOUNT/100 AS 记账金额2,aa.AFTERBALANCE/100 AS 账户余额, aa.accountcode,  
  to_char(t2.END_TIME ,'yyyy-mm-dd hh24:mi:ss') AS 分账时间
FROM TXS_PAY_TRADE_ORDER t 
  INNER JOIN TXS_SPLIT_ORDER t2 ON t.CUSTOMER_CODE=t2.CUSTOMER_CODE AND t.OUT_TRADE_NO=t2.OUT_TRADE_NO  
  --INNER JOIN ACC_ACCOUNTFLOW aa ON t2.Transaction_No=aa.transactionno AND aa.ACCOUNTCODE='JY-A'||t2.CUSTOMER_CODE AND aa.Sourcecustomercode=t2.customer_code
WHERE t.BUSINESS_CODE LIKE 'Global%' AND t.STATE='00' AND t2.STATE='00' AND t.CREATE_TIME > sysdate - 60
ORDER BY t.CUSTOMER_CODE , t.TRANSACTION_NO ;

--分润入金
SELECT a.CUSTOMER_CODE AS 代理商编号 , a.AMOUNT / 100 AS 交易金额, a."PROCEDURE" / 100 AS 交易手续费 , a.FR_AMOUNT / 100 AS 分润金额 , a.FR_PROCEDURE / 100 AS 分润手续费, a.REAL_FR_AMOUNT / 100 AS 实际分润金额,
  b.TRANSACTION_NO AS 分润交易单号,a.REAL_FR_AMOUNT / 100 AS 记账金额,
  aa.AMOUNT/100 AS 记账金额2,aa.AFTERBALANCE/100 AS 账户余额, aa.accountcode, 
  to_char(a.create_time,'yyyy-mm-dd hh24:mi:ss') as create_time
FROM SETT_SETTMENT_FR_RECORD  a 
  LEFT JOIN SETT_SETTMENT_FR_ACC_FLOW b ON a.ID =b.SETTMENT_FR_RECORD_ID 
  LEFT JOIN acc_accountflow aa ON b.TRANSACTION_NO=aa.TRANSACTIONNO OR a.account_voucher_no=aa.accountvouchcerno
;
select * from acc_accountflow f where f.transactionno='42202306274248463746219';
--前置平台入金
select * 
from chk_qra_all t inner join CHK_XX_MCHT_MAP m on t.mcht_no=m.mcht_no where t.transaction_time > sysdate - 10;
select * from CHK_XX_MCHT_MAP;
select * from acc_accountflow t where t.transactionno='95516000641065530412018293810524';

--线下入金（直连）
SELECT t.CUSTOMER_CODE AS 商户编号, t.transaction_no as 交易单号, 
  t.tran_amt/100 AS 交易金额,t.PROCEDURE_FEE/100 手续费金额,t.income_amt/100 as 记账金额,
  aa.AMOUNT/100 AS 记账金额2,aa.AFTERBALANCE/100 AS 账户余额, aa.accountcode,  
  t.tran_date as 交易时间
FROM CHK_XX_CUSTOMER_INCOME t 
  left JOIN ACC_ACCOUNTFLOW AA ON t.transaction_no=aa.transactionno
;

--线下入金（间连）
select t.merchant_no as 商户编号,t.reference_no as 参考号, t.acc_transaction_no as 交易单号,t.acc_business_code as 业务编码,
  t.amount as 交易金额, t.acc_procedure_fee/100 as 手续费,t.acc_income_amt / 100 as 记账金额,
  aa.AMOUNT/100 AS 记账金额2,aa.AFTERBALANCE/100 AS 账户余额, aa.accountcode, 
  to_char(t.creation_time,'yyyy-mm-dd hh24:mi:ss') as 订单时间
from zhy_posp_record t 
  inner join acc_accountflow aa on t.acc_transaction_no=aa.transactionno
;
  
--税筹（参考分账）

--鉴权费用&银行卡核验费用
select t.customer_code as 商户编号,t.transaction_no as 交易单号,t.business_code as 业务代码,
  t.procedure_fee/100 as 费用,t.chanl_procedure_fee/100 as 渠道成本,t.procedure_fee/100*-1 as 记账金额,
  aa.AMOUNT/100 AS 记账金额2,aa.AFTERBALANCE/100 AS 账户余额, aa.accountcode, 
  to_char(t.create_time,'yyyy-mm-dd hh24:mi:ss') as create_time
from txs_consistcheck_trade t 
  inner join acc_accountflow aa on t.transaction_no=aa.transactionno
where t.state='00' and t.procedure_fee > 0
order by t.create_time desc;

select * from acc_accountflow aa where aa.transactionno='12202307268670792957165';
select * from cust_customer c where c.customer_no='****************';
select * from txs_consistcheck_trade t where t.transaction_no='12202307268670792957165';

--账户支付

--退款，不包含分账退款,不包含拆单（含returnTo）退款
select t.customer_code as 商户编号,t.transaction_no as 退款单号,
  t.refund_fee /100 申请退款金额,t.backpay_procedurefee/100 退回支付手续费,t.back_split_procedure_fee/100 退回分账手续费,
  t.real_amount/100 实际退款金额,
  aa.AMOUNT/100 AS 记账金额2,aa.AFTERBALANCE/100 AS 账户余额, aa.accountcode, 
  to_char(t.create_time,'yyyy-mm-dd hh24:mi:ss') as create_time
from txs_refund_pre_order t 
  inner join acc_accountflow aa on t.transaction_no=aa.transactionno
where t.pay_state='00' and t.return_to_info_list is null and not exists(select 1 from txs_refund_split_record r where t.transaction_no=r.transaction_no)
  order by t.create_time desc;

select * from acc_accountflow t where t.transactionno='31202308157995277167121';
select * from txs_refund_split_record t where t.transaction_no='31202308157995277167121';
select * from txs_refund_pre_order t where t.transaction_no='31202308157995277167121';
select * from txs_pay_trade_order t where t.transaction_no='32202308150047973789543';
