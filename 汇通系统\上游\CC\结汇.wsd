@startuml 结汇
title 结汇
actor 商户 as M
actor 运营 as O
box 跨境系统 #LightBlue
participant 汇通全球 as HT
participant 易票联KJ  as KJ
participant 易云帐VA as VA
participant 易票联EP  as EP
end box
participant CurrencyCloud  as CC #Grey

==结汇==
M -> HT: 申请资金入账（fundsTransIn）
note right of HT: 类接口fundsTransIn，但订单状态为待确认
HT --> HT: 创建结汇单，结汇单结汇单状态：待确认
HT -> VA: 查询手续费（率）
VA --> HT: 返回手续费（率）
HT -> VA: 准备结汇，CNH币种账户减额
note right of HT: 申请结汇金额=CHN减额=结汇到账金额+汇通手续费+全球付款手续费
VA --> HT: 返回扣减结果

alt 额度扣减成功
    group CC资金提现到人跨户，异步
        HT -> CC: 发起转账(Transfer接口）
        note left of CC: RMB资金从子账户转入主账户，接口：/v2/transfers/create
        CC --> HT: 转账结果
        HT -> CC: 发起提现，目标户：人跨户
        note left of CC: CNH资金从主账户转出到易票联HK外币账户，接口：/v2/payments/create
        CC --> HT: 提现结果
        CC ->> HT: 提现结果异步通知
        alt 提现成功
            HT --> HT: 资金入账单状态——待审核
            O -> KJ: 资金到账审核
            KJ --> O: 审核结果，资金已到账，一般成功
            KJ -> EP: 平台商加额
            KJ --> KJ: 登记平台商加额流水
            KJ ->> HT: 异步通知，资金已到账
        else 提现失败
            KJ ->> HT: 资金未到账——失败，结束
            HT -> VA: CNH币种账户回滚
        end
    end 

    loop 直到申报额度大于或等于申请结汇金额
        M -> HT: 提交结汇申报材料
    end
    HT -> HT: 申请结汇（transOut）
    note right of HT: 当前自动发起，V0.3版本由前端页面发起
    O -> KJ: 审核结汇单
    KJ --> O: 审核结果
    alt 审核通过
        KJ --> KJ: 更新结汇单状态——审核通过
        KJ -> EP: 资金转账到子商户账户（全球付款交易，分账）
        HT ->> M: 通知结汇成功
    else 审核不通过
        KJ --> KJ: 更新结汇单状态——审核不通过
        HT ->> M: 审核不通过，结汇失败
        HT -> VA: CNH币种账户回滚（手工）
        ...手动回滚CC账户...
    end
else 额度扣减失败
    HT -> VA: CNH币种账户回滚
    HT --> HT: 更新结汇单状态——失败
    HT ->> M: 通知结汇失败
end

@enduml


