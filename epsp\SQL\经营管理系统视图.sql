﻿--创建经营信息view
create view chk_business_jingying_report_view  as 
select 
  a.ID as id,
  to_date(a.TASK_DATE ,'yyyy-mm-dd') as TASK_DATE  ,
  to_date(a.TASK_DATE_SHOW ,'yyyy-mm-dd') as TASK_DATE_SHOW ,
  a.BUSINESS_TYPE as BUSINESS_TYPE,
  a.CUSTOMER_CODE as CUSTOMER_CODE,
  a.CUSTOMER_NAME as CUSTOMER_NAME,
  a.BUDGET_GROUP_NAME as BUDGET_GROUP_NAME,
  a.AGENT_CUSTOMER_CODE as AGENT_CUSTOMER_CODE,
  a.AGENT_CUSTOMER_NAME as AGENT_CUSTOMER_NAME,
  a.PLATFORM_CUSTOMER_CODE as PLATFORM_CUSTOMER_CODE,
  a.PLATFORM_CUSTOMER_NAME as PLATFORM_CUSTOMER_NAME,
  a.BUSINESS as BUSINESS,
  a.BUSINESS_MAN as BUSINESS_MAN,
  a.COMPANY_NAME as COMPANY_NAME,
  a.PAY_METHOD as PAY_METHOD,
  a.CHANNEL_INST_CODE as CHANNEL_INST_CODE,
  a.CHANNEL_INST_NAME as CHANNEL_INST_NAME,
  a.BANK_NAME as BANK_NAME,
  a.EPL_TERM_NO as EPL_TERM_NO,
  a.ITEM_CODE as ITEM_CODE,
  TO_NUMBER(TRAN_CNT) as TRAN_CNT ,
  TO_NUMBER(TRAN_AMOUNT) as TRAN_AMOUNT ,
  TO_NUMBER(TRAN_MCHT_PROCEDURE_FEE) as TRAN_MCHT_PROCEDURE_FEE ,
  TO_NUMBER(TRAN_MCHT_PROCEDURE_FEE2) as TRAN_MCHT_PROCEDURE_FEE2 ,
  TO_NUMBER(TRAN_LOCAL_PROCEDURE_FEE) as TRAN_LOCAL_PROCEDURE_FEE,
  TO_NUMBER(TRAN_LOCAL_PROCEDURE_FEE2) as TRAN_LOCAL_PROCEDURE_FEE2 ,
  TO_NUMBER(TRAN_CHANNEL_PROCEDURE_FEE) as TRAN_CHANNEL_PROCEDURE_FEE ,
  TO_NUMBER(TRAN_CHANNEL_PROCEDURE_FEE2) as TRAN_CHANNEL_PROCEDURE_FEE2,
  TO_NUMBER(ARRIVE_AMOUNT) as ARRIVE_AMOUNT ,
  a.CREATE_TIME as CREATE_TIME,
  a.STATIC_PERIOD as STATIC_PERIOD,
  TO_NUMBER(FEE) as FEE ,
  TO_NUMBER(PURE_FEE) as PURE_FEE ,
  TO_NUMBER(INCOME) as INCOME  ,
  TO_NUMBER(PURE_INCOME) as PURE_INCOME,
  TO_NUMBER(COST) as COST ,
  TO_NUMBER(PURE_COST) as PURE_COST,
  TO_NUMBER(PROFIT) as PROFIT ,
  TO_NUMBER(PURE_PROFIT) as PURE_PROFIT,
  a.SEQ_NO as SEQ_NO,
  a.REMARK as REMARK,
  a.RECORD_SOURCE as RECORD_SOURCE,
  a.BATCH_NO as BATCH_NO,
  b.PROVINCE_CODE as PROVINCE_CODE,
  b.CITY_CODE as CITY_CODE,
  b.DISTRICT_CODE as DISTRICT_CODE
from chk_business_jingying_report  a
left join cust_customer b on a.CUSTOMER_CODE=b.CUSTOMER_NO 
order by a.task_date desc;

--创建商户信息view
create view customer_all_view as select c.CLIENT_NO as client_no,ccd.name as name, ccd.CUSTOMER_NO AS customer_no,
       to_date(TO_CHAR(ccd.CREATE_TIME ,'yyyy-mm-dd'),'yyyy-mm-dd') as create_time,
       (CASE ccd.category WHEN 0 THEN '商户'
                        WHEN 2 THEN '平台商户'
                        WHEN 3 THEN '服务商'
                        WHEN 4 THEN '个人'
                        WHEN 5 THEN '结算商户' END) as category,
       (CASE WHEN ccd."TYPE"=10 THEN '个体工商户'
             WHEN ccd."TYPE"=20 THEN '企业'
             WHEN ccd."TYPE"=30 THEN '境外商户'
             WHEN ccd."TYPE"=50 THEN '小微'
             WHEN ccd."TYPE"=60 THEN '个人客户'
             WHEN ccd."TYPE"=70 THEN '政府/事业单位'
             ELSE '其他组织' end) AS type,
       ccd.plat_customer_no as plat_customer_no,
       (select name from cust_customer c1 where c1.customer_no=ccd.plat_customer_no) as plat_customer_name,
       ccd.service_customer_no as service_customer_no,
       (select name from cust_customer c1 where c1.customer_no=ccd.service_customer_no) AS service_customer_name,
       (SELECT count(1) FROM CUST_BUSINESS cb WHERE cb.customer_id = ccd.customer_id AND state =1 ) as cust_nusiness_count,
       ccd.terminal_code as terminal_code,
       (select pc.name from pas_company pc where  pc.company_id=ccd.company_id) as company_name,
       u.REAL_NAME as real_name,
       (CASE WHEN ccd.source_channel = '1' THEN 'EPSP平台录入'
             WHEN ccd.source_channel = '2' THEN 'EPSP接口'
             WHEN ccd.source_channel = '3' THEN '云闪付开放平台'
             WHEN ccd.source_channel = '4' THEN '旧系统'
             WHEN ccd.source_channel = '5' THEN 'H5自助平台'
             WHEN ccd.source_channel = '6' THEN 'PC自助平台'
             WHEN ccd.source_channel = '7' THEN '终端自助平台'
             WHEN ccd.source_channel = '8' THEN '代理商门户'
             WHEN ccd.source_channel = '9' THEN '代理商APP'
             WHEN ccd.source_channel = '10' THEN '业务员进件'END ) as source_channel,
       (select name from CUST_DICT_BUSINESS_TYPE bt where bt.code=ccd.inner_business_type) as business_type,
       (CASE WHEN ccd.SIGN_STATUS = 1 THEN '已签约' ELSE '未签约'END)AS SIGN_STATUS,
       (CASE WHEN c.STATUS = 1 THEN '正常'
             WHEN c.STATUS = 2 THEN '冻结'
             WHEN c.STATUS = 3 THEN '注销'
             WHEN c.STATUS = 4 THEN '止付'
             WHEN c.STATUS = 5 THEN '禁止入金' END ) AS STATUS,
       (CASE ccd.AUDIT_STATUS
            WHEN '00' THEN '待初审'
            WHEN '01' THEN '初审未通过'
            WHEN '02' THEN '待复审'
            WHEN '03' THEN '复审未通过'
            WHEN '04' THEN '审核成功'
            WHEN '05' THEN '草稿'
            WHEN '06' THEN '待预审'
            WHEN '07' THEN '预审未通过' END)  as AUDIT_STATUS,
       CASE ra.TEMPORARY_STATUS WHEN '2' THEN '已限制' ELSE '正常' end as TEMPORARY_STATUS,
       CASE c.rc_status WHEN '0' THEN '正常' WHEN '1' THEN '冻结' end as rc_status,
       (SELECT param_value FROM CUST_STATIC_PARAM csp
        WHERE csp.PARAM_TYPE='NEW_BUDGET_GROUP' AND csp.PARAM_NAME=ccd.budget_group) AS budget_group,
       nvl((select (CASE WHEN ei.value='1' then '是' end) as aa
            from CUST_CUSTOMER_EXTEND_INFO ei WHERE ei.CUSTOMER_ID = ccd.customer_id
                                                AND ei.TYPE = 'recyclePaperAgreement'),'否') as recyclePaperAgreement,
       (SELECT param_value FROM CUST_STATIC_PARAM csp
        WHERE csp.PARAM_TYPE='BUSINESS_ROLE' AND csp.PARAM_NAME=ccd.BUSINESS_ROLE) AS BUSINESS_ROLE,
       CASE sr.STOP_CASH_IN WHEN '1' THEN '已限制' ELSE '正常' end as STOP_CASH_IN,
       to_date(TO_CHAR(c.register_time,'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') as register_time,
       to_date(TO_CHAR(c.update_time ,'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') as update_time,
       to_date(TO_CHAR((CASE WHEN c.rc_status =1 THEN c.frozen_time ELSE NULL END),'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') as frozen_time,
       (CASE WHEN c.rc_status =1 THEN ra.RC_STATUS_REASON ELSE NULL END) as RC_STATUS_REASON,
       to_date(TO_CHAR((CASE WHEN c.STATUS = 2 THEN r.STATUS_TIME ELSE NULL END),'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') as account_frozen_time,
       (CASE WHEN c.STATUS = 2 THEN ra.ACCOUNT_STATUS_REASON ELSE NULL END) as account_frozen_REASON,
       to_date(TO_CHAR((CASE WHEN c.STATUS = 3 THEN r.STATUS_TIME ELSE NULL END),'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') as account_cancel_time,
       (CASE cr.REASON
            WHEN '01' THEN '商户不使用'
            WHEN '02' THEN '商户无交易'
            WHEN '03' THEN '疑似风险商户'
            WHEN '04' THEN '商户资质注销或过期'
            WHEN '05' THEN '自主注销'
            WHEN '99' THEN '其他' END)  as account_cancel_REASON,
       to_date(TO_CHAR((CASE WHEN c.STATUS = 4 THEN r.STATUS_TIME ELSE NULL END),'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') as STOP_PAY_TIME,
       (CASE WHEN c.STATUS = 4 THEN ra.ACCOUNT_STATUS_REASON ELSE NULL END) as STOP_PAY_REASON,
       to_date(TO_CHAR((CASE WHEN c.STATUS = 5 THEN r.STATUS_TIME ELSE NULL END),'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') as STOP_CASH_IN_TIME,
       (CASE WHEN c.STATUS = 5 THEN ra.ACCOUNT_STATUS_REASON ELSE NULL END) as STOP_CASH_IN_REASON,
       (select ei.VALUE from CUST_CUSTOMER_EXTEND_INFO ei where ei.TYPE='remarks' and ei.CUSTOMER_ID=ccd.CUSTOMER_Id) as remarks
from CUST_CUSTOMER_DRAFT ccd
LEFT JOIN cust_customer c ON c.CUSTOMER_ID =ccd.CUSTOMER_ID
LEFT JOIN pas_user u ON ccd.BUSINESS_MAN_ID =u.USER_ID
LEFT JOIN CUST_CUSTOMER_TIME_RECORD r on r.CUSTOMER_ID=ccd.CUSTOMER_ID
LEFT JOIN CUST_CUSTOMER_SETTING_RECORD sr on sr.CUSTOMER_ID=ccd.CUSTOMER_ID
LEFT JOIN rc_archive ra ON ccd.customer_no = ra.archive_code and ra.archive_type='005'
LEFT JOIN CUST_CUSTOMER_CANCEL_RECORD cr on cr.CUSTOMER_NO=ccd.CUSTOMER_NO;
