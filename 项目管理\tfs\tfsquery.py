import requests
from requests.auth import HTT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def query_tfs_with_auth(tfs_url, project_name, iteration_path, username, password):
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }

    url = f"{tfs_url}/{project_name}/_apis/wit/wiql?api-version=6.0"
    query = f"SELECT [System.Id], [System.WorkItemType], [System.Title], [System.IterationPath] FROM WorkItems WHERE [System.IterationPath] = '{iteration_path}'"

    payload = {
        "query": query
    }
    
    response = requests.post(url, headers=headers, json=payload, auth=HTTPBasicAuth(username, password))
    response.raise_for_status()

    data = response.json()
    work_items = data["workItems"]

    projects = set()
    tasks = []

    for work_item in work_items:
        project = work_item["fields"]["System.TeamProject"]
        projects.add(project)

        task = {
            "id": work_item["id"],
            "type": work_item["fields"]["System.WorkItemType"],
            "title": work_item["fields"]["System.Title"],
            "iteration_path": work_item["fields"]["System.IterationPath"]
        }

        tasks.append(task)

    return list(projects), tasks

# Example usage
tfs_url = "http://172.16.2.49:8080/tfs"
project_name = "EPSP"
iteration_path = "迭代 083（24.06.25上线）"
username = "wanghaifeng"
password = "4gWdBn9oPI"

projects, tasks = query_tfs_with_auth(tfs_url, project_name, iteration_path, username, password)
print("Projects:", projects)
print("Tasks:")
for task in tasks:
    print(f"ID: {task['id']}, Type: {task['type']}, Title: {task['title']}, Iteration Path: {task['iteration_path']}")