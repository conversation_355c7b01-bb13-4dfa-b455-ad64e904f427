import base64
import OpenSSL.crypto
import json
import requests

def load_private_key(pfx_path, pfx_password):
    with open(pfx_path, 'rb') as f:
        pfx_data = f.read()
    pfx = OpenSSL.crypto.load_pkcs12(pfx_data, pfx_password)
    private_key = pfx.get_privatekey()
    return private_key

def sign_data(private_key, data):
    sign = OpenSSL.crypto.sign(private_key, data.encode('utf-8'), 'sha256')
    return base64.b64encode(sign).decode('utf-8')

def send_signed_request(url, headers, data, pfx_path, pfx_password):
    private_key = load_private_key(pfx_path, pfx_password)
    data_str = json.dumps(data, separators=(',', ':'))
    signature = sign_data(private_key, data_str)
    headers['x-efps-sign'] = signature

    response = requests.post(url, headers=headers, json=data)
    return response

if __name__ == '__main__':
    url = 'https://waika-test.epaylinks.cn/api/agent/cybersource'
    headers = {
        'Content-Type': 'application/json',
        'x-efps-sign-no': 'yilr20240826',
        'x-efps-sign-type': 'RSAwithSHA256',
        'x-efps-timestamp': '20190924160000'
    }
    data = {
        "api": "/risk/v1/decisions",
        "seqNo": "20190924160000000000000000000000000000000000000",
        "requestData": {
            "paymentInformation.card.number": "",
            "paymentInformation.card.bin": "",
            "paymentInformation.card.expirationMonth": "",
            "paymentInformation.card.expirationYear": "",
            "paymentInformation.card.type": "",
            "paymentInformation.instrumentIdentifier.id": "",
            "paymentInformation.paymentInstrument.id": "",
            "paymentInformation.customer.customerId": "",
            "orderInformation.amountDetails.totalAmount": "",
            "orderInformation.amountDetails.currency": "",
            "orderInformation.lineItems[].unitPrice": "",
            "orderInformation.lineItems[].quantity": "",
            "orderInformation.lineItems[].productCode": "",
            "orderInformation.lineItems[].productName": "",
            "orderInformation.lineItems[].productSKU": "",
            "orderInformation.billTo.firstName": "",
            "orderInformation.billTo.lastName": "",
            "orderInformation.billTo.address1": "",
            "orderInformation.billTo.address2": "",
            "orderInformation.billTo.locality": "",
            "orderInformation.billTo.administrativeArea": "",
            "orderInformation.billTo.postalCode": "",
            "orderInformation.billTo.country": "",
            "orderInformation.billTo.email": "",
            "orderInformation.billTo.phoneNumber": "",
            "buyerInformation.merchantCustomerId": "",
            "buyerInformation.dateOfBirth": "",
            "orderInformation.shipTo.firstName": "",
            "orderInformation.shipTo.lastName": "",
            "orderInformation.shipTo.address1": "",
            "orderInformation.shipTo.address2": "",
            "orderInformation.shipTo.locality": "",
            "orderInformation.shipTo.administrativeArea": "",
            "orderInformation.shipTo.postalCode": "",
            "orderInformation.shipTo.country": "",
            "orderInformation.shipTo.phoneNumber": "",
            "deviceInformation.ipAddress": "",
            "deviceInformation.fingerprintSessionId": "",
            "merchantDefinedInformation[].key": "",
            "merchantDefinedInformation[].value": ""
        },
        "nonceStr": "12345678901234567890123456789012"
    }
    pfx_path = 'f.pfx'
    pfx_password = '123456'

    private_key = load_private_key(pfx_path, pfx_password)
    data_str = json.dumps(data, separators=(',', ':'))
    signature = sign_data(private_key, data_str)
    print(signature)

    response = send_signed_request(url, headers, data, pfx_path, pfx_password)
    print(response.status_code)
    print(response.text)
