# SM2客户端使用说明

## 概述

`testclientsm2.py` 已经使用gmssl库重构，支持通过SM2算法访问服务端。该客户端支持以下功能：

- 标准SSL/TLS连接
- SM2证书验证
- SM2加密/解密通信
- 灵活的配置管理

## 依赖要求

### 必需依赖
- Python 3.6+
- gmssl库

### 安装gmssl
```bash
pip install gmssl
```

注意：gmssl库需要底层的GmSSL C库支持。在Windows环境下，可能需要额外的配置。

## 配置文件

### sm2_config.py
配置文件包含以下主要配置项：

#### 服务器配置
```python
SERVER_CONFIG = {
    'host': '************',    # 服务器地址
    'port': 9445,              # 服务器端口
    'timeout': 10.0            # 连接超时时间
}
```

#### 证书配置
```python
CERT_CONFIG = {
    'sm2_cert_path': 'C:/haproxy-3.2/certs/sm2.pem',  # SM2证书路径
    'cert_dir': 'C:/haproxy-3.2/certs',               # 证书目录
    'ca_cert_path': 'C:/haproxy-3.2/certs/ca.pem'     # CA证书路径
}
```

#### SM2密钥配置
```python
SM2_CONFIG = {
    'public_key': '...',              # SM2公钥（PEM格式）
    'private_key': '...',             # SM2私钥（PEM格式）
    'enable_sm2_encryption': True,    # 是否启用SM2加密
    'public_key_file': '...',         # 公钥文件路径
    'private_key_file': '...'         # 私钥文件路径
}
```

## 使用方法

### 基本使用
```bash
cd 应用/tcp
python testclientsm2.py
```

### 配置SM2密钥

1. **直接在配置文件中设置密钥**：
   编辑 `sm2_config.py`，在 `SM2_CONFIG` 中设置 `public_key` 和 `private_key`

2. **从文件加载密钥**：
   将密钥文件放在指定路径，程序会自动加载

3. **启用/禁用SM2加密**：
   设置 `SM2_CONFIG['enable_sm2_encryption']` 为 `True` 或 `False`

## 功能特性

### 1. 自动降级
- 如果gmssl库不可用，自动使用标准SSL连接
- 如果SM2密钥未配置，跳过SM2加密，使用标准通信

### 2. 证书验证
- 支持SM2证书加载和验证
- 可配置是否忽略证书错误

### 3. 加密通信
- 支持SM2加密/解密消息
- 自动处理加密失败的情况

### 4. 调试支持
- 详细的日志输出
- 可配置的调试级别
- 测试模式支持

## 配置示例

### 启用SM2加密的完整配置
```python
# 在sm2_config.py中
SM2_CONFIG = {
    'public_key': '''-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE...
-----END PUBLIC KEY-----''',
    
    'private_key': '''-----BEGIN PRIVATE KEY-----
MIGHAgEAMBMGByqGSM49AgEGCCqBHM9VAYIt...
-----END PRIVATE KEY-----''',
    
    'enable_sm2_encryption': True
}
```

### 仅使用标准SSL
```python
SM2_CONFIG = {
    'enable_sm2_encryption': False
}
```

## 故障排除

### 常见问题

1. **gmssl库导入失败**
   - 确保已正确安装gmssl库
   - 检查是否需要安装GmSSL C库

2. **证书加载失败**
   - 检查证书文件路径是否正确
   - 确保证书文件格式正确（PEM格式）

3. **SM2加密失败**
   - 检查密钥格式是否正确
   - 确保公钥和私钥匹配

4. **连接失败**
   - 检查服务器地址和端口
   - 确认服务器支持相应的加密套件

### 调试模式
设置 `DEBUG_CONFIG['verbose'] = True` 以获得详细的调试信息。

## 安全注意事项

1. **密钥管理**：
   - 生产环境中不要将私钥硬编码在配置文件中
   - 使用安全的密钥管理系统
   - 定期轮换密钥

2. **证书验证**：
   - 生产环境中应启用严格的证书验证
   - 不要忽略证书错误

3. **网络安全**：
   - 确保通信链路的安全性
   - 使用适当的网络安全措施

## 版本历史

- v1.0: 初始版本，支持标准SSL
- v2.0: 重构支持SM2，使用gmssl库
