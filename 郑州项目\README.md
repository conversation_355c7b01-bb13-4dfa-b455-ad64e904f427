# 环境
![alt text](image/企业微信截图_17509241199748.png)

# 编译说明
## 编译环境
- 操作系统：Windows 10
- JDK：OPEN-JDK-17
- Maven：3.8.1
- IDE：IntelliJ IDEA 2025
## 编译步骤
1. 将ant-contrib-1.0b3.jar文件拷贝到maven的缓存目录\ant-contrib\ant-contrib\1.0b3\ant-contrib-1.0b3.jar
2. 执行mvn clean package -Dmaven.test.skip=true
3. 编译成功

# 部署说明
## 部署数据库
创建数据库，执行以下脚本
```sql
CREATE DATABASE `hezhong-test` CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_general_ci';
```
创建用户
```sql
CREATE USER 'hezhong'@'%' IDENTIFIED BY 'vfLl7)D9cUNs';
GRANT ALL PRIVILEGES ON hezhong-test.* TO 'hezhong'@'%';
FLUSH PRIVILEGES;
```
从数据库备份文件中恢复数据
```bash
mysql -h ************* -P 3306 -u hezhong -pvfLl7)D9cUNs hezhong-test < hezhong-test.sql
```
## 部署配置