@startuml 业务流程

actor 服务商 as SP
actor 持卡人 as C
actor 商户 as M
actor 运营 as O
participant 外卡系统 as WK
participant 会员系统 as MS
participant 万事达 as MC

==服务商注册==
SP -> O: 申请成为服务商（线下）
O -> MS: 制作服务商数据
O --> SP: 返回服务商数据

==商户注册==
M -> WK: 注册商户
SP -> WK: 审核商户&配置商户信息
WK --> M: 返回商户信息

==交易流程==
C -> M: 提供卡片信息，申请支付
M -> WK: 请求支付
WK -> WK: 验证支付信息（风控）
WK -> MS: 请求支付
MS -> MC: 请求支付
MC --> WK: 返回支付结果
WK --> M: 返回支付结果
M --> C: 返回支付结果
@enduml