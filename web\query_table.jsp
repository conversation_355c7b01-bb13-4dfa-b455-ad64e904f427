<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.sql.*, java.util.*, com.alibaba.fastjson.JSON"%>
<%@ page import="java.sql.*,com.lot.common.util.SecUtil" %>
<%
String url = "513953ec53422e6d503172d5a1db9578c33e401fe074bfaa08e43ca438214c991ffedc5269c577fcc2c82799f5cb6d29";
String user = "b23fa9185371ccb29b27281e4037af3b";
String password = "d3f7d147dbf5fa132e345358d339c7e7";

    String tableName = request.getParameter("table_name");
    int pageSize = Integer.parseInt(request.getParameter("page_size"));
    int pageNum = Integer.parseInt(request.getParameter("page_num"));
    Map<String, List<String>> tableFields = new HashMap<>();
    // 假设tableFields已经被填充
    tableFields.put("country_risk_level", Arrays.asList("name_en","name_cn","name_en2","name_cn2","iso3","iso2","risk_level_cn","risk_level"));

    if (tableName == null || !tableFields.containsKey(tableName)) {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        out.print("{\"error\": \"Invalid table name\"}");
        return;
    }

    List<String> fields = tableFields.get(tableName);
    StringBuilder whereClause = new StringBuilder(" WHERE 1=1");
    List<String> paramValues = new ArrayList<>();
    for (String field : fields) {
        String paramValue = request.getParameter(field);
        if (paramValue != null) {
            whereClause.append(" AND ").append(field).append(" = ?");
            paramValues.add(paramValue);
        }
    }

    String countQuery = String.format("SELECT COUNT(*) FROM %s%s", tableName, whereClause.toString());
    String dataQuery = String.format(
        "SELECT * FROM (SELECT a.*, ROWNUM rnum FROM (SELECT * FROM %s%s) a WHERE ROWNUM <= ?) WHERE rnum > ?",
        tableName, whereClause.toString()
    );

    Connection conn = null;
    PreparedStatement countStmt = null;
    PreparedStatement dataStmt = null;
    ResultSet countRs = null;
    ResultSet dataRs = null;
    int totalRowCount = 0;
    List<Map<String, Object>> resultList = new ArrayList<>();

    try {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        conn = DriverManager.getConnection(SecUtil.decrypt(url), SecUtil.decrypt(user), SecUtil.decrypt(password));

        // 获取总行数
        countStmt = conn.prepareStatement(countQuery);
        for (int i = 0; i < paramValues.size(); i++) {
            countStmt.setString(i + 1, paramValues.get(i));
        }
        countRs = countStmt.executeQuery();
        if (countRs.next()) {
            totalRowCount = countRs.getInt(1);
        }

        // 获取数据
        dataStmt = conn.prepareStatement(dataQuery);
        for (int i = 0; i < paramValues.size(); i++) {
            dataStmt.setString(i + 1, paramValues.get(i));
        }
        dataStmt.setInt(paramValues.size() + 1, pageNum * pageSize);
        dataStmt.setInt(paramValues.size() + 2, (pageNum - 1) * pageSize);
        dataRs = dataStmt.executeQuery();

        ResultSetMetaData rsmd = dataRs.getMetaData();
        int columnCount = rsmd.getColumnCount();

        while (dataRs.next()) {
            Map<String, Object> row = new HashMap<>();
            for (int i = 1; i <= columnCount; i++) {
                row.put(rsmd.getColumnName(i).toLowerCase(), dataRs.getObject(i));
            }
            resultList.add(row);
        }
    } catch (Exception e) {
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.print("{\"error\": \"" + e.getMessage() + "\"}");
        return;
    } finally {
        if (countRs != null) try { countRs.close(); } catch (SQLException ignore) {}
        if (dataRs != null) try { dataRs.close(); } catch (SQLException ignore) {}
        if (countStmt != null) try { countStmt.close(); } catch (SQLException ignore) {}
        if (dataStmt != null) try { dataStmt.close(); } catch (SQLException ignore) {}
        if (conn != null) try { conn.close(); } catch (SQLException ignore) {}
    }

    Map<String, Object> result = new HashMap<>();
    result.put("total_row_count", totalRowCount);
    result.put("row_count", resultList.size());
    result.put("rows", resultList);

    String jsonResult = JSON.toJSONString(result);
    out.print(jsonResult);
%>
