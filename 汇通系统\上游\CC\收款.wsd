@startuml 收款
title 收款
actor 商户 as M
actor 付款人 as P
box 跨境系统 #LightBlue
participant 汇通全球 as HT
participant 易云帐VA as VA
participant 风控模块 as RC
end box
participant "CurrencyCloud/SKYEE" as CC #Grey

M -> HT: 登录
HT --> M: 进入汇通全球
M -> HT: 查询收款账户
HT -> CC: 查询收款账户
note right of CC: Find Funding Accounts接口\n参数account_id和currency
CC --> HT: 收款账户
HT --> M: 展现收款账户

M ---\ P: 提供收款账户(线下)
P -[#red]\ CC: 完成付款（线下银行转款等）
note right of P: 银行转账等方式，demo环境使用Emulate inbound funds接口模拟

==异步通知处理==
CC ->> HT: 付款异步通知，Pending状态
HT -> HT: 创建处理中订单
CC ->> HT: 付款异步通知，Failed状态
HT -> HT: 更新订单状态失败
CC ->> HT: 付款异步通知，Completed状态
HT -> CC: 查询付款人信息（Get Sender Details接口）
CC --> HT: 付款人信息
HT -> HT: 保存付款人信息
==名单筛查&交易风控==
HT -> RC: 请求黑名单（收款人、付款人）判定
RC --> HT: 返回结果
alt 是黑名单
    HT -> HT: 更新订单状态失败
    ...手动安排退款...
else 非黑名单
    HT -> RC: 请求名单筛查
    RC -[#red]\ HT: 异步通知筛查结果
    alt 名单筛查结果可疑
        HT -> HT: 更新订单状态失败
        ...手动安排退款...
    end
    group 调用风控限额接口
        HT -> RC: 调用交易风控接口（限额、限次）
        RC --> HT: 返回风控结果
        alt 风控未通过，标记风控结果
            HT -> HT: 更新订单风控状态：风控未通过\n并记录风控失败原因
        end 
    end group 
    HT -> HT: 更新订单状态：待提交资料
    M -> HT: 提交收款资料
    HT -> HT: 更新订单状态：待人工审核
    HT -> HT: 人工审核
    alt 审核通过
        HT -> VA: 调用充值接口
        VA --> HT: 返回充值结果+手续费
        note right of VA: 手续费=实际收款金额*商户的收款费率\n手续费内扣，通知收款金额=手续费+商户加额
        HT -> HT: 更新订单状态成功+手续费
        alt 上游是CurrencyCloud
            HT -[#red]> CC: 转存手续费，接口Transfer
            CC --[#red]> HT: 返回转存结果
        end 
    else 审核未通过
        HT -> HT: 更新订单状态：待提交资料
        ...继续提交资料...
    end

end
@enduml


