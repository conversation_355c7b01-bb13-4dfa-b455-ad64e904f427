<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{ book_name }}</title>
    <style>
        .body_style {
            padding: 0 5px; 
            font-size: 16px; 
            line-height: 26px;
            background-color: #a0a0a0;
        }
        .link-unclicked {
            color: #007BFF; /* 未点击的链接颜色 */
            font-size: 16px; 
        }
        .link-clicked {
            color: #FF5733; /* 点击后的链接颜色 */
            font-size: 20px; 
        }
        ul {
            padding: 0;
            margin: 0;
        }
        li {
            margin-left: 10px; /* 缩小左边距 */
        }
    </style>
    <script>
    function loadChapter(chapterId) {
        if (window.parent != null && window.parent.document.getElementById("right_content") != null) {
            window.parent.document.getElementById("right_content").src = "/chapter/" + chapterId;
        } else {
            window.location.href = "/chapter/" + chapterId;
        }
        document.cookie = "{{ book_name }}-reading=" + chapterId + "; expires=Fri, 31 Dec 9999 23:59:59 GMT; path=/";
    }

    function getReadingChapterId() {
        var name = "{{ book_name }}-reading=";
        var decodedCookie = decodeURIComponent(document.cookie);
        var ca = decodedCookie.split(';');
        for(var i = 0; i <ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') {
                c = c.substring(1);
            }
            if (c.indexOf(name) == 0) {
                return c.substring(name.length, c.length);
            }
        }
        return "";
    }
    
    function showSelectedChapter(chapterId) {
        var links = document.querySelectorAll("ul li a");
        links.forEach(function(link) {
            if(link.dataset.chapterId == chapterId){
                link.classList.remove("link-unclicked");
                link.classList.add("link-clicked");
            } else {
                link.classList.remove("link-clicked");
                link.classList.add("link-unclicked");
            }
        });
        // 滚动到对应链接的位置
        var selectedLink = document.getElementById("chapter-" + chapterId);
        if (selectedLink) {
            selectedLink.scrollIntoView({ behavior: "smooth", block: "center" });
        }
    }

    // 页面加载完成后自动模拟点击第一个 li 中的 a 标签
    window.onload = function() {
        // 获取阅读进度的 cookie 值
        var readingChapterId = getReadingChapterId();
        var ch = document.getElementById("chapter-" + readingChapterId);
        if (ch) {
            ch.click();
        } else {
            var ch = document.querySelector("ul li:first-child a");
            if (ch) {
                ch.click();
            }
        }
        // 给所有链接添加点击事件监听器
        var links = document.querySelectorAll("ul li a");
        links.forEach(function(link) {
            link.addEventListener("click", function(event) {
                event.preventDefault(); // 阻止默认行为
                // 移除所有链接的点击样式
                links.forEach(function(link) {
                    link.classList.remove("link-clicked");
                    link.classList.add("link-unclicked");
                });
                // 给当前点击的链接添加点击样式
                this.classList.remove("link-unclicked");
                this.classList.add("link-clicked");
                // 加载章节
                loadChapter(this.dataset.chapterId);
            });
        });
    };
    </script>
</head>
<body class="body_style">
    <ul>
    {% for ch in chapters %}
        <li><a id="chapter-{{ ch[0] }}" href="javascript:void(0);" data-chapter-id="{{ ch[0] }}" class="link-unclicked" onclick="loadChapter({{ ch[0] }})">{{ ch[1] }}  </a></li>
    {% endfor %}
    </ul>
</body>
</html>