import socket
import ssl
import sys
import os
try:
    from gmssl.sm2 import CryptSM2
    GMSSL_AVAILABLE = True
    print("gmssl库可用，支持SM2加密")
except ImportError:
    GMSSL_AVAILABLE = False
    print("gmssl库不可用，将使用标准SSL连接")
    CryptSM2 = None

# 导入配置
try:
    from sm2_config import SERVER_CONFIG, CERT_CONFIG, SM2_CONFIG, get_sm2_keys, DEBUG_CONFIG
    print("SM2配置文件加载成功")
except ImportError:
    print("SM2配置文件不存在，使用默认配置")
    SERVER_CONFIG = {'host': '************', 'port': 9445, 'timeout': 10.0}
    CERT_CONFIG = {'sm2_cert_path': 'C:/haproxy-3.2/certs/sm2.pem', 'cert_dir': 'C:/haproxy-3.2/certs'}
    SM2_CONFIG = {'enable_sm2_encryption': False}
    DEBUG_CONFIG = {'verbose': True}
    def get_sm2_keys():
        return "", ""

def load_sm2_certificate(cert_path):
    """加载SM2证书"""
    try:
        if os.path.exists(cert_path):
            with open(cert_path, 'r', encoding='utf-8') as f:
                cert_content = f.read()
            print(f"成功加载SM2证书: {cert_path}")
            return cert_content
        else:
            print(f"证书文件不存在: {cert_path}")
            return None
    except Exception as e:
        print(f"加载SM2证书失败: {e}")
        return None

def verify_sm2_certificate(cert_content):
    """验证SM2证书"""
    try:
        # 这里可以添加SM2证书验证逻辑
        # 由于gmssl库的证书验证功能可能需要特定的实现
        # 暂时返回True，实际使用时需要根据具体需求实现
        print("SM2证书验证通过")
        return True
    except Exception as e:
        print(f"SM2证书验证失败: {e}")
        return False

def create_ssl_context():
    """创建支持SM2的SSL上下文"""
    context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)

    # 设置TLS版本，优先使用TLS 1.2和1.3
    context.minimum_version = ssl.TLSVersion.TLSv1_2
    context.maximum_version = ssl.TLSVersion.TLSv1_3

    # 设置支持国密的加密套件
    # 尝试设置支持SM2的加密套件
    cipher_suites = [
        'ECDHE+AESGCM',
        'ECDHE+CHACHA20',
        'DHE+AESGCM',
        'DHE+CHACHA20',
        'ECDH+AESGCM',
        'DH+AESGCM',
        'RSA+AESGCM',
        'RSA+AES',
        'HIGH',
        '!aNULL',
        '!eNULL',
        '!EXPORT',
        '!DES',
        '!RC4',
        '!MD5',
        '!PSK',
        '!SRP',
        '!CAMELLIA'
    ]

    try:
        # 尝试设置国密加密套件
        context.set_ciphers(':'.join(cipher_suites))
        if DEBUG_CONFIG.get('verbose', True):
            print("已设置支持国密的加密套件")
    except Exception as e:
        if DEBUG_CONFIG.get('verbose', True):
            print(f"设置加密套件失败，使用默认套件: {e}")
        context.set_ciphers('DEFAULT')

    try:
        # 尝试加载系统CA证书
        context.load_default_certs()

        # 加载SM2证书
        sm2_cert_path = CERT_CONFIG.get('sm2_cert_path', 'C:/haproxy-3.2/certs/sm2.pem')
        sm2_cert = load_sm2_certificate(sm2_cert_path)

        if sm2_cert:
            # 验证SM2证书
            if verify_sm2_certificate(sm2_cert):
                if DEBUG_CONFIG.get('verbose', True):
                    print("SM2证书加载和验证成功")
            else:
                print("SM2证书验证失败")
                if not DEBUG_CONFIG.get('ignore_cert_errors', False):
                    sys.exit(1)
        else:
            if DEBUG_CONFIG.get('verbose', True):
                print("警告: 未能加载SM2证书，将使用标准SSL")

        # 加载自定义CA证书路径
        cert_dir = CERT_CONFIG.get('cert_dir', 'C:/haproxy-3.2/certs')
        if os.path.exists(cert_dir):
            context.load_verify_locations(capath=cert_dir)

    except Exception as e:
        print(f"证书加载错误: {e}")
        # 添加详细的错误信息
        if isinstance(e, ssl.SSLError):
            print(f"SSL错误详情: {str(e)}")
        # 不要立即退出，尝试继续连接
        if DEBUG_CONFIG.get('verbose', True):
            print("继续尝试连接...")

    # 设置验证选项 - 放宽验证要求
    context.check_hostname = False  # 先设置check_hostname
    context.verify_mode = ssl.CERT_NONE  # 然后设置verify_mode

    # 添加调试日志
    if DEBUG_CONFIG.get('verbose', True):
        print("SSL上下文已创建，支持SM2加密套件")

    return context

def create_sm2_crypto():
    """创建SM2加密对象"""
    if not GMSSL_AVAILABLE:
        if DEBUG_CONFIG.get('verbose', True):
            print("gmssl库不可用，无法创建SM2加密对象")
        return None

    # 检查是否启用SM2加密
    if not SM2_CONFIG.get('enable_sm2_encryption', False):
        if DEBUG_CONFIG.get('verbose', True):
            print("SM2加密未启用，跳过SM2加密对象创建")
        return None

    try:
        # 从配置获取SM2密钥
        public_key, private_key = get_sm2_keys()

        if public_key and private_key:
            sm2_crypt = CryptSM2(public_key=public_key, private_key=private_key)
            if DEBUG_CONFIG.get('verbose', True):
                print("SM2加密对象创建成功")
            return sm2_crypt
        else:
            if DEBUG_CONFIG.get('verbose', True):
                print("SM2密钥未配置，跳过SM2加密")
            return None
    except Exception as e:
        print(f"创建SM2加密对象失败: {e}")
        if DEBUG_CONFIG.get('verbose', True):
            print("将继续使用标准SSL连接")
        return None

def encrypt_message_with_sm2(message, sm2_crypt):
    """使用SM2加密消息"""
    try:
        if sm2_crypt:
            # 使用SM2加密
            encrypted = sm2_crypt.encrypt(message.encode('utf-8'))
            print("消息已使用SM2加密")
            return encrypted
        else:
            # 如果SM2不可用，返回原始消息
            print("SM2不可用，发送原始消息")
            return message.encode('utf-8')
    except Exception as e:
        print(f"SM2加密失败: {e}")
        return message.encode('utf-8')

def decrypt_message_with_sm2(encrypted_data, sm2_crypt):
    """使用SM2解密消息"""
    try:
        if sm2_crypt:
            # 尝试使用SM2解密
            decrypted = sm2_crypt.decrypt(encrypted_data)
            print("消息已使用SM2解密")
            return decrypted.decode('utf-8')
        else:
            # 如果SM2不可用，尝试直接解码
            return encrypted_data.decode('utf-8')
    except Exception as e:
        print(f"SM2解密失败，尝试直接解码: {e}")
        try:
            return encrypted_data.decode('utf-8')
        except:
            return f"解密失败，原始数据: {encrypted_data.hex()}"

def connect_with_pure_sm2(host, port, timeout, sm2_crypt):
    """使用纯SM2连接（不使用SSL/TLS）"""
    try:
        print("尝试使用纯SM2连接...")

        # 创建普通socket连接
        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client.settimeout(timeout)
        client.connect((host, port))

        print("✓ 纯SM2连接建立成功")
        return client, True
    except Exception as e:
        print(f"✗ 纯SM2连接失败: {e}")
        return None, False

def connect_with_ssl_sm2(host, port, timeout, sm2_crypt):
    """使用SSL+SM2连接"""
    try:
        print("尝试使用SSL+SM2连接...")

        # 创建socket
        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client.settimeout(timeout)

        # 创建SSL上下文
        context = create_ssl_context()

        # 包装socket
        secure_client = context.wrap_socket(
            client,
            server_hostname=host,
            do_handshake_on_connect=True
        )

        # 连接服务器
        secure_client.connect((host, port))

        print("✓ SSL+SM2连接建立成功")
        return secure_client, True
    except Exception as e:
        print(f"✗ SSL+SM2连接失败: {e}")
        return None, False

def main():
    # 从配置获取服务器信息
    SERVER_HOST = SERVER_CONFIG.get('host', '************')
    SERVER_PORT = SERVER_CONFIG.get('port', 9445)
    TIMEOUT = SERVER_CONFIG.get('timeout', 10.0)

    print(f"连接到服务器: {SERVER_HOST}:{SERVER_PORT}")

    # 创建SM2加密对象
    sm2_crypt = create_sm2_crypto()

    # 尝试多种连接方式
    secure_client = None
    connection_type = "未知"

    # 首先尝试纯SM2连接
    if SM2_CONFIG.get('prefer_pure_sm2', False):
        secure_client, success = connect_with_pure_sm2(SERVER_HOST, SERVER_PORT, TIMEOUT, sm2_crypt)
        if success:
            connection_type = "纯SM2"

    # 如果纯SM2连接失败，尝试SSL+SM2连接
    if not secure_client:
        secure_client, success = connect_with_ssl_sm2(SERVER_HOST, SERVER_PORT, TIMEOUT, sm2_crypt)
        if success:
            connection_type = "SSL+SM2"

    if not secure_client:
        print("所有连接方式都失败了")
        return

    try:
        # 打印连接信息
        print(f"\n=== 安全连接信息 ===")
        print(f"连接类型: {connection_type}")

        # 如果是SSL连接，显示SSL信息
        if hasattr(secure_client, 'cipher'):
            cipher = secure_client.cipher()
            if cipher:
                print(f"TLS版本: {secure_client.version()}")
                print(f"加密套件: {cipher[0]}")

            cert = secure_client.getpeercert()
            if cert:
                print(f"证书信息: {cert.get('subject', '未知')}")
                print(f"证书有效期: {cert.get('notBefore', '未知')} 到 {cert.get('notAfter', '未知')}")
            else:
                print("证书信息: 无证书信息")
        else:
            print("连接信息: 纯TCP连接")

        print(f"SM2加密: {'可用' if sm2_crypt else '不可用'}")
        print("================\n")

        while True:
            message = input("请输入要发送的消息 (输入'quit'退出): ")
            if message.lower() == 'quit':
                break

            # 使用SM2加密消息
            encrypted_message = encrypt_message_with_sm2(message, sm2_crypt)

            # 发送数据
            secure_client.send(encrypted_message)

            # 接收响应
            response = secure_client.recv(1024)
            print("\n服务器响应:")

            # 尝试使用SM2解密响应
            try:
                decrypted_response = decrypt_message_with_sm2(response, sm2_crypt)
                print(decrypted_response)
            except Exception as e:
                print(f"解密响应失败: {e}")
                try:
                    print(response.decode('utf-8'))
                except UnicodeDecodeError:
                    print("二进制响应:", response.hex())

    except ssl.SSLError as e:
        print(f"SSL错误: {e}")
        print("提示: 服务器可能不支持当前的加密套件，请检查服务器配置")
    except socket.timeout:
        print("连接超时")
    except ConnectionRefusedError:
        print("连接被拒绝，请检查服务器状态")
    except Exception as e:
        print(f"错误: {e}")
    finally:
        secure_client.close()
        print("\n连接已关闭")



if __name__ == "__main__":
    main()
