#--------------加/解密
java -cp jasypt-1.9.2.jar org.jasypt.intf.cli.JasyptPBEStringDecryptionCLI input=qmicECWudMFyQi8oi0EfCSH5WHiet2x5 password=efps-epaylinks algorithm=PBEWithMD5AndDES
java -cp jasypt-1.9.2.jar org.jasypt.intf.cli.JasyptPBEStringEncryptionCLI input=EFps#123 password=efps-epaylinks algorithm=PBEWithMD5AndDES

#------------------------------kafka命令------------------------------

#Kafka Topic
./kafka-topics.sh --bootstrap-server ***********:9092,***********:9093,***********:9094 --create --replication-factor 3  --partitions 24 --topic guan
./kafka-topics.sh --bootstrap-server ***********:9092,***********:9093,***********:9094 --alter  --partitions 3 --topic CLR_To_TXS_NfcTagPayNotify
./kafka-topics.sh --bootstrap-server ***********:9092,***********:9093,***********:9094 --delete --topic guan
./kafka-topics.sh --bootstrap-server ***********:9092,***********:9093,***********:9094 --list
./kafka-topics.sh --bootstrap-server ***********:9092,***********:9093,***********:9094 --describe
./kafka-topics.sh --bootstrap-server ***********:9092,***********:9093,***********:9094 --describe --topic PAY_PayResult

#Kafka consumer group
./kafka-consumer-groups.sh --bootstrap-server ***********:9092,***********:9093,***********:9094 --new-consumer --list
./kafka-consumer-groups.sh --bootstrap-server ***********:9092,***********:9093,***********:9094 --describe --group txs-group
./kafka-consumer-groups.sh --bootstrap-server ***********:9092,***********:9093,***********:9094 --describe --group txs-group | egrep "(CLR_PayGatewayResult|PAY_PayResult)"
./kafka-consumer-groups.sh --bootstrap-server ***********:9092,***********:9093,***********:9094 --execute --group txs-group --topic PAY_PayResult  --reset-offsets --to-datetime 2020-04-08T12:00:00

#Kafka producer
./kafka-console-producer.sh --broker-list ***********:9092 --topic PAY_PayResult
./kafka-console-producer.sh --broker-list ***********:9091 --topic PAY_PayResult --property "parse.key=true" --property "key.separator=$"

#Kafka consumer
./kafka-console-consumer.sh --bootstrap-server ***********:9092,***********:9093,***********:9094 --topic CLR_PayGatewayResult --from-beginning
./kafka-console-consumer.sh --bootstrap-server ***********:9092,***********:9093,***********:9094 --topic CLR_PayGatewayResult

#Test
./kafka-producer-perf-test.sh --topic w --num-records 1000000 --record-size 100 --throughput 1000  --producer-props bootstrap.servers=172.20.4.80:9091,172.20.4.80:9092

#Kafka producer
./kafka-console-producer.sh --broker-list ***********:9091,***********:9092,***********:9093 --topic CLR_PayGatewayResult --property "parse.key=true" --property "key.separator=^"

#支付结果通知
./kafka-console-producer.sh --broker-list ***********:9091,***********:9092,***********:9093 --topic PAY_PayResult --property "parse.key=true" --property "key.separator=^"

#------------------------------Redis--------------------------------
redis-cli -h *********** -p 6381 KEYS $i | xargs redis-cli -h *********** -p 6381  DEL

#------------------------------日志统计------------------------------
#监控网络
watch  -n 1 'netstat -na |grep -E "125.77.145.39|27.155.71.71|27.148.154.117|222.218.87.66|121.11.81.252|125.90.207.230|125.77.132.110|219.137.170.164|113.104.14.69|27.148.164.109|113.16.209.126" >>/tmp/netstat_111.log'
#统计用时超过5s的接口
grep  sendPostXmlMessage clr-monitorDetail.*2025-05-08*  |fgrep POST  |awk -F '|' '{print $9,$1,$7,$8}'|sort -rn|awk '$1>5000' 
cat *-monitorDetail.*2025-10-12*  |fgrep POST  |awk -F '|' '{print $9,$1,$7,$8}'|sort -rn|awk '$1>200' 
cat txs-monitorDetail.*2025-10-12*  |fgrep POST  |awk -F '|' '{print $9,$1,$7,$8}'|sort -rn|awk '$1>5000' 


#上游访问最大、平均、最小
cat clr-monitorDetail.2025-07-19* | grep HttpsUtils | grep sendPostXmlMessage | grep POST |awk -F "|" '{if(m < $9) m = $9;} END {print "max=" m }'
cat clr-monitorDetail.2025-05-07* | grep HttpsUtils | grep sendPostXmlMessage | grep POST |awk -F "|" '{sum += $9;} END {print "sum=" sum; print "average=" sum/NR }'
cat txs-monitorDetail.2025-10-12* | grep TransactionController | grep NativePayment | grep POST |awk -F "|" '{sum += $9;} END {print "sum=" sum; print "average=" sum/NR }'
cat clr-monitorDetail.2025-07-19* | grep HttpsUtils | grep sendPostXmlMessage | grep POST |awk -F "|" '{if($9 !="" && m > $9) m = $9;} END {print "min=" m }'


cat *-monitorDetail.2025-10-25* | grep certificateAuth | grep POST |awk -F "|" '{if(m < $9) m = $9;} END {print "max=" m }'
cat *-monitorDetail.2025-10-25* | grep certificateAuth | grep POST |awk -F "|" '{sum += $9;} END {print "sum=" sum; print "average=" sum/NR }'
cat *-monitorDetail.2025-10-25* | grep certificateAuth | grep POST |awk -F "|" '{if($9 !="" && m > $9) m = $9;} END {print "min=" m }'

#平均值
cat cust-monitorDetail.2023-08-11*.log | grep submitAudit | grep POST |awk -F "|" '{sum += $9;} END {print "sum=" sum; print "average=" sum/NR }'
#最大
cat cust-monitorDetail.2023-08-11*.log | grep submitAudit | grep POST |awk -F "|" '{if(m < $9) m = $9;} END {print "max=" m }'
#最小
cat cas-monitorDetail.2023-05-09*.log | grep checkBankCardRisk | grep POST |awk -F "|" '{if($9 !="" && m > $9) m = $9;} END {print "min=" m }'

#计算类CustomerController中各方法出现的次数
cat cum-monitorDetail.2025-05-28* | grep Controller| grep POST |awk -F "|" '{sum[$8]+=1}END{for(i in sum)print i"\t"sum[i]}'

cat cum-monitorDetail.2025-05-28* | grep gatewayPayQuery | grep POST |awk -F "|" '{sum[$8]+=1}END{for(i in sum)print i"\t"sum[i]}'


-------------------------------
#按天
cat clr-monitorDetail.2025-06-2*.log | grep POST | grep gtewayPayment | awk -F "|" '{if(m < $9) m = $9;} END {print "max=" m }'
#按小时
cat clr-monitorDetail.2025-06-22*.log | grep 05\: | grep POST | grep gtewayPayment | awk -F "|" '{if(m < $9) m = $9;} END {print "max=" m }'
#按天
cat clr-monitorDetail.2025-06-2*.log | grep POST | grep gtewayPayment | awk -F "|" '{sum += $9;} END {print "sum=" sum; print "average=" sum/NR }'
#按小时
cat clr-monitorDetail.2025-06-22*.log | grep 05\: | grep POST | grep gtewayPayment | awk -F "|" '{sum += $9;} END {print "sum=" sum; print "average=" sum/NR }'

#统计通知失败的日志
cat *monitorDetail.2025-07-19* | grep notifyFail | grep PRE | grep FAILED

#过滤掉注释行
grep -Ev "^$|^[#;]" storage.conf

# curl 压测
curl  -o /dev/null -s -w "time_connect: %{time_connect}\ntime_starttransfer: %{time_starttransfer}\ntime_total: %{time_total}\n" -H "Content-Type:application/json" -H "x-efps-sign-type:SHA256withRSA" -H "x-efps-sign:ope2M/fmgsiGE8Lob4/h7BB7MUJkYv3oPKLHRTP6fa0OJr4vEdEW4/1PTy9zTqV6f/RvVvG1q9yLWuyzNG8QmgXsx8IRqAYOB1SG3/gCOI5ggKmmq7uEmwSVT9O38PBM1Brwh6XlieomekNzqH3naBFYWKFI5DJ5UZFbPL9CqLcg3vyzd2In+OKlVAyP8bKqiBNK+KUTJDNTSubdr7aHqxorCNLAiSemRNBGAR8HJlq+Tlwb6ssqvvG9aP41KX2CtYDb7+azZrCCN5ej0ih4QhYJAbrVHoSvwH2J9xDUKeUuTl0jJfIJF5UUqbVdLqTBZUI14eS3cbV8BT0OMJ3nnQ==" -H "x-efps-sign-no:20250304yhys10" -H "x-efps-timestamp:20251024164101" -X POST --data '{"customerCode":"5651300003052001","nonceStr":"c049dad8b1a74f6989ec35fb140631e7","outTradeNo":"20251011101001809","transactionNo":""}' http://***********:8020/txs/pay/PaymentQuery

curl  -s -w "\ntime_connect: %{time_connect}\ntime_starttransfer: %{time_starttransfer}\ntime_total: %{time_total}\n" -H "Content-Type:application/json" -H "x-efps-sign-type:SHA256withRSA" -H "x-efps-sign:f6ZtuJYUH7a6YNEaONaR4xZT9PNZrafF1CKY4O7ag3v4g/bND3opbg2ZF0yDlmB1LUZrqQV3X73ZsnUPGQkWl+WgrHBfrL453EoLfzcUgD1EChmplU+O+JRLu9TURmyq9I5u/PdvMZJIzSWXFHWGkhZ1u1o4G/QqVIaW4/1a7iXwu7ZFot24LisThprffdSQExGipv8gfiyXrFqWPQ075P8WYd1WmcamOqyBJi3gmuVVqyrG5yg/wBhchCQCj902QNy0EhXtfHSAyagSLP0uG8k+DTHFS9McHwy609z46FcMj1jxJqBbnxgr/L28gfRr5dhG1q/I+wlci01XSR6s5A==" -H "x-efps-sign-no:20250815test" -H "x-efps-timestamp:20251024164101" -X POST --data '{"customerCode":"562737003279932","nonceStr":"dbb72044885b4d86b7be212d28e8687e","outTradeNo":"new20200103144626006"}' https://efps.epaylinks.cn/api/txs/pay/PaymentQuery

#maven install local file
mvn install:install-file -DgroupId=com.epaylinks.efps -DartifactId=ocx -Dversion=1.0.0 -Dpackaging=jar -Dfile=ocx-1.0.0.jar

grep RUN cust-monitorDetail.2023-08-15.0.log | grep -v 记录提审时间| grep -v customer_login| grep -v ERR | grep -v 商户| grep -v 网联| grep -v 结束| grep -v 审核| grep -v 图片| grep -v 方法| grep -v 订阅| grep -v 业务| grep -v 查询| grep -v 上游| grep -v 节点| grep -v 新
