# 环境

| 模块 | 服务&版本                         | 说明           |
| ---- | --------------------------------- | -------------- |
| nmd  | tomcat 8.5.100<br />jdk 1.8.0_144 | tomcat部署     |
| as   | tomcat 8.5.95                     | SpringBoot服务 |

## 开发环境

Gitlab: http://172.16.2.195/waltranx_dev/application-config.git  (waltranx_dev/Aps090807)
注册中心： http://172.16.2.190:8000/

服务器: 172.16.2.190
内存： 8G
Cpu: 6核
用户/密码:  waltranx/waltranx

商户门户：https://dev-merchant.waltranx.com/#/login
运营门户：https://dev-admin.waltranx.com/nmdAdmin/#/login

## 测试环境

Gitlab: http://172.16.2.195/waltranx_test/application-config.git  (waltranx_test/Aps090807)
注册中心： http://172.16.2.183:8000/

服务器: 172.16.2.183
内存： 8G
Cpu: 4核
用户/密码:  waltranx/waltranx

商户门户：https://sandbox-merchant.waltranx.com/#/login
运营门户：https://sandbox-admin.waltranx.com/nmdAdmin/#/login

支付令牌交易的：
<EMAIL>
付款交易的：
<EMAIL>

密码都是Abc123456@

## 生产环境

HT-busiapi：**********/32
info/INfo#2018

商户门户：https://merchant.waltranx.com/#/login
运营门户：https://admin.waltranx.com/nmdAdmin/#/login
API门户：https://api.waltranx.com/api/adminserver

![*************](image/README/*************.png)

《部署资源需求》

# 人跨专户

Company Name：EPAYLINKS CO., LIMITED
Company Address：UNIT A 11/F 368 GUANGZHOU AVENUE SOUTH HAIZHU DISTRICT GUANGZHOU CHINA
Bank Name：INDUSTRIAL AND COMMERCIAL BANK OF CHINA
Bank Address：5 CHANGSHOU ROAD HUIZHOU CHINA
SWIFT Code：ICBKCNBJHUZ
Account No.：2008020129200919047

# 功能导图

## 功能图

```mermaid
mindmap
  root((汇通系统))
    id1{{上游}}
      全球付
        VA收款
        VA付款
      CC
        VA收款
        VA付款
      寻汇
        支付令牌
    id2{{2、业务}}
      2.1、汇通接入模式
        **平台商模式**
        **代理商模式**
      2.2、商户接入
      2.3、VA收款
        **电商收款**<br>CC通道
        **服贸收款**<br>场景：航旅、海外广告<br>全球付
      2.4、VA付款
        **结算付款**<br>付款到国内同名户
        **B2B结汇**<br>根据贸易单材料付款<br>全球付
        **Swift付款到大陆公户**
      2.5、支付令牌
        **充值**<br>来自同名VA收款账户<br>全球付 - 寻汇
        **海外广告付款**<br>广告平台：亚马逊、其他
        **航旅付款**
    id3{{商户门户}}
      商户入驻
        注册
        实名认证
        业务申请
        业务费率
      VA账户
        余额
        收款交易
      付款
        收款人
        换汇
        付款交易
      支付令牌
        账户
        充值
        授权交易
    id4{{运营门户}}
```

## 说明

### 汇通接入模式

【平台商模式】
建立平台商资金池账户，所有资金都流向该账户，通过汇通本地账户完成商户资金记账，交易过程中，需要通过汇通系统计算手续费
【代理商模式】
在上游每个商户都有自己的账户，手续费由上游计算，交易过程中，上游计算手续费，最终通过上游的分润规则，将手续费分给下游的代理商 `<br>`可以根据业务需要，决定是否多代理商还是单一代理商，多代理商主要是考虑到上游的手续费计算

### 商户接入

# 技术架构重构

## 待处理事宜

```mermaid
mindmap
    root((架构重构))
        id0{{环境部署}}
            1、开发测试环境框架搭建
            2、基础数据表导入（参考外卡重构项目）
        id1{{运营门户}}
            1、登录接口重构（PAS登录接口）
                1.1、检查与as接口是否一致，不一致则修改前端
                1.2、密码算法不一样则直接使密码过期
                1.3、修改密码和图形验证码
                1.4、兼容UAA认证和AS认证
            2、系统管理
                2.1、检查接口兼容，不兼容则反馈和优化
                2.2、鉴权数据入UAA_SERVICE
            3、商户管理（鉴权数据）
            4、交易管理（鉴权数据）
            3、风控管理（鉴权数据）
            4、结算管理（鉴权数据）
            5、清理AS代码
                5.1、登录&系统管理代码
                5.2、认证&鉴权代码
        id2{{业务模块}}
            1、新建微服务busiapi
                1.1、业务模块代码迁移到busiapi
                1.2、业务模块数据迁移到UAA_SERVICE
            2、创建客户端访问的SDK
            3、梳理公共模块
                3.1、UM-DAO模块
                3.2、COMMON模块
        id3{{商户门户}}
            1、使用业务模块SDK
            2、重构登录以匹配UAA鉴权
        id4{{接口网关}}
            1、在AS中构建，复用代码
            2、复用UAA证书鉴权
        id5{{其他重构}}
            1、业务模块提供的SDK使用POJO对象
```

# 渠道参数

## 渠道异步通知地址

汇通测试环境异步通知接口映射调整

| 渠道   | 上游访问URL                                                                                                                                                         | 新系统URL                                                                                                                                                                   |
| ------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 全球付 | [https://merchant.waltranx.com/nmdWeb/globalcash/notify](https://merchant.waltranx.com/nmdWeb/globalcash/notify)                                                       | [https://api.waltranx.com/api/busiapi/globalcash/notify](https://api.waltranx.com/api/busiapi/globalcash/notify)                                                               |
| SKYEE  | [https://merchant.waltranx.com/nmdWeb/skyeeNotify/va](https://merchant.waltranx.com/nmdWeb/skyeeNotify/va)                                                             | [https://api.waltranx.com/api/busiapi/skyeeNotify/va](https://api.waltranx.com/api/busiapi/skyeeNotify/va)                                                                     |
| CC     | [https://merchant.waltranx.com/nmdWeb/currencycloud/notify](https://merchant.waltranx.com/nmdWeb/currencycloud/notify)                                                 | [https://api.waltranx.com/api/busiapi/currencycloud/notify](https://api.waltranx.com/api/busiapi/currencycloud/notify)                                                         |
| 寻汇1  | [https://api.waltranx.com/adminserver/sunrate/callBack/cardAuth/accId/30487333](https://api.waltranx.com/adminserver/sunrate/callBack/cardAuth/accId/30487333)         | [https://api.waltranx.com/api/adminserver/sunrate/callBack/cardAuth/accId/30487333](https://api.waltranx.com/api/adminserver/sunrate/callBack/cardAuth/accId/30487333)         |
| 寻汇2  | [https://api.waltranx.com/adminserver/sunrate/callBack/cardClearing/accId/30487333](https://api.waltranx.com/adminserver/sunrate/callBack/cardClearing/accId/30487333) | [https://api.waltranx.com/api/adminserver/sunrate/callBack/cardClearing/accId/30487333](https://api.waltranx.com/api/adminserver/sunrate/callBack/cardClearing/accId/30487333) |
| 寻汇3  | [https://api.waltranx.com/adminserver/sunrate/callBack/cardAuth/accId/69682887](https://api.waltranx.com/adminserver/sunrate/callBack/cardAuth/accId/69682887)         | [https://api.waltranx.com/api/adminserver/sunrate/callBack/cardAuth/accId/69682887](https://api.waltranx.com/api/adminserver/sunrate/callBack/cardAuth/accId/69682887)         |
| 寻汇4  | [https://api.waltranx.com/adminserver/sunrate/callBack/cardClearing/accId/69682887](https://api.waltranx.com/adminserver/sunrate/callBack/cardClearing/accId/69682887) | [https://api.waltranx.com/api/adminserver/sunrate/callBack/cardClearing/accId/69682887](https://api.waltranx.com/api/adminserver/sunrate/callBack/cardClearing/accId/69682887) |
