<mxfile host="65bd71144e">
    <diagram id="UMX4-9qXxnuDuLeX8y8q" name="第 1 页">
        <mxGraphModel dx="880" dy="1776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="8" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="670" y="160" width="270" height="190" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="EPSP DB&lt;div&gt;主库&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="690" y="215" width="60" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="200" y="-80" width="350" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="200" y="160" width="350" height="190" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="" style="shape=flexArrow;endArrow=classic;startArrow=classic;html=1;exitX=0.998;exitY=0.447;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="7" target="8" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="690" y="350" as="sourcePoint"/>
                        <mxPoint x="790" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="私有专线" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="9" vertex="1" connectable="0">
                    <mxGeometry x="0.4157" relative="1" as="geometry">
                        <mxPoint x="-33" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="加密机" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="850" y="225" width="70" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="东涌机房" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="360" y="350" width="70" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="IDC机房" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="775" y="360" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="上游：银联、网联" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="190" y="-110" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="" style="shape=flexArrow;endArrow=classic;startArrow=classic;html=1;" parent="1" source="6" target="7" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="639" y="255" as="sourcePoint"/>
                        <mxPoint x="800" y="259" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="17" value="上游专线" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="16" vertex="1" connectable="0">
                    <mxGeometry x="0.4157" relative="1" as="geometry">
                        <mxPoint y="-17" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="线上收单" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="220" y="-30" width="70" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="线下收单" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="300" y="-30" width="70" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="结算业务" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="380" y="-30" width="70" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="资金业务" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="460" y="-30" width="70" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="TXS" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="225" y="225" width="70" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="... 其他微服务" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="320" y="240" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="" style="endArrow=classic;html=1;" parent="1" target="7" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="80" y="260" as="sourcePoint"/>
                        <mxPoint x="550" y="190" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="26" value="uat.epaylinks.cn" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="25" vertex="1" connectable="0">
                    <mxGeometry x="-0.2111" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="27" value="EPSP DB&lt;div&gt;从库&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="770" y="215" width="60" height="80" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>