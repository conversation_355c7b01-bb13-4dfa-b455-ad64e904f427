﻿alter table tmp_xf_0810_ins_flow add acq_mchnt_cd varchar2(50);
alter table tmp_xf_0810_ins_flow add acq_mchnt_nm varchar2(200);
create index idx_xf_0810_ref on tmp_xf_0810_ins_flow(retri_ref_no);
create index idx_xf_0810_mon on tmp_xf_0810_ins_flow(settle_dt);

declare
  settle_month varchar2(6);
begin
  for x in (select * from tmp_xf_0810_ins_flow f where f.acq_mchnt_cd is null and f.settle_dt like settle_month || '%') loop
    for y in(select * from ZHY_POSP_RECORD p where p.reference_no=x.retri_ref_no and p.amount=x.trans_amt) loop
      update tmp_xf_0810_ins_flow t 
      set t.acq_mchnt_cd=y.fee_customer_code,t.acq_mchnt_nm=y.acc_customer_name
      where t.retri_ref_no=x.retri_ref_no and t.settle_dt=x.settle_dt and t.acq_mchnt_cd is null;
  using 
end;

select t.ref_no,t.* from CHK_XX_TRAN_RECORD t where t.ref_no='092311242172';
select t.reference_no,t.* from ZHY_POSP_RECORD t where t.reference_no='085957541131';
select t.retri_ref_no,t.trans_amt,t.settle_dt,t.* from tmp_xf_0810_ins_flow t where t.settle_dt like '2021%';
