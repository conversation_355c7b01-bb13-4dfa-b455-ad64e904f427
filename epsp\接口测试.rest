###----------PPS接口----------
###商户门户密码控件登录使用API代替JSP——PPS接口测试
get https://test-efps.epaylinks.cn/api/pps/ocx/securityPlug
###
get https://test-efps.epaylinks.cn/api/pps/ocx/srandNum

###商户门户密码控件登录使用API代替JSP——JSP接口测试
get https://test-efps.epaylinks.cn/ocx/jsp/securityPlug.jsp
###
get https://test-efps.epaylinks.cn/ocx/jsp/srand_num.jsp

###----------PAS接口测试----------
###查询JOB分区
get http://172.16.2.92:8030/queryJobsPartition
###
post http://172.16.2.92:8030/timeTask/queryAllJobs

###重发ACS充值通知，id：PAS_ACCT_QUOTA_RECORD表ID字段
post http://172.16.2.92:8030/AcctQuota/notifyCustomer?id=3141005

###----------CLR接口测试----------
#完成订单
post http://172.16.2.92:8100/ArtificialModification?transactionNo=32202410255169517184457&state=00

###----------REPORT接口测试----------
###重跑商户画像数据
get http://172.16.2.92:8310/createMatterMiddleStatistics?startDateStr=20241111&endDateStr=20241113

###重跑金蝶财务数据
POST http://172.16.2.92:8310/insertFinAccStatistics?vcDateBegin=20240601&vcDateEnd=20240630

###----------厦分支付接口测试----------
###生产环境
post https://epsp.epaylinks.cn/api/txsb/xfPay/NativePayment
###UAT环境
post https://test-efps.epaylinks.cn/api/txsb/xfPay/NativePayment

###----------CHK接口测试----------
###补充银联卡Hash值
http://172.16.2.92:8197/cardHash/getWithdrawCardHash?withdrawDate=20241231

###
post https://efps.epaylinks.cn/api/txs/pay/NativePayment

###
post https://test-epsp.epaylinks.cn/api/txs/qrCodeSubmitPay?amount=10&remark=&qc=202506230001&sc=202506230001&token=c288dbb303d3441d87c70d691ce58507&latitude=&longitude=&paymethod=89
