# -*- coding: utf-8 -*-

import subprocess
import re
import json
import pandas as pd
import logging

# 配置日志输出
logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',  # 只包含日志消息本身
    handlers=[
        logging.StreamHandler()  # 控制台处理器
    ]
)
logger = logging.getLogger(__name__)

##SVN stat
def get_svn_log(svn_dir, start_rev, end_rev):
    cmd = f'svn log {svn_dir} -r {start_rev}:{end_rev} --limit 10000 --verbose'
    logger.info(cmd)
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True, text=True, encoding='gbk')
    if result.returncode != 0:
        raise Exception(f'Error executing SVN log command: {result.stderr}')
    return result.stdout

def parse_svn_log(log_output):
    commits = []
    commit_pattern = re.compile(r'^r(\d+) \| (\w+) \| (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \+0800 \((\w+), (\d{2}) (\w+) (\d{4})\) \| (\d+) line$')
    for line in log_output.splitlines():
        match = commit_pattern.match(line)
        if match:
            commits.append({
                'revision': match.group(1),
                'author': match.group(2),
                'date': match.group(3),
                'lines': int(match.group(8))
            })
    return commits

def get_svn_changed_files(svn_dir, commit_revision):
    cmd = f'svn diff {svn_dir} -c {commit_revision} --summarize'
    logger.info(cmd)
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True, text=True, encoding='utf-8')
    if result.returncode != 0:
        raise Exception(f'Error executing SVN diff command: {result.stderr}')
    return result.stdout

def parse_svn_changed_files(diff_output):
    files = []
    if not diff_output:
        return files
    file_pattern = re.compile(r'^[AM]\s+(\S+\.\S+)$')
    for line in diff_output.splitlines():
        match = file_pattern.match(line)
        if match:
            files.append(match.group(1))
    return files

def get_svn_blame_info(file_path, commit_revision):
    cmd = f'svn blame -r {commit_revision} {file_path}'
    logger.info(cmd)
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True, text=True, encoding='utf-8')
    if result.returncode != 0:
        logger.warning(f'>>> Error executing SVN blame command: {result.stderr}')
        return []
    if not result.stdout:
        return []
    lines = result.stdout.encode('utf-8', 'ignore').decode('utf-8')
    if not lines:
        return []
    lines = lines.splitlines()
    lines = [line for line in lines if f'{commit_revision} ' in line]
    return lines

def cal_svn_changes(proj, svn_dir, start_rev, end_rev):
    cmd = f"svn update {svn_dir}"
    logger.info(cmd)
    subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True, text=True, encoding='utf-8')
    log_output = get_svn_log(svn_dir, start_rev, end_rev)
    commits = parse_svn_log(log_output)
    
    rev_list = []
    for commit in commits:
        author = commit['author']
        files = get_svn_changed_files(svn_dir, commit['revision'])
        files = parse_svn_changed_files(files)
        commit_lines = 0
        for file in files:
            lines = get_svn_blame_info(file, commit['revision'])
            commit_lines += len(lines)
            
        rev = {}
        rev['project'] = proj
        rev['revision'] = commit['revision']
        rev['date'] = commit['date']
        rev['author'] = author
        rev['lines'] = commit_lines
        rev['files'] = len(files)
        rev_list.append(rev)
        
    return rev_list

##GIT stat

def get_git_log(repo_path, start_date, end_date):
    cmd = f'git -C {repo_path} log --pretty="format:%H %ad", --date="iso" --since="{start_date}" --until="{end_date}" --numstat'
    logger.info(cmd)
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True, text=True, encoding='utf-8')
    if result.returncode != 0:
        logger.error(f'Error executing Git log command: {result.stderr}')
    return result.stdout

def parse_git_log(git_log_output):
    # 正则表达式匹配 commit hash 和日期
    commit_pattern = re.compile(r'(?P<hash>[a-f0-9]+) (?P<date>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} [+-]\d{4})')
    file_pattern = re.compile(r'(?P<added>\d+|-)\s+(?P<deleted>\d+|-)\s+(?P<file>.+)')
    
    commits = []
    current_commit = None
    for line in git_log_output.splitlines():
        commit_match = commit_pattern.match(line)
        if commit_match:
            if current_commit:
                commits.append(current_commit)
            current_commit = {
                'hash': commit_match.group('hash'),
                'date': commit_match.group('date'),
                'files': [],
                'total_files': 0,
                'total_added': 0,
                'total_deleted': 0
            }
        else:
            file_match = file_pattern.match(line)
            if file_match and current_commit:
                added, deleted, file_path = file_match.groups()
                added = int(added) if added != '-' else 0
                deleted = int(deleted) if deleted != '-' else 0
                current_commit['files'].append({
                    'file': file_path,
                    'added': added,
                    'deleted': deleted
                })
                current_commit['total_files'] += 1
                current_commit['total_added'] += added
                current_commit['total_deleted'] += deleted
    
    if current_commit:
        commits.append(current_commit)
    
    return commits

def get_author_by_revision(repo_path, revision):
    cmd = f'git -C {repo_path} show -s --format="%an" {revision}'
    logger.info(cmd)
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True, text=True, encoding='utf-8')
    if result.returncode != 0:
        raise Exception(f'Error executing Git show command: {result.stderr}')
    return result.stdout.strip()

def cal_git_changes(proj, repo_path, start_date, end_date):
    log_output = get_git_log(repo_path, start_date, end_date)
    commits = parse_git_log(log_output)
    
    rev_list = []
    for commit in commits:
        revision = commit['hash']
        author = get_author_by_revision(repo_path, revision)
        commit_lines = 0
        for file in commit['files']:
            commit_lines = file['added'] + file['deleted']
        rev = {}
        rev['project'] = proj
        rev['revision'] = revision
        rev['date'] = commit['date'][0:19]
        rev['author'] = author
        rev['lines'] = commit_lines
        rev['files'] = len(commit['files'])
        rev_list.append(rev)
        
    return rev_list

def get_work_days(date_in_month):
    import datetime
    import calendar
    date_obj = datetime.datetime.strptime(date_in_month, '%Y-%m-%d')
    _, days = calendar.monthrange(date_obj.year, date_obj.month)
    work_days = 0
    for day in range(1, days + 1):
        current_date = datetime.date(date_obj.year, date_obj.month, day)
        if current_date.weekday() < 5:  # 0-4 表示周一到周五
            work_days += 1
    return work_days

########################################################################################
author_alias = {
    'king': '王海峰',
    'WangHaiFeng': '王海峰',
    'wanghaifeng': '王海峰',
    'WangWenWei': '王文威',
    'wangwenwei': '王文威',
    'ChenZhuoLin': '陈卓林',
    'chenzhulin': '陈卓林',
    'ZhangGuoLiang': '张国良',
    'zhangguoliang': '张国良',
    'zhanggl': '张国良',
    'LiChao': '李超',
    'lichao': '李超',
    'LiuMin': '刘敏',
    'liumin': '刘敏',
    'ChenHaiYang': '陈海阳',
    'chenhaiyang': '陈海阳',
    'guojinghe': '郭镜和',
    'liangxiongfu': '梁雄福',
    'zhangrenquan': '张任全'
}
def stat_code(start_date = None, end_date = None, iteration_path = None, output_excel_writer=None):
    from 项目统计 import get_iteration_obj,BASE_DIR,PROJECT_REPOS
    iteration_obj = get_iteration_obj(iteration_path)
    if iteration_obj:
        start_date = iteration_obj['start_date']
        end_date = iteration_obj['end_date']
    logger.info(f"\n---------------代码行统计{start_date} ~ {end_date}---------------")
    logger.info(">>命令日志")
    rev_list = []
    for repo in PROJECT_REPOS:
        if repo['type'] == 'SVN':
            rev_list.extend(cal_svn_changes(repo['name'], repo['path'], f"{{{start_date}}}", f"{{{end_date}}}"))
        elif repo['type'] == 'GIT':
            rev_list.extend(cal_git_changes(repo['name'], repo['path'], f"{start_date}", f"{end_date}"))
    rev_list = [
        {**rev, 'author': author_alias.get(rev['author'], rev['author'])} if rev['author'] in author_alias else rev
        for rev in rev_list
        if rev['lines'] > 0 and rev['author'] 
    ]
    
    logger.info(f">>版本信息")
    
    if iteration_obj:
        excel_file_path = iteration_obj['excel_file_path']
    else:
        excel_file_path = f"{start_date}_{end_date}"
    excel_file_path = f'{BASE_DIR}/{excel_file_path}_代码统计.xlsx'
    excel_writer = pd.ExcelWriter(excel_file_path, engine='xlsxwriter') if output_excel_writer is None else output_excel_writer
    #
    df = pd.DataFrame(rev_list)
    logger.info(f">>统计结果")
    grouped_df = df.groupby(['author']).agg({'revision': 'count', 'files': 'sum', 'lines':'sum'}).reset_index()
    grouped_df['产出效率(行/天)'] = round(grouped_df['lines'] / get_work_days(end_date), 0)
    grouped_df.rename(columns={'revision':'提交版本','files':'文件数','lines':'代码行数','author':'提交人'},inplace=True)
    grouped_df.to_excel(excel_writer, sheet_name='代码开发人员统计', index=False)
    #
    grouped_df = df.groupby(['project']).agg({'revision': 'count', 'files': 'sum', 'lines':'sum'}).reset_index()
    grouped_df.rename(columns={'revision':'提交版本','files':'文件数','lines':'代码行数','project':'项目'},inplace=True)
    grouped_df.to_excel(excel_writer, sheet_name='代码项目统计', index=False)
    #
    df.rename(columns={'revision':'提交版本','date':'提交时间','files':'文件数','lines':'代码行数','author':'提交人','project':'项目'},inplace=True)
    df[['提交版本','提交时间','提交人','项目','文件数','代码行数']].to_excel(excel_writer, sheet_name='代码提交详细', index=False)
    #
    if output_excel_writer is None:
        excel_writer.close()
    logger.info(f"=========================================================")

if __name__ == '__main__':
    iteration_path = input("请输入迭代路径：")
    # stat_code('2024-11-27', '2024-12-25')
    stat_code(iteration_path=iteration_path)
    # print(get_work_days('2025-02-01'))
    # cal_svn_changes('D:/EPSP/JavaSource', '{2024-12-10}', 'HEAD')
    # print(cal_git_changes('TOOL', 'D:\GitHub\pytools', '2025-01-05', '2025-01-15'))
