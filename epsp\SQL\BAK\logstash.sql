﻿---epsp转自动月分区表
alter table CHK_OPERATION_GROUP_STATICS set interval(numtoyminterval(1,'month'));
alter table CHK_UNIONPAY_CHANNEL_RECORD set interval(numtoyminterval(1,'month'));
alter table CLR_REFUND_RECORD set interval(numtoyminterval(1,'month'));

alter table PAY_REFUND_ORDER_INFO set interval(numtoyminterval(1,'month'));
alter table RC_TXS_ORDER set interval(numtoyminterval(1,'month'));
alter table TXS_REPAY_TRADE_ORDER set interval(numtoyminterval(1,'month'));

insert into CHK_OPERATION_GROUP_STATICS(id,Create_Time) values(-1, to_date('20230101','yyyymmdd'));
insert into CHK_UNIONPAY_CHANNEL_RECORD(id,Create_Time) values(-1, to_date('20230101','yyyymmdd'));
insert into CLR_REFUND_RECORD(id,Create_Time) values(-1, to_date('20230101','yyyymmdd'));
insert into PAY_REFUND_ORDER_INFO(id,From_System_Id,Transaction_No,Pay_Amount,Transaction_Type,Create_Time) values(-1,'01','01',0,'ZF',to_date('20230101','yyyymmdd'));
insert into RC_TXS_ORDER(Out_Trade_No,Transaction_No,Business_Type,Indexs,Business_Target_Ids,Create_Time) values('0000','0000','0000','0000','0000',to_date('20230101','yyyymmdd'));
insert into TXS_REPAY_TRADE_ORDER(Id,Transaction_No,Out_Trade_No,Customer_Code,Pay_Method,Amount,Currency_Type,State,Create_Time) values(-3, '0000','0000','0000','0000',0,'CNY', '01',to_date('20230101','yyyymmdd'));

delete from CHK_OPERATION_GROUP_STATICS where id=-1;
delete from CHK_UNIONPAY_CHANNEL_RECORD where id=-1;
delete from CLR_REFUND_RECORD where id=-1;
delete from PAY_REFUND_ORDER_INFO where id=-1;
delete from RC_TXS_ORDER where Transaction_No='0000';
delete from TXS_REPAY_TRADE_ORDER where id=-3;
