# RAG介绍

## 基本介绍
### 脑图
```mermaid
mindmap
  root((RAG))
    概念
      "检索增强生成"
      结合检索+生成两阶段
      核心组件
        检索器
        生成器
        外部知识库
    用途
      1、提升生成准确性
      2、解决幻觉问题
      3、动态知识更新
      4、典型场景
        QA系统
        客服机器人
        研究报告生成
        法律咨询
    现状
      1、技术融合
        LLM+向量数据库
        提示工程优化
      2、行业应用
        医疗/金融/教育
      3、挑战
        检索效率
        知识覆盖度
        多跳推理
      4、前沿方向
        自优化RAG
        多模态RAG
    搭建步骤
      1. 知识库构建
        数据清洗
        向量化Embedding
        存储/FAISS/Pinecone
      2. 检索模块
        相似度算法
        多路召回
        结果排序
      3. 生成模块
        LLM选择(GPT/Claude等)
        提示模板设计
        结果校验
      4. 服务部署
        API封装
        缓存机制
        监控系统
```

### 介绍
RAG（Retrieval-Augmented Generation）是一种基于检索增强生成的技术，它结合了检索和生成两个阶段，以提高生成的准确性和效率。

### 核心组件
- 检索器（Retriever）：负责从外部知识库中检索相关信息。
- 生成器（Generator）：根据检索到的信息生成最终的输出。
- 外部知识库（Knowledge Base）：存储了丰富的文本信息，如文档、文章、新闻等。

### 用途
- 提升生成准确性：通过检索相关信息，生成器可以获得更准确的上下文，从而生成更准确的输出。
- 解决幻觉问题：由于生成器的输入来自检索器，生成器可以避免幻觉问题的发生。
- 动态知识更新：外部知识库可以随着时间的推移不断更新，生成器可以实时获取最新的知识。
- 典型场景：
  - QA系统：用户提问时，系统通过检索相关信息，生成器生成答案。
  - 客服机器人：用户提问时，系统通过检索相关信息，生成器生成回复。
  - 研究报告生成：系统通过检索相关信息，生成器生成研究报告。
  - 法律咨询：用户提问时，系统通过检索相关信息，生成器生成法律咨询。
  - 

### 现状
- 技术融合
- 行业应用
- 挑战
- 前沿方向
- 搭建步骤

## 核心优势

```mermaid
flowchart LR
    A[用户提问] --> B[向量检索]
    B --> C[相关文档]
    C --> D[LLM生成]
    D --> E[引用来源的答案]
```

## 推荐框架
```mermaid
flowchart TB
    Client-->Gateway
    Gateway-->Retriever
    Retriever-->VectorDB
    Retriever-->Cache
    VectorDB-->Generator
    Generator-->Client
```