select * from sys_pay_config spc ;
select * from sys_oss_config soc ;
select * from sys_config;

select * from channel_whitelist cw ;
select * from channel_withdraw_config cwc ;

select * from agent_info ai ; 

select * from mer_info mi  order by id desc; 
select * from mer_channel_income mci  where mer_id=100324;
select user_id,count(0) from mer_info group by user_id having count(0) > 1;

select * from mer_info_com mic ;
select t.business_licence,t.* from mer_info_com t;
update mer_info_com set business_licence='深圳市全智选供应链有限公司' where id=100302;

select * from mer_info_com_user micu where mer_id=100291;

select * from mer_collect_record mcr  order by id desc;
select * from mer_info a inner join mer_info_com b on a.id =b.mer_id where a.full_name like '%明司贞婉%';

select * from mer_pay_config mpc ;

select * from mer_card_info mci ;
select *from mer_account ma order by id desc;
select * from mer_account_change_fee_info macfi ;
select * from mer_account_change_log macl ;

select * from mer_auth_config mac ;
select * from mer_channel_amt_ex_log mcael ;
select * from mer_channel_amt_log mcal  where mer_id=100069 order by create_time ;
select * from mer_qrcode_info mqi ;
select * from mer_login_protocol_agree mlpa where mer_code='mer-100291';
select * from sys_config;

select * from mer_card_info;
select *from mer_account ma ;
select * from mer_info_img order by id desc;
select * from mer_info_com order by id desc;
select * from mer_info_com_user micu  order by id desc;
select * from mer_channel_config mcc  order by id desc;
select * from mer_account ma order by id desc;

select * from sys_user where user_name in('admin','alibaba','admin2') or user_id=512;
select * from sys_user_role where user_id in(1,4250,455);
select * from sys_role where role_key in('business','busineess_my') or role_name like '%业务员%';
select * from sys_user_auth sua ;

create or replace view v_base_salesman_info as 
select distinct u.user_id as id,u.nick_name as salesman_name,d.dept_name,u.phonenumber as contacts_phone,u.email,'1' as notify_type,u.remark,u.create_time,u.create_by,u.update_time,u.update_by,u.del_flag 
from sys_user u 
	left join sys_dept d on u.dept_id=d.dept_id 
	inner join sys_user_role ur on u.user_id =ur.user_id 
	inner join sys_role r on ur.role_id =r.role_id 
where u.status=0 and r.role_key in('biz_admin','salesman');

select * from v_base_salesman_info bsi ;

select * from mer_info_img mii ;
