﻿--Function GET_KV
CREATE OR REPLACE TYPE KV_OBJECT_TYPE AS OBJECT (
   K varchar2(100),
   V varchar2(1000)
);

CREATE OR REPLACE TYPE KV_type IS TABLE OF KV_OBJECT_TYPE;

CREATE OR REPLACE FUNCTION GET_KV(str VARCHAR2)
RETURN KV_type
IS
   kv KV_OBJECT_TYPE;
   tab_kv KV_type:=KV_type();
   i_tmp number;
BEGIN
  for val in (select regexp_substr(str,'[^,]+', 1, level, 'i') as v from dual connect by level <= length(str)-length(regexp_replace(str, ',', ''))+1) loop
    i_tmp := instr(val.v, '=');
    if i_tmp > 0 then
      kv := KV_OBJECT_TYPE(substr(val.v,1,i_tmp-1), substr(val.v, i_tmp+1, length(val.v)));
    else
      kv := KV_OBJECT_TYPE(val.v,null);
    end if;
    tab_kv.extend;
    tab_kv(tab_kv.count) := kv;
  end loop;
  RETURN tab_kv;
END GET_KV;

--Function stat
CREATE OR REPLACE TYPE CUSTOMER_BALANCE_STAT_TYPE AS OBJECT (
   customer_code varchar2(100),
   name varchar2(1000),
   init_balance number,
   in_amount number,
   in_rows number,
   out_amount number,
   out_rows number,
   first_time timestamp,
   last_time timestamp,
   cal_balance number,
   real_balance number,
   check_result number
);
--drop type CBS_TYPE;
CREATE OR REPLACE TYPE CBS_TYPE IS TABLE OF CUSTOMER_BALANCE_STAT_TYPE;

CREATE OR REPLACE FUNCTION CUSTOMER_BALANCE_STAT(customer_codes VARCHAR2, from_time varchar2, to_time varchar2)
RETURN CBS_TYPE
IS
   cbs CUSTOMER_BALANCE_STAT_TYPE;
   tab CBS_TYPE:=CBS_TYPE();
   v_cnt number;
BEGIN
  for r in(
      select /*parallel(f,5)*/ c.customer_no, c.name,
        sum(case when f.amount>0 then f.amount else 0 end)/100 as in_amount,
        sum(case when f.amount>0 then 1 else 0 end) as in_rows,
        sum(case when f.amount<0 then f.amount else 0 end)/100 as out_amount,
        sum(case when f.amount<0 then 1 else 0 end) as out_rows,
        min(f.accountdatetime) as first_time,
        max(f.accountdatetime) as last_time
      from acc_accountflow f
        inner join cust_customer c on f.accountcode='JY-A'||c.customer_no
      where f.type<>2 and f.isrollback<>1 and f.transactiontype is not null
        and f.accountcode in(select 'JY-A'||K from table(get_kv(customer_codes)))
        and f.accountdatetime >= to_date(from_time, 'yyyy-mm-dd hh24:mi:ss')
        and f.accountdatetime < to_date(to_time, 'yyyy-mm-dd hh24:mi:ss')
      group by c.customer_no,c.name
    ) loop
    cbs := CUSTOMER_BALANCE_STAT_TYPE(r.customer_no, r.name, 0, r.in_amount, r.in_rows, r.out_amount, r.out_rows, r.first_time, r.last_time,0,0,0);
    --期初余额
    select count(*) into v_cnt from dual
    where exists(
        select /*parallel(5)*/1 from acc_accountflow t
        where accountdatetime < to_date(from_time, 'yyyy-mm-dd hh24:mi:ss') and accountcode='JY-A'||r.customer_no);
    if v_cnt > 0 then
      select afterbalance/100 into cbs.init_balance
      from (select /*parallel(5)*/f.*,row_number() over(order by accountdatetime desc) rn
            from acc_accountflow f where accountdatetime < to_date(from_time, 'yyyy-mm-dd hh24:mi:ss') and accountcode='JY-A'||r.customer_no)
      where rn=1;
    end if;
    --期末余额
    select count(*) into v_cnt from dual
    where exists(
        select /*parallel(5)*/1 from acc_accountflow t
        where accountdatetime < to_date(to_time, 'yyyy-mm-dd hh24:mi:ss') and accountcode='JY-A'||r.customer_no);
    if v_cnt > 0 then
      select /*parallel(5)*/afterbalance/100 into cbs.real_balance
      from (select f.*,row_number() over(order by accountdatetime desc) rn
            from acc_accountflow f where accountdatetime < to_date(to_time, 'yyyy-mm-dd hh24:mi:ss') and accountcode='JY-A'||r.customer_no)
      where rn=1;
    else
      select (a.availablebalance+a.floatbalance)/100 into cbs.real_balance from acc_account a where code='JY-A'||r.customer_no;
    end if;
    --计算余额
    cbs.cal_balance := cbs.init_balance + cbs.in_amount + cbs.out_amount;
    --比较结果
    cbs.check_result := cbs.real_balance - cbs.cal_balance;
    --返回
    tab.extend;
    tab(tab.count) := cbs;
  end loop;

  RETURN tab;
END CUSTOMER_BALANCE_STAT;

--测试
select t.customer_code as 商户编号, t.name as 商户名称, t.init_balance as 期初余额,
  t.in_amount as 入金金额, in_rows as 入金单数,t.out_amount as 出金金额, out_rows as 出金单数, 
  t.cal_balance as 计算余额, t.real_balance as 实际余额, t.check_result as 差额,
  to_char(first_time,'yyyy-mm-dd hh24:mi:ss') as 首笔交易时间,to_char(last_time,'yyyy-mm-dd hh24:mi:ss') as 末笔交易时间
from table(CUSTOMER_BALANCE_STAT('***************,***************,***************,****************','2023-10-01 00:00:00','2023-10-15 00:00:00')) t;


SELECT C.APINAME,(SELECT NAME FROM CAS_CHANNEL_DICT D WHERE C.APINAME=D.CODE AND D.GROUP_TYPE='APINAME') AS 接口,(SELECT NAME FROM CAS_CHANNEL_DICT D WHERE C.Channelno=D.CODE AND D.GROUP_TYPE='CHANNEL') AS 渠道 FROM CAS_CHANNEL C  where state='1';
