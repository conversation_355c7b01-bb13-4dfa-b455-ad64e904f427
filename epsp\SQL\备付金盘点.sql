﻿--【表1】：按商户维度，统计每天收支明细汇总：日期、商户编号、商户名称、期初余额、资金流入总额、资金流出总额、期末余额
--*****本SQL执行效率极其低，建议不统计或者统计某一天的；*****
select customer_code 商户号, name  AS 商户名称, 期初余额, 期末余额,
       totalin       AS 资金流入总额, totalout      AS 资金流出总额
  from (select c.customer_code,
               c.name,
               CASE WHEN d.after_availablebalance is not null then d.after_availablebalance else 0 END as "期初余额",
               a.after_availablebalance as "期末余额",
               row_number() over(partition by a.code order by a.after_version desc) xn,
               e.totalIn, f.totalOut
          from (select t.*,
                       row_number() over(partition by t.accountcode order by t.ACCOUNTDATETIME desc) rn
                  from acc_accountflow t
                 where t.ACCOUNTDATETIME < timestamp'2022-09-20 00:00:00'
                   and t.accountcode like 'JY-A%' and t.balancetype = '1') x
         inner join acc_tempaccount a on a.after_uid = x.u_id
         inner join cum_customer_info c on c.customer_code =
               substrc(x.accountcode, 5, length(x.accountcode))
          left join (select *
                      from (select a.code, a.after_availablebalance, a.after_uid,
                                   row_number() over(partition by a.code order by a.after_version desc) xn
                              from (select t.*,
                                           row_number() over(partition by t.accountcode order by t.ACCOUNTDATETIME desc) rn
                                      from acc_accountflow t
                                     where t.ACCOUNTDATETIME > timestamp '2022-09-01 00:00:00'
                                       and t.accountcode like 'JY-A%' and t.balancetype = '1') x
                             inner join acc_tempaccount a on a.after_uid = x.u_id
                             where x.rn = '1') y
                     where y.xn = '1') d on d.code = x.accountcode
          left join (select t.accountcode, sum(t.amount) as totalIn
                      from acc_accountflow t
                     where t.ACCOUNTDATETIME between timestamp '2022-09-01 00:00:00' and timestamp '2022-09-20 00:00:00'
                       and t.balancetype = 1 and t.amount > 0 and t.accountcode like 'JY-A%'
                     group by t.accountcode) e on e.accountcode = x.accountcode
          left join (select t.accountcode, sum(t.amount) as totalOut
                      from acc_accountflow t
                     where t.ACCOUNTDATETIME between timestamp '2022-09-01 00:00:00' and timestamp '2022-09-20 00:00:00'
                       and t.balancetype = 1 and t.amount < 0 and t.accountcode like 'JY-A%'
                     group by t.accountcode) f
            on f.accountcode = x.accountcode
         where x.rn = '1')
 where xn = '1';


--【表2】：按商户维度，统计每天交易明细，只统计交易成功，按订单结算时间：
--创建统计业务
create table t_business as
select code, name from pas_business where name like '%快捷%' 
  or name like '%网银%' 
  or name like '%银联二维码%' 
  or name like '%碰一碰%'
  or name like '%微信%'  
  or name like '%支付宝%'  
  or code like '%POS%'
  or name like '%云闪付APP%' 
  or name like '%订单转账%' ;
 
--1、 交易订单（剔除交易分账）：订单明细，字段包括：订单结算时间、易票联订单号、业务类型、交易商户编号、交易商户名称、交易金额、订单实收金额、订单手续费、结算金额（结算给商户的金额）
--①是否包含企业网银？②是否包含
SELECT to_char(t.END_TIME,'yyyy-mm-dd hh24:mi:ss') 订单结算时间,t.TRANSACTION_NO 易票联订单号 ,pb.NAME 业务类型,t.CUSTOMER_CODE 交易商户编号,
  t.AMOUNT / 100 交易金额, t.ACTUAL_PAY_AMOUNT/100 订单实收金额,t.PROCEDURE_FEE  / 100 订单手续费, t.SETTLEMENT_AMOUNT / 100 结算金额
FROM TXS_PAY_TRADE_ORDER t INNER JOIN T_BUSINESS pb ON t.BUSINESS_CODE =pb.CODE 
WHERE t.CREATE_TIME > timestamp '2022-09-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-09-16 00:00:00' AND t.STATE ='00'
  AND NOT EXISTS (SELECT 1 FROM TXS_SPLIT_ORDER s WHERE s.CREATE_TIME > timestamp '2022-09-01 00:00:00' AND s.CREATE_TIME < timestamp '2022-09-16 00:00:00' AND t.STATE ='00' 
      AND t.CUSTOMER_CODE=s.CUSTOMER_CODE AND t.OUT_TRADE_NO=s.OUT_TRADE_NO);

--2、分账订单（交易分账）：订单明细，字段包括：订单结算时间、易票联订单号、业务类型、分账商户编号、分账商户名称、分账商户属性、分账金额、收单手续费、分账手续费、结算金额（结算给商户的金额：分账金额-对应手续费）
SELECT to_char(t.END_TIME,'yyyy-mm-dd hh24:mi:ss') 订单结算时间 ,t.TRANSACTION_NO 易票联订单号, b.name 业务类型,
  tr.SOURCE_CUSTOMER_CODE 分账商户编号, tr.SOURCE_CUSTOMERNAME 分账商户名称,
  decode(c."TYPE", 10, '个体工商户', 20, '企业', 30, '境外商户', 50, '小微', 60, '个人客户', 70, '政府/事业单位') AS 分账商户属性,
  tr.AMOUNT / 100 分账金额, tr.PROCEDUREFEE / 100 分账手续费, (tr.AMOUNT - tr.PROCEDUREFEE) / 100 结算金额
FROM TXS_SPLIT_RECORD tr
  INNER JOIN TXS_SPLIT_ORDER tso ON tr.TRANSACTION_NO =tso.TRANSACTION_NO 
  INNER JOIN TXS_PAY_TRADE_ORDER t ON tso.CUSTOMER_CODE =t.CUSTOMER_CODE AND tso.OUT_TRADE_NO =t.OUT_TRADE_NO 
  INNER JOIN t_business b ON t.BUSINESS_CODE =b.code
  INNER JOIN CUST_CUSTOMER c ON tr.SOURCE_CUSTOMER_CODE =c.CUSTOMER_NO 
WHERE tr.STATE ='3' AND t.CREATE_TIME > timestamp '2022-09-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-09-16 00:00:00' AND t.STATE ='00'
  AND t.TRANSACTION_TYPE !='ZHFZ';

--3、分账订单（账户分账）：订单明细，字段包括：订单结算时间、易票联订单号、交易商户编号、交易商户名称、分账商户编号、分账商户名称、分账金额、分账手续费、结算金额（结算给商户的金额：分账金额-对应手续费）
SELECT to_char(t.END_TIME,'yyyy-mm-dd hh24:mi:ss') 订单结算时间 ,t.TRANSACTION_NO 易票联订单号, 
  t.CUSTOMER_CODE 交易商户编号, t.CUSTOMERNAME 交易商户名称,
  tr.SOURCE_CUSTOMER_CODE 分账商户编号, tr.SOURCE_CUSTOMERNAME 分账商户名称,
  tr.AMOUNT / 100 分账金额, tr.PROCEDUREFEE / 100 分账手续费, (tr.AMOUNT - tr.PROCEDUREFEE) / 100 结算金额
FROM TXS_SPLIT_RECORD tr
  INNER JOIN TXS_SPLIT_ORDER tso ON tr.TRANSACTION_NO =tso.TRANSACTION_NO 
  INNER JOIN TXS_PAY_TRADE_ORDER t ON tso.CUSTOMER_CODE =t.CUSTOMER_CODE AND tso.OUT_TRADE_NO =t.OUT_TRADE_NO 
  INNER JOIN CUST_CUSTOMER c ON tr.SOURCE_CUSTOMER_CODE =c.CUSTOMER_NO 
WHERE tr.STATE ='3' AND t.CREATE_TIME > timestamp '2022-09-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-09-16 00:00:00' AND t.STATE ='00'
  AND t.TRANSACTION_TYPE ='ZHFZ';
 
--4、费差分账：订单明细，字段包括：订单结算时间、易票联订单号、商户、平台商户编号、平台商户名称、费差分账金额
SELECT fr.TRANS_DATE 订单结算时间, fr.TRANSACTION_NO 易票联订单号,fr.SUB_CUSTOMER_CODE 商户编号,fr.SUB_CUSTOMER_NAME 商户名称,
  fr.PLAT_CUSTOMER_CODE 平台商户编号,fr.PLAT_CUSTOMER_NAME 平台商户名称, fr.FR_AMOUNT/100 费差分账金额
FROM REPORT_FR_DETAIL fr
  INNER JOIN TXS_PAY_TRADE_ORDER t ON fr.TRANSACTION_NO =t.TRANSACTION_NO 
WHERE t.CREATE_TIME > timestamp '2022-09-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-09-16 00:00:00' AND t.STATE ='00';

--【表3】按商户维度，统计每天ACS转账数据、手工调账数据，只统计审批通过的：
--5、字段包括：日期、易票联流水号、缴费类型、商户编号、商户名称、充值金额、手续费、缴费金额
----缴费类型：银行转账、商户赔付、商户退款、商户补款
SELECT to_char(p.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') 日期, p.TRANSACTION_NO 易票联流水号,
  decode(p.FUND_TYPE, '1', '银行转账', '2', '商户赔付', '3', '商户退款', '4', '商户补款','5', '跨境手续费') AS 缴费类型,
  p.CUSTOMER_CODE 商户编号, c.NAME 商户名称, p.AMOUNT / 100 充值金额, p.PROCEDURE_FEE / 100 手续费, (p.AMOUNT - p.PROCEDURE_FEE) / 100 缴费金额
FROM PAS_ACCT_QUOTA_RECORD p
  INNER JOIN CUST_CUSTOMER c ON p.CUSTOMER_CODE = c.CUSTOMER_NO 
WHERE p.CREATE_TIME > timestamp '2022-09-01 00:00:00' AND p.CREATE_TIME < timestamp '2022-09-16 00:00:00'
  AND p.FUND_TYPE IN('1','2','3','4') AND p.CHANGE_TYPE ='1' AND p.AUDIT_STATE ='00' AND p.TRANSACTION_NO NOT LIKE '33%';

--【表4】按商户维度，统计每天商户出金数据，只统计出金成功和处理中的：
--6、字段包括：日期、易票联订单号、商户编号、商户名称、交易金额、订单金额、手续费、到账金额
SELECT to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') 日期, t.TRANSACTION_NO 易票联订单号,
  t.CUSTOMER_CODE ,t.CUSTOMERNAME ,t.TOTAL_FEE/100 订单金额 ,t.PROCEDURE_FEE/100 手续费 ,(t.TOTAL_FEE - t.PROCEDURE_FEE) / 100 到账金额
FROM TXS_WITHDRAW_TRADE_ORDER t
WHERE t.CREATE_TIME > timestamp '2022-09-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-09-16 00:00:00' AND t.PAY_STATE IN('00','03');

--【表5】按商户维度，统计每天商户退款订单，只统计退款成功的：
--7、字段包括：日期、易票联订单号、原易票联订单号、商户编号、商户名称、实际退回金额、退回收单手续费
SELECT to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') 日期, t.TRANSACTION_NO 易票联订单号, t.PAY_TRANSACTIONNO 原易票联订单号,
  t.CUSTOMER_CODE 商户编号,t.CUSTOMERNAME 商户名称,t.REFUND_FEE/100 实际退回金额 , t.BACKPAY_PROCEDUREFEE/100 退回收单手续费
FROM TXS_REFUND_PRE_ORDER t 
WHERE t.CREATE_TIME > timestamp '2022-09-01 00:00:00' AND t.CREATE_TIME < timestamp '2022-09-16 00:00:00' AND t.PAY_STATE IN('00');

--【表6】按商户维度，统计每天商户提款订单，只统计审批成功的：
--8、字段包括：日期、易票联流水号、提款类型、商户编号、商户名称、提款金额
----提款类型：所有
SELECT to_char(p.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') 日期, p.TRANSACTION_NO 易票联流水号,
  decode(p.FUND_TYPE, '1', '银行转账', '2', '商户赔付', '3', '商户退款', '4', '商户补款','5', '跨境手续费') AS 提款类型,
  p.CUSTOMER_CODE 商户编号, c.NAME 商户名称, p.AMOUNT / 100 提款金额
FROM PAS_ACCT_QUOTA_RECORD p
  INNER JOIN CUST_CUSTOMER c ON p.CUSTOMER_CODE = c.CUSTOMER_NO 
WHERE p.CREATE_TIME > timestamp '2022-09-01 00:00:00' AND p.CREATE_TIME < timestamp '2022-09-16 00:00:00'
  AND p.CHANGE_TYPE ='2' AND p.AUDIT_STATE ='00' AND p.TRANSACTION_NO NOT LIKE '33%';

--【表7】按商户维度，统计每天商户退汇订单，只统计处理成功的：
--9、字段包括：日期、退汇流水号、商户名称、商户编码、原订单金额、原手续费、退汇金额
SELECT to_char(h.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') 日期, h.TRANSACTION_NO 退汇流水号,
  t.CUSTOMER_CODE 商户编码, t.CUSTOMERNAME 商户名称,t.TRANSACTION_NO 原订单金额,t.PROCEDURE_FEE /100 原手续费, t.TOTAL_FEE /100 退汇金额
FROM ACC_TUIHUI_RECORD h 
  INNER JOIN TXS_WITHDRAW_TRADE_ORDER t ON h.ORGI_TXN_NO =t.TRANSACTION_NO ;
