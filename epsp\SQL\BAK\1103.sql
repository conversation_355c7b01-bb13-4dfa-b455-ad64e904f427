﻿
declare
  last_audit_type number;
  last_version number;
  type audit_record is record( 
    submit_time        TIMESTAMP(6),
    submit_msg         CHAR(1),
    pre_audit_time     TIMESTAMP(6),
    pre_audit_result   VARCHAR2(600),
    pre_audit_msg      VARCHAR2(600),
    first_audit_time   TIMESTAMP(6),
    first_audit_result VARCHAR2(600),
    first_audit_msg    VARCHAR2(600),
    last_audit_time    TIMESTAMP(6),
    last_audit_result  VARCHAR2(600),
    last_audit_msg     VARCHAR2(600)
  );
  ar audit_record;
begin
  for x in (select * from cust_last_audit_record t where t.flag !=1 or t.flag is null order by t.customer_id) loop
    last_audit_type :=-1;
    last_version := 1;
    for y in (select * from cust_customer_audit_record r where r.customer_id=x.customer_id and r.version <= x.min_last_audit_version order by r.version, r.create_time) loop
      if last_audit_type > y.audit_type then --保存数据，开始新一轮审核
         insert into CUST_AUDIT_RECORD(customer_id,customer_no,name,create_time,
             plat_customer,service_customer,business_man,version,
             submit_time,submit_msg,
             pre_audit_time,pre_audit_result,pre_audit_msg,
             first_audit_time,first_audit_result,first_audit_msg,
             last_audit_time,last_audit_result,last_audit_msg)
         select
             c.customer_id, c.customer_no,c.name, c.create_time,
             c.plat_customer_no || (case when c.plat_customer_no is not null then '-'||(select name from cust_customer x where x.customer_no=c.plat_customer_no) else '' end) as plat_customer,
             c.service_customer_no || (case when c.service_customer_no is not null then '-'||(select name from cust_customer x where x.customer_no=c.service_customer_no) else '' end) as service_customer,
             b.real_name as business_man, last_version,
             ar.submit_time, ar.submit_msg,
             ar.pre_audit_time,ar.pre_audit_result,ar.pre_audit_msg,
             ar.first_audit_time,ar.first_audit_result,ar.first_audit_msg,
             ar.last_audit_time,ar.last_audit_result,ar.last_audit_msg
         from cust_customer c left join pas_user b on c.business_man_id=b.user_id
         where c.customer_id=x.customer_id;
         --清理数据
         ar.submit_time := null;
         ar.pre_audit_time := null;
         ar.pre_audit_result := null;
         ar.pre_audit_msg := null;
         
         ar.first_audit_time := null;
         ar.first_audit_result := null;
         ar.first_audit_msg := null;
         
         ar.last_audit_time := null;
         ar.last_audit_result := null;
         ar.last_audit_msg := null;
         
         last_version := last_version + 1;
      end if;
      
      last_audit_type := y.audit_type;
      if ar.submit_time is null then
        ar.submit_time := y.create_time;
        ar.submit_msg := null;
      end if;
      if y.audit_type = 0 then 
         ar.pre_audit_time := y.audit_time;
         ar.pre_audit_result := (case y.audit_result when 0 then '待审核' when 1 then '通过' when 2 then '不通过' end);
         ar.pre_audit_msg := y.remarks;
      end if;
      if y.audit_type = 1 then 
         ar.first_audit_time := y.audit_time;
         ar.first_audit_result := (case y.audit_result when 0 then '待审核' when 1 then '通过' when 2 then '不通过' end);
         ar.first_audit_msg := y.remarks;
      end if;
      if y.audit_type = 2 then 
         ar.last_audit_time := y.audit_time;
         ar.last_audit_result := (case y.audit_result when 0 then '待审核' when 1 then '通过' when 2 then '不通过' end);
         ar.last_audit_msg := y.remarks;
      end if;
    end loop;
    --保存数据，开始新一轮审核
    insert into CUST_AUDIT_RECORD(customer_id,customer_no,name,create_time,
       plat_customer,service_customer,business_man,version,
       submit_time,submit_msg,
       pre_audit_time,pre_audit_result,pre_audit_msg,
       first_audit_time,first_audit_result,first_audit_msg,
       last_audit_time,last_audit_result,last_audit_msg)
    select
       c.customer_id, c.customer_no,c.name, c.create_time,
       c.plat_customer_no || (case when c.plat_customer_no is not null then '-'||(select name from cust_customer x where x.customer_no=c.plat_customer_no) else '' end) as plat_customer,
       c.service_customer_no || (case when c.service_customer_no is not null then '-'||(select name from cust_customer x where x.customer_no=c.service_customer_no) else '' end) as service_customer,
       b.real_name as business_man, last_version,
       ar.submit_time, ar.submit_msg,
       ar.pre_audit_time,ar.pre_audit_result,ar.pre_audit_msg,
       ar.first_audit_time,ar.first_audit_result,ar.first_audit_msg,
       ar.last_audit_time,ar.last_audit_result,ar.last_audit_msg
    from cust_customer c left join pas_user b on c.business_man_id=b.user_id
    where c.customer_id=x.customer_id;
    --清理数据
    ar.submit_time := null;
    ar.pre_audit_time := null;
    ar.pre_audit_result := null;
    ar.pre_audit_msg := null;
             
    ar.first_audit_time := null;
    ar.first_audit_result := null;
    ar.first_audit_msg := null;
             
    ar.last_audit_time := null;
    ar.last_audit_result := null;
    ar.last_audit_msg := null;
    
    last_version := 1;
    commit;
  end loop;
end;

--select * from CUST_AUDIT_RECORD t where version > 1 order by t.customer_id, version;
