@startuml 客户状态图
title 客户状态图

state "待提交"  as DTJ
state "待筛查"  as DSC
state "筛查结果" as SCJG <<choice>>
state "待初审" as DCS{
    state "节点1审核" as DCS1
    state "节点2审核" as DCS2
    DCS1 -> DCS2
}
state "初审结果" as CSJG <<choice>>
state "待复审" as DFS
state "复审结果" as FSJG <<choice>>
state "审批驳回" as BH
state "启用" as QY
state "停用" as TY
state "注销" as ZX

[*] -> DTJ: 客户注册
DTJ --> DSC: 客户提交申请
DSC -> SCJG: 完成筛查
SCJG --> ZX: 筛查拒绝
SCJG --> DCS: 筛查通过

DCS -> CSJG: 汇通初审完成
CSJG --> DFS: 初审通过\n提交Skyee复审
CSJG -> BH: 初审驳回

DFS --> FSJG: Skyee复审完成
FSJG --> QY: 复审通过
FSJG --> ZX: 复审拒绝
FSJG -> BH: 复审驳回

BH --> DTJ: 重新修改

QY --> TY: 客户停用
TY --> QY: 客户启用
QY --> ZX: 客户注销
TY --> ZX: 客户注销

ZX -> [*]: 客户退出

@enduml

@startuml 付款状态图（RemitOrder）
title 付款状态图（RemitOrder）
note: 状态：Status

state "00付款成功"  as 00
state "01待处理/待审核"  as 01
state "02付款失败"  as 02
state "03上游付款（配置）"  as 03 {
    state "02付款中"  as C02
    state "00付款成功"  as C00
    state "01付款失败"  as C01
    C02 --> C00: 上游付款成功
    C02 --> C01: 上游付款失败
}
note left of 03: 状态：Channel_Status

[*] -> 01: 申请付款
01 --> 03: 审核通过，调用上游付款接口
01 --> [*]: 审核不通过，订单关闭

03 --> 00: 付款成功
00 --> [*]: 付款成功结束

03 --> 02: 付款失败
02 --> [*]: 付款失败结束

@enduml