@startuml 开户
title 开户顺序图
actor  运营 as O
actor 商户 as M
participant 易票联EP as EP
participant 易票联跨境 as KJ
participant 汇通全球 as HT
participant 易云帐VA as VA
participant CurrencyCloud as CC #Grey
==易票联EP商户进件==
O -> EP: 进件
note right of EP: 复用现有易票联EP进件流程
EP --> O: 获取商户编码
EP ->> M: 发送短信通知商户用户信息

==添加CC渠道商户==
O -> KJ: 添加CC渠道商户（现有创建平台商户）
note right of KJ: 复用现有跨境运营平台创建平台商户功能
KJ --> O: 平台商创建成功

==国际收款汇通全球==
M -> HT: 登录
HT --> M: 进入汇通全球
M -> HT: 申请国际收款开户
HT -> CC: 申请开户
note right of CC: Account.Create接口\nyour_reference传入CustomerCode
CC --> HT: account_id和状态
HT -> CC: 创建联系人
note right of CC: Contact.Create接口\nyour_reference传入ContactID
CC --> HT: contact_id和状态

HT -> HT: 保存account_id和contact_id
HT -> VA: 申请开通币种账户\n携带account_id和contact_id
note right of VA: 保存account_id和contact_id\n可以根据account_id和contact_id查询账户
VA --> HT: 返回开通状态
HT --> M: 返回开通成功
@enduml

@startuml 国际收款开户流程图
title 国际收款开户流程图
(*)-->"查询商户信息"
-->"申请开户"
-->"检查商户是否初次开户"
if "是初次开户？" then
--> [是]"创建CC子账户"
--> "创建CC联系人"
--> 易云帐VA开户
note left : 需要携带前面环节产生\n的account_id和contact_id
--> 易云帐VA开卡
else
--> [否]查询账户信息
--> 易云帐VA开卡

--> 返回账户状态
--> (*)

@enduml