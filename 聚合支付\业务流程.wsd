@startuml 进件&商户配置

@enduml

@startuml 支付交易
actor 商户 as m
box 聚合支付
    participant 交易模块 as t
    participant 清算模块 as c
    participant 异步通知 as n
end box
participant 三方支付 as b
==支付交易==
m -> t: 申请支付
t -> t: 生成支付订单
t -> c: 请求支付
c -> b: 请求支付
b --> c: 返回支付结果
alt 支付成功/失败（订单终态）
    c -> c: 更新订单状态
    c -> n: 通知商户
    n --\ m: 通知商户支付结果
    c --> t: 返回支付结果
    t --> m: 返回支付结果
else 支付错误（系统错误）或者已受理
    c --> t: 返回支付结果
    t --> m: 返回支付结果
end
==交易查询==
m -> t: 查询订单
t -> t: 检查本地订单状态
alt 订单已经终态
    t -> m: 返回订单状态
else 订单未终态
    t -> c: 查询订单状态
    c -> b: 查询订单状态
    b --> c: 返回订单状态
    alt 订单已经终态
        c -> c: 更新订单状态
        c -> n: 通知商户
        n --\ m: 通知商户支付结果
        c --> t: 返回订单状态
        t --> m: 返回订单状态
    else 订单未终态
        c --> t: 返回订单状态
        t --> m: 返回订单状态
    end
end
==异步通知==
b -> c: 通知支付结果
alt 订单已经终态
    c -> c: 更新订单状态
    c -> n: 通知商户
    n --\ m: 通知商户支付结果
    c --> b: 成功处理通知
end
@enduml