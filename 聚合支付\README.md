# 部署

## 生产环境资源


| 编号 | 服务器         | 项目     | 说明          | 监听端口 | IP                                           | 用户/密码         | ZONE       | 应用说明 |
| ---- | -------------- | -------- | ------------- | -------- | -------------------------------------------- | ----------------- | ---------- | -------- |
| 1    | WEB服务        | Nginx    | web反代       | 80/443   | *************                                |                   | Nginx      |          |
| 1    | WEB服务        | Nginx    | web反代       | 80/443   | *************                                |                   | Nginx      |          |
| 2    | 生产应用       | 开发应用 | 前端+后端应用 |          | ************1                                |                   | APP区      |          |
| 3    | 生产应用       | 生产应用 | 前端+后端应用 |          | *************                                |                   | APP区      |          |
| 3    | 配置平台gitlab | gitlab   | gitlab        |          | [http://************03](http://************03/) | epayaps/Aps090807 | 配置平台   |          |
| 6    | 中间件服务器   | ZK       | Zookeeper     | 2181     | ************01                               |                   | 公共资源   |          |
| 6    | 中间件服务器   | Kafka    | Kafka         | 9092     | ************02                               |                   | 公共资源   |          |
| 7    | 中间件服务器   | Redis    | Redis         | 6381     | ************03                               |                   | 公共资源   |          |
| 7    | 中间件服务器   | Redis    | Redis         | 6382     |                                              |                   | 公共资源   |          |
| 8    | 文件服务器     | FastDFS  | 文件存储      | 11211    | ************01                               |                   | 文件服务器 |          |
| 8    | 文件服务器     | FastDFS  | 文件存储      | 23000    | ************02                               |                   | 文件服务器 |          |
| 9    | 数据库服务器   | Oracle19 | Oracle19      | 1521     | opdb实例  10.2.2.25                          |                   |            |          |


![1733903779024](image/info/1733903779024.png)

## 开发、测试环境资源

域名： XXXX.yicaile.cn(易彩乐)

开发环境: 172.16.2.189  用户/密码： aps_dev/aps_dev

测试环境: 172.16.2.191  用户/密码： aps_test/aps_test

jdk: java version "1.8.0_144"

Redis :
172.16.2.195:6381,172.16.2.195:6382,172.16.2.196:6381,172.16.2.196:6382,172.16.2.197:6381,172.16.2.197:6382

Zookeeper: 172.16.2.195:2181,172.16.2.196:2181,172.16.2.197:2181

Kafka: 172.16.2.195:9092,172.16.2.196:9092,172.16.2.197:9092

Gitlab: [http://172.16.2.195](http://172.16.2.195)
 用户/密码： epayaps/Aps090807

Fastdfs: 172.16.2.195:22122,172.16.2.1

## 拓扑图

![1733903670377](image/info/1733903670377.png)
