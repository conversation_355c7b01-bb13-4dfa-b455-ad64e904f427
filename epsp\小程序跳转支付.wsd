

@startuml 小程序跳转支付
title 小程序跳转支付

actor 用户 as u
participant 商户小程序 as a
participant 商户服务 as m
box "EPSP" #LightBlue
    participant TXS服务 as t
    participant 支付小程序 as b
    participant Cashier服务 as c
endbox
participant 两联 as k
participant 微信小程序平台 as p

u -> a: 打开商户小程序并申请支付
a -> m: 申请支付
m -> t: 商户下单，申请orderToken\n接口：IF-Cashier-01
t --> m: 返回orderToken
m --> a: 返回orderToken、商户号等
a -> b: 唤起易票联支付小程序，传入参数customerCode、orderToken等
b -> c: 获取openid\n接口：jscode2session
c -> p: 申请openid
p --> c: 返回openid
c --> b: 返回openid
b -> c: 调用接口获取微信支付参数\n接口：IF-Cashier-04
c -> k: 调用接口获取微信支付参数
k -> p: 获取微信支付参数
p --> k: 返回微信支付参数
k --> c: 返回微信支付参数
c --> b: 返回微信支付参数
b -> b: 调起微信支付
u -> b: 微信支付授权
@enduml