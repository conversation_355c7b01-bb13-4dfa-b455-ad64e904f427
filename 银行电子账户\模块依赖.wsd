@startuml 模块依赖
title 模块依赖

state "bic-vue-plus" as M1
state "bic-admin" as M2
state "bic-basic" as M3
state "bic-basic.bic-gen" as M4
state "bic-basic.bic-oss" as M5
state "bic-basic.bic-pay" as M6
state "bic-basic.bic-sms" as M7
state "bic-basic.bic-sys" as M8
state "bic-business" as M9
state "bic-business.bic-job" as M10        
state "bic-business.bic-service" as M11    
state "bic-business.bic-service-api" as M12
state "bic-business.bic-service-out" as M13
state "bic-common" as M14
state "bic-extend" as M15
state "bic-extend.bic-epay-api" as M16
state "bic-extend.bic-epay-demo" as M17
state "bic-extend.bic-monitor-admin" as M18
state "bic-extend.bic-xxl-job-admin" as M19
state "bic-frame" as M20
M2 --> M20
M2 --> M8
M2 --> M10
M2 --> M5
M2 --> M4
M2 --> M7
M2 --> M11
M4 --> M14
M5 --> M14
M6 --> M14
M7 --> M14
M8 --> M14
M8 --> M7
M10 --> M14
M10 --> M11
M10 --> M13
M10 --> M5
M11 --> M6
M11 --> M12
M11 --> M7
M11 --> M8
M11 --> M5
M12 --> M14
M13 --> M6
M13 --> M12
M13 --> M11
M16 --> M20
M16 --> M8
M16 --> M11
M16 --> M5
M16 --> M13
M20 --> M14

@enduml