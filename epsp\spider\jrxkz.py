import requests 
import json
import csv
import time

url = "https://xkz.cbirc.gov.cn/jr/KXOXOP/getLicence.do?useState=3"
def fetch(pageNum, csvfile, fullName=''):
    start = (pageNum - 1) * 10+1
    resp = requests.post(url.format(start), 
                  headers={"Content-type": "application/x-www-form-urlencoded", 
                           "Accept": "*/*", 
                           "Accept-Encoding": "gzip, deflate, br",
                           "Accept-Language": "zh-CN,zh;q=0.9",
                           "Content-Length": "20",
                           "Connection": "keep-alive",
                           "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                           "referer": "https://xkz.cbirc.gov.cn/jr/",
                           "cookie": "isClick=true; JSESSIONID=0000BfkofqQdRx2tsP_7Cg0ck6M:-1",
                           "Sec-Fetch-Dest": "empty",
                           "Sec-Fetch-Mode": "cors",
                           "Sec-Fetch-Site": "same-origin",
                           "X-Requested-With": "XMLHttpRequest",
                           "sec-ch-ua": "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"80\", \"Google Chrome\";v=\"80\"",
                           "sec-ch-ua-mobile": "?0",
                           "sec-ch-ua-platform": "\"Windows\"",
                           "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36"}, 
                  data={"start": start, "limit": 10, "fullName": fullName})
    datas = json.loads(resp.text).get("datas")
    pageSize= len(datas)
    if pageSize > 0:
        with open(csvfile, "a", encoding="utf-8") as f:
            writer = csv.writer(f)
            for d in datas:
                id = d.get("id")
                flowNo = d.get("flowNo")
                certCode = d.get("certCode")
                fullName = d.get("fullName")
                setDate = d.get("setDate")
                print(certCode, fullName, setDate)
                writer.writerow((id, flowNo, certCode, fullName, setDate))
    else:
        print(resp.text)
    return pageSize

if __name__ == "__main__":
    totalRowCount = 0
    # pageNum = 5155
    pageNum = 1
    pageSize = fetch(pageNum, "D:/TMP/JR2.csv", '江苏苏州农村商业银行股份有限公司')
    while True:
        if pageSize == 0:
            time.sleep(10)
        else:
            pageNum += 1
        pageSize = fetch(pageNum, "D:/TMP/JR2.csv")
        print(pageNum, pageSize)
    print(totalRowCount)