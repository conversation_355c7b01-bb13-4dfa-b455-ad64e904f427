import requests
from bs4 import BeautifulSoup
import os
import urllib.request

def spider(url, page):
    print(url)
    response = requests.get(url)
    soup = BeautifulSoup(response.text, 'html.parser')

    next_url ="";
    for a_tag in soup.find_all('a'):
        at = a_tag.get('data-pagination')
        if at and a_tag.get('href') and at == "next":
            next_url = "https://anh.im/"+a_tag.get('href')
            print("next_url", next_url)

    image_urls = []
    for img_tag in soup.find_all('img'):
        src = img_tag.get('src')
        if src and '.md.' in src:
            src = src.replace('.md.', '.')
            image_urls.append(src)

    for i, image_url in enumerate(image_urls):
        filename = os.path.join('C:\\TMP\\白莉愛吃巧克力', 'image{}-{}.jpg'.format(page, i))
        response = requests.get(image_url)
        with open(filename, 'wb') as f:
            f.write(response.content)
        print('Downloaded image save to {}, source:{}'.format(filename, image_url))
    return next_url

if __name__ == '__main__':
    url = 'https://anh.im/bigradish/?page=1&peek=2023-09-06+14%3A35%3A34.6MwC'
    url = 'https://anh.im//bigradish/?page=22&seek=2023-09-05+16%3A33%3A36.6MvS'
    for page in range(22, 23):
        url = spider(url, page)