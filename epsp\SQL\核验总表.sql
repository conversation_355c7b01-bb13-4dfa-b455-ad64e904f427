/**
 1、创建临时表 create table ybdr_4482(customer_code varchar2(50))
 2、商户号数据插入Step1的临时表中
 3、替换如下SQL中时间区间，如下，开始时间为：2023-01-01 00:00:00，结束时间为：2024-01-01 00:00:00
 */
select 
        customer_code_base 商户编号,
        (select name from cum_customer_info where customer_code = customer_code_base) 商户名称,
        nvl(BEGIN_BALANCE, 0)/100 期初余额元, 
        (nvl(INCOME_PAY, 0)+nvl( BE_SPLIT_INCOME , 0 )+nvl( ACS_INCOME , 0 )+nvl( jl_in , 0 )+nvl( xx_in , 0 ))/100 收入类发生额元,
        (nvl( OUT_COME_WITHDRAW , 0 )+nvl( SPLIT_OUT , 0 )+nvl( PS_OUT_COME , 0 )+nvl( BANK_CARD_VERY_OUT , 0 )+nvl( ACS_OUTCOME , 0 )+ nvl(xh_outcome, 0))/100 支出类发生额元,
        (nvl(BEGIN_BALANCE, 0)+(nvl(INCOME_PAY, 0)+nvl( BE_SPLIT_INCOME , 0 )+nvl( ACS_INCOME , 0 )+nvl( jl_in , 0 )+nvl( xx_in , 0 ))-(nvl( OUT_COME_WITHDRAW , 0 )+nvl( SPLIT_OUT , 0 )+nvl( PS_OUT_COME , 0 )+nvl( BANK_CARD_VERY_OUT , 0 )+nvl( ACS_OUTCOME , 0 )))/100 计算期末余额元,
        nvl( END_BALANCE ,  nvl(BEGIN_BALANCE, 0) )/100 实际期末余额元,
        (nvl(BEGIN_BALANCE, 0)+(nvl(INCOME_PAY, 0)+nvl( BE_SPLIT_INCOME , 0 )+nvl( ACS_INCOME , 0 )+nvl( jl_in , 0 )+nvl( xx_in , 0 ))-( nvl(xh_outcome, 0)+nvl( OUT_COME_WITHDRAW , 0 )+nvl( SPLIT_OUT , 0 )+nvl( PS_OUT_COME , 0 )+nvl( BANK_CARD_VERY_OUT , 0 )+nvl( ACS_OUTCOME , 0 )) - nvl( END_BALANCE , nvl(BEGIN_BALANCE, 0)))/100 差值元,
        nvl(ACS_INCOME, 0)/100 ACS收入, 
        nvl(acs_in_cnt, 0) ACS收入笔数,
        nvl(ACS_OUTCOME, 0)/100 ACS支出, 
        nvl(acs_out_cnt, 0) ACS支出笔数,
        nvl(INCOME_PAY, 0)/100 线上收单金额, 
        nvl(PAY_CNT, 0) 线上收单笔数,
        nvl(BE_SPLIT_INCOME, 0)/100 被分账收入,
        nvl(fz_in_cnt, 0) 被分账笔数,
        nvl(OUT_COME_WITHDRAW, 0)/100 提现支出,
        nvl(WITHDRAW_CNT, 0) 提现笔数,
        nvl(SPLIT_OUT, 0)/100 分账支出,
        nvl(fz_out_cnt, 0) 分账支出笔数,
        nvl(PS_OUT_COME, 0)/100 支付服务费应记账支出,
        nvl(pay_service_cnt, 0) 支付服务费应记账笔数,
        nvl(BANK_CARD_VERY_OUT, 0)/100 银行卡核验支出, 
        nvl(bank_card_very_cnt, 0) 银行卡核验笔数,
        nvl(xh_outcome, 0)/100 销户出金,
        nvl(jl_in, 0)/100 间联入金,
        nvl(jl_cnt, 0) 间联入金笔数,
        nvl(xx_in, 0)/100 线下直连入金,
        nvl(xx_cnt, 0) 线下直连入金笔数      
from (
        select * from (select customer_code customer_code_base from ybdr_4482  ) customers
        left join 
                        (select substr(accountcode, 5)customer_code, accountcode, AFTERBALANCE begin_balance,to_char(CREATEDATETIME,'yyyymmdd') TASK_DATE from 
                                                (
                                                select tb.*,ROW_NUMBER() over( partition by ACCOUNTCODE order by after_version desc ) rn
                                                        from (
                                                                        select t1.after_version, t.* from acc_accountflow t, acc_tempaccount t1
                                                                 where 
                                                                 	exists (select 1 from ybdr_4482 y4482 where 'JY-A'||y4482.customer_code = t.accountcode) and t.CREATEDATETIME < timestamp'2023-01-01 00:00:00' 
                                                                   and t.u_id = t1.after_uid 
                                                      )tb
                                ) where rn = 1  )
        begin_balance on customers.customer_code_base = begin_balance.customer_code
        left join 
                        (
                        select substr(accountcode, 5) customer_code, accountcode, AFTERBALANCE end_balance ,to_char(CREATEDATETIME,'yyyymmdd') TASK_DATE from 
                                                                (
                                                                select tb.*,ROW_NUMBER() over( partition by ACCOUNTCODE order by after_version desc) rn
                                                                        from (
                                                                                        select t1.after_version, t.* from acc_accountflow t, acc_tempaccount t1
                                                                                 where
                                                                                  exists (select 1 from ybdr_4482 y4482 where 'JY-A'||y4482.customer_code = t.accountcode) and t.CREATEDATETIME <= timestamp'2024-01-01 00:00:00' 
                                                                                   and t.u_id = t1.after_uid 
                                                                      )tb
                                                ) where rn = 1 
                        )
        end_balance on customers.customer_code_base = end_balance.customer_code        left join 
        (
	        select g1.customer_code, (g1.CASH_AMOUNT-g1.PROCEDURE_FEE-g1.REFUND_FEE+g1.REFUND_PROCEDURE_FEE)income_pay,g1.pay_cnt from  (	                
                                SELECT
                                        t.customer_code, 
                                        sum( t.cash_amount ) CASH_AMOUNT,
                                        sum(t.PROCEDURE_FEE ) PROCEDURE_FEE,
                                        sum(nvl(t.REFUND_FEE, 0))REFUND_FEE,
                                        round(sum(nvl(t.REFUND_FEE, 0))/ sum( t.cash_amount ) * sum(t.PROCEDURE_FEE )) refund_procedure_fee ,
                                        count(*) pay_cnt
                                        from (   
                                                   SELECT
                                                                        t.customer_code, 
                                                                        (case when t.CASH_AMOUNT is null then t.amount else t.cash_amount end)CASH_AMOUNT,
                                                                       (case when  t.procedure_customercode is not null then 0 else t.PROCEDURE_FEE end ) PROCEDURE_FEE,
                                                                        (nvl(refund_2.refund_fee_2, 0))REFUND_FEE
                                                                        FROM TXS_PAY_TRADE_ORDER t
                                                                        LEFT JOIN (select trpo.customer_code,  trpo.pay_transactionno , count(*) cnt_refund, sum(nvl(refund_fee, 0)) refund_fee_2
                                                                                                from  txs_refund_pre_order trpo where 
                                                                                                exists (select 1 from ybdr_4482 y4482 where y4482.customer_code = trpo.CUSTOMER_CODE) and trpo.create_time >= timestamp'2023-01-01 00:00:00' and trpo.create_time < timestamp'2024-01-01 00:00:00'  and trpo.pay_state='00' 
                                                                                                                group by trpo.customer_code,  trpo.pay_transactionno ) refund_2 on t.transaction_no = refund_2.pay_transactionno
                                                                WHERE 
                                                                        exists (select 1 from ybdr_4482 y4482 where y4482.customer_code = t.CUSTOMER_CODE) and t.create_time >= timestamp'2023-01-01 00:00:00' and t.create_time < timestamp'2024-01-01 00:00:00'  and t.state='00'  and t.TRANSACTION_TYPE != 'ZHFZ' )
                                                 t
                                        group by customer_code )g1   
        )
        pay   on customers.customer_code_base = pay.customer_code        left join 
                (
                select g1.customer_code, (g1.TOTAL_FEE ) out_come_withdraw,g1.withdraw_cnt from (
                                SELECT 
                                        t.CUSTOMER_CODE ,
                                         sum(t.amount)  as TOTAL_FEE,
                                         sum(t.PROCEDURE_FEE) as PROCEDURE_FEE,
                                         count(*) withdraw_cnt
                                                FROM (                                
                                                        SELECT 
                                                                t.CUSTOMER_CODE ,
                                                                 (case when procedure_customercode is not null then t.actual_fee else t.total_fee end )amount,
                                                                 t.PROCEDURE_FEE
                                                                        FROM TXS_WITHDRAW_TRADE_ORDER t
                                                        WHERE 
                                                                exists (select 1 from ybdr_4482 y4482 where y4482.customer_code = t.CUSTOMER_CODE) and t.create_time >= timestamp'2023-01-01 00:00:00' and t.create_time < timestamp'2024-01-01 00:00:00' 
                                                                and (t.pay_state='00' or t.pay_state='03' )
                                                                and (t.th_state is null or t.th_state != '1'))t
                                        group by t. CUSTOMER_CODE)g1
                )
        withdraw on customers.customer_code_base = withdraw.customer_code
        left join 
                (
                SELECT 
                        t.CUSTOMER_CODE,
                        sum(tsr.AMOUNT) -sum(nvl(t.REFUND_FEE, 0))  split_out,
                        count(*) fz_out_cnt
                        FROM TXS_SPLIT_ORDER t
                        left join cum_customer_info cc ON t.CUSTOMER_CODE = cc.CUSTOMER_code
                        INNER JOIN TXS_SPLIT_RECORD tsr ON t.TRANSACTION_NO = tsr.TRANSACTION_NO
                where 
                        exists (select 1 from ybdr_4482 y4482 where y4482.customer_code = t.CUSTOMER_CODE) and t.create_time >= timestamp'2023-01-01 00:00:00' and t.create_time < timestamp'2024-01-01 00:00:00' 
                        and t.STATE = '00'
                        group by t.CUSTOMER_CODE
                )
        fz_out on  customers.customer_code_base = fz_out.customer_code       left join 
                (
                SELECT 
                 t.CUSTOMER_CODE ,
                (sum( t.AMOUNT)- sum(nvl(t.REFUND_FEE, 0))) be_split_income,
                count(*) fz_in_cnt
                        FROM TXS_SPLIT_RECORD t
                        INNER JOIN TXS_SPLIT_ORDER tso ON t.TRANSACTION_NO = tso.TRANSACTION_NO
                where 
                  exists (select 1 from ybdr_4482 y4482 where y4482.customer_code = t.CUSTOMER_CODE) and t.create_time >= timestamp'2023-01-01 00:00:00' and t.create_time < timestamp'2024-01-01 00:00:00' 
                 and t.STATE = '3' 
                 group by t.customer_code
                 )
         fz_in on  customers.customer_code_base = fz_in.customer_code
         left join 
                 (
                select 
                          t.CUSTOMER_CODE ,
                          sum(t.SERVICE_FEE) PS_OUT_COME,
                          count(*) pay_service_cnt
                           from txs_pay_service_order t 
                        where 
                        exists (select 1 from ybdr_4482 y4482 where y4482.customer_code = t.CUSTOMER_CODE) and t.create_time >= timestamp'2023-01-01 00:00:00' and t.create_time < timestamp'2024-01-01 00:00:00' 
                        group by t.CUSTOMER_CODE
                 )
         pay_service on  customers.customer_code_base = pay_service.customer_code
         left join 
                 (
                 select 
                        t.CUSTOMER_CODE ,
                        sum(nvl( t.PROCEDURE_FEE, 0)) bank_card_very_out  ,
                        count(*) bank_card_very_cnt
                      from V_BANK_CARD_RISK_CHECK t
                                where 
                            exists (select 1 from ybdr_4482 y4482 where y4482.customer_code = t.CUSTOMER_CODE) and t.create_time >= timestamp'2023-01-01 00:00:00' and t.create_time < timestamp'2024-01-01 00:00:00' 
                        group by t.customer_code
                 )
         bank_card_very  on  customers.customer_code_base = bank_card_very.customer_code         left join 
                 (
                 SELECT 
                        t.CUSTOMER_CODE ,  
                        (sum(nvl(t.AMOUNT, 0)))acs_income, 
                        count(*) acs_in_cnt
                        FROM PAS_ACCT_QUOTA_RECORD t
                where 
                        exists (select 1 from ybdr_4482 y4482 where y4482.customer_code = t.CUSTOMER_CODE) and t.create_time >= timestamp'2023-01-01 00:00:00' and t.create_time < timestamp'2024-01-01 00:00:00' 
                        and t.ACCT_STATE = '00' 
                        and t.CHANGE_TYPE = '1'
                        and t.fund_type != '7' 
                group by t.customer_code
                 )
         acs_in   on  customers.customer_code_base = acs_in.customer_code
         left join 
                 (
                 SELECT 
                        t.CUSTOMER_CODE ,
                        (sum(nvl(t.AMOUNT, 0))-sum(nvl(t.procedure_fee,0))) acs_outcome,
                        count(*) acs_out_cnt
                        FROM PAS_ACCT_QUOTA_RECORD t
                where 
                           exists (select 1 from ybdr_4482 y4482 where y4482.customer_code = t.CUSTOMER_CODE) and t.create_time >= timestamp'2023-01-01 00:00:00' and t.create_time < timestamp'2024-01-01 00:00:00' 
                        and t.ACCT_STATE = '00' 
                        and t.CHANGE_TYPE = '2'
                group by t.customer_code
                 )
         acs_out    on  customers.customer_code_base = acs_out.customer_code
         left join 
                 (
                 SELECT 
                        t.CUSTOMER_CODE ,
                        (nvl(t.JY_AVAILABLE_BALANCE, 0)+nvl(t.JY_FLOAT_BALANCE, 0)) xh_outcome
                        FROM ACC_CANCEL_RECORD t
                 )
         xh_out    on  customers.customer_code_base = xh_out.customer_code         
         left join     
                 (
                select 
                          t.MERCHANT_NO CUSTOMER_CODE ,
                          sum(nvl(ACC_INCOME_AMT,0)) jl_in,
                          count(*) jl_cnt
                           from ZHY_POSP_RECORD t 
                        where 
                            exists (select 1 from ybdr_4482 y4482 where y4482.customer_code = t.MERCHANT_NO) and t.CREATION_TIME >= timestamp'2023-01-01 00:00:00' and t.CREATION_TIME < timestamp'2024-01-01 00:00:00'                         
                        group by t.MERCHANT_NO
                 )
         jl on  customers.customer_code_base = jl.customer_code    
         left join  
                 (
                select 
                          t.CUSTOMER_CODE ,
                          sum(t.amt-t.PROCEDURE_FEE) xx_in,
                          count(*) xx_cnt
                           from CHK_XX_TRAN_RECORD t 
                        where 
                           exists (select 1 from ybdr_4482 y4482 where y4482.customer_code = t.CUSTOMER_CODE) and t.END_TIME >= timestamp'2023-01-01 00:00:00' and t.END_TIME < timestamp'2024-01-01 00:00:00'  
                        group by t.CUSTOMER_CODE
                 )
         xx on  customers.customer_code_base = xx.customer_code             
 )
 