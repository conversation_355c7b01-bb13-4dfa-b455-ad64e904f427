import sqlparse
import re
import json
import os

# 获取当前文件的目录路径
current_dir = os.path.dirname(os.path.abspath(__file__))

# 拼接其他文件的相对路径
file_path = os.path.join(current_dir, '资金分析.sql')

# 使用open函数打开文件
with open(file_path, 'r', encoding='utf-8') as file:
    sql_content = file.read()

# 解析SQL文件
parsed = sqlparse.parse(sql_content)

# 提取UNION ALL语句中的信息
pattern = re.compile(r"SELECT f\.\*, '([^']+)' AS category FROM v_customer_accountflow f WHERE f\.direction=([-\d]+) AND f\.transactiontype='([^']+)'(?: AND f\.accounttype = '([^']+)')?(?: AND f\.balancetype=([-\d]+))?")

results = []
for statement in parsed:
    for match in pattern.finditer(str(statement)):
        category = match.group(1)
        direction = int(match.group(2))
        transactiontype = match.group(3)
        accounttype = match.group(4) if match.group(4) else None
        balancetype = int(match.group(5)) if match.group(5) else None
        
        result = {
            'category': category,
            'direction': direction,
            'transactiontype': transactiontype,
            'accounttype': accounttype,
            'balancetype': balancetype
        }
        results.append(result)

# 转换为JSON格式
json_output = json.dumps(results, ensure_ascii=False, indent=4)
print(json_output)