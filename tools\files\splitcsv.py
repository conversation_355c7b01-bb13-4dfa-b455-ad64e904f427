import csv
import os

input_file = "d:/TMP/s.csv"
output_dir = "D:/TMP/output"

if not os.path.exists(output_dir):
    os.makedirs(output_dir)

with open(input_file, "r", encoding="utf-8") as infile:
    reader = csv.reader(infile)
    header = next(reader)

    # Create a dictionary to store the data for each unique value in the second column
    data_dict = {}
    for row in reader:
        split_column_value = row[3]
        if split_column_value not in data_dict:
            data_dict[split_column_value] = []
        data_dict[split_column_value].append(row)

    # Write the data to separate CSV files
    for key, rows in data_dict.items():
        output_file = os.path.join(output_dir, f"{key}.csv")
        with open(output_file, "w", newline="", encoding="utf-8") as outfile:
            writer = csv.writer(outfile, quoting=csv.QUOTE_ALL)
            writer.writerow(header)
            writer.writerows(rows)
