CREATE OR REPLACE TYPE KV_OBJECT_TYPE AS OBJECT (
   K varchar2(100),
   V varchar2(1000)
);

CREATE OR REPLACE TYPE KV_type IS TABLE OF KV_OBJECT_TYPE;

CREATE OR REPLACE FUNCTION GET_KV(str VARCHAR2)
RETURN KV_type
IS
   kv KV_OBJECT_TYPE;
   tab_kv KV_type:=KV_type();
   i_tmp number;
BEGIN
  for val in (select regexp_substr(str,'[^,]+', 1, level, 'i') as v from dual connect by level <= length(str)-length(regexp_replace(str, ',', ''))+1) loop
    i_tmp := instr(val.v, '=');
    if i_tmp > 0 then
      kv := KV_OBJECT_TYPE(substr(val.v,1,i_tmp-1), substr(val.v, i_tmp+1, length(val.v)));
    else
      kv := KV_OBJECT_TYPE(val.v,null);
    end if;
    tab_kv.extend;
    tab_kv(tab_kv.count) := kv;
  end loop;
  RETURN tab_kv;
END GET_KV;

select K,v from table(get_kv('a=b,a=c,4234,3245,6,7,8,9'));
