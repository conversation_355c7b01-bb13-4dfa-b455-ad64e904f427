# 进件

## 取号

### 数据表

cust_union_inlet_record -- 259新银联商户上游取号表

cust_inlet_record -- 微信、支付宝上游取号表

CUST_NU_REPORT -- 网联取号表

CUST_NU_REPORT_DETAIL -- 网联取号- 网联取号详情表

# 过检

## 压测报告

### 要求

压测报告，参考过检老师提供，其中的并发数据等由老师确定

两份：互联网+POS收单

### 监控服务端资源

用命令 ./nmon -f -t -s30 -c 180 就可以开始，这表示30秒打一次点，一共180次，可以根据情况调整

# 关键问题处理

## 资金平衡

包含商户资金、机构资金

### 资金平衡的记账处理

问题：每笔交易实时记账，后面清算等根据时间切分，然后在记账的过程中会出现后面交易后调用系统，但系统先响应，导致时间差

方案：

1. 交易时对商户打上清算标记，标记可以按小时区分
2. 所有交易均采用延迟记账，决定对该批次（清算标记对应的交易）清算时才记账，全部记账完成后输出商户账户快照

### 账单资金平衡

账单包含两方面的目的：

1. 交易方面的，展现的是交易状态
2. 结算方面的，映射当日可结算金额

参考寻汇的经验，两种账单分开提供给商户是比较好的做法，特别是当部分交易在一些场景下不能T+1结算时，例如微信商户资金管控下的
