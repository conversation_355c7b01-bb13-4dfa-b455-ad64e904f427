﻿create table tmp_cust(customer_code varchar2(40),account_code varchar2(40),batch_no varchar2(10));
create table tmp_stat(customer_code varchar2(40),business_code varchar2(40),total_amount number,total_procedure_fee number,total_count number,last_order timestamp, batch_no varchar2(10));

create procedure sp_refresh_stat(

insert into tmp_stat(customer_code,business_code,total_amount,total_procedure_fee,total_count,last_order,batch_no)
SELECT /*+parallel(10)*/t.CUSTOMER_CODE, t.BUSINESS_CODE ,
       sum(t.TOTAL_FEE), sum(t.PROCEDURE_FEE),count(t.TRANSACTION_NO),max(t.CREATE_TIME)
FROM epsp.TXS_WITHDRAW_TRADE_ORDER t 
  INNER JOIN tmp_cust_0602 c ON t.CUSTOMER_CODE =c.CUSTOMER_code and c.state is null
WHERE t.PAY_STATE ='00' AND t.CREATE_TIME >= timestamp '2021-08-30 00:00:00' AND t.CREATE_TIME < timestamp '2023-05-23 00:00:00'
GROUP BY t.CUSTOMER_CODE ,t.BUSINESS_CODE ;

select * from txs_sql_template

insert into tmp_cust_0602(customer_code, name) select c.CUSTOMER_CODE,c.NAME from epsp.v_cum_customer_info c where c.PLAT_CUSTOMER_CODE='562970003311213';
select * from tmp_cust_0602 where state is null;
select * from tmp_pay_0602;

--update tmp_pay_0602 t set t.batch_no=2 where t.batch_no is null;
---------------OUT------------------------
insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(5)*/t.CUSTOMER_CODE AS 商户编号,t.CUSTOMERNAME AS 商户名称, 
       t.BUSINESS_CODE  AS 业务代码, b.NAME AS 业务名称 ,
       sum(t.TOTAL_FEE) / 100  AS 总金额, sum(t.PROCEDURE_FEE) / 100 AS 总手续费,
       count(t.TRANSACTION_NO) AS 总笔数,to_char(max(t.CREATE_TIME),'yyyy-mm-dd hh24:mi:ss') AS 最后一笔时间
FROM epsp.TXS_WITHDRAW_TRADE_ORDER t 
  INNER JOIN tmp_cust_0602 c ON t.CUSTOMER_CODE =c.CUSTOMER_code and c.state is null
  LEFT JOIN epsp.PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE 
WHERE t.PAY_STATE ='00' AND t.CREATE_TIME >= timestamp '2021-08-30 00:00:00' AND t.CREATE_TIME < timestamp '2023-05-23 00:00:00'
GROUP BY t.CUSTOMER_CODE ,t.CUSTOMERNAME,t.BUSINESS_CODE ,b.NAME ;
-------------------退汇----------------------
select * from txs_tuihui_record
---------------PAY------------------------
insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(5)*/t.CUSTOMER_CODE AS 商户号,t.CUSTOMERNAME AS 商户名称, 
       t.BUSINESS_CODE  AS 业务代码, b.NAME AS 业务名称 ,
       sum(t.AMOUNT) / 100 AS 总金额, sum(t.PROCEDURE_FEE) / 100 AS 总手续费, count(t.TRANSACTION_NO) AS 总笔数, to_char(max(t.CREATE_TIME),'yyyy-mm-dd hh24:mi:ss') AS 最后一笔时间
FROM epsp.TXS_PAY_TRADE_ORDER t 
  INNER JOIN info.tmp_cust_0602 c ON t.CUSTOMER_CODE =c.CUSTOMER_CODE and c.state is null
  LEFT JOIN epsp.PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE 
WHERE t.STATE ='00' AND t.CREATE_TIME >= timestamp '2021-08-30 00:00:00' AND t.CREATE_TIME < timestamp '2023-05-23 00:00:00'
GROUP BY t.CUSTOMER_CODE ,t.CUSTOMERNAME,t.BUSINESS_CODE ,b.NAME ;
-------------------POS-----------------------
insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(5)*/MERCHANT_NO, a.name,'POS', 'POS', sum(AMOUNT) as total_amount, 0 as fee, count(1) as cnt,null
from epsp.ZHY_POSP_RECORD t 
     inner join  tmp_cust_0602 a on t.merchant_no=a.customer_code  and a.state is null
where CREATION_TIME >= timestamp '2021-08-30 00:00:00'
  and CREATION_TIME < timestamp '2023-05-23 00:00:00'
group by MERCHANT_NO,a.name;
-------------------ACS----------------------
insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(5)*/t.CUSTOMER_CODE AS 商户号,c.name,'ACS','ACS' ,
       sum(t.AMOUNT) / 100 AS 总金额, sum(t.PROCEDURE_FEE) / 100 AS 总手续费,
       count(t.TRANSACTION_NO) AS 总笔数,null
FROM epsp.PAS_ACCT_QUOTA_RECORD t
  INNER JOIN info.tmp_cust_0602 c ON t.CUSTOMER_CODE =c.CUSTOMER_CODE and c.state is null
WHERE t.FUND_TYPE ='1' AND t.ACCT_STATE ='00' 
      AND t.CREATE_TIME >= timestamp '2021-08-30 00:00:00' AND t.CREATE_TIME < timestamp '2023-05-23 00:00:00'
GROUP BY t.CUSTOMER_CODE,c.name;
-------------------XX----------------------
insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(5)*/t.customer_code,t.customer_name,'XX','XX',sum(t.amt) / 100 as total_amount, sum(t.procedure_fee) as total_fee, count(0), null
from epsp.chk_xx_tran_record t 
     inner join tmp_cust_0602 c on t.customer_code=c.customer_code and  c.state is null
where t.create_time > timestamp '2021-08-30 00:00:00' and t.create_time < timestamp '2023-05-23 00:00:00'
group by t.customer_code,t.customer_name;
-------------------FZ----------------------
insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(5)*/t.source_customer_code,c.name,'FZ-OUT','FZ-分出',sum(t.amount) / 100 as total_amount,sum(t.procedurefee), count(0) as cnt,null
from epsp.txs_split_record t 
     inner join  tmp_cust_0602 c on t.source_customer_code=c.customer_code  and c.state is null
where t.create_time > timestamp '2021-08-30 00:00:00' and t.create_time < timestamp '2023-05-23 00:00:00'  
      and t.state='3' and t.customer_code <> t.source_customer_code
group by t.source_customer_code,c.name;

insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(5)*/t.customer_code,c.name,'FZ-IN','FZ-分入',sum(t.amount) / 100 as total_amount,sum(t.procedurefee), count(0) as cnt,null
from epsp.txs_split_record t 
     inner join  tmp_cust_0602 c on t.customer_code=c.customer_code  and c.state is null
where t.create_time > timestamp '2021-08-30 00:00:00' and t.create_time < timestamp '2023-05-23 00:00:00'  
      and t.state='3' and t.customer_code <> t.source_customer_code
group by t.customer_code,c.name;

-------------------Refund----------------------
insert into tmp_pay_0602(商户号,商户名称,业务代码,业务名称,总金额,总手续费,总笔数,最后一笔时间)
SELECT /*+parallel(5)*/t.customer_code,t.customername,'Refund', 'Refund',sum(t.refund_fee)/100,sum(t.procedure_fee)/100,count(0), null 
from epsp.txs_refund_pre_order t 
     inner join  tmp_cust_0602 c on t.customer_code=c.customer_code and c.state is null
where t.create_time > timestamp '2021-08-30 00:00:00' and t.create_time < timestamp '2023-05-23 00:00:00' AND t.pay_state='00'
group by t.customer_code,t.customername;

-------------------合计----------------------
select  v.*,a1.availablebalance/100 as 可用余额,a1.floatbalance/100 as 在途余额,a2.availablebalance/100 as 待分账余额
from v_cust_stat_0602 v 
  inner join epsp.acc_account a1 on 'JY-A'||v.商户号=a1.code
  left join epsp.acc_account a2 on 'BJ-B'||v.商户号=a2.code;

-------------分账数据-------------
SELECT /*+parallel(5)*/ t.SOURCE_CUSTOMER_CODE as 分出商户编号, t.SOURCE_CUSTOMERNAME as 分出商户名称, t.CUSTOMER_CODE AS 分入商户编号 ,t.CUSTOMERNAME AS 分入商户名称, t.OUT_TRADE_NO AS 商户单号, t.CREATE_TIME as 创建时间, t.TRANSACTION_NO AS 易票联分账单号, t.PAYTRANSACTION_NO as 原交易单号,
  tso.AMOUNT/100 as 分账总金额元,t.AMOUNT/100 as 被分账金额元, t.pay_procedurefee/100 as 收单手续费元,t.split_procedure_fee/100 as 分账手续费元,(case when tso.procedure_customer_code=t.customer_code then '是' else '否' end) as 是否手续费商户
FROM TXS_SPLIT_RECORD t
INNER JOIN TXS_SPLIT_ORDER tso ON t.TRANSACTION_NO = tso.TRANSACTION_NO
WHERE t.CREATE_TIME >= timestamp '2021-08-30 00:00:00' and t.create_time < timestamp '2023-05-23 00:00:00' 
  and (t.SOURCE_CUSTOMER_CODE IN(select customer_code from info.tmp_cust_0602) OR t.CUSTOMER_CODE IN(select customer_code from info.tmp_cust_0602))
  and t.STATE = 3
ORDER BY t.SOURCE_CUSTOMER_CODE, t.CUSTOMER_CODE, t.transaction_no;

-------------ACS充值-------------
SELECT c.NAME AS 商户名称 , t.CUSTOMER_CODE AS 商户编号 ,to_char(t.CREATE_TIME,'yyyymmddhh24miss') AS 交易时间, 
	t.AMOUNT / 100 AS 交易金额 , 0 AS 余额, 
	t.DEBTOR_ACCOUNT, t.DEBTOR_ACCOUNT_NAME , 
	' ' AS 交易对手开户行,
	t.REMARK AS 交易摘要, TRANSACTION_NO AS 交易流水号, ' ' AS 交易IP
FROM PAS_ACCT_QUOTA_RECORD t
	INNER JOIN CUM_CUSTOMER_INFO c ON t.CUSTOMER_CODE =c.CUSTOMER_CODE
WHERE t.FUND_TYPE ='1' AND t.ACCT_STATE ='00'
	AND t.CUSTOMER_CODE in(select customer_code from info.tmp_cust_0602);

-------------出金交易-------------
SELECT /*+parallel(5)*/ t.CUSTOMER_CODE AS 商户编号, t.CUSTOMERNAME AS 商户名称, t.OUT_TRADE_NO AS 商户单号, t.TRANSACTION_NO AS 易票联单号, 
 to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 下单时间, to_char(t.END_TIME,'yyyy-mm-dd hh24:mi:ss') AS 完成时间 , 
 (CASE WHEN t.PAY_STATE='00' THEN '成功' WHEN t.PAY_STATE='01' THEN '失败' WHEN t.PAY_STATE='03' THEN '处理中' ELSE t.PAY_STATE END) AS 订单状态, 
 t.TOTAL_FEE / 100 AS 订单金额 , t.PROCEDURE_FEE / 100 AS 手续费金额, 
 t.CARD_NO AS 提现卡, t.CARD_NO_CIPHER AS enc_card_no, t.BANK_USER_NAME_FULL AS 持卡人,
 t.BANK_NAME,t.REMARK ,
 t.CHANNEL_ORDER AS 上游单号,t.BATCH_NO AS 批量代付批号
FROM TXS_WITHDRAW_TRADE_ORDER t
WHERE t.CREATE_TIME >= timestamp '2021-08-30 00:00:00' AND t.CREATE_TIME < timestamp '2023-05-23 00:00:00' 
  AND t.CUSTOMER_CODE IN(select customer_code from info.tmp_cust_0602) 
  and t.pay_state='00';

-------------线上收单交易-------------
SELECT /*+parallel(t,5)*/t.CUSTOMER_CODE AS 商户编号 ,t.CUSTOMERNAME AS 商户名称 , 
  t.BUSINESS_CODE AS 业务代码 ,b.NAME AS 业务名称, 
  t.OUT_TRADE_NO AS 商户单号, t.TRANSACTION_NO AS 易票联单号, 
  to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 下单时间, to_char(t.END_TIME,'yyyy-mm-dd hh24:mi:ss') AS 完成时间 , 
  (CASE t.STATE WHEN '00' THEN '成功' WHEN '01' THEN '失败' WHEN '03' THEN '处理中' ELSE t.STATE END) AS 订单状态, 
  t.AMOUNT / 100 AS 订单金额 , t.ACTUAL_PAY_AMOUNT / 100 AS 应付金额, t.CASH_AMOUNT / 100 AS 实付金额, t.DISCOUNTABLE_AMOUNT AS 优惠金额 , t.PROCEDURE_FEE / 100 AS 手续费金额, t.REFUND_FEE / 100 AS 退款金额, 
  (CASE t.CARD_TYPE WHEN 'D' THEN '贷记卡' WHEN 'C' THEN '借记卡' ELSE null END)AS 付款卡类型, t.BANK_CODE AS 付款卡开户行,
  t.CHANNEL_ORDER AS 上游单号, c.INSTITUTION_NAME AS 交易机构,
  t.CLIENT_IP AS 交易IP,
  t.REMARK AS 交易摘要
FROM TXS_PAY_TRADE_ORDER t 
  INNER JOIN CUST_CUSTOMER cc ON t.CUSTOMER_CODE =cc.CUSTOMER_NO 
  LEFT JOIN CUM_INSTITUTION c ON t.INSTITUTION_ID =c.ID
  LEFT JOIN PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE 
WHERE t.CREATE_TIME >= timestamp '2021-08-30 00:00:00' AND t.CREATE_TIME < timestamp '2023-05-23 00:00:00' 
  AND t.state='00' AND t.transaction_type <> 'ZHFZ' AND cc.CUSTOMER_NO IN (select customer_code from info.tmp_cust_0602)
ORDER BY t.CUSTOMER_CODE;

-------------退款交易记录-------------
SELECT t.CUSTOMER_CODE as 商户号, t.CUSTOMERNAME as 商户名称, t.OUT_REFUND_NO as 退款申请单号, t.transaction_no as 退款交易单号, t.REFUND_FEE/100 as 退款申请金额
FROM TXS_REFUND_PRE_ORDER t
WHERE t.create_time > timestamp '2021-08-30 00:00:00' and t.create_time < timestamp '2023-05-23 00:00:00' 
  AND t.CUSTOMER_CODE  IN (select customer_code from info.tmp_cust_0602) AND t.PAY_STATE ='00';


---------------------------------------------------------------------------
--退汇交易
SELECT t.CUSTOMER_CODE AS 商户编号, t.CUSTOMERNAME AS 商户名称, t.TRANSACTION_NO AS 退汇单号, t.OUT_TRADE_NO AS 原商户单号,h.orgi_txn_no as 原交易单号,
  to_char(h.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 退汇时间, 
  t.actual_fee / 100 AS 原订单金额 , t.PROCEDURE_FEE / 100 AS 原订单手续费,
  t.CARD_NO_CIPHER AS enc_card_no,
  t.CARD_NO AS 提现卡, t.BANK_USER_NAME_FULL AS 持卡人,
  t.BANK_NAME AS 银行名称,t.REMARK AS 备注,
  t.CHANNEL_ORDER AS 上游单号
FROM TXS_WITHDRAW_TRADE_ORDER t
  INNER JOIN txs_tuihui_record h on t.transaction_no=h.orgi_txn_no
  INNER JOIN CUST_CUSTOMER cc ON t.CUSTOMER_CODE =cc.CUSTOMER_NO
WHERE t.CUSTOMER_CODE in (select customer_code from info.tmp_cust_0608);

--线上收单交易
SELECT /*+parallel(10)*/ t.CUSTOMER_CODE AS 商户编号 ,t.CUSTOMERNAME AS 商户名称 ,
  t.BUSINESS_CODE AS 业务代码 ,b.NAME AS 业务名称,
  t.OUT_TRADE_NO AS 商户单号, t.TRANSACTION_NO AS 易票联单号,
  to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 下单时间, to_char(t.END_TIME,'yyyy-mm-dd hh24:mi:ss') AS 完成时间 ,
  (CASE t.STATE WHEN '00' THEN '成功' WHEN '01' THEN '失败' WHEN '03' THEN '处理中' ELSE t.STATE END) AS 订单状态,
  t.AMOUNT / 100 AS 订单金额元 , t.ACTUAL_PAY_AMOUNT / 100 AS 应付金额元, t.CASH_AMOUNT / 100 AS 实付金额元, t.DISCOUNTABLE_AMOUNT AS 优惠金额元 , t.PROCEDURE_FEE / 100 AS 手续费金额元, t.REFUND_FEE / 100 AS 退款金额元,
  (CASE t.CARD_TYPE WHEN 'D' THEN '贷记卡' WHEN 'C' THEN '借记卡' ELSE null END)AS 付款卡类型, t.BANK_CODE AS 付款卡开户行,
  t.CHANNEL_ORDER AS 上游单号, c.INSTITUTION_NAME AS 交易机构,
  t.CLIENT_IP AS 交易IP,
  t.REMARK AS 交易摘要
FROM TXS_PAY_TRADE_ORDER t
  LEFT JOIN CUM_INSTITUTION c ON t.INSTITUTION_ID =c.ID
  LEFT JOIN PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE
WHERE t.state='00'
  and t.CUSTOMER_CODE in (select customer_code from info.tmp_cust_0608)
  and t.TRANSACTION_TYPE != 'ZHFZ';

--缴费登记数据
SELECT t.TRANSACTION_NO as 易票联流水号, t.CUSTOMER_CODE as 商户编号, cc.NAME as 商户名称, decode(t.FUND_TYPE, '1','银行转账','2','商户赔付','3','商户退款','4','商户补款','6','结汇','7','收单充值') as 调整类型, t.AMOUNT/100 as 缴费金额元,
  t.DEBTOR_ACCOUNT as 付款人账号, t.DEBTOR_ACCOUNT_NAME as 付款人名称, t.CREATE_TIME as 创建时间
FROM PAS_ACCT_QUOTA_RECORD t
  INNER JOIN CUST_CUSTOMER cc ON t.CUSTOMER_CODE = cc.CUSTOMER_NO
where t.CUSTOMER_CODE in (select customer_code from info.tmp_cust_0608)
  and t.ACCT_STATE = '00' and t.CHANGE_TYPE = '1' and t.fund_type !='7';

--出金交易
SELECT t.CUSTOMER_CODE AS 商户编号, t.CUSTOMERNAME AS 商户名称, t.OUT_TRADE_NO AS 商户单号, t.TRANSACTION_NO AS 易票联单号,
  to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 下单时间, to_char(t.END_TIME,'yyyy-mm-dd hh24:mi:ss') AS 完成时间 ,
  t.actual_fee / 100 AS 订单金额元 , t.PROCEDURE_FEE / 100 AS 手续费金额元,
  t.CARD_NO_CIPHER AS enc_card_no,
  t.CARD_NO AS 提现卡, t.BANK_USER_NAME_FULL AS 持卡人,
  t.BANK_NAME AS 银行名称,t.REMARK AS 备注,
  t.CHANNEL_ORDER AS 上游单号,t.BATCH_NO AS 批量代付批号,
  (case when id<0 then '是' else '否' end) as 是否补单,
  (case when th_state='1' then '是' else '否'end) as 是否退汇
FROM TXS_WITHDRAW_TRADE_ORDER t
  INNER JOIN CUST_CUSTOMER cc ON t.CUSTOMER_CODE =cc.CUSTOMER_NO
WHERE t.pay_state='00' 
  and t.CUSTOMER_CODE in (select customer_code from info.tmp_cust_0608);

--退款订单
SELECT t.CUSTOMER_CODE AS 商户编号 ,t.CUSTOMERNAME AS 商户名称 ,
  b.NAME AS 业务名称,t.OUT_REFUND_NO as 商户退款单号,
   t.TRANSACTION_NO AS 易票联退款单号,
  to_char(t.CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') AS 退款时间, to_char(t.END_TIME,'yyyy-mm-dd hh24:mi:ss') AS 完成时间 ,
  t.REFUND_FEE / 100 AS 退款金额元 , t.BACKPAY_PROCEDUREFEE / 100 AS 退回手续费金额元,
  t.CHANNEL_TRADE_NO AS 上游单号, t.CHANNEL_NAME as 上游渠道名称
FROM TXS_REFUND_PRE_ORDER t
  LEFT JOIN PAS_BUSINESS b ON t.BUSINESS_CODE =b.CODE
WHERE t.PAY_STATE='00'
  and t.CUSTOMER_CODE in (select customer_code from info.tmp_cust_0608);

-------------------------------------------

-------------收单交易手续费-------------
SELECT /*+parallel(10)*/ t.CUSTOMER_CODE AS 商户编号 ,c.NAME AS 商户名称 ,
  to_char(t.CREATE_TIME,'yyyy-mm') AS 月份,
  sum(t.procedure_fee) / 100 AS 手续费
FROM TXS_PAY_TRADE_ORDER t
  INNER JOIN info.tmp_cust_0602 c ON t.customer_code =c.customer_code
WHERE t.state='00'
  and t.TRANSACTION_TYPE != 'ZHFZ'
GROUP BY t.CUSTOMER_CODE,c.NAME,
  to_char(t.CREATE_TIME,'yyyy-mm');
  
-------------出金交易手续费-------------
SELECT /*+parallel(10)*/ t.CUSTOMER_CODE AS 商户编号, c.NAME AS 商户名称, to_char(t.CREATE_TIME,'yyyy-mm') AS 月份, 
  sum(t.procedure_fee)/100 as 手续费
FROM TXS_WITHDRAW_TRADE_ORDER t
  INNER JOIN info.tmp_cust_0602 c ON t.CUSTOMER_CODE =c.CUSTOMER_CODE
WHERE t.pay_state='00'
GROUP BY t.CUSTOMER_CODE,c.NAME,
  to_char(t.CREATE_TIME,'yyyy-mm');
  
-------------缴费手续费-------------
SELECT t.CUSTOMER_CODE as 商户编号, c.NAME as 商户名称, to_char(t.CREATE_TIME,'yyyy-mm') AS 月份, 
  sum(t.procedure_fee)/100 as 手续费
FROM PAS_ACCT_QUOTA_RECORD t
  INNER JOIN info.tmp_cust_0602 c ON t.CUSTOMER_CODE = c.CUSTOMER_CODE
WHERE t.ACCT_STATE = '00' and t.CHANGE_TYPE = '1' and t.Fund_Type!='7'
GROUP BY t.CUSTOMER_CODE,c.NAME,
  to_char(t.CREATE_TIME,'yyyy-mm');
  
-------------统计-------------
select  v.*,a1.availablebalance/100 as 可用余额,a1.floatbalance/100 as 在途余额,a2.availablebalance/100 as 待分账余额
from v_cust_stat_0609 v 
  inner join epsp.acc_account a1 on 'JY-A'||v.商户号=a1.code
  left join epsp.acc_account a2 on 'BJ-B'||v.商户号=a2.code;


select * from meta_columns t where t.field_name='CANCEL_WITHDRAW_ID';
select t.institution_name,to_char(t.create_time,'yyyy-mm-dd') as trans_date,count(0) as trans_count from clr_pay_record t where t.create_time > to_date(to_char(sysdate - 2,'yyyymmdd'),'yyyymmdd') group by t.institution_name,to_char(t.create_time,'yyyy-mm-dd') order by t.institution_name,to_char(t.create_time,'yyyy-mm-dd');
