# 商户迁移子公司（或部门）

1、修改所属商户的company_id

update cust_customer set company_id=new_pas_company_id where customer_id in()

update cust_customer_draft set company_id=new_pas_company_idwhere customer_id in()

update cum_customer_info set company_name=new_pas_company_id,customer_id=xx where customer_code in()

2、修改商户业务员ID

update cust_customer t set company_id=new_pas_company_id, t.business_man_id=new_pas_user_id where customer_code =xx;

update cust_customer_draft t set company_id=new_pas_company_id, t.business_man_id=new_pas_user_id where customer_code =xx;

update cum_customer_info t set t.company_name=new_company_name, company_id=, business_man=,business_man_id= where customer_code in()

3、清除缓存

-- 清除缓存，导出脚本到服务器执行
select 'curl -X GET "http://10.200.9.220:8070/redis/hash/delete?key=cum%3Acache1%3A'||d.CUSTOMER_NO||'" -H "accept: */*"' as SQL
from cust_customer_draft d
where d.customer_no in ('562441004460036','562533004484435','562459004485148','562126004484346','562640004484321','562071004485106','562848004485086');
