import pygame
import random
import sys

# 初始化游戏
pygame.init()

# 游戏常量
WIDTH = 800
HEIGHT = 600
CELL_SIZE = 20
FPS = 10
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLACK = (0, 0, 0)

# 初始化显示窗口
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption('Eating snake')
clock = pygame.time.Clock()

class Snake:
    def __init__(self, color):
        self.body = [[WIDTH//2, HEIGHT//2]]
        self.direction = 'RIGHT'
        self.color = color

    def move(self):
        head = self.body[0].copy()
        if self.direction == 'RIGHT':
            head[0] += CELL_SIZE
        elif self.direction == 'LEFT':
            head[0] -= CELL_SIZE
        elif self.direction == 'UP':
            head[1] -= CELL_SIZE
        elif self.direction == 'DOWN':
            head[1] += CELL_SIZE
        self.body.insert(0, head)

class Food:
    def __init__(self):
        self.position = self.randomize_position()

    def randomize_position(self):
        return [
            random.randrange(1, (WIDTH//CELL_SIZE)-1)*CELL_SIZE,
            random.randrange(1, (HEIGHT//CELL_SIZE)-1)*CELL_SIZE
        ]

# 游戏主循环
def main():
    snake = Snake(GREEN)
    food = Food()
    score = 0
    game_over = False

    while True:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_RIGHT and snake.direction != 'LEFT':
                    snake.direction = 'RIGHT'
                elif event.key == pygame.K_LEFT and snake.direction != 'RIGHT':
                    snake.direction = 'LEFT'
                elif event.key == pygame.K_UP and snake.direction != 'DOWN':
                    snake.direction = 'UP'
                elif event.key == pygame.K_DOWN and snake.direction != 'UP':
                    snake.direction = 'DOWN'

        snake.move()

        # 碰撞检测
        if snake.body[0] == food.position:
            food.position = food.randomize_position()
            score += 10
        else:
            # 未吃到食物时移除尾部
            snake.body.pop()

        # 边界碰撞检测
        if (snake.body[0][0] < 0 or snake.body[0][0] >= WIDTH or
            snake.body[0][1] < 0 or snake.body[0][1] >= HEIGHT):
            game_over = True

        # 自身碰撞检测
        for segment in snake.body[1:]:
            if snake.body[0] == segment:
                game_over = True

        # 游戏结束处理
        if game_over:
            font = pygame.font.Font(None, 74)
            text = font.render('Game over', True, WHITE)
            screen.blit(text, (WIDTH//2-140, HEIGHT//2-50))
            
            small_font = pygame.font.Font(None, 36)
            restart_text = small_font.render('Click screen and restart', True, WHITE)
            screen.blit(restart_text, (WIDTH//2-120, HEIGHT//2+50))
            
            score_text = small_font.render(f'Score: {score}', True, WHITE)
            screen.blit(score_text, (10, 10))
            
            pygame.display.update()
            
            waiting = True
            while waiting:
                for event in pygame.event.get():
                    if event.type == pygame.QUIT:
                        pygame.quit()
                        sys.exit()
                    if event.type == pygame.MOUSEBUTTONDOWN:
                        main()
                        waiting = False

        screen.fill(BLACK)
        
        # 绘制蛇和食物
        # 在绘制部分修改颜色参数
        for pos in snake.body:
            pygame.draw.rect(screen, snake.color, (pos[0], pos[1], CELL_SIZE, CELL_SIZE))
        pygame.draw.rect(screen, RED, (food.position[0], food.position[1], CELL_SIZE, CELL_SIZE))

        # 显示实时得分
        font = pygame.font.Font(None, 36)
        score_surface = font.render(f'Score: {score}', True, WHITE)
        screen.blit(score_surface, (10, 10))

        pygame.display.update()
        clock.tick(FPS)

if __name__ == '__main__':
    main()