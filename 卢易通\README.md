# 环境

| 模块 | 服务&版本                         | 说明           |
| ---- | --------------------------------- | -------------- |
| nmd  | tomcat 8.5.100<br />jdk 1.8.0_144 | tomcat部署     |
| as   | tomcat 8.5.95                     | SpringBoot服务 |

## 代码仓库

https://***********:8888/svn/001-DevLib/001-Web/003-Server/109-YD-Projects/0001-Epaylinks/V1.0

## 生产环境

卢易通-易兑信息收集系统项目-生产环境
生产：luyitong.gdyidui.cn

| 编号 | 服务器       | 项目          | 说明          | 监听端口    | IP                            | ZONE   | 应用说明                                  |
| ---- | ------------ | ------------- | ------------- | ----------- | ----------------------------- | ------ | ----------------------------------------- |
| 1    | WEB服务      | Nginx         | web反代       | 80/443      | ***********                   | Nginx  |                                           |
| 2    | 前端服务     | 前端+后端应用 | 前端+后端应用 |             | ************** ************** | 应用层 |                                           |
| 3    | 中间件服务器 | FastDFS       | 文件存储      | 11211 23000 | ************** ************** |        | 生产跨境FastDFS Redis:**************:6379 |
| 4    | 数据库服务器 | Oracle19      | Oracle19      | 1521        | *************                 |        | ip:************* 实例wwxm                 |

短信配置
sms:
  enable: true
  aliyun:
    url: dysmsapi.aliyuncs.com
    AppKey:       "LTAI5tLCzQXc7RG3YvFxP1s4",
    AppSecret:    "******************************",
    TemplateCode: "SMS_300446212",
    SignName:     "广东易兑信息技术有限公司",

## 测试环境

卢易通-易兑信息收集系统项目-测试环境
生产：luyitong.gdyidui.cn
开发：luyitong-dev.gdyidui.cn

| 编号 | 服务器       | 项目          | 说明          | 监听端口    | IP                        | ZONE   | 应用说明                                   |
| ---- | ------------ | ------------- | ------------- | ----------- | ------------------------- | ------ | ------------------------------------------ |
| 1    | WEB服务      | Nginx         | web反代       | 80/443      | ***********               | Nginx  |                                            |
| 2    | 前端服务     | 前端+后端应用 | 前端+后端应用 |             | ************              | 应用层 |                                            |
| 3    | 中间件服务器 | FastDFS       | 文件存储      | 11211 23000 | ************ ************ |        | 测试跨境FastDFS redis ************:6379    |
| 4    | 数据库服务器 | Oracle19      | Oracle19      | 1521        | ************              |        | 创建用户lyt_dev/lyt_dev和lyt_test/lyt_test |
