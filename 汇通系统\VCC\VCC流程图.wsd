@startuml 开卡流程
title 开卡流程

actor 商户 as mer
actor 运营 as op
participant 汇通系统 as ht
participant 寻汇VCC as vcc

mer -> ht: 申请开户（服贸）
op -> ht: 商户开户审核
mer -> ht: 申请开通商务卡业务
ht -> ht: 申请单：待初审
op -> ht: 业务审核
alt 通过
    ht -> ht: 申请单：通过
    ht -> vcc: 创建虚拟卡（接口：*******）
    vcc --> ht: 返回虚拟卡信息

    ht --\ mer: 发送通知邮件
end


@enduml

@startuml 充值&回款流程
title 充值&回款流程

actor 商户 as mer
actor 运营 as op
participant 汇通系统 as ht
participant EPSP
participant 银行 as bank
participant 跨境 as kj
participant 寻汇VCC as vcc

==充值下单==
mer -> ht: 充值请求
ht -> EPSP: 订单支付转账请求
note left of EPSP
以易彩乐身份下单
无手续费，充值额=订单金额
选择只能使用订单转账支付
end note
EPSP --> ht: 订单支付链接
ht -> ht: 充值订单：支付中
ht --> mer: 订单支付链接

==支付加额==
mer -> bank: 订单支付
bank --> mer: 支付成功
bank -\ EPSP: 订单支付异步通知
EPSP -> EPSP: 对易彩乐加额
EPSP -\ ht: 订单支付异步通知
ht -> ht: 充值订单：待提交材料
loop 直到审核通过，否则一直待提交材料
    mer -> ht: 提交交易清单
    op -> ht: 审核清单
    alt 审核通过
        ht -> ht: 商户卡CNY账户加额
        ht -> ht: 充值订单：成功
    end
end

==资金回款（出境）==
alt 开通境外收款人（借助跨境收款人管理功能获取，汇通无需开发）
    ht -> vcc: 添加易票联国际在寻汇开的VA作为境外收款人
    note right of ht
        Swagger 操作
        涉及接口：3.4.2境外收款人申请、3.4.1境外收款人查询
    end note
end

op -> ht: 申请资金回款
ht -> ht: 组装服贸申报清单Excel\n（取数规则？）
ht -> kj: 交易申报
note left of kj
    涉及接口：
    3.4.7服务类交易申报、
    3.4.8服务类交易申报查询
end note
kj -> kj: 审核申报材料
alt 审核通过
    ...材料申报给建行...
    loop 直到成功，否则继续付款申请
        ht -> kj: 付款申请(接口：3.4.10全球付款申请)
        kj --> ht: 付款申请已受理
        ...手动付款到寻汇VA...
        kj -\ ht: 全球付款结果通知（3.4.12全球付款结果通知）
        ht -> ht: 汇款单状态：成功
    end
end

==资金转入==
mer -> ht: 资金转入请求
ht -> ht: 本地换汇，CNY -> USD\nCNY减额，USD加额
note right of ht
    本地USD账户映射上游卡账户USD账户
end note
ht -> vcc: 调用寻汇转入接口
note left of vcc
    涉及接口：3.1.4 转入(多币种钱包转入卡账户余额)
    上游（寻汇）卡账户余额USD金额增加，多币种钱包余额USD金额减少
end note
vcc --> ht: 返回转入结果
ht -\ mer: 异步通知转入结果

==账户余额查询==
op -> ht: 查询账户余额
ht -> vcc: 查询账户余额
note left of vcc
    涉及接口：
    3.1.1卡账户余额查询、
    3.1.3多币种钱包余额查询
    涉及每个上游账户ID
end note
vcc --> ht: 返回账户余额（多币种余额）
ht --> op: 返回账户余额

==查询交易流水==
loop 定时任务查询，每个上游账号ID逐个执行
    ht -> vcc: 查询交易流水，查询当日所有交易
    note left of vcc
        涉及接口：
        3.2.7授权记录查询、
        3.2.8结算记录查询
    end note
    vcc --> ht: 返回交易流水
    ht -> ht: 交易流水入库&计算交易手续费
    ht -> ht: 账户余额更新
end
@enduml

@startuml 回款&转入流程V2
title 回款&转入流程V2

actor 商户 as mer
actor 运营 as op
participant 汇通系统 as ht
participant 全球付 as va
participant 寻汇VCC as vcc

==创建易票联国际账户==
op -> ht: 创建易票联国际账户(epay_intl)
op -> va: 创建易票联国际账户VA

==资金回款(付款到寻汇多币种账户)==
op -> ht: 申请资金回款
ht -> ht: 创建付款单
ht -> va: 付款到寻汇多币种账户
note left of va
    接口：3.4.8付款申请
    收款人：寻汇多币种账户
    付款人：易票联国际账户VA
    本地epay_intl VA账户减额
end note
va --> ht: 返回付款结果
alt 付款成功
    ht -> ht: 更新付款单状态
    ht -> va: 查询付款手续费
    note left of va
        接口：3.4.10付款手续费查询
    end note
    va --> ht: 返回付款手续费
else 付款失败
    ht -> ht: 付款单状态回滚
    ht -> ht: 本地epay_intl VA账户回滚加额
end
==资金转入==
mer -> ht: 资金转入请求
ht -> va: 申请资金调拨
note left of va
    资金从客户VA账户转入易票联国际VA账户
    接口：缺接口？？
    客户VA账户减额
    epay_intl VA账户加额（到冻结户）
end note
ht -> vcc: 调用寻汇转入接口
note left of vcc
    接口：3.1.4 转入(多币种钱包转入卡账户余额)
end note
vcc --> ht: 返回转入结果
alt 转入成功
    ht -> ht: 解冻资金，账户加额
    note right of ht
        本地epay_intl VA账户加额（解冻）
        客户本地VCC账户加额
    end note
else 转入失败
    ht -> ht: 记账回滚
    note right of ht
        本地epay_intl账户减额(冻结户)
        客户VA账户加额
    end note
end
ht --> ht: 更新转入结果

==账户余额查询==
op -> ht: 查询账户余额
ht -> vcc: 查询账户余额
note left of vcc
    涉及接口：
    3.1.1卡账户余额查询、
    3.1.3多币种钱包余额查询
    涉及每个上游账户ID
end note
vcc --> ht: 返回账户余额（多币种余额）
ht --> op: 返回账户余额

==查询交易流水==
loop 定时任务查询，每个上游账号ID逐个执行
    ht -> vcc: 查询交易流水，查询当日所有交易
    note left of vcc
        涉及接口：
        3.2.7授权记录查询、
        3.2.8结算记录查询
    end note
    vcc --> ht: 返回交易流水
    ht -> ht: 交易流水入库&计算交易手续费
    ht -> ht: 账户余额更新
end
@enduml