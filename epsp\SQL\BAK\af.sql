
SELECT POLICE                 AS "移送公安机关",
       OPERATOR_NAME          AS "经办人姓名",
       OPERATOR_PHONE_NUMBER  AS "经办人电话",
       DESCRIBE               AS "违法事实描述",
       AUDIT_TIME             AS "公安业务审核通过时间",
       PERIOD_VALIDITY        AS "有效期",
       START_DATE             AS "惩戒开始日期",
       END_DATE               AS "惩戒结束日期",
       DECIDED_DATE           AS "做出惩戒决定日期",
       BATCH_NO               AS "批次号",
       RESERVED1              AS "预留字段 1",
       RESERVED2              AS "预留字段 2",
       RESERVED3              AS "预留字段 3",
       RESERVED4              AS "预留字段 4",
       RESERVED5              AS "预留字段 5",
       TARGET_ORGANIZATION_ID AS "接收机构 Id",
       REMARK                 AS "名单说明",
       CREATE_TIME            AS "建表时间",
       ID                     AS "ID",
       TX_CODE                AS "交易类型编码 A00701",
       MESSAGE_FROM           AS "发送机构编号",
       TRANS_SERIAL_NUMBER    AS "传输报文流水号（参见附录 H）",
       APPLICATION_ID         AS "业务申请编号（参见附录 I）",
       APPLICATION_ORG_ID     AS "申请机构编码",
       APPLICATION_TIME       AS "申请时间",
       UPDATE_TYPE            AS "更新类型:01-增加；02-删除",
       CASE_TYPE              AS "案件类型 0011-个人、0012-单位",
       NAME                   AS "名称 ",
       ID_TYPE                AS "惩戒账户类型",
       ID_NUMBER              AS "惩戒证件号码",
       ADMINIS_CODE           AS "户籍地或单位注册地地区代码",
       ADMINIS_NAME           AS "户籍地或单位注册地地区名称",
       EDUCATION              AS "学历（仅针对个人）",
       ORGANIZATION_ID        AS "机构 Id",
       ACCOUNT_NAME           AS "开户名称（户名）",
       ACCOUNT_OPEN_TIME      AS "开户日期",
       LEGAL_REPRESENTATIVE   AS "法定代表人姓名",
       LEGAL_ID_TYPE          AS "法定代表人证件类型",
       LEGAL_ID_NUMBER        AS "法定代表人证件号",
       ILLEGAL_TYPE           AS "违法类型"
  FROM RISK_A00701 ra
 WHERE CREATE_TIME > TIMESTAMP '2022-06-01 00:00:00';
 
SELECT ID                   AS "ID",
       TX_CODE              AS "交易类型编码 A00701",
       MESSAGE_FROM         AS "发送机构编号",
       TRANS_SERIAL_NUMBER  AS "传输报文流水号",
       APPLICATION_ID       AS "业务申请编号",
       APPLICATION_ORG_ID   AS "申请机构编码",
       APPLICATION_TIME     AS "申请时间",
       UPDATETYPE           AS "更新类型",
       CASETYPE             AS "案件类型",
       ACCOUNTTYPE          AS "0-卡；1-存折；2-对公；3-账户",
       ACCOUNT              AS "涉案账号",
       ORGANIZATIONID       AS "机构 ID",
       NAME                 AS "名称",
       DEPOSITBANKBRANCH    AS "开户网点",
       DEPOSITBANKBRANCHPRO AS "开户网点所在省",
       DEPOSITBANKBRANCHCIT AS "开户网点所在市",
       DEPOSITBANKBRANCHDIS AS "开户网点所在区（县）",
       ACCOUNTNAME          AS "开户名称（户名）",
       ACCOUNTOPENTIME      AS "开户日期",
       LEGALREPRESENTATIVE  AS "法人姓名",
       LEGALIDTYPE          AS "法人证件类型",
       LEGALIDNUMBER        AS "法人证件号",
       POLICE               AS "移送公安机关",
       OPERATORNAME         AS "经办人姓名",
       OPERATORPHONENUMBER  AS "经办人电话",
       AUDITTIME            AS "公安审核通过时间",
       PERIODVALIDITY       AS "有效期",
       TARGETORGANIZATIONID AS "接收机构 ID",
       REMARK               AS "名单说明",
       CREATE_TIME          AS "CREATE_TIME"
  FROM RISK_A00703 ra
 WHERE CREATE_TIME > TIMESTAMP '2022-06-01 00:00:00';
 
SELECT ID                  AS "ID",
       TX_CODE             AS "交易编码 ",
       MESSAGE_FROM        AS "发送机构编号",
       TRANS_SERIAL_NUMBER AS "传输报文流水号（参见附录 H）",
       APPLICATION_ID      AS "业务申请编号（参见附录 I）",
       FILE_NAME           AS "文件名",
       INSERT_TIME         AS "插表时间"
  FROM RISK_CONTROL_ATTACHMENT rca
 WHERE INSERT_TIME > TIMESTAMP '2022-06-01 00:00:00';
 
SELECT EXPIRE_TIME                 AS "止付截止时间",
       APPLICATION_TIME            AS "申请时间",
       APPLICATION_ORG_ID          AS "申请机构编码",
       APPLICATION_ORG_NAME        AS "申请机构名称",
       OPERATOR_ID_TYPE            AS "经办人证件类型",
       OPERATOR_ID_NUMBER          AS "经办人证件号",
       OPERATOR_NAME               AS "经办人姓名",
       OPERATOR_PHONE_NUMBER       AS "经办人电话",
       INVESTIGATOR_ID_TYPE        AS "协查人证件类型",
       INVESTIGATOR_ID_NUMBER      AS "协查人证件号",
       INVESTIGATOR_NAME           AS "协查人姓名",
       APPLICATION_TYPE            AS "是否补流程",
       ORIGINAL_APPLICATION_ID     AS "原举报申请编号",
       TRANSACTION_DATE            AS "交易时间",
       BANK_ID                     AS "银行编号",
       CASEDOCUMENT_ID             AS "冻结法律文书号",
       ID_TYPE                     AS "冻结账户主体证件类型",
       ID_NUMBER                   AS "冻结账号主体证件号码",
       FREEZE_TYPE                 AS "冻结方式",
       BALANCE                     AS "金额",
       INQUIRY_MODE                AS "查询内容",
       ACCOUNT_NUMBER              AS "帐卡号",
       ACCOUNT_OWNER_NAME          AS "开户主体姓名或企业名称",
       ACCOUNT_OWNER_ID_TYPE       AS "开户主体证件类型",
       INSERT_TIME                 AS "记录插表时间",
       ID                          AS "数据库序列",
       TX_CODE                     AS "报文代码",
       MESSAGE_FROM                AS "发送机构编号",
       TRANS_SERIAL_NUMBER         AS "传输报文流水号",
       FILE_PATH                   AS "文件路径？",
       APPLICATION_ID              AS "业务申请编号",
       CASE_NUMBER                 AS "案件编号",
       CASE_TYPE                   AS "案件类型",
       TRANSFER_OUT_BANK_ID        AS "付款方卡银行编码",
       TRANSFER_OUT_BANK_NAME      AS "付款方卡银行机构编码",
       TRANSFER_OUT_ACCOUNT_NAME   AS "转出账户名",
       TRANSFER_OUT_ACCOUNT_NUMBER AS "(受害人)转出帐号",
       CURRENCY                    AS "币种",
       TRANSFER_AMOUNT             AS "转出金额(单位到元)",
       TRANSFERTIME                AS "转出时间",
       ONLINE_PAY_COMPANY_ID       AS "止付支付机构编号",
       ONLINE_PAY_COMPANY_NAME     AS "止付支付机构名称",
       SUBJECT_TYPE                AS "止付账号类别",
       DATA_TYPE                   AS "止付传入参数类型",
       DATA                        AS "止付操作的传入参数",
       REASON                      AS "止付事由",
       REMARK                      AS "止付说明",
       START_TIME                  AS "止付起始时间"
  FROM RISK_CONTROL_MESSAGE rcm
 WHERE INSERT_TIME > TIMESTAMP '2022-06-01 00:00:00';

SELECT ID                      AS "ID",
       TX_CODE                 AS "交易编码",
       MESSAGE_FROM            AS "发送机构编号",
       TRANS_SERIAL_NUMBER     AS "传输报文流水号",
       EPAYLINKS_SERIAL_NUMBER AS "易票联订单号",
       APPLICATION_ID          AS "业务申请编号",
       MESSAGE_MODE            AS "未使用",
       MESSAGE_TO              AS "接收机构编号",
       PRIORITY                AS "优先级",
       TIMES                   AS "次数",
       DIRECTION               AS "报文方向",
       INT_ACTION              AS "执行类别",
       BEGIN_TIME              AS "开始时间",
       END_TIME                AS "结束时间",
       STATE                   AS "状态",
       RESULT_CODE             AS "应答码",
       RESP_CODE               AS "返回码",
       RESP_DESCRIPTION        AS "返回描述",
       DT_CREATE_TIME          AS "创建时间",
       DT_MODIFY_TIME          AS "修改时间",
       VC_MEMO                 AS "请求类型?",
       INSERT_TIME             AS "插表时间"
  FROM RISK_CONTROL_RESULT rcr
 WHERE INSERT_TIME > TIMESTAMP '2022-06-01 00:00:00';

SELECT wmsys.wm_concat(a.COLUMN_NAME || ' AS "' || (CASE WHEN b.COMMENTS IS NULL THEN a.column_name ELSE b.comments END) || '"')  
FROM USER_TAB_COLS a INNER JOIN USER_COL_COMMENTS b ON a.TABLE_NAME =b.TABLE_NAME AND a.COLUMN_NAME =b.COLUMN_NAME  
WHERE a.TABLE_NAME ='RISK_CONTROL_RESULT' ORDER BY a.column_id;
