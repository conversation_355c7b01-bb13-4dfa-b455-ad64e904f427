create or replace view v_cust_stat as
select t.商户号,t.商户名称, 
SUM(case when 业务代码='WxJSAPI' then 总金额 else null end) as 微信公众号支付 , 
SUM(case when 业务代码='WxJSAPI' then 总笔数 else null end) as 微信公众号支付笔数 , 
SUM(case when 业务代码='UnionQrcode' then 总金额 else null end) as 银联二维码被扫支付 , 
SUM(case when 业务代码='UnionQrcode' then 总笔数 else null end) as 银联二维码被扫支付笔数 , 
SUM(case when 业务代码='UnionQrcodeDebitCard' then 总金额 else null end) as 银联二维码被扫支付借记 , 
SUM(case when 业务代码='UnionQrcodeDebitCard' then 总笔数 else null end) as 银联二维码被扫支付借记笔数 , 
SUM(case when 业务代码='UnionSweep' then 总金额 else null end) as 银联二维码主扫支付 , 
SUM(case when 业务代码='UnionSweep' then 总笔数 else null end) as 银联二维码主扫支付笔数 , 
SUM(case when 业务代码='AuthAliMicro' then 总金额 else null end) as 支付宝被扫预授权支付 , 
SUM(case when 业务代码='AuthAliMicro' then 总笔数 else null end) as 支付宝被扫预授权支付笔数 , 
SUM(case when 业务代码='UnionAppCredit' then 总金额 else null end) as 云闪付APP支付贷记 , 
SUM(case when 业务代码='UnionAppCredit' then 总笔数 else null end) as 云闪付APP支付贷记笔数 , 
SUM(case when 业务代码='UnionOnline' then 总金额 else null end) as 银联在线储蓄卡支付 , 
SUM(case when 业务代码='UnionOnline' then 总笔数 else null end) as 银联在线储蓄卡支付笔数 , 
SUM(case when 业务代码='FZ-WxMicro' then 总金额 else null end) as 分账微信被扫支付 , 
SUM(case when 业务代码='FZ-WxMicro' then 总笔数 else null end) as 分账微信被扫支付笔数 , 
SUM(case when 业务代码='AliNative' then 总金额 else null end) as 支付宝主扫支付 , 
SUM(case when 业务代码='AliNative' then 总笔数 else null end) as 支付宝主扫支付笔数 , 
SUM(case when 业务代码='WxMiniProgram' then 总金额 else null end) as 微信小程序支付 , 
SUM(case when 业务代码='WxMiniProgram' then 总笔数 else null end) as 微信小程序支付笔数 , 
SUM(case when 业务代码='ProtocolPay' then 总金额 else null end) as 储蓄卡快捷协议支付 , 
SUM(case when 业务代码='ProtocolPay' then 总笔数 else null end) as 储蓄卡快捷协议支付笔数 , 
SUM(case when 业务代码='WxMicro' then 总金额 else null end) as 微信被扫支付 , 
SUM(case when 业务代码='WxMicro' then 总笔数 else null end) as 微信被扫支付笔数 , 
SUM(case when 业务代码='SavingCardPay' then 总金额 else null end) as 个人网银储蓄卡支付 , 
SUM(case when 业务代码='SavingCardPay' then 总笔数 else null end) as 个人网银储蓄卡支付笔数 , 
SUM(case when 业务代码='AliMicro' then 总金额 else null end) as 支付宝被扫支付 , 
SUM(case when 业务代码='AliMicro' then 总笔数 else null end) as 支付宝被扫支付笔数 , 
SUM(case when 业务代码='FZ-WxJSAPI' then 总金额 else null end) as 分账微信公众号支付 , 
SUM(case when 业务代码='FZ-WxJSAPI' then 总笔数 else null end) as 分账微信公众号支付笔数 , 
SUM(case when 业务代码='AuthWxMicro' then 总金额 else null end) as 微信被扫预授权支付 , 
SUM(case when 业务代码='AuthWxMicro' then 总笔数 else null end) as 微信被扫预授权支付笔数, 
SUM(case when 业务代码='ProtocolPayCredit' then 总金额 else null end) as 信用卡快捷协议支付 , 
SUM(case when 业务代码='ProtocolPayCredit' then 总笔数 else null end) as 信用卡快捷协议支付笔数, 
SUM(case when 业务代码='UnionJS' then 总金额 else null end) as 银联二维码JS支付 , 
SUM(case when 业务代码='UnionJS' then 总笔数 else null end) as 银联二维码JS支付笔数 , 
SUM(case when 业务代码='UnionOnlineCredit' then 总金额 else null end) as 银联在线信用卡支付 , 
SUM(case when 业务代码='UnionOnlineCredit' then 总笔数 else null end) as 银联在线信用卡支付笔数 , 
SUM(case when 业务代码='UnionApp' then 总金额 else null end) as 云闪付APP支付借记 , 
SUM(case when 业务代码='UnionApp' then 总笔数 else null end) as 云闪付APP支付借记笔数 , 
--SUM(case when 业务代码='AccountSplit' then 总金额 else null end) as 账户分账 , 
--SUM(case when 业务代码='AccountSplit' then 总笔数 else null end) as 账户分账笔数 , 
SUM(case when 业务代码='FZ-WxMiniProgram' then 总金额 else null end) as 分账微信小程序支付 , 
SUM(case when 业务代码='FZ-WxMiniProgram' then 总笔数 else null end) as 分账微信小程序支付笔数, 
SUM(case when 业务代码='AliJSAPI' then 总金额 else null end) as 支付宝生活号支付 , 
SUM(case when 业务代码='AliJSAPI' then 总笔数 else null end) as 支付宝生活号支付笔数 , 
SUM(case when 业务代码='EnterpriseUnion' then 总金额 else null end) as 企业网银支付 , 
SUM(case when 业务代码='EnterpriseUnion' then 总笔数 else null end) as 企业网银支付笔数 , 
SUM(case when 业务代码='FZ-AliNative' then 总金额 else null end) as 分账支付宝主扫支付 , 
SUM(case when 业务代码='FZ-AliNative' then 总笔数 else null end) as 分账支付宝主扫支付笔数 , 
SUM(case when 业务代码='NfcTagBusiness' then 总金额 else null end) as 银联碰一碰支付 , 
SUM(case when 业务代码='NfcTagBusiness' then 总笔数 else null end) as 银联碰一碰支付笔数 , 
SUM(case when 业务代码='UnionSweepDebit' then 总金额 else null end) as 银联二维码主扫借记 , 
SUM(case when 业务代码='UnionSweepDebit' then 总笔数 else null end) as 银联二维码主扫借记笔数 , 
SUM(case when 业务代码='EntrustPayBatch' then 总金额 else null end) as 批量储蓄卡代收 , 
SUM(case when 业务代码='EntrustPayBatch' then 总笔数 else null end) as 批量储蓄卡代收笔数  ,
SUM(case when 业务代码='POS' then 总金额 else null end) as POS , 
SUM(case when 业务代码='POS' then 总笔数 else null end) as POS笔数, 
SUM(case when 业务代码='XX' then 总金额 else null end) as XX , 
SUM(case when 业务代码='XX' then 总笔数 else null end) as XX笔数, 
SUM(case when 业务代码='ACS' then 总金额 else null end) as ACS , 
SUM(case when 业务代码='ACS' then 总笔数 else null end) as ACS笔数 , 
SUM(case when 业务代码='WithdrawToSettmentDebit' then 总金额 else null end) as 提现 , 
SUM(case when 业务代码='WithdrawToSettmentDebit' then 总笔数 else null end) as 提现笔数 , 
SUM(case when 业务代码='Withdraw' then 总金额 else null end) as 代付到储蓄卡 , 
SUM(case when 业务代码='Withdraw' then 总笔数 else null end) as 代付到储蓄卡笔数,
SUM(case when 业务代码='GlobalPay' then 总金额 else null end) as 全球付款 , 
SUM(case when 业务代码='GlobalPay' then 总笔数 else null end) as 全球付款笔数 , 
SUM(case when 业务代码='FZ' then 总金额 else null end) as FZ , 
SUM(case when 业务代码='FZ' then 总笔数 else null end) as FZ笔数 , 
SUM(case when 业务代码='Refund' then 总金额 else null end) as Refund , 
SUM(case when 业务代码='Refund' then 总笔数 else null end) as Refund笔数
from tmp_pay_0808 t 
group by t.商户号,t.商户名称;

--select * from v_cust_stat;
