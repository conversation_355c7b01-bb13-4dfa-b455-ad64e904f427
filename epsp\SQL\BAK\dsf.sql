--代收付订单
select * from t_dsf_order t where t.app_time>=to_date('********','yyyymmdd') and  rownum<=50
union all
select t.order_id as ORDER_NO,'-' as ACCOUNT_NO,'-' as TRAN_ACCOUNT_NO,'-' as TRAN_ACCOUNT_NAME,'-' as SF_TYPE,
t.business_code BUSI_CODE, t.customer_code MERCHANT_NO, t.terminal_no as TERMINAL_NO, t.out_trade_no as MER_ORDER_NO,'-' as BATCH_NO,'-' as SN,to_char(t.create_time,'yyyymmdd') as TRADE_DATE,
t.procedure_fee / 100 as DEAL_FEE, 0 as OTHER_PAY_FEE,t.procedure_fee / 100 as SERVICE_FEE, 0 as SERVICE_FEE_COST,
t.create_time as APP_TIME, null as OPER_OPNO,t.pay_method as PAY_TYPE, t.pay_passway as PAY_WAY,t.state as STATUS,'-' as STATUS_MEMO,
t.create_time as OPER_TIME, t.end_time as FINISH_TIME,'-' as  SUMMARY, t.remark as MEMO, 
t.channel_resp_code as RET_CODE, t.channel_resp_msg as RET_MSG, t.channel_order as PAY_NO,
'-' as MOB_NO,'-' as EMAIL,'-' as SMS_TYPE,t.card_no_enc as BANK_ACC_NO,null as TO_FLAG, t.bank_code TRAN_BANK_CODE,'-' as TRAN_BANK_ACC_PROP,'-' as TRAN_BANK_ACC_TYPE,
null as REQ_TIME, t.card_no_mosaic as TRAN_BANK_ACC_NO,
'-' as UTC_CODE,'-' as UTC_CODE2,'-' as SERVICE_FEE_WHO,'CNY' as CUR,null as REC_FEE, null as REC_CUR,null as RATE,'-' as UTC_CODE3,
null as CONVERT_AMOUNT,null as CONVERT_FEE, null as EXCHANGE_RATE, null as REVERSE_ORDER_NO, null as ORIGN_ORDER_NO,null as BRANCH_CODE,
t.card_no_enc as BANK_ACC_NO_CIPHER,t.card_no_enc as TRAN_BANK_ACC_NO_CIPHER, t.card_owner_mosaic as TRAN_ACCOUNT_NAME_CIPHER,'-' as MOB_NO_CIPHER,'-' as EMAIL_CIPHER, null as DAIFU_TIME 
from txs_pay_trade_order t where t.state='00' and t.business_code in ('UnionQrcode', 'UnionSweep', 'UnionOnline', 'UnionQrcodeDebitCard', 'EntrustPay', 'EntrustPayCreditBatch', 'NfcTagBusinessDebit', 'UnionAppCredit', 'ProtocolPay', 'QuickPayCredit', 'EntrustPayCredit', 'QuickInstalments', 'SavingCardPay', 'ForeignCard', 'QuickPay', 'ProtocolPayCredit', 'FZ-UnionSweep', 'recharge', 'UnionJS', 'UnionOnlineCredit', 'UnionApp', 'FZ-NocardPayCredit', 'EnterpriseUnion', 'NfcTagBusiness', 'FZ-ProtocolPay', 'EntrustPayBatch', 'UnionSweepDebit');

--代收付签约
select t2.* from t_tbankcs_bankaccmount t2 where t2.MODITIME >=to_date('********','yyyymmdd') and  rownum<=50
union all
select t.bank_card_no_mosaic as PAYACCNO, t.phone_num_mosaic as PAYMOBILE, t.user_name_mosaic as PAYACCNAMECN,
'-' as PAYBRANCH, t.bankcardtype as PAYACCTYPE, '-' as PAYSTORECODE, t.bankcardicon as PAYBANKCODE,'-' as PAYPORTNO,
t.protocol as PAYCONTRACTNO, null as PAYSMSADDR, null as PAYEMAILADDR, null as PAYENABLED, null as UMPSLOSS,
t.createtime as PAYENABLEDTIME, null as UMPSLOSSTIME,t.updatetime as MODITIME, null as MODIUSER, null as PAYORGAN,
null as PAYCITYCODE, null as PAYMODITIME, null as PAYMODIUSER, null as PAYPACTNO,
null as RECMAXNUMBER, null as RECMAXMONEY, null as PAYMAXNUMBER, null as PAYMAXMONEY, null as RECHANDMONEY, null as RECMINMONEY, null as RECAUTOMONEY, null as PAYHANDMONEY, null as PAYMINMONEY, null as PAYAUTOMONEY, null as RECAUTOMINACCOUNT,
null as LOADTOUMPS, null as PAYMAXMONEYDAY, null as RECHANDFEEFLAG, null as PAYHANDFEEFLAG,
t.channel_acq_inscode as ACCEPT_CHANNEL, null as ACCOUNT_TYPE, null as ISDEFAULTACC,
null as IS_SIGN_USER, null as COUNTRY, null as ADRESS, null as EXCHANGE_CURRENCY, null as BANK_NAME, null as PROXY_BANK, 
null as BANK_COUNTRY, null as BANK_CITY, null as SWIFT_CODE, null as ID_TYPE, t.bank_card_no_mosaic as ID_NO, 
null as SING_BEGIN_DATE, null as SING_END_DATE, null as SIGN_DATE, null as SIGN_USER_TEL, null as BIND_SOURCE, null as BSA_TYPE,
null as ACCEPT_CHANNEL_DAIFU, null as CLEARING_CODE, null as CVV2, null as VALIDITY, 
null as PAYSMSADDRCIPHER, t.bankcardno as PAYACCNOCIPHER, t.customername as PAYACCNAMECNCIPHER, null as CVV2CIPHER, null as VALIDITYCIPHER, t.bankcardno as ID_NO_CIPHER, null as PAYEMAILADDRCIPHER, null as ADRESSCIPHER 
from CUM_QUICKPAYCUSTOMERINFO t where state='Valid' and t.createtime > sysdate - 90;

--银联收单订单
select * from posp_order o where o.creation_time >=to_date('********','yyyymmdd') and  rownum<=50
union all
select ID, t.create_time as CREATION_TIME, t.pos_batch_no as BATCH_NO, t.transaction_no as ORDER_NO, t.amount as AMOUNT,t.currency_code as CURRENCY_CODE, 
to_number(t.terminal_code) as TERMINAL_ID,0 as STATUS,t.update_time as MODIFICATION_TIME,t.card_no_hide as ACCOUNT,t.card_no_enc as ACCOUNT_ENCRYPT,
t.procedure_fee as MERCHANT_FEE, t.procedure_fee as COST_FEE,'-' as ACCOUNT_HASH
from POSP_TXN t;

--银联收单交易
select * from posp_transaction pt where pt.creation_time >=to_date('********','yyyymmdd') and  rownum<=50;
