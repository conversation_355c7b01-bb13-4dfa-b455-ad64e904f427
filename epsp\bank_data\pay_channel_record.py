# -*- python3.7 -*-
# -*- coding: utf-8 -*-
import requests
import json
import csv
import cx_Oracle

CUM_SERVER = "http://172.16.2.92:8070/"
DB_CONNECT_STR = 'efps01/efps01@172.16.1.2/testdb'
DB_CONNECT_STR2 = 'info/INfo#2018@10.100.1.25:1521/epsp'

def epccProtocolChannel(channelId, bankCode, feeRate, amountMax, dayTotalLimit):
    headers = {
        "Content-Type": "application/json",
        "x-userid": "0"
    }
            
    data = {"feePer":"0","rate":"0","rateMode":"2","institutionId":18,"categoryId":"11","channelId":"42","channelInstCode":"Z2001944000017",
            "bankCode":"CMB2","amountMin":"0","amountMax":"5000000","dayTotalLimit":"********",
            "timeList":[],"stopStartTime":"","stopEndTime":"","priority":1,"rateStandard":"1",
            "rateTiereds":[{"feeFrom":0,"feeTo":**********,"feeMode":2,"feePer":0,"feeRate":0.1,
                            "orderno":1,"feeFromMode":2,"feeToMode":4,"ladderType":"1"}],
            "ipSupportType":"2","accountType":""}
    data.update({"channelId":channelId, "bankCode":bankCode, 
                 "rate":feeRate, "amountMax":amountMax, "dayTotalLimit":dayTotalLimit})
    data["rateTiereds"][0].update({"feeRate":feeRate})
    response = requests.post(url=CUM_SERVER+"payChannelRecord/create", data=json.dumps(data), headers=headers)
    resp = json.loads(response.text)
    if(resp.get("returnCode") != '0000'):
        print(channelId, bankCode, feeRate, amountMax, dayTotalLimit, resp.get("returnCode"), resp.get("returnMsg"))
    else:
        print(channelId, bankCode,"Success")

def readcsv4EpccProtocolChannel(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        render = csv.reader(f)
        header = next(render)
        print(header)
        for row in render:
            if(row[0] != '' and row[1] != ''):  #借记
                feeRate = 0.1 if row[7] == '1' else 0.15
                epccProtocolChannel(channelId="42", bankCode=row[0], feeRate=feeRate * 10000, amountMax=row[2], dayTotalLimit=row[3])
            if(row[0] != '' and row[4] != ''):  #贷记
                feeRate = 0.2 if row[7] == '1' else 0.3
                epccProtocolChannel(channelId="41", bankCode=row[0], feeRate=feeRate * 10000, amountMax=row[5], dayTotalLimit=row[6])


def unionProtocolChannel(channelId, bankCode, feeRate, amountMax, dayTotalLimit):
    headers = {
        "Content-Type": "application/json",
        "x-userid": "0"
    }
            
    data = {"feePer":"0","rate":"0","rateMode":"2","institutionId":15,"categoryId":"11","channelId":"XX","channelInstCode":"********",
            "bankCode":"XX","amountMin":"0","amountMax":"5000000","dayTotalLimit":"********",
            "timeList":[],"stopStartTime":"","stopEndTime":"","priority":1,"rateStandard":"1",
            "rateTiereds":[{"feeFrom":0,"feeTo":9**********,"feeMode":2,"feePer":0,"feeRate":0.1,
                            "orderno":1,"feeFromMode":2,"feeToMode":4,"ladderType":"1"}],
            "ipSupportType":"2","accountType":""}
    data.update({"channelId":channelId, "bankCode":bankCode, 
                 "rate":feeRate, "amountMax":amountMax, "dayTotalLimit":dayTotalLimit})
    data["rateTiereds"][0].update({"feeRate":feeRate})
    response = requests.post(url=CUM_SERVER+"payChannelRecord/create", data=json.dumps(data), headers=headers)
    resp = json.loads(response.text)
    if(resp.get("returnCode") != '0000'):
        print(channelId, bankCode, feeRate, amountMax, dayTotalLimit, resp.get("returnCode"), resp.get("returnMsg"))
    else:
        print(channelId, bankCode,"Success")

def readcsv4UnionProtocolChannel(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        render = csv.reader(f)
        header = next(render)
        print(header)
        for row in render:
            if row[5] == '' : continue
            if(row[1] == '借记卡'):
                feeRate = 0.1
                unionProtocolChannel(channelId="42", bankCode=row[5], feeRate=feeRate * 10000, amountMax=int(row[2])*100, dayTotalLimit=int(row[3])*100)
            elif row[1] == '贷记卡':
                feeRate = 0.2
                unionProtocolChannel(channelId="41", bankCode=row[5], feeRate=feeRate * 10000, amountMax=int(row[2])*100, dayTotalLimit=int(row[3])*100)
            else:
                print(row)

def selectChannelId():
    db = cx_Oracle.connect(DB_CONNECT_STR)
    try:
        cursor = db.cursor()
        cursor.execute("select listagg(id,',') within group(order by id) as ids from cum_pay_channel_record where create_time > sysdate -1")
        result = cursor.fetchall()
        if result.__len__() > 0: # 查询到数据
            return result[0][0]
        else:
            return ""
    finally:
        db.close()

def enablePayChannel(channelIds, remark):
    headers = {
        "Content-Type": "application/json",
        "x-userid": "0"
    }
    data = {"remark":remark,"payChannelList":channelIds.split(",")}
    json_data = json.dumps(data)
    response = requests.post(url=CUM_SERVER+"payChannelRecord/enablePayChannel", data=json_data, headers=headers)
    resp = json.loads(response.text);
    print(response.text)

if __name__ == "__main__":
    readcsv4EpccProtocolChannel(filename="G:/Python/ESQuery/bank-epcc-********.csv")
    enablePayChannel(channelIds=str(selectChannelId()), remark="********导入") 
    # readcsv4UnionProtocolChannel(filename="G:/Python/ESQuery/bank-union-********.csv")
    # enablePayChannel(channelIds=str(selectChannelId()), remark="********导入")