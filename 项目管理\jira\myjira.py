from jira import JIRA
import pandas as pd
import requests

server = JIRA("http://172.20.4.54:18080", basic_auth=("wanghaifeng", "kingwang"))

def stat_jira2():
    # 获取所有问题的经办人和问题键
    issues = server.search_issues(jql='project = "缺陷管理"', maxResults=None)
    data = [{'Assignee': issue.fields.assignee.displayName, 'IssueKey': issue.key} for issue in issues]

    # 创建DataFrame
    df = pd.DataFrame(data)

    # 统计每个经办人的问题数量
    result = df.groupby('Assignee').count().rename(columns={'IssueKey': 'IssueCount'})


def get_issue_from_filter(filter_id):
    issues = []
    assignee = []
    reporter = []
    status = []
    filter_results = server.search_issues(f'filter={filter_id}', maxResults=1000)
    for issue in filter_results:
        issues.append(issue.id)
        assignee.append(issue.fields.assignee.name)
        reporter.append(issue.fields.reporter.name)
        status.append(issue.fields.status.name)
    return {"assignee": assignee, 
            "reporter": reporter, 
            "status": status,
            "issues": issues}

def test1():
    data = get_issue_from_filter("12100")
    df = pd.DataFrame(data)
    grouped = df.groupby(['assignee', 'status'])
    result = grouped.agg({'issues': 'count'})
    print(result)
    
def stat_jira(version):
    base_url = "http://172.20.4.54:18080/rest/api/2/search"
    username = "wanghaifeng"
    password = "kingwang"
    jql_query = 'project = "BUG" AND 测试版本号 ~ "{}"'.format(version)
    
    response = requests.get(base_url, auth=(username, password), params={'jql': jql_query, 'maxResults': 1000})
    if response.status_code == 200:
        data = response.json()

        # 统计每个经办人和每个状态的ISSUE数量
        assignee_counts = {}
        status_counts = {}
        reporter_counts = {}
        severity_counts = {}
        stage_counts = {}
        pos_counts = {}
        module_counts = {}
        
        sum_count = data['issues'].__len__()
        
        for issue in data['issues']:
            assignee = issue['fields']['assignee']['displayName']
            status = issue['fields']['status']['name']
            reporter = issue['fields']['reporter']['displayName']
            severity = issue['fields']['customfield_10211']['value']
            stage = issue['fields']['customfield_10210']['value']
            pos = issue['fields']['customfield_10205']['value']
            mod = issue['fields']['customfield_10208']['value']

            # 统计每个报告人的ISSUE数量
            if reporter in reporter_counts:
                reporter_counts[reporter] += 1
            else:
                reporter_counts[reporter] = 1
                
            if severity in severity_counts:
                severity_counts[severity] += 1
            else:
                severity_counts[severity] = 1
                
            if stage in stage_counts:
                stage_counts[stage] += 1
            else:
                stage_counts[stage] = 1
                
            if pos in pos_counts:
                pos_counts[pos] += 1
            else:
                pos_counts[pos] = 1
                
            if mod in module_counts:
                module_counts[mod] += 1
            else:
                module_counts[mod] = 1
            
            if status in status_counts:
                status_counts[status] += 1
            else:
                status_counts[status] = 1
                
            # 统计每个经办人的ISSUE数量
            if assignee in assignee_counts:
                if status in assignee_counts[assignee]:
                    assignee_counts[assignee][status] += 1
                else:
                    assignee_counts[assignee][status] = 1
            else:
                assignee_counts[assignee] = {status: 1}

        # 打印每个经办人的ISSUE数量
        print(f"-----版本：{version} BUG数量统计-----")
        print(">>经办人统计：")
        for assignee, status_count in assignee_counts.items():
            count = sum(status_count.values())
            print(f"{assignee}，BUG数：{count}，占比：{count/sum_count*100:.2f}%\n \t {status_count}")
        
        print("\n>>状态统计：")
        for status, count in status_counts.items():
            print(f"{status}，BUG数：{count}，占比：{count/sum_count*100:.2f}%")
        
        print("\n>>报告人统计：")
        for reporter, count in reporter_counts.items():
            print(f"{reporter}，BUG数：{count}，占比：{count/sum_count*100:.2f}%")
        
        print("\n>>严重程度统计：")
        for severity, count in severity_counts.items():
            print(f"{severity}，BUG数：{count}，占比：{count/sum_count*100:.2f}%")
        
        print("\n>>阶段统计：")
        for stage, count in stage_counts.items():
            print(f"{stage}，BUG数：{count}，占比：{count/sum_count*100:.2f}%")
        
        print("\n>>模块统计：")
        for mod, count in module_counts.items():
            print(f"{mod}，BUG数：{count}，占比：{count/sum_count*100:.2f}%")
        
        print("\n>>来源统计：")
        for pos, count in pos_counts.items():
            print(f"{pos}，BUG数：{count}，占比：{count/sum_count*100:.2f}%")
            
        print(f"\n总BUG数：{sum_count}\n======================\n")

    else:
        print("API请求失败，错误代码：", response.status_code)
        print(response)
    
if __name__ == "__main__":
    # stat_jira("6.6.14")
    # stat_jira("6.7.19")
    stat_jira("6.8.3")
    # stat_jira("6.8.16")