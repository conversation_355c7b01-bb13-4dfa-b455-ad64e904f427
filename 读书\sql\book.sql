DELIMITER //

DROP PROCEDURE if exists remove_duplicate_chapters;

CREATE PROCEDURE remove_duplicate_chapters()
BEGIN
    -- 定义变量
    DECLARE done INT DEFAULT FALSE;
    DECLARE book_name VARCHAR(255);

    DECLARE done2 INT DEFAULT FALSE;
    DECLARE CHAPTER_ID BIGINT;

    -- 定义游标
    DECLARE cur CURSOR FOR SELECT book FROM bk_book where perfect=0 and state=1 limit 1,10;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    -- 打开游标
    OPEN cur;
    -- 开始循环
    read_loop: LOOP
        -- 提取数据
        FETCH cur INTO book_name;
        IF done THEN
            LEAVE read_loop;
        END IF;

        -- 删除其他记录
        DECLARE cur2 FOR select id FROM bk_chapter WHERE book = book_name AND id not in(select min(id) from bk_chapter where book=book_name group by book, title);
        DECLARE CONTINUE HANDLER FOR NOT FOUND SET done2 = TRUE;

        OPEN cur2
        FETCH cur2 INTO CHAPTER_ID;
        read_loop2: LOOP
            IF done2 THEN
                LEAVE read_loop2;
            END IF;
            delete from bk_chapter where id=CHAPTER_ID;
        END LOOP;
        update bk_book set perfect=1 where book=book_name;
        commit;
    END LOOP;

    -- 关闭游标
    CLOSE cur;
END //

DELIMITER ;
