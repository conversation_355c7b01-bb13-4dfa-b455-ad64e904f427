from datetime import datetime
import json
import requests

data = {
    "api_name": "gct_mso_dowjones_name_screening",
    "ver": "1.0",
    "format": "json",
    "app_id": "MgkE7sbGTsEoNpZykqaMqWFb",
    "terminal_no": "43181318",
    "device_type": "PC",
    "device_info": "PC WEB",
    "lang": "ZHCN",
    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    "name_list": [
        {
            "assess_id": "140101199807152252",
            "monitor_flag": "0",
            "name": "A HANDLER LOGISTICS CO., LIMITED",
            "name_type": "E",
            "merchant_id": "1234567890123456",
            "cert_type": "0",
            "cert_id": "123456789012345678",
            "country": "中国",
            "sex": "0",
            "birthday": "1998-07-15"
        }
    ]
}
sorted_keys = sorted(data.keys())

for k in sorted_keys:
    data[k] = json.dumps(data[k])
    print(k+"="+data[k])
json_data = json.dumps(data)

url = "https://openapi.alipay.com/gateway.do"

# response = requests.post(url, json_data)

# print(response.text)