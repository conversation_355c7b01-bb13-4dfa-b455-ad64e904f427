﻿--清空表
truncate table info.tmp_hdf_0811;
--导入excel数据，或者其它造数据（好多分商户号，交易日期，金额分）
INSERT INTO info.tmp_hdf_0811 (CUSTOMER_CODE, TXN_DATE, AMOUNT)
VALUES ('562100003755296', '20211230105003', 1400);

--清空表
truncate table info.TXS_WITHDRAW_TRADE_ORDER_HDF;

--每个商户取1条代付数据
insert into info.TXS_WITHDRAW_TRADE_ORDER_HDF(ID, OUT_TRADE_NO, TRANSACTION_NO, TOTAL_FEE, PAY_CURRENCY, CUSTOMER_CODE,
                                    CHANNEL_TYPE, NOTIFY_URL, PAY_STATE, CREATE_TIME, ARRIVAL_TYPE, ACTUAL_FEE,
                                    PROCEDURE_FEE, CARD_NO, BEGIN_TIME, END_TIME, BUSINESS_INST_ID, PRODUCE_RATE,
                                    CHANNEL_ORDER, CHANNEL_NAME, SOURCE_TYPE, BANK_ACCOUNT_TYPE, CUSTOMERNAME,
                                    ACCOUNT_TYPE_CODE, BUSINESS_CODE, TH_STATE, BANK_USER_NAME, BANK_NAME, BANK_NO,
                                    OPR_OPERATED, PAY_METHOD, PAY_CHANNEL_ID, CARD_NO_CIPHER, BANK_USER_NAME_FULL,
                                    BANK_USER_CERT_FULL, CHANNEL_RESP_CODE, CHANNEL_RESP_MSG, CHANNEL_QUERY_CODE,
                                    CHANNEL_QUERY_MSG, BUSINESS_MAN, BUSINESS_MAN_ID, COMPANY_NAME, COMPANY_ID,
                                    TRADE_SOURCE, FEE_PER, RATE_MODE, TRANSACTION_TYPE, TERMINAL_NO)
select o.ID, o.OUT_TRADE_NO, o.TRANSACTION_NO, o.TOTAL_FEE, o.PAY_CURRENCY, h.CUSTOMER_CODE,
                                    CHANNEL_TYPE, o.NOTIFY_URL, o.PAY_STATE, o.CREATE_TIME, o.ARRIVAL_TYPE, o.ACTUAL_FEE,
                                    PROCEDURE_FEE, o.CARD_NO, o.BEGIN_TIME, o.END_TIME, o.BUSINESS_INST_ID, o.PRODUCE_RATE,
                                    CHANNEL_ORDER, o.CHANNEL_NAME, o.SOURCE_TYPE, o.BANK_ACCOUNT_TYPE, o.CUSTOMERNAME,
                                    ACCOUNT_TYPE_CODE, o.BUSINESS_CODE, o.TH_STATE, o.BANK_USER_NAME, o.BANK_NAME, o.BANK_NO,
                                    OPR_OPERATED, o.PAY_METHOD, o.PAY_CHANNEL_ID, o.CARD_NO_CIPHER, o.BANK_USER_NAME_FULL,
                                    BANK_USER_CERT_FULL, o.CHANNEL_RESP_CODE, o.CHANNEL_RESP_MSG, o.CHANNEL_QUERY_CODE,
                                    CHANNEL_QUERY_MSG, o.BUSINESS_MAN, o.BUSINESS_MAN_ID, o.COMPANY_NAME, o.COMPANY_ID,
                                    TRADE_SOURCE, o.FEE_PER, o.RATE_MODE, o.TRANSACTION_TYPE, o.TERMINAL_NO
from epsp.TXS_WITHDRAW_TRADE_ORDER o,
     info.TMP_HDF_OUT h
where o.TRANSACTION_NO = h.WITHDRAW_TRANSACTION_NO
  and h.CUSTOMER_CODE = '***************'
  and rownum = 1;


--关联
truncate table info.TXS_WITHDRAW_HDF_FINAL;
INSERT INTO info.TXS_WITHDRAW_HDF_FINAL (ID, OUT_TRADE_NO, TRANSACTION_NO, TOTAL_FEE, PAY_CURRENCY, CUSTOMER_CODE,
                                    CHANNEL_TYPE, NOTIFY_URL, PAY_STATE, CREATE_TIME, ARRIVAL_TYPE, ACTUAL_FEE,
                                    PROCEDURE_FEE, CARD_NO, BEGIN_TIME, END_TIME, BUSINESS_INST_ID, PRODUCE_RATE,
                                    CHANNEL_ORDER, CHANNEL_NAME, SOURCE_TYPE, BANK_ACCOUNT_TYPE, CUSTOMERNAME,
                                    ACCOUNT_TYPE_CODE, BUSINESS_CODE, TH_STATE, BANK_USER_NAME, BANK_NAME, BANK_NO,
                                    OPR_OPERATED, PAY_METHOD, PAY_CHANNEL_ID, CARD_NO_CIPHER, BANK_USER_NAME_FULL,
                                    BANK_USER_CERT_FULL, CHANNEL_RESP_CODE, CHANNEL_RESP_MSG, CHANNEL_QUERY_CODE,
                                    CHANNEL_QUERY_MSG, BUSINESS_MAN, BUSINESS_MAN_ID, COMPANY_NAME, COMPANY_ID,
                                    TRADE_SOURCE, FEE_PER, RATE_MODE, TRANSACTION_TYPE, TERMINAL_NO)
select -(round(dbms_random.value(********00, ********99)))                  as id,
       h.txn_date || (round(dbms_random.value(********000, ********999)))   as out_trade_no,
       '44' || h.txn_date || (round(dbms_random.value(********, ********))) as transaction_no,
       h.amount                                                             as TOTAL_FEE,
       o.PAY_CURRENCY,
       o.CUSTOMER_CODE,
       o.CHANNEL_TYPE,
       o.NOTIFY_URL,
       o.PAY_STATE,
       to_date(h.txn_date, 'YYYYMMDDHH24MISS')                              as create_time,
       o.ARRIVAL_TYPE,
       h.amount                                                             as ACTUAL_FEE,
       o.PROCEDURE_FEE,
       o.CARD_NO,
       to_date(h.txn_date, 'YYYYMMDDHH24MISS')                              as begin_time,
       to_date(h.txn_date, 'YYYYMMDDHH24MISS')                              as end_time,
       o.BUSINESS_INST_ID,
       o.PRODUCE_RATE,
       '44' || h.txn_date || (round(dbms_random.value(********, ********))) as channel_order,
       o.CHANNEL_NAME,
       o.SOURCE_TYPE,
       o.BANK_ACCOUNT_TYPE,
       o.CUSTOMERNAME,
       o.ACCOUNT_TYPE_CODE,
       o.BUSINESS_CODE,
       o.TH_STATE,
       o.BANK_USER_NAME,
       o.BANK_NAME,
       o.BANK_NO,
       o.OPR_OPERATED,
       o.PAY_METHOD,
       o.PAY_CHANNEL_ID,
       o.CARD_NO_CIPHER,
       o.BANK_USER_NAME_FULL,
       o.BANK_USER_CERT_FULL,
       o.CHANNEL_RESP_CODE,
       o.CHANNEL_RESP_MSG,
       o.CHANNEL_QUERY_CODE,
       o.CHANNEL_QUERY_MSG,
       o.BUSINESS_MAN,
       o.BUSINESS_MAN_ID,
       o.COMPANY_NAME,
       o.COMPANY_ID,
       o.TRADE_SOURCE,
       o.FEE_PER,
       o.RATE_MODE,
       o.TRANSACTION_TYPE,
       o.TERMINAL_NO
from info.tmp_hdf_0811 h
         left join info.TXS_WITHDRAW_TRADE_ORDER_HDF o on h.customer_code = o.CUSTOMER_CODE;

update info.TXS_WITHDRAW_HDF_FINAL
set CHANNEL_ORDER = TRANSACTION_NO;

--TXS_WITHDRAW_HDF_FINAL为最终数据 插入到TXS_WITHDRAW_TRADE_ORDER
insert into epsp.TXS_WITHDRAW_TRADE_ORDER select * from info.TXS_WITHDRAW_HDF_FINAL;

--新增的插入到中间表
insert into info.TMP_HDF_OUT (order_no, withdraw_order_no, transaction_no, withdraw_transaction_no, state, customer_code,
                         order_date, customername)
select o.TRANSACTION_NO,
       o.OUT_TRADE_NO,
       o.TRANSACTION_NO,
       o.TRANSACTION_NO,
       '1',
       o.CUSTOMER_CODE,
       TO_CHAR(o.CREATE_TIME, 'YYYY-MM-DD') as ORDER_DATE,
       c.NAME
from info.TXS_WITHDRAW_HDF_FINAL o
         left join epsp.CUST_CUSTOMER c on o.CUSTOMER_CODE = c.CUSTOMER_NO
where id < 0;
